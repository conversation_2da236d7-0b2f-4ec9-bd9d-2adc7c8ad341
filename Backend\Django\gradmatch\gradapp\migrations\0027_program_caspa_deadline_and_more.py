# Generated by Django 5.0.1 on 2025-07-21 00:25

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('gradapp', '0026_applicationrequirement_non_resident_tuition_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='program',
            name='caspa_deadline',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='caspa_deadline_requirement',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='caspa_member',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='program',
            name='city_state',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='credentials_offered_json',
            field=models.J<PERSON><PERSON>ield(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='curriculum_focus_json',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='email',
            field=models.EmailField(blank=True, max_length=254, null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='international_application_link',
            field=models.URLField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='last_updated',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='program_length_months',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='program_social',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='program_website',
            field=models.URLField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='required_onsite_interview',
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='type_of_masters_degree',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='types_of_interviews',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='upcoming_caspa_cycle',
            field=models.BooleanField(default=True),
        ),
        migrations.CreateModel(
            name='CASPARequirement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('caspa_member', models.BooleanField(default=True)),
                ('upcoming_caspa_cycle', models.BooleanField(default=True)),
                ('application_deadline', models.DateField(blank=True, null=True)),
                ('deadline_requirement', models.TextField(blank=True, null=True)),
                ('supplemental_application', models.BooleanField(default=False)),
                ('supplemental_deadline', models.DateField(blank=True, null=True)),
                ('supplemental_application_fee', models.CharField(blank=True, max_length=50, null=True)),
                ('supplemental_application_fee_waiver', models.BooleanField(default=False)),
                ('view_supplemental_application', models.BooleanField(default=False)),
                ('program', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='caspa_requirements', to='gradapp.program')),
            ],
        ),
        migrations.CreateModel(
            name='ProgramCurriculum',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('curriculum_focus', models.JSONField(blank=True, null=True)),
                ('didactic_phase_length', models.CharField(blank=True, max_length=50, null=True)),
                ('clinical_phase_length', models.CharField(blank=True, max_length=50, null=True)),
                ('total_program_length', models.CharField(blank=True, max_length=50, null=True)),
                ('didactic_courses', models.JSONField(blank=True, null=True)),
                ('clinical_rotations', models.JSONField(blank=True, null=True)),
                ('elective_rotations', models.JSONField(blank=True, null=True)),
                ('program', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='program_curriculum', to='gradapp.program')),
            ],
        ),
        migrations.CreateModel(
            name='TuitionInformation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('separate_tuition_rates', models.BooleanField(default=False)),
                ('tuition', models.CharField(blank=True, max_length=50, null=True)),
                ('resident_tuition', models.CharField(blank=True, max_length=50, null=True)),
                ('non_resident_tuition', models.CharField(blank=True, max_length=50, null=True)),
                ('seat_deposit', models.BooleanField(default=False)),
                ('seat_deposit_cost', models.CharField(blank=True, max_length=20, null=True)),
                ('refundable_seat_deposit', models.BooleanField(default=False)),
                ('program', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tuition_information', to='gradapp.program')),
            ],
        ),
    ]
