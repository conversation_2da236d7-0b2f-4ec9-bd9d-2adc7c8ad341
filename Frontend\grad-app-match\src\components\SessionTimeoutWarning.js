import React from 'react';
import styled from '@emotion/styled';

const Overlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
`;

const Modal = styled.div`
  background: white;
  border-radius: 8px;
  padding: 2rem;
  max-width: 400px;
  width: 90%;
  text-align: center;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
`;

const Title = styled.h3`
  color: #dc2626;
  margin: 0 0 1rem 0;
`;

const Timer = styled.div`
  font-size: 2rem;
  font-weight: bold;
  color: #dc2626;
  margin: 1rem 0;
`;

const Button = styled.button`
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.375rem;
  font-size: 1rem;
  cursor: pointer;
  margin: 0.5rem;
  transition: background-color 0.2s;
`;

const PrimaryButton = styled(Button)`
  background-color: #3b82f6;
  color: white;

  &:hover {
    background-color: #2563eb;
  }
`;

const SecondaryButton = styled(Button)`
  background-color: #6b7280;
  color: white;

  &:hover {
    background-color: #4b5563;
  }
`;

const SessionTimeoutWarning = ({ timeLeft, onExtend, onLogout }) => {
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <Overlay>
      <Modal>
        <Title>⚠️ Session Expiring</Title>
        <p>Your session will expire in:</p>
        <Timer>{formatTime(timeLeft)}</Timer>
        <p>Would you like to extend your session?</p>
        
        <div>
          <PrimaryButton onClick={onExtend}>
            Stay Logged In
          </PrimaryButton>
          <SecondaryButton onClick={onLogout}>
            Log Out Now
          </SecondaryButton>
        </div>
      </Modal>
    </Overlay>
  );
};

export default SessionTimeoutWarning;