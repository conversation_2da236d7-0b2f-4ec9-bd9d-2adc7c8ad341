# Generated by Django 5.0.1 on 2025-01-24 21:34

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('gradapp', '0008_remove_prerequisites_field'),
    ]

    operations = [
        migrations.AlterField(
            model_name='applicationrequirement',
            name='admission_type',
            field=models.Char<PERSON><PERSON>(max_length=255),
        ),
        migrations.AlterField(
            model_name='applicationrequirement',
            name='supplemental_fee',
            field=models.Char<PERSON>ield(max_length=100),
        ),
        migrations.AlterField(
            model_name='gparequirement',
            name='minimum_overall',
            field=models.Char<PERSON>ield(max_length=100),
        ),
        migrations.AlterField(
            model_name='gparequirement',
            name='minimum_prereq',
            field=models.CharField(max_length=100),
        ),
        migrations.AlterField(
            model_name='gparequirement',
            name='minimum_science',
            field=models.CharField(max_length=100),
        ),
        migrations.Alter<PERSON>ield(
            model_name='grerequirement',
            name='analytical_percentile',
            field=models.Char<PERSON>ield(max_length=100),
        ),
        migrations.AlterField(
            model_name='grerequirement',
            name='analytical_score',
            field=models.CharField(max_length=100),
        ),
        migrations.AlterField(
            model_name='grerequirement',
            name='quantitative_percentile',
            field=models.CharField(max_length=100),
        ),
        migrations.AlterField(
            model_name='grerequirement',
            name='quantitative_score',
            field=models.CharField(max_length=100),
        ),
        migrations.AlterField(
            model_name='grerequirement',
            name='required',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='grerequirement',
            name='verbal_percentile',
            field=models.CharField(max_length=100),
        ),
        migrations.AlterField(
            model_name='grerequirement',
            name='verbal_score',
            field=models.CharField(max_length=100),
        ),
        migrations.AlterField(
            model_name='healthcareexperience',
            name='status',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='healthcareexperience',
            name='time_limit',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='patientcareexperience',
            name='paid_status',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='patientcareexperience',
            name='time_limit',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='patientcareexperience',
            name='volunteer_status',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='prerequisitecourse',
            name='course_name',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='prerequisitecourse',
            name='credits',
            field=models.CharField(max_length=100),
        ),
        migrations.AlterField(
            model_name='shadowingrequirement',
            name='other_shadowing',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='shadowingrequirement',
            name='pa_shadowing',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='shadowingrequirement',
            name='physician_shadowing',
            field=models.CharField(max_length=255),
        ),
    ]
