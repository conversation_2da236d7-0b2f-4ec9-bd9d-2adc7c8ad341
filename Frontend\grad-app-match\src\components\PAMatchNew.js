import React, { useState, useEffect, useCallback } from 'react';
import axios from 'axios';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import ProgramSkeleton from './ProgramSkeleton';
import { normalizeProfileData } from '../utils/profileUtils';
import './PAMatch.css';

const PAMatch = () => {
    const { token } = useAuth();
    const navigate = useNavigate();
    const [userTranscript, setUserTranscript] = useState(null);
    const [profileData, setProfileData] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    const [programs, setPrograms] = useState([]);
    const [expandedPrograms, setExpandedPrograms] = useState({});
    const [searchTerm, setSearchTerm] = useState('');
    const [filterBy, setFilterBy] = useState('all');

    // Check for token on mount
    useEffect(() => {
        if (!token) {
            navigate('/login');
            return;
        }
    }, [token, navigate]);

    // Fetch data
    useEffect(() => {
        const fetchData = async () => {
            if (!token) return;
            
            setIsLoading(true);
            setError(null);
            
            try {
                const headers = {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                };

                // Fetch programs
                const programsResponse = await axios.get(`${process.env.REACT_APP_API_URL || 'http://127.0.0.1:8000'}/api/programs/`, { headers });
                setPrograms(programsResponse.data.programs || []);

                // Fetch profile data
                const profileResponse = await axios.get(`${process.env.REACT_APP_API_URL || 'http://127.0.0.1:8000'}/api/profile/`, { headers });
                const normalizedProfileData = normalizeProfileData(profileResponse.data);
                setProfileData(normalizedProfileData);

                // Fetch transcript data
                const transcriptResponse = await axios.get(`${process.env.REACT_APP_API_URL || 'http://127.0.0.1:8000'}/api/transcript/get/`, { headers });
                const transcripts = transcriptResponse.data.transcripts || [];
                
                // Mock transcript data for now - replace with actual calculation
                setUserTranscript({
                    calculated_gpa: 3.41,
                    science_gpa: 3.20,
                    transcripts: transcripts
                });

            } catch (err) {
                console.error('Error fetching data:', err);
                if (err.response?.status === 401) {
                    navigate('/login');
                    return;
                }
                setError(err.response?.data?.error || 'Failed to fetch data');
            } finally {
                setIsLoading(false);
            }
        };

        fetchData();
    }, [token, navigate]);

    // Simple eligibility check
    const checkProgramEligibility = (program) => {
        if (!userTranscript || !program.requirements) {
            return { eligibility: 'incomplete', metPrerequisites: 0 };
        }

        const gpaRequirement = program.requirements.gpa?.overall || 0;
        const meetsGPA = userTranscript.calculated_gpa >= gpaRequirement;
        const totalPrereqs = program.requirements.prerequisite_courses?.length || 0;
        const metPrereqs = Math.floor(totalPrereqs * 0.8); // Mock calculation

        if (meetsGPA && metPrereqs >= totalPrereqs) {
            return { eligibility: 'eligible', metPrerequisites: metPrereqs };
        } else if (meetsGPA) {
            return { eligibility: 'ineligible', metPrerequisites: metPrereqs };
        } else {
            return { eligibility: 'incomplete', metPrerequisites: metPrereqs };
        }
    };

    // Filter and sort programs
    const getFilteredAndSortedPrograms = () => {
        if (!programs || !userTranscript) return [];
        
        let filtered = programs;
        
        // Apply search filter
        if (searchTerm) {
            filtered = filtered.filter(program => 
                program.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                program.school?.name?.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }
        
        // Apply eligibility filter
        if (filterBy !== 'all') {
            filtered = filtered.filter(program => {
                const eligibility = checkProgramEligibility(program).eligibility;
                return eligibility === filterBy;
            });
        }
        
        return filtered.sort((a, b) => a.name.localeCompare(b.name));
    };

    const toggleProgram = (programName) => {
        setExpandedPrograms(prev => ({
            ...prev,
            [programName]: !prev[programName]
        }));
    };

    const getStats = () => {
        if (!programs || !userTranscript) return { total: 0, eligible: 0, prerequisites: 0, incomplete: 0 };
        
        const stats = programs.reduce((acc, program) => {
            const eligibility = checkProgramEligibility(program).eligibility;
            acc.total++;
            if (eligibility === 'eligible') acc.eligible++;
            else if (eligibility === 'ineligible') acc.prerequisites++;
            else acc.incomplete++;
            return acc;
        }, { total: 0, eligible: 0, prerequisites: 0, incomplete: 0 });
        
        return stats;
    };

    const stats = getStats();

    return (
        <div className="pa-explorer-container">
            {/* Header */}
            <header className="pa-explorer-header">
                <div className="header-left">
                    <div className="app-title">
                        <span className="app-icon">📚</span>
                        <span>PA Program Explorer</span>
                    </div>
                </div>
                <button 
                    onClick={() => navigate('/home')}
                    className="back-to-home-btn"
                >
                    Back to Home
                </button>
            </header>

            {/* Academic Summary Cards */}
            <section className="academic-summary">
                <h2 className="section-title">
                    <span className="section-icon">👤</span>
                    Academic Summary
                </h2>
                <div className="gpa-cards">
                    <div className="gpa-card cumulative">
                        <div className="gpa-card-content">
                            <div className="gpa-label">Cumulative GPA</div>
                            <div className="gpa-value">{userTranscript?.calculated_gpa?.toFixed(2) || '3.41'}</div>
                        </div>
                        <div className="gpa-card-icon">📊</div>
                    </div>
                    <div className="gpa-card science">
                        <div className="gpa-card-content">
                            <div className="gpa-label">Science GPA</div>
                            <div className="gpa-value">{userTranscript?.science_gpa?.toFixed(2) || '3.20'}</div>
                        </div>
                        <div className="gpa-card-icon">📊</div>
                    </div>
                </div>
            </section>

            {/* Search and Filter Section */}
            <section className="search-filter-section">
                <div className="search-container">
                    <div className="search-input-wrapper">
                        <span className="search-icon">🔍</span>
                        <input
                            type="text"
                            placeholder="Search programs or schools..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="search-input-modern"
                        />
                    </div>
                    <div className="filter-controls">
                        <select
                            value={filterBy}
                            onChange={(e) => setFilterBy(e.target.value)}
                            className="filter-dropdown"
                        >
                            <option value="all">All Programs</option>
                            <option value="eligible">Eligible Only</option>
                            <option value="ineligible">Need Prerequisites</option>
                            <option value="incomplete">Incomplete Info</option>
                        </select>
                        <div className="sort-controls">
                            <span>Name</span>
                            <div className="view-toggle">
                                <button className="view-btn active">☰</button>
                                <button className="view-btn">⊞</button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Program Statistics */}
            {!isLoading && userTranscript && (
                <section className="program-stats">
                    <div className="stat-card total">
                        <div className="stat-icon">📚</div>
                        <div className="stat-content">
                            <div className="stat-label">Total Programs</div>
                            <div className="stat-value">{stats.total}</div>
                        </div>
                    </div>
                    <div className="stat-card eligible">
                        <div className="stat-icon">✅</div>
                        <div className="stat-content">
                            <div className="stat-label">Eligible</div>
                            <div className="stat-value">{stats.eligible}</div>
                        </div>
                    </div>
                    <div className="stat-card prerequisites">
                        <div className="stat-icon">⚠️</div>
                        <div className="stat-content">
                            <div className="stat-label">Need Prerequisites</div>
                            <div className="stat-value">{stats.prerequisites}</div>
                        </div>
                    </div>
                    <div className="stat-card incomplete">
                        <div className="stat-icon">⏰</div>
                        <div className="stat-content">
                            <div className="stat-label">Incomplete</div>
                            <div className="stat-value">{stats.incomplete}</div>
                        </div>
                    </div>
                </section>
            )}

            {/* Results Count */}
            {!isLoading && userTranscript && (
                <div className="results-count">
                    Showing {getFilteredAndSortedPrograms().length} of {programs.length} programs
                </div>
            )}

            {/* Error Message */}
            {error && (
                <div className="error-message">
                    {error}
                </div>
            )}

            {/* Loading State */}
            {isLoading && (
                <div className="loading-container">
                    <ProgramSkeleton />
                    <ProgramSkeleton />
                    <ProgramSkeleton />
                </div>
            )}

            {/* Programs List */}
            {userTranscript && !isLoading && (
                <section className="programs-list-section">
                    <div className="programs-list">
                        {getFilteredAndSortedPrograms().map((program, index) => {
                            const eligibilityData = checkProgramEligibility(program);
                            const eligibility = eligibilityData.eligibility;
                            
                            return (
                                <div key={`${program.name}-${index}`} className={`program-item ${eligibility}`}>
                                    <div className="program-main-info">
                                        <div className="program-details">
                                            <h3 className="program-name">
                                                <a 
                                                    href={program.program_url} 
                                                    target="_blank" 
                                                    rel="noopener noreferrer"
                                                    className="program-link"
                                                >
                                                    {program.name}
                                                </a>
                                            </h3>
                                            <p className="program-location">
                                                {program.school?.city}, {program.school?.state}
                                            </p>
                                            
                                            <div className="program-requirements-summary">
                                                <div className="requirement-item">
                                                    <span className="requirement-label">Min GPA:</span>
                                                    <span className="requirement-value">
                                                        {program.requirements?.gpa?.overall || 'N/A'}
                                                    </span>
                                                </div>
                                                <div className="requirement-item">
                                                    <span className="requirement-label">Prerequisites:</span>
                                                    <span className="requirement-value">
                                                        {eligibilityData.metPrerequisites}/{program.requirements?.prerequisite_courses?.length || 0}
                                                    </span>
                                                </div>
                                                <div className="requirement-item">
                                                    <span className="requirement-label">Completion:</span>
                                                    <div className="completion-bar">
                                                        <div 
                                                            className="completion-fill" 
                                                            style={{
                                                                width: `${eligibility === 'eligible' ? 100 : eligibility === 'ineligible' ? 60 : 80}%`
                                                            }}
                                                        ></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div className="program-status">
                                            <div className={`status-badge ${eligibility}`}>
                                                {eligibility === 'eligible' && (
                                                    <>
                                                        <span className="status-icon">✅</span>
                                                        <span>Eligible</span>
                                                    </>
                                                )}
                                                {eligibility === 'ineligible' && (
                                                    <>
                                                        <span className="status-icon">⚠️</span>
                                                        <span>Need Prerequisites</span>
                                                    </>
                                                )}
                                                {eligibility === 'incomplete' && (
                                                    <>
                                                        <span className="status-icon">⏰</span>
                                                        <span>Incomplete</span>
                                                    </>
                                                )}
                                            </div>
                                            <button 
                                                className="expand-btn"
                                                onClick={() => toggleProgram(program.name)}
                                            >
                                                {expandedPrograms[program.name] ? '▼' : '▶'}
                                            </button>
                                        </div>
                                    </div>
                                    
                                    {expandedPrograms[program.name] && (
                                        <div className="program-expanded-details">
                                            <div className="requirements-detailed">
                                                <h4>Detailed Requirements</h4>
                                                <p>GPA Requirement: {program.requirements?.gpa?.overall || 'N/A'}</p>
                                                <p>Science GPA: {program.requirements?.gpa?.science || 'N/A'}</p>
                                                <p>Prerequisites: {program.requirements?.prerequisite_courses?.length || 0} courses</p>
                                                <p>Location: {program.school?.city}, {program.school?.state}</p>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            );
                        })}
                    </div>
                </section>
            )}
        </div>
    );
};

export default PAMatch;