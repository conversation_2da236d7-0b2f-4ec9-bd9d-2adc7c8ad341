# Generated manually to fix field length constraints
# This migration increases field lengths for enhanced data models to prevent "value too long" errors

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('gradapp', '0031_extend_char_fields'),
    ]

    operations = [
        # MatriculantDemographics field length increases
        migrations.Alter<PERSON>ield(
            model_name='matriculantdemographics',
            name='female_count',
            field=models.Char<PERSON>ield(max_length=50, null=True, blank=True),
        ),
        migrations.AlterField(
            model_name='matriculantdemographics',
            name='male_count',
            field=models.Char<PERSON><PERSON>(max_length=50, null=True, blank=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='matriculantdemographics',
            name='non_binary_count',
            field=models.Char<PERSON><PERSON>(max_length=50, null=True, blank=True),
        ),
        migrations.AlterField(
            model_name='matriculantdemographics',
            name='gender_unknown_count',
            field=models.Char<PERSON><PERSON>(max_length=50, null=True, blank=True),
        ),
        migrations.Alt<PERSON><PERSON><PERSON>(
            model_name='matriculantdemographics',
            name='american_indian_count',
            field=models.Char<PERSON>ield(max_length=50, null=True, blank=True),
        ),
        migrations.AlterField(
            model_name='matriculantdemographics',
            name='asian_count',
            field=models.CharField(max_length=50, null=True, blank=True),
        ),
        migrations.AlterField(
            model_name='matriculantdemographics',
            name='black_count',
            field=models.CharField(max_length=50, null=True, blank=True),
        ),
        migrations.AlterField(
            model_name='matriculantdemographics',
            name='hispanic_count',
            field=models.CharField(max_length=50, null=True, blank=True),
        ),
        migrations.AlterField(
            model_name='matriculantdemographics',
            name='hawaiian_pacific_count',
            field=models.CharField(max_length=50, null=True, blank=True),
        ),
        migrations.AlterField(
            model_name='matriculantdemographics',
            name='white_count',
            field=models.CharField(max_length=50, null=True, blank=True),
        ),
        migrations.AlterField(
            model_name='matriculantdemographics',
            name='other_count',
            field=models.CharField(max_length=50, null=True, blank=True),
        ),
        
        # PANCEPassRate field length increases
        migrations.AlterField(
            model_name='pancepassrate',
            name='program_pass_rate',
            field=models.CharField(max_length=50, null=True, blank=True),
        ),
        migrations.AlterField(
            model_name='pancepassrate',
            name='national_pass_rate',
            field=models.CharField(max_length=50, null=True, blank=True),
        ),
        migrations.AlterField(
            model_name='pancepassrate',
            name='ultimate_pass_rate',
            field=models.CharField(max_length=50, null=True, blank=True),
        ),
        
        # AttritionData field length increases
        migrations.AlterField(
            model_name='attritiondata',
            name='attrition_rate',
            field=models.CharField(max_length=50, null=True, blank=True),
        ),
        migrations.AlterField(
            model_name='attritiondata',
            name='graduation_rate',
            field=models.CharField(max_length=50, null=True, blank=True),
        ),
    ]
