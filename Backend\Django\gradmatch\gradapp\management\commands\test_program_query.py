from django.core.management.base import BaseCommand
from gradapp.models import Program
import traceback

class Command(BaseCommand):
    help = 'Test the program query used in the API endpoint'

    def handle(self, *args, **options):
        try:
            self.stdout.write('Testing program query...')
            
            # Test the same query used in get_program_list
            programs = Program.objects.select_related('school').prefetch_related(
                'prerequisite_courses',
                'gpa_requirements',
                'healthcare_experience',
                'patient_care_experience',
                'shadowing_requirements',
                'gre_requirements',
                'caspa_requirements',
                'tuition_information',
                'enhanced_class_profiles',
                'program_curriculum',
                'matriculant_demographics',
                'attrition_data',
                'pance_pass_rates',  # Added this line
                'other_requirements',
                'recommendation_requirements'
            ).all()
            
            program_count = programs.count()
            self.stdout.write(f'Successfully queried {program_count} programs')
            
            if program_count > 0:
                # Test processing the first program
                program = programs.first()
                self.stdout.write(f'Testing program: {program.name}')
                
                # Test PANCE data access
                pance_data = program.pance_pass_rates.first()
                if pance_data:
                    self.stdout.write(f'PANCE data found: {pance_data.program_pass_rate}')
                else:
                    self.stdout.write('No PANCE data found')
                
                # Test class profile access
                class_profile = program.enhanced_class_profiles.first()
                if class_profile:
                    self.stdout.write(f'Class profile found: {class_profile.total_applications}')
                else:
                    self.stdout.write('No class profile found')
                
                # Test attrition access
                attrition = program.attrition_data.first()
                if attrition:
                    self.stdout.write(f'Attrition data found: {attrition.graduation_rate}')
                else:
                    self.stdout.write('No attrition data found')
                    
                self.stdout.write(self.style.SUCCESS('Program query test completed successfully!'))
            else:
                self.stdout.write(self.style.WARNING('No programs found in database'))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error in program query: {str(e)}'))
            self.stdout.write(f'Traceback: {traceback.format_exc()}')
