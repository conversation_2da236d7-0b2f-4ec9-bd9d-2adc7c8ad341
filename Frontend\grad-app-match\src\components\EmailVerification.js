import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import styled from '@emotion/styled';

const Container = styled.div`
  max-width: 500px;
  margin: 2rem auto;
  padding: 2rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
`;

const Title = styled.h2`
  color: #2c3e50;
  margin-bottom: 1.5rem;
`;

const Message = styled.div`
  padding: 1rem;
  border-radius: 0.375rem;
  margin-bottom: 1.5rem;
`;

const SuccessMessage = styled(Message)`
  color: #27ae60;
  background-color: #eafaf1;
  border: 1px solid #27ae60;
`;

const ErrorMessage = styled(Message)`
  color: #e74c3c;
  background-color: #fdecea;
  border: 1px solid #e74c3c;
`;

const InfoMessage = styled(Message)`
  color: #3498db;
  background-color: #ebf3fd;
  border: 1px solid #3498db;
`;

const Button = styled.button`
  padding: 0.75rem 1.5rem;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
  margin: 0.5rem;

  &:hover:not(:disabled) {
    background-color: #2980b9;
  }

  &:disabled {
    background-color: #bdc3c7;
    cursor: not-allowed;
  }
`;

const SecondaryButton = styled(Button)`
  background-color: #95a5a6;
  
  &:hover:not(:disabled) {
    background-color: #7f8c8d;
  }
`;

const EmailVerification = () => {
  const [status, setStatus] = useState('verifying'); // 'verifying', 'success', 'error', 'expired'
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  
  const { supabase } = useAuth();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  useEffect(() => {
    const verifyEmail = async () => {
      try {
        // Get the token from URL parameters
        const token = searchParams.get('token');
        const type = searchParams.get('type');
        
        if (!token || type !== 'signup') {
          setStatus('error');
          setMessage('Invalid verification link. Please check your email for the correct link.');
          return;
        }

        // Verify the email using Supabase
        const { data, error } = await supabase.auth.verifyOtp({
          token_hash: token,
          type: 'signup'
        });

        if (error) {
          console.error('Email verification error:', error);
          
          if (error.message?.includes('expired')) {
            setStatus('expired');
            setMessage('Your verification link has expired. Please request a new one.');
          } else if (error.message?.includes('invalid')) {
            setStatus('error');
            setMessage('Invalid verification link. Please check your email for the correct link.');
          } else {
            setStatus('error');
            setMessage('Email verification failed. Please try again or contact support.');
          }
          return;
        }

        // Success
        setStatus('success');
        setMessage('Email verified successfully! You can now log in to your account.');
        
        // Auto-redirect to login after 3 seconds
        setTimeout(() => {
          navigate('/login');
        }, 3000);

      } catch (err) {
        console.error('Verification error:', err);
        setStatus('error');
        setMessage('An unexpected error occurred during verification. Please try again.');
      }
    };

    verifyEmail();
  }, [searchParams, supabase, navigate]);

  const handleResendVerification = async () => {
    setIsLoading(true);
    
    try {
      // This would require the user's email - you might want to store it in localStorage
      // or ask them to enter it again
      const email = localStorage.getItem('pendingVerificationEmail');
      
      if (!email) {
        setMessage('Please go back to the registration page and sign up again to receive a new verification email.');
        setIsLoading(false);
        return;
      }

      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: email
      });

      if (error) {
        throw error;
      }

      setMessage('New verification email sent! Please check your inbox and spam folder.');
      
    } catch (err) {
      console.error('Resend verification error:', err);
      setMessage('Failed to resend verification email. Please try registering again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoToLogin = () => {
    navigate('/login');
  };

  const handleGoToRegister = () => {
    navigate('/register');
  };

  return (
    <Container>
      <Title>Email Verification</Title>
      
      {status === 'verifying' && (
        <InfoMessage>
          Verifying your email address, please wait...
        </InfoMessage>
      )}
      
      {status === 'success' && (
        <div>
          <SuccessMessage>{message}</SuccessMessage>
          <p>Redirecting to login page in 3 seconds...</p>
          <Button onClick={handleGoToLogin}>Go to Login Now</Button>
        </div>
      )}
      
      {status === 'error' && (
        <div>
          <ErrorMessage>{message}</ErrorMessage>
          <div>
            <Button onClick={handleGoToRegister}>Register Again</Button>
            <SecondaryButton onClick={handleGoToLogin}>Go to Login</SecondaryButton>
          </div>
        </div>
      )}
      
      {status === 'expired' && (
        <div>
          <ErrorMessage>{message}</ErrorMessage>
          <div>
            <Button 
              onClick={handleResendVerification} 
              disabled={isLoading}
            >
              {isLoading ? 'Sending...' : 'Resend Verification Email'}
            </Button>
            <SecondaryButton onClick={handleGoToRegister}>Register Again</SecondaryButton>
          </div>
        </div>
      )}
    </Container>
  );
};

export default EmailVerification;