# PA JSON Parser Utility

## Overview

The `PAJSONParser` utility class is designed to read, parse, and validate JSON files containing PA program data from the "D:\ScrapperPAEAReal" directory. This utility is part of the database schema update migration process and provides comprehensive error handling and validation.

## Features

- **File Discovery**: Automatically discovers all JSON files in the specified directory
- **JSON Parsing**: Safely reads and parses JSON files with proper error handling
- **Data Validation**: Comprehensive validation including:
  - Required field validation
  - Data type validation
  - Business logic validation (GPA ranges, date formats, etc.)
- **Error Handling**: Robust error handling for malformed or incomplete JSON files
- **Batch Processing**: Process multiple files with detailed reporting
- **Logging**: Comprehensive logging of processing status and errors

## Usage

### Basic Usage

```python
from gradapp.utils.json_parser import PAJSONParser

# Initialize parser with default directory
parser = PAJSONParser()

# Or specify custom directory
parser = PAJSONParser("/path/to/json/files")

# Process all files in the directory
valid_data, summary = parser.process_all_files()

print(f"Processed {summary['processed_successfully']} out of {summary['total_files']} files")
```

### Processing Individual Files

```python
from pathlib import Path

# Process a single file
file_path = Path("program_data.json")
program_data = parser.process_single_file(file_path)

if program_data:
    print(f"Successfully processed: {program_data['program_name']}")
else:
    print("File processing failed")
```

### Validation Only

```python
# Read and validate data without processing
data = parser.read_json_file(file_path)
is_valid, errors = parser.validate_json_data(data, file_path)

if not is_valid:
    print("Validation errors:")
    for error in errors:
        print(f"  - {error}")
```

## Validation Rules

### Required Fields
- `program_name`
- `program_url`
- `mission_statement`
- `caspa_member`
- `upcoming_caspa_cycle`

### Data Type Validation
- **String fields**: program_name, program_url, mission_statement, etc.
- **Boolean fields**: caspa_member, upcoming_caspa_cycle, part_time_option, etc.
- **Array fields**: curriculum_focus, credentials_offered, types_of_interviews, etc.
- **Object fields**: address, incoming_class_profile, etc.

### Business Logic Validation
- **GPA values**: Must be between 0.0 and 4.0
- **Date formats**: Supports MM/DD/YYYY and YYYY-MM-DD formats
- **Class size**: Must be positive integer
- **Tuition**: Must be non-negative numeric value

## Error Handling

The parser handles various error conditions:

- **File not found**: Missing JSON files
- **Invalid JSON**: Malformed JSON syntax
- **Encoding errors**: File encoding issues
- **Validation errors**: Missing required fields, invalid data types, business rule violations

## Processing Summary

The `process_all_files()` method returns a summary with:

```python
{
    'total_files': int,
    'processed_successfully': int,
    'failed_files': int,
    'success_rate': float,
    'processed_file_list': List[str],
    'failed_file_details': List[dict]
}
```

## Logging

The parser uses Python's logging module with the logger name `pa_json_parser`. Log levels:

- **INFO**: Successful processing, file discovery
- **WARNING**: Validation failures
- **ERROR**: File reading errors, parsing failures
- **DEBUG**: Detailed processing information

## Integration with Migration

This utility is designed to be used in the database migration process:

1. **Discovery**: Find all JSON files in the data directory
2. **Validation**: Validate each file's data structure
3. **Processing**: Extract valid data for database insertion
4. **Reporting**: Provide detailed processing statistics

## Example Output

```
2025-07-20 10:00:00 - pa_json_parser - INFO - Discovered 150 JSON files in D:\ScrapperPAEAReal
2025-07-20 10:00:01 - pa_json_parser - INFO - Successfully processed: program1.json
2025-07-20 10:00:02 - pa_json_parser - WARNING - Validation failed for program2.json: 3 errors
2025-07-20 10:00:02 - pa_json_parser - WARNING -   - Missing required field: program_name
2025-07-20 10:00:03 - pa_json_parser - INFO - Processing complete: 148/150 files successful
```

## Testing

Run the test suite to verify functionality:

```bash
python manage.py test gradapp.tests.test_json_parser
```

## Demo

Run the demonstration script to see the parser in action:

```bash
python gradapp/utils/demo_json_parser.py
```