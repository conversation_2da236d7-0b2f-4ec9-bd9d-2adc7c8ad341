from django.core.management.base import BaseCommand
from gradapp.models import Program, PANCEPassRate, AttritionData, MatriculantDemographics, ProgramCurriculum
import json


class Command(BaseCommand):
    help = 'Display detailed information for a specific program'

    def add_arguments(self, parser):
        parser.add_argument('program_name', type=str, help='Name of the program to display')
        parser.add_argument('--json', action='store_true', help='Show raw JSON data')
        parser.add_argument('--enhanced', action='store_true', help='Show enhanced data records')
        parser.add_argument('--comprehensive', action='store_true', help='Show all comprehensive data from JSON')
        parser.add_argument('--all', action='store_true', help='Show everything (enhanced + comprehensive + JSON)')

    def handle(self, *args, **options):
        program_name = options['program_name']
        
        try:
            program = Program.objects.get(name__icontains=program_name)
        except Program.DoesNotExist:
            self.stdout.write(self.style.ERROR(f'Program not found: {program_name}'))
            # Show similar programs
            similar = Program.objects.filter(name__icontains=program_name.split()[0])[:5]
            if similar:
                self.stdout.write('Similar programs:')
                for p in similar:
                    self.stdout.write(f'  - {p.name}')
            return
        except Program.MultipleObjectsReturned:
            programs = Program.objects.filter(name__icontains=program_name)
            self.stdout.write(self.style.ERROR(f'Multiple programs found:'))
            for p in programs:
                self.stdout.write(f'  - {p.name}')
            return

        # Display basic program info
        self.stdout.write(self.style.SUCCESS(f'\n=== {program.name} ==='))
        self.stdout.write(f'School: {program.school.name}')
        self.stdout.write(f'State: {program.state or "Not specified"}')
        self.stdout.write(f'City: {program.city or "Not specified"}')
        self.stdout.write(f'URL: {program.url}')
        
        if options['enhanced'] or options['all']:
            # Display enhanced data records
            self.stdout.write(self.style.SUCCESS('\n=== ENHANCED DATA RECORDS ==='))
            
            # PANCE Pass Rates
            pance_data = program.pance_pass_rates.all()
            self.stdout.write(f'\nPANCE Pass Rates ({pance_data.count()} records):')
            for pance in pance_data:
                self.stdout.write(f'  Year: {pance.year}, Group: {pance.group}')
                self.stdout.write(f'    Program Pass Rate: {pance.program_pass_rate}')
                self.stdout.write(f'    National Pass Rate: {pance.national_pass_rate}')
                self.stdout.write(f'    Candidates: {pance.candidates_took_pance}')
            
            # Attrition Data
            attrition_data = program.attrition_data.all()
            self.stdout.write(f'\nAttrition Data ({attrition_data.count()} records):')
            for attrition in attrition_data:
                self.stdout.write(f'  Class Year: {attrition.class_year}')
                self.stdout.write(f'    Attrition Rate: {attrition.attrition_rate}')
                self.stdout.write(f'    Graduation Rate: {attrition.graduation_rate}')
                self.stdout.write(f'    Class Size: {attrition.entering_class_size}')
            
            # Demographics
            demographics = program.matriculant_demographics.all()
            self.stdout.write(f'\nDemographics ({demographics.count()} records):')
            for demo in demographics:
                self.stdout.write(f'  Year: {demo.year}')  # Note: using 'year' not 'class_year'
                self.stdout.write(f'    Female: {demo.female_count}, Male: {demo.male_count}')
                self.stdout.write(f'    Non-binary: {demo.non_binary_count}')
                self.stdout.write(f'    Asian: {demo.asian_count}, White: {demo.white_count}')
                self.stdout.write(f'    Black: {demo.black_count}, Hispanic: {demo.hispanic_count}')
            
            # Curriculum
            curriculum_data = program.program_curriculum.all()
            self.stdout.write(f'\nCurriculum ({curriculum_data.count()} records):')
            for curriculum in curriculum_data:
                self.stdout.write(f'  Didactic Courses: {curriculum.didactic_courses}')
                self.stdout.write(f'  Clinical Rotations: {curriculum.required_rotations}')

        if options['comprehensive'] or options['all']:
            self.show_comprehensive_data(program)

        if options['json'] or options['all']:
            # Display raw JSON data
            self.stdout.write(self.style.SUCCESS('\n=== RAW JSON DATA ==='))
            if program.enhanced_data:
                formatted_json = json.dumps(program.enhanced_data, indent=2)
                # Truncate if too long
                if len(formatted_json) > 2000:
                    self.stdout.write(formatted_json[:2000] + '\n... (truncated)')
                else:
                    self.stdout.write(formatted_json)
            else:
                self.stdout.write('No enhanced JSON data available')

    def show_comprehensive_data(self, program):
        """Display all comprehensive data from the JSON structure"""
        self.stdout.write(self.style.SUCCESS('\n=== COMPREHENSIVE PROGRAM DATA ==='))

        if not program.enhanced_data or 'program_data' not in program.enhanced_data:
            self.stdout.write('No comprehensive data available')
            return

        program_data = program.enhanced_data['program_data']

        # General Information
        general_info = program_data.get('general_information', {})
        if general_info:
            self.stdout.write(self.style.WARNING('\n--- GENERAL INFORMATION ---'))
            self.stdout.write(f'Phone: {general_info.get("phone", "N/A")}')
            self.stdout.write(f'Address: {general_info.get("address", "N/A")}')
            self.stdout.write(f'Mission Statement: {general_info.get("mission_statement", "N/A")}')
            self.stdout.write(f'Unique Features: {general_info.get("unique_program_features", "N/A")}')
            self.stdout.write(f'Curriculum Focus: {general_info.get("curriculum_focus", "N/A")}')
            self.stdout.write(f'Credentials Offered: {general_info.get("credentials_offered", "N/A")}')
            self.stdout.write(f'Bridge/Dual Degree: {general_info.get("bridge_or_dual_degree", "N/A")}')
            self.stdout.write(f'Doctorate Offered: {general_info.get("doctorate_degree_offered", "N/A")}')
            self.stdout.write(f'Masters Type: {general_info.get("type_of_masters_degree", "N/A")}')
            self.stdout.write(f'Program Length: {general_info.get("program_length", "N/A")}')
            self.stdout.write(f'Start Month: {general_info.get("start_month", "N/A")}')
            self.stdout.write(f'Estimated Class Size: {general_info.get("estimated_incoming_class_size", "N/A")}')
            self.stdout.write(f'Part-time Option: {general_info.get("part_time_option", "N/A")}')
            self.stdout.write(f'Distance Learning: {general_info.get("distance_learning", "N/A")}')
            self.stdout.write(f'On-campus Housing: {general_info.get("on_campus_housing", "N/A")}')
            self.stdout.write(f'Required Interview: {general_info.get("required_onsite_interview", "N/A")}')
            self.stdout.write(f'Interview Types: {general_info.get("types_of_interviews", "N/A")}')
            self.stdout.write(f'Admission Type: {general_info.get("admission_type", "N/A")}')

        # Class Profile
        class_profile = program_data.get('class_profile', {})
        if class_profile:
            self.stdout.write(self.style.WARNING('\n--- CLASS PROFILE ---'))
            self.stdout.write(f'Year: {class_profile.get("year", "N/A")}')
            self.stdout.write(f'Total Applications: {class_profile.get("total_applications", "N/A")}')
            self.stdout.write(f'Total Interviewed: {class_profile.get("total_interviewed", "N/A")}')
            self.stdout.write(f'Total Matriculants: {class_profile.get("total_matriculants", "N/A")}')
            self.stdout.write(f'Average Overall GPA: {class_profile.get("average_overall_gpa", "N/A")}')
            self.stdout.write(f'Average Science GPA: {class_profile.get("average_science_gpa", "N/A")}')
            self.stdout.write(f'Average Healthcare Hours: {class_profile.get("average_healthcare_hours", "N/A")}')

            # GRE Percentiles
            gre_percentiles = class_profile.get('mean_gre_percentiles', {})
            if gre_percentiles:
                self.stdout.write('GRE Mean Percentiles:')
                self.stdout.write(f'  Verbal: {gre_percentiles.get("verbal", "N/A")}')
                self.stdout.write(f'  Quantitative: {gre_percentiles.get("quantitative", "N/A")}')
                self.stdout.write(f'  Analytical: {gre_percentiles.get("analytical", "N/A")}')

        # CASPA Deadlines
        caspa = program_data.get('caspa_deadlines', {})
        if caspa:
            self.stdout.write(self.style.WARNING('\n--- CASPA DEADLINES ---'))
            self.stdout.write(f'CASPA Member: {caspa.get("caspa_member", "N/A")}')
            self.stdout.write(f'Upcoming Cycle: {caspa.get("upcoming_caspa_cycle", "N/A")}')
            self.stdout.write(f'Application Deadline: {caspa.get("application_deadline", "N/A")}')
            self.stdout.write(f'Deadline Requirement: {caspa.get("deadline_requirement", "N/A")}')
            self.stdout.write(f'Supplemental App: {caspa.get("supplemental_application", "N/A")}')
            self.stdout.write(f'Supplemental Deadline: {caspa.get("supplemental_deadline", "N/A")}')
            self.stdout.write(f'Supplemental Fee: {caspa.get("supplemental_application_fee", "N/A")}')
            self.stdout.write(f'Fee Waiver: {caspa.get("supplemental_application_fee_waiver", "N/A")}')

        # Tuition & Deposits
        tuition = program_data.get('tuition_deposits', {})
        if tuition:
            self.stdout.write(self.style.WARNING('\n--- TUITION & DEPOSITS ---'))
            self.stdout.write(f'Separate Tuition Rates: {tuition.get("separate_tuition_rates", "N/A")}')
            self.stdout.write(f'Tuition: {tuition.get("tuition", "N/A")}')
            self.stdout.write(f'Seat Deposit: {tuition.get("seat_deposit", "N/A")}')
            self.stdout.write(f'Seat Deposit Cost: {tuition.get("seat_deposit_cost", "N/A")}')
            self.stdout.write(f'Refundable Deposit: {tuition.get("refundable_seat_deposit", "N/A")}')

        # Matriculants (detailed)
        matriculants = program_data.get('matriculants', {})
        if matriculants:
            self.stdout.write(self.style.WARNING('\n--- MATRICULANT DETAILS ---'))
            self.stdout.write(f'Out-of-State Accepted: {matriculants.get("out_of_state_students_accepted", "N/A")}')
            self.stdout.write(f'Transfer Students: {matriculants.get("transfer_students_accepted", "N/A")}')
            self.stdout.write(f'DACA Considered: {matriculants.get("daca_status_applicants_considered", "N/A")}')
            self.stdout.write(f'International Accepted: {matriculants.get("international_applicants_accepted", "N/A")}')
            self.stdout.write(f'Veteran Support: {matriculants.get("support_for_veterans", "N/A")}')

            # Gender breakdown
            gender = matriculants.get('gender', {})
            if gender:
                self.stdout.write('Gender Distribution:')
                self.stdout.write(f'  Female: {gender.get("female", "N/A")}')
                self.stdout.write(f'  Male: {gender.get("male", "N/A")}')
                self.stdout.write(f'  Non-binary: {gender.get("non_binary", "N/A")}')
                self.stdout.write(f'  Unknown: {gender.get("gender_unknown", "N/A")}')

            # Ethnicity breakdown
            ethnicity = matriculants.get('ethnicity', {})
            if ethnicity:
                self.stdout.write('Ethnicity Distribution:')
                self.stdout.write(f'  American Indian/Alaskan: {ethnicity.get("american_indian_or_alaskan_native", "N/A")}')
                self.stdout.write(f'  Asian: {ethnicity.get("asian", "N/A")}')
                self.stdout.write(f'  Black/African American: {ethnicity.get("black_or_african_american", "N/A")}')
                self.stdout.write(f'  Hispanic/Latino: {ethnicity.get("hispanic_latino_or_spanish", "N/A")}')
                self.stdout.write(f'  Hawaiian/Pacific Islander: {ethnicity.get("native_hawaiian_or_pacific_islander", "N/A")}')
                self.stdout.write(f'  White: {ethnicity.get("white", "N/A")}')
                self.stdout.write(f'  Other: {ethnicity.get("other", "N/A")}')

        # GRE Scores
        gre_scores = program_data.get('gre_scores', {})
        if gre_scores:
            self.stdout.write(self.style.WARNING('\n--- GRE SCORES ---'))
            avg_scores = gre_scores.get('average_scores', {})
            avg_percentiles = gre_scores.get('average_percentiles', {})

            if avg_scores:
                self.stdout.write('Average Scores:')
                self.stdout.write(f'  Verbal Reasoning: {avg_scores.get("verbal_reasoning", "N/A")}')
                self.stdout.write(f'  Quantitative Reasoning: {avg_scores.get("quantitative_reasoning", "N/A")}')
                self.stdout.write(f'  Analytical Writing: {avg_scores.get("analytical_writing", "N/A")}')

            if avg_percentiles:
                self.stdout.write('Average Percentiles:')
                self.stdout.write(f'  Verbal Reasoning: {avg_percentiles.get("verbal_reasoning", "N/A")}')
                self.stdout.write(f'  Quantitative Reasoning: {avg_percentiles.get("quantitative_reasoning", "N/A")}')
                self.stdout.write(f'  Analytical Writing: {avg_percentiles.get("analytical_writing", "N/A")}')

        # Requirements
        requirements = program_data.get('requirements', {})
        if requirements:
            self.stdout.write(self.style.WARNING('\n--- REQUIREMENTS ---'))

            # References
            references = requirements.get('references', {})
            if references:
                self.stdout.write('References:')
                self.stdout.write(f'  Minimum Required: {references.get("minimum_number_required", "N/A")}')
                self.stdout.write(f'  Types Required: {references.get("types_of_references_required", "N/A")}')

            # GPA Requirements
            gpa_reqs = requirements.get('gpa_requirements', {})
            if gpa_reqs:
                self.stdout.write('GPA Requirements:')
                self.stdout.write(f'  Minimum Overall GPA: {gpa_reqs.get("minimum_overall_gpa", "N/A")}')
                self.stdout.write(f'  Minimum Prereq GPA: {gpa_reqs.get("minimum_prereq_gpa", "N/A")}')
                self.stdout.write(f'  Minimum Science GPA: {gpa_reqs.get("minimum_science_gpa", "N/A")}')

            # Standardized Testing
            testing = requirements.get('standardized_testing', {})
            if testing:
                self.stdout.write('Standardized Testing:')
                self.stdout.write(f'  Tests Required: {testing.get("tests_required", "N/A")}')

            # Healthcare Experience
            healthcare_exp = requirements.get('healthcare_experience', {})
            if healthcare_exp:
                self.stdout.write('Healthcare Experience:')
                self.stdout.write(f'  Required/Recommended: {healthcare_exp.get("required_or_recommended", "N/A")}')
                self.stdout.write(f'  Hours Needed: {healthcare_exp.get("hours_needed", "N/A")}')
                self.stdout.write(f'  Time Limit: {healthcare_exp.get("time_limit", "N/A")}')

                accepted_types = healthcare_exp.get('accepted_experience_types', {})
                if accepted_types:
                    self.stdout.write('  Accepted Experience Types:')
                    self.stdout.write(f'    Paid Healthcare: {accepted_types.get("paid_health_care", "N/A")}')
                    self.stdout.write(f'    Volunteer Community: {accepted_types.get("volunteer_community_service", "N/A")}')
                    self.stdout.write(f'    Paid Direct Patient Care: {accepted_types.get("paid_direct_patient_care", "N/A")}')
                    self.stdout.write(f'    Volunteer Direct Patient Care: {accepted_types.get("volunteer_direct_patient_care", "N/A")}')
                    self.stdout.write(f'    Virtual Healthcare: {accepted_types.get("virtual_health_care_accepted", "N/A")}')

            # Shadowing
            shadowing = requirements.get('shadowing', {})
            if shadowing:
                self.stdout.write('Shadowing:')
                self.stdout.write(f'  PA Shadowing: {shadowing.get("shadowing_pa", "N/A")}')
                self.stdout.write(f'  Physician Shadowing: {shadowing.get("shadowing_physician", "N/A")}')
                self.stdout.write(f'  Other Provider Shadowing: {shadowing.get("shadowing_other_health_care_provider", "N/A")}')
                self.stdout.write(f'  Virtual Shadowing: {shadowing.get("virtual_shadowing_accepted", "N/A")}')

            # Research Experience
            research = requirements.get('research_experience', {})
            if research:
                self.stdout.write('Research Experience:')
                self.stdout.write(f'  Clinical/Non-clinical: {research.get("clinical_or_non_clinical", "N/A")}')

        # Prerequisites
        prerequisites = program_data.get('prerequisites', {})
        if prerequisites:
            self.stdout.write(self.style.WARNING('\n--- PREREQUISITES ---'))
            self.stdout.write(f'Time Limit: {prerequisites.get("prereq_time_limit", "N/A")}')

            courses = prerequisites.get('courses', [])
            if courses:
                self.stdout.write('Required Courses:')
                for course in courses:
                    name = course.get('name', 'N/A')
                    credits = course.get('credits', 'N/A')
                    self.stdout.write(f'  {name}: {credits}')

        # Program Curriculum (detailed)
        curriculum = program_data.get('program_curriculum', {})
        if curriculum:
            self.stdout.write(self.style.WARNING('\n--- PROGRAM CURRICULUM ---'))

            didactic = curriculum.get('didactic_phase', {})
            if didactic:
                self.stdout.write('Didactic Phase:')
                self.stdout.write(f'  Term Type: {didactic.get("term_type", "N/A")}')
                self.stdout.write(f'  Terms: {didactic.get("terms", "N/A")}')

            clinical = curriculum.get('clinical_phase', {})
            if clinical:
                self.stdout.write('Clinical Phase:')
                self.stdout.write(f'  Required Rotations: {clinical.get("required_rotations", "N/A")}')

        # PANCE Pass Rates (from JSON)
        pance_rates = program_data.get('pance_pass_rates')
        if pance_rates:
            self.stdout.write(self.style.WARNING('\n--- PANCE PASS RATES (JSON) ---'))
            self.stdout.write(f'PANCE Data: {pance_rates}')

        # Attrition (from JSON)
        attrition = program_data.get('attrition', {})
        if attrition:
            self.stdout.write(self.style.WARNING('\n--- ATTRITION (JSON) ---'))
            self.stdout.write(f'Class Year: {attrition.get("class_year", "N/A")}')
            self.stdout.write(f'Max Class Size: {attrition.get("maximum_entering_class_size", "N/A")}')
            self.stdout.write(f'Entering Class Size: {attrition.get("entering_class_size", "N/A")}')
            self.stdout.write(f'Graduates: {attrition.get("graduates", "N/A")}')
            self.stdout.write(f'Attrition Rate: {attrition.get("attrition_rate", "N/A")}')
            self.stdout.write(f'Graduation Rate: {attrition.get("graduation_rate", "N/A")}')

        # Timestamp
        timestamp = program.enhanced_data.get('timestamp')
        if timestamp:
            self.stdout.write(self.style.WARNING('\n--- DATA INFO ---'))
            self.stdout.write(f'Data Timestamp: {timestamp}')
