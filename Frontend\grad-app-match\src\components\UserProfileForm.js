import React, { useState, useContext } from 'react';
import axios from 'axios';
import { useAuth } from '../contexts/AuthContext';

function UserProfileForm() {
    // Use the useAuth hook to access the current user's information
    const { currentUser } = useAuth();

    // Initialize state for profile data
    const [profileData, setProfileData] = useState({
        bio: '',
        location: '',
        document: null,
        // Use currentUser.id if currentUser exists, otherwise null
        user: currentUser ? currentUser.id : null,
    });

    // Handle change for text inputs
    const handleChange = (e) => {
        const { name, value } = e.target;
        setProfileData({ ...profileData, [name]: value });
    };

    // Handle file change for the document upload
    const handleFileChange = (e) => {
        setProfileData({ ...profileData, document: e.target.files[0] });
    };

    // Handle form submission
    const handleSubmit = async (e) => {
        e.preventDefault();
        const formData = new FormData();
        formData.append('bio', profileData.bio);
        formData.append('location', profileData.location);
        formData.append('user', profileData.user);

        // Append the document if it's available
        if (profileData.document) {
            formData.append('document', profileData.document);
        }

        try {
            // Adjust the API URL according to your environment variable or direct URL
            const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/userprofile/`, formData, {
                headers: {
                    // Let the browser set the Content-Type for FormData by not setting it explicitly
                },
            });
            console.log('Profile created successfully:', response.data);
            alert("Profile created successfully!");

            // Reset form data after successful submission
            setProfileData({
                bio: '',
                location: '',
                document: null,
                user: currentUser ? currentUser.id : null,
            });
        } catch (error) {
            console.error("Error submitting form:", error);
            alert("Failed to create profile.");
        }
    };

    return (
        <form onSubmit={handleSubmit} encType="multipart/form-data">
            {/* This form is likely deprecated or not the primary profile form.
                The main profile form is in Dashboard.js.
                Keeping this file for now but removing content as requested.
            */}
            <p>This form is not currently in use.</p>
        </form>
    );
}

export default UserProfileForm;
