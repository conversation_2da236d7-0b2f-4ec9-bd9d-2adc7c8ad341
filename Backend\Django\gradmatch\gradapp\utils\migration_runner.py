#!/usr/bin/env python3
"""
Standalone utility script for running program data migration

This script can be used to run the migration process outside of Django
management commands, useful for testing or automated deployment scenarios.
"""

import os
import sys
import django
import logging
from pathlib import Path

# Add the Django project to the Python path
current_dir = Path(__file__).parent
django_project_dir = current_dir.parent.parent
sys.path.insert(0, str(django_project_dir))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gradmatch.settings')
django.setup()

from gradapp.services.program_data_migration import ProgramDataMigrationProcessor, MigrationError


def setup_logging(log_level='INFO'):
    """
    Setup logging configuration
    
    Args:
        log_level: Logging level string
    """
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('migration.log')
        ]
    )


def run_migration(json_directory, batch_size=50, log_level='INFO', save_report=True):
    """
    Run the program data migration
    
    Args:
        json_directory: Path to directory containing JSON files
        batch_size: Number of programs to process in each batch
        log_level: Logging level
        save_report: Whether to save detailed report to file
        
    Returns:
        Migration report dictionary
    """
    setup_logging(log_level)
    logger = logging.getLogger(__name__)
    
    try:
        # Initialize processor
        processor = ProgramDataMigrationProcessor(
            json_directory_path=json_directory,
            log_level=getattr(logging, log_level.upper())
        )
        
        logger.info(f"Starting migration from: {json_directory}")
        logger.info(f"Batch size: {batch_size}")
        
        # Run migration
        report = processor.process_all_programs(batch_size=batch_size)
        
        # Save report if requested
        if save_report:
            import json
            from datetime import datetime
            
            report_filename = f"migration_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Detailed report saved to: {report_filename}")
        
        # Print summary
        print("\n" + "="*60)
        print("MIGRATION COMPLETED")
        print("="*60)
        print(processor.get_processing_summary())
        
        return report
        
    except MigrationError as e:
        logger.error(f"Migration error: {str(e)}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        sys.exit(1)


def validate_directory(json_directory):
    """
    Validate that the JSON directory exists and contains JSON files
    
    Args:
        json_directory: Path to validate
        
    Returns:
        True if valid, False otherwise
    """
    directory_path = Path(json_directory)
    
    if not directory_path.exists():
        print(f"Error: Directory does not exist: {json_directory}")
        return False
    
    if not directory_path.is_dir():
        print(f"Error: Path is not a directory: {json_directory}")
        return False
    
    json_files = list(directory_path.glob("*.json"))
    if not json_files:
        print(f"Warning: No JSON files found in directory: {json_directory}")
        return False
    
    print(f"Found {len(json_files)} JSON files in directory")
    return True


def main():
    """
    Main entry point for the migration runner
    """
    import argparse
    
    parser = argparse.ArgumentParser(
        description='Run PA program data migration from JSON files'
    )
    
    parser.add_argument(
        'json_directory',
        help='Path to directory containing JSON program files'
    )
    
    parser.add_argument(
        '--batch-size',
        type=int,
        default=50,
        help='Number of programs to process in each batch (default: 50)'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='Set the logging level (default: INFO)'
    )
    
    parser.add_argument(
        '--no-report',
        action='store_true',
        help='Do not save detailed migration report to file'
    )
    
    parser.add_argument(
        '--validate-only',
        action='store_true',
        help='Only validate directory and files, do not run migration'
    )
    
    args = parser.parse_args()
    
    # Validate directory
    if not validate_directory(args.json_directory):
        sys.exit(1)
    
    if args.validate_only:
        print("Directory validation completed successfully")
        sys.exit(0)
    
    # Run migration
    try:
        report = run_migration(
            json_directory=args.json_directory,
            batch_size=args.batch_size,
            log_level=args.log_level,
            save_report=not args.no_report
        )
        
        # Exit with error code if there were failures
        if report['statistics']['failed_processing'] > 0:
            print(f"\nMigration completed with {report['statistics']['failed_processing']} failures")
            sys.exit(1)
        else:
            print("\nMigration completed successfully!")
            sys.exit(0)
            
    except KeyboardInterrupt:
        print("\nMigration interrupted by user")
        sys.exit(1)


if __name__ == '__main__':
    main()