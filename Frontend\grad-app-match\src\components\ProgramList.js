// grad-app-match/src/components/ProgramList.js
import React from 'react';

function ProgramList({ programs }) {
    if (!programs || !programs.length) {
        return <div>No programs available</div>;
    }

    return (
        <div className="program-list">
            <h3>Available Programs:</h3>
            <ul>
                {programs.map((program) => (
                    <li key={program.id} className="program-item">
                        <h4>{program.name}</h4>
                    </li>
                ))}
            </ul>
        </div>
    );
}

export default ProgramList;
// JavaScript source code
