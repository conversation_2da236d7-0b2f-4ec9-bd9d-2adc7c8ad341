import { useEffect, useRef, useState } from 'react';
import { useAuth } from '../contexts/AuthContext';

export const useSessionTimeout = (timeoutMinutes = 30) => {
  const [showWarning, setShowWarning] = useState(false);
  const [timeLeft, setTimeLeft] = useState(0);
  const { signOut, currentUser } = useAuth();
  
  const timeoutRef = useRef(null);
  const warningRef = useRef(null);
  const intervalRef = useRef(null);
  
  const resetTimeout = () => {
    // Clear existing timeouts
    if (timeoutRef.current) clearTimeout(timeoutRef.current);
    if (warningRef.current) clearTimeout(warningRef.current);
    if (intervalRef.current) clearInterval(intervalRef.current);
    
    setShowWarning(false);
    
    if (!currentUser) return;
    
    const timeoutMs = timeoutMinutes * 60 * 1000;
    const warningMs = timeoutMs - (5 * 60 * 1000); // 5 minutes before timeout
    
    // Set warning timer (5 minutes before logout)
    warningRef.current = setTimeout(() => {
      setShowWarning(true);
      setTimeLeft(5 * 60); // 5 minutes in seconds
      
      // Start countdown
      intervalRef.current = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 1) {
            handleTimeout();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
      
    }, warningMs);
    
    // Set logout timer
    timeoutRef.current = setTimeout(handleTimeout, timeoutMs);
  };
  
  const handleTimeout = async () => {
    setShowWarning(false);
    if (timeoutRef.current) clearTimeout(timeoutRef.current);
    if (warningRef.current) clearTimeout(warningRef.current);
    if (intervalRef.current) clearInterval(intervalRef.current);
    
    try {
      await signOut();
    } catch (error) {
      console.error('Error during session timeout logout:', error);
    }
  };
  
  const extendSession = () => {
    resetTimeout();
  };
  
  useEffect(() => {
    if (currentUser) {
      resetTimeout();
      
      // Reset timeout on user activity
      const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
      
      const resetOnActivity = () => {
        if (!showWarning) {
          resetTimeout();
        }
      };
      
      events.forEach(event => {
        document.addEventListener(event, resetOnActivity, true);
      });
      
      return () => {
        events.forEach(event => {
          document.removeEventListener(event, resetOnActivity, true);
        });
        
        if (timeoutRef.current) clearTimeout(timeoutRef.current);
        if (warningRef.current) clearTimeout(warningRef.current);
        if (intervalRef.current) clearInterval(intervalRef.current);
      };
    }
  }, [currentUser, showWarning]);
  
  return {
    showWarning,
    timeLeft,
    extendSession,
    handleTimeout
  };
};