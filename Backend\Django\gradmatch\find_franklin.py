import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gradmatch.settings')
django.setup()

from gradapp.models import Program

# Search for programs with <PERSON> in the name
programs = Program.objects.filter(name__icontains='<PERSON>')
print(f"Found {programs.count()} programs with '<PERSON>' in the name:")
for p in programs:
    print(f"- {p.name}")

print("\n" + "="*50)

# Search for programs with <PERSON><PERSON> in the name  
programs = Program.objects.filter(name__icontains='<PERSON>lind')
print(f"Found {programs.count()} programs with '<PERSON><PERSON>' in the name:")
for p in programs:
    print(f"- {p.name}")