const path = require('path');
const ESLintPlugin = require('eslint-webpack-plugin');

module.exports = {
  webpack: {
    plugins: {
      add: [
        new ESLintPlugin({
          extensions: ['js', 'jsx'],
          failOnError: false,
          emitWarning: true,
          quiet: true
        })
      ]
    },
    configure: (webpackConfig) => {
      // Remove any existing ESLint rules
      webpackConfig.module.rules = webpackConfig.module.rules.filter(
        rule => !rule.use || !rule.use.some(use => use.loader === 'eslint-loader')
      );

      return {
        ...webpackConfig,
        devServer: {
          ...webpackConfig.devServer,
          setupMiddlewares: (middlewares, devServer) => {
            if (!devServer) {
              throw new Error('webpack-dev-server is not defined');
            }
            return middlewares;
          }
        }
      };
    }
  },
  devServer: {
    hot: true,
    client: {
      overlay: {
        errors: true,
        warnings: false,
      },
    }
  }
};