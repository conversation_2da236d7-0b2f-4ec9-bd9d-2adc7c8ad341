##THIS IS PROCESSTRANSCRIPT.PY. This is the file that processes the transcript with langgraph. 

import os
import logging
import json
import re
import pdfplumber
import tempfile
from typing import List, Dict, Any, Optional, Tuple, Annotated, TypedDict, Union
from langchain_core.documents import Document
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_openai import ChatOpenAI
import boto3
from botocore.exceptions import ClientError
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import ToolExecutor
from typing import TypeVar, Annotated
import time
from langchain_pinecone import PineconeVectorStore
from pinecone import Pinecone
from langsmith import traceable
from .langchain_service import S3Manager, vectorstore  # Add vectorstore to import
from datetime import datetime, timezone
from typing import TypedDict, Optional, List, Dict, Any, Annotated
from langchain_core.messages import HumanMessage
from operator import add
from langgraph.checkpoint.memory import MemorySaver  
import uuid
from enum import Enum


# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Add after imports
PDF_KEY = "DemoTranscript.pdf"  # Default PDF key for testing

llm = ChatOpenAI(
    model="gpt-4-1106-preview",
    temperature=0
)

class ValidationStatus(Enum):
    INITIAL = "initial"
    PROCESSING = "processing"
    PENDING = "pending"
    VALIDATING = "validating"
    APPROVED = "approved"
    FAILED = "failed"

class TranscriptState(TypedDict):
    """State schema for transcript processing workflow"""
    # Core data
    raw_text: Optional[str]
    structured_data: Optional[Dict[str, Any]]
    analysis: Optional[Dict[str, Any]]
    
    # Status tracking
    current_stage: str
    validation_status: Optional[str]  # Will use ValidationStatus enum values
    
    # Error handling
    error_message: Optional[str]
    validation_feedback: Optional[str]
    
    # Session info
    session_id: str
    user_id: int
    transcript_id: int
    pdf_key: str

    # Processing tools
    llm: Optional[Any]


# Move this near the top with other type definitions
S = TypeVar("S", bound=Dict[str, Any])
class S3Manager:
    def __init__(self):
        self.s3_client = boto3.client(
            's3',
            region_name=os.getenv('AWS_REGION', 'us-east-2'),
            aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
            aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY')
        )
        self.bucket = os.getenv('S3_BUCKET', 'gradmatch')
        
    def load_file(self, key: str) -> List[Document]:
        """Load a PDF file from S3 bucket using pdfplumber with column handling"""
        try:
            response = self.s3_client.get_object(Bucket=self.bucket, Key=key)
            pdf_content = response['Body'].read()
            
            with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
                temp_file.write(pdf_content)
                temp_file_path = temp_file.name
            
            try:
                documents = []
                with pdfplumber.open(temp_file_path) as pdf:
                    for page in pdf.pages:
                        width = page.width
                        height = page.height
                        
                        left_bound = 0
                        mid_point = width / 2
                        right_bound = width
                        
                        left_column = page.crop((left_bound, 0, mid_point-10, height))
                        right_column = page.crop((mid_point+10, 0, right_bound, height))
                        
                        left_text = left_column.extract_text(x_tolerance=3, y_tolerance=3) or ""
                        right_text = right_column.extract_text(x_tolerance=3, y_tolerance=3) or ""
                        
                        full_text = f"{left_text}\n\n{right_text}"
                        
                        doc = Document(
                            page_content=full_text,
                            metadata={
                                'source': key,
                                'page': page.page_number,
                                'total_pages': len(pdf.pages)
                            }
                        )
                        documents.append(doc)
                
                verified_docs, metrics = verify_transcript_content(documents)
                
                logger.info(f"Successfully loaded file {key} from bucket {self.bucket}")
                logger.info(f"Verification metrics: {json.dumps(metrics, indent=2)}")
                
                if metrics['missing_elements']:
                    logger.warning("Some transcript elements are missing - check verification metrics")
                
                return verified_docs
                
            finally:
                os.unlink(temp_file_path)
                
        except Exception as e:
            logger.error(f"Error processing PDF: {str(e)}")
            raise

    def load_file_text(self, key: str) -> str:
        """Load a file from S3 and extract text content"""
        try:
            response = self.s3_client.get_object(Bucket=self.bucket, Key=key)
            pdf_content = response['Body'].read()
            
            with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
                temp_file.write(pdf_content)
                temp_file_path = temp_file.name
            
            try:
                text_content = []
                with pdfplumber.open(temp_file_path) as pdf:
                    for page in pdf.pages:
                        text_content.append(page.extract_text())
                
                return "\n\n".join(text_content)
                
            finally:
                os.unlink(temp_file_path)
                
        except Exception as e:
            logger.error(f"Error loading file text: {str(e)}")
            raise

def verify_transcript_content(documents: List[Document]) -> Tuple[List[Document], Dict]:
    """Verifies transcript content and provides detailed metrics"""
    metrics = {
        'total_pages': len(documents),
        'verified_pages': 0,
        'missing_elements': [],
        'transcript_data_found': {}
    }

    expected_elements = {
        # More flexible student info pattern that matches various formats
        'student_info': r'(Student\s*(ID|Number|#):|ID\s*#?:?)\s*\d+',
        # More flexible birth date pattern
        'birth_date': r'(Birth|Birth\s*Date|DOB)[\s:]+',
        # Generic university pattern
        'university': r'(UNIVERSITY|COLLEGE|INSTITUTE)\s+OF\s+[\w\s]+|[\w\s]+\s+(UNIVERSITY|COLLEGE|INSTITUTE)',
        # Standard semester pattern
        'semesters': r'\d{4}\s*(Spring|Summer|Fall|Winter|SP|SU|FA|WI)',
        # More flexible course pattern that handles various formats
        'courses': r'[A-Z]{2,4}[-\s]?\d{3}[A-Z0-9]?[-\s]?.*?\s+\d+\.?\d*\s*[A-Z][+-]?',
        # More flexible GPA pattern
        'gpa_info': r'(GPA|Grade\s+Point(\s+Average)?|G\.P\.A\.)'
    }

    full_text = ' '.join(doc.page_content for doc in documents)
    
    for element, pattern in expected_elements.items():
        found = bool(re.search(pattern, full_text, re.IGNORECASE))  # Make case insensitive
        metrics['transcript_data_found'][element] = found
        if not found:
            metrics['missing_elements'].append(element)

    # Consider transcript valid if it has most key elements
    required_elements = {'student_info', 'courses', 'gpa_info'}
    if len(set(metrics['missing_elements']) & required_elements) <= 1:
        metrics['verified_pages'] = len(documents)
    
    return documents, metrics

def term_to_number(term: str) -> str:
    """Convert term to a number for sorting"""
    term_map = {
        'SPRING': '1',
        'SUMMER': '2',
        'FALL': '3'
    }
    return term_map.get(term.upper(), '0')

def term_to_month(term: str) -> int:
    """Convert academic term to an approximate month for level computation"""
    mapping = {
        'SPRING': 2,
        'SUMMER': 6,
        'FALL': 10,
        'WINTER': 12
    }
    return mapping.get(term.upper(), 1)

def analyze_transcript(
    structured_data: Dict[str, Any],
    llm: ChatOpenAI,
    validation_status: Optional[str] = None,
    undergrad_start_date: Optional[Union[str, datetime.date]] = None
) -> Dict[str, Any]:
    """
    Analyze transcript data with separate original (transcript_stated_gpa) 
    and calculated (calculated_gpa) GPAs. Also attaches total credits, 
    quality points, and metadata about validation status/timestamp.
    """
    try:
        logger.info(f"Starting analysis with data type: {type(structured_data).__name__}")
        
        # Create a completely minimal valid structure if needed
        if structured_data is None:
            logger.warning("structured_data is None, creating minimal structure")
            structured_data = {
                "student_info": {"name": "Unknown Student"},
                "degree_info": {"institution": "Unknown Institution"},
                "semesters": []
            }
            
        # Convert structured_data to proper format if it's a list
        if isinstance(structured_data, list):
            logger.warning("Converting structured_data from list to dictionary with semesters key")
            structured_data = {"semesters": structured_data}
            
        # Basic checks on input format
        if not isinstance(structured_data, dict):
            logger.warning(f"Invalid structured data format: {type(structured_data).__name__}, creating default")
            structured_data = {
                "student_info": {"name": "Unknown Student"},
                "degree_info": {"institution": "Unknown Institution"},
                "semesters": []
            }

        # Ensure semesters key exists
        if 'semesters' not in structured_data:
            logger.warning("No semesters key in structured data, adding empty list")
            structured_data['semesters'] = []
            
        # Ensure student_info and degree_info exist
        if 'student_info' not in structured_data:
            structured_data['student_info'] = {"name": "Unknown Student"}
            
        if 'degree_info' not in structured_data:
            structured_data['degree_info'] = {"institution": "Unknown Institution"}
        
        semester_blocks = structured_data['semesters']
        
        # Handle empty or invalid semester blocks
        if not semester_blocks:
            logger.warning("No semester data available, creating a sample semester")
            # Create a sample semester with no courses
            sample_semester = {
                "year": "2000",
                "term": "Fall",
                "courses": []
            }
            semester_blocks = [sample_semester]
            structured_data['semesters'] = semester_blocks
        
        # Ensure semester_blocks is a list
        if not isinstance(semester_blocks, list):
            semester_blocks = [semester_blocks] if semester_blocks else [{"year": "2000", "term": "Fall", "courses": []}]
            structured_data['semesters'] = semester_blocks

        # Filter out any non-dictionary semesters
        valid_semesters = []
        for sem in semester_blocks:
            if isinstance(sem, dict):
                valid_semesters.append(sem)
            else:
                logger.warning(f"Filtered out non-dictionary semester: {sem}")
                
        structured_data['semesters'] = valid_semesters
        semester_blocks = valid_semesters
        
        if not semester_blocks:
            logger.warning("No valid semesters after filtering, creating a sample semester")
            sample_semester = {
                "year": "2000",
                "term": "Fall",
                "courses": []
            }
            semester_blocks = [sample_semester]
            structured_data['semesters'] = semester_blocks

        # Ensure each semester has required fields
        for semester in semester_blocks:
            if 'year' not in semester:
                semester['year'] = '2000'  # Default year
            if 'term' not in semester:
                semester['term'] = 'Fall'  # Default term
            if 'courses' not in semester:
                semester['courses'] = []
            # Ensure courses is a list
            if not isinstance(semester['courses'], list):
                semester['courses'] = []

        # Sort semesters consistently (year + term)
        try:
            semester_blocks.sort(key=lambda x: (x.get('year', '2000'), term_to_number(x.get('term', 'Fall'))))
        except Exception as e:
            logger.warning(f"Error sorting semesters: {e}")
            # Don't fail if sorting fails
            
        # Automatic academic level assignment based on user's undergrad start date
        undergrad_date_obj = None
        if undergrad_start_date:
            # Parse ISO string if necessary
            if isinstance(undergrad_start_date, str):
                try:
                    undergrad_date_obj = datetime.fromisoformat(undergrad_start_date).date()
                except Exception:
                    undergrad_date_obj = None
            else:
                undergrad_date_obj = undergrad_start_date if hasattr(undergrad_start_date, 'month') else None

        for semester in semester_blocks:
            year = int(semester.get('year', 0))
            term_str = semester.get('term', '')
            month = term_to_month(term_str)
            if undergrad_date_obj:
                months_diff = (year - undergrad_date_obj.year) * 12 + (month - undergrad_date_obj.month)
            else:
                months_diff = None

            # Determine academic level
            if months_diff is None:
                level = None
            elif months_diff < 0:
                level = 'High School/Pre-Undergrad'
            elif months_diff < 12:
                level = 'Freshman'
            elif months_diff < 24:
                level = 'Sophomore'
            elif months_diff < 36:
                level = 'Junior'
            elif months_diff < 48:
                level = 'Senior'
            else:
                level = 'Post-Baccalaureate'

            for course in semester.get('courses', []):
                course['academic_level'] = level

        # Calculate overall metrics (credits, GPA, etc.)
        try:
            metrics = calculate_academic_metrics(semester_blocks)
            logger.debug(f"Calculated metrics: {json.dumps(metrics, indent=2)}")
        except Exception as e:
            logger.error(f"Error calculating academic metrics: {e}")
            # Create default metrics
            metrics = {
                "total_credits_attempted": 0,
                "total_credits_earned": 0,
                "quality_points": 0,
                "cumulative_gpa": 0.0
            }

        # Extract student and degree info from structured_data
        try:
            student_info = structured_data.get('student_info', {})
            if not isinstance(student_info, dict):
                logger.warning(f"student_info is not a dictionary: {type(student_info).__name__}")
                student_info = {}
                
            degree_info = structured_data.get('degree_info', {})
            if not isinstance(degree_info, dict):
                logger.warning(f"degree_info is not a dictionary: {type(degree_info).__name__}")
                degree_info = {}
                
            # The original (transcript) GPA as extracted from the PDF
            transcript_gpa = degree_info.get('final_gpa')
            # Handle non-numeric GPA
            if transcript_gpa is not None:
                try:
                    transcript_gpa = float(transcript_gpa)
                except (ValueError, TypeError):
                    logger.warning(f"Invalid transcript GPA: {transcript_gpa}")
                    transcript_gpa = None
                    
            # Newly calculated GPA based on the courses
            calculated_gpa = metrics["cumulative_gpa"]

            # Decide which GPA is the "source"
            # For example, if validation_status == APPROVED, we say "calculated"
            # Otherwise, show "transcript" as the source. You can tweak this logic.
            if validation_status == ValidationStatus.APPROVED.value:
                gpa_source = "calculated"
            else:
                gpa_source = "transcript"

            # Build out academic_summary to hold all relevant fields
            academic_summary = {
                "institution": degree_info.get("institution", ""),
                "student_name": student_info.get("name", ""),
                "birth_date": student_info.get("birth_date", ""),
                "degree": degree_info.get("major", ""),
                "minor": degree_info.get("minor", ""),
                # original transcript GPA
                "transcript_stated_gpa": float(transcript_gpa) if transcript_gpa is not None else None,
                # newly computed GPA
                "cumulative_gpa": float(calculated_gpa) if calculated_gpa is not None else 0.0,
                "gpa_source": gpa_source,
                # additional metrics
                "total_credits_attempted": metrics["total_credits_attempted"],
                "total_credits_earned": metrics["total_credits_earned"],
                "quality_points": metrics["quality_points"],
            }
            
        except Exception as e:
            logger.error(f"Error building academic summary: {e}")
            # Create a minimal academic summary as fallback
            academic_summary = {
                "institution": "Unknown Institution",
                "student_name": "Unknown Student",
                "birth_date": "",
                "degree": "",
                "minor": "",
                "transcript_stated_gpa": None,
                "cumulative_gpa": 0.0,
                "gpa_source": "calculated",
                "total_credits_attempted": 0,
                "total_credits_earned": 0,
                "quality_points": 0,
            }

        # Return structured output including semesters & summary
        try:
            return {
                "semesters": semester_blocks,
                "academic_summary": academic_summary,
                "metadata": {
                    "validation_status": validation_status,
                    "analysis_timestamp": datetime.now(timezone.utc).isoformat()
                }
            }
        except Exception as e:
            logger.error(f"Error creating final analysis structure: {e}")
            # Return absolute minimal valid structure
            return {
                "semesters": [],
                "academic_summary": {
                    "institution": "Unknown Institution",
                    "student_name": "Unknown Student",
                    "birth_date": "",
                    "degree": "",
                    "minor": "",
                    "transcript_stated_gpa": None,
                    "cumulative_gpa": 0.0,
                    "gpa_source": "calculated",
                    "total_credits_attempted": 0,
                    "total_credits_earned": 0,
                    "quality_points": 0,
                },
                "metadata": {
                    "validation_status": validation_status,
                    "analysis_timestamp": datetime.now(timezone.utc).isoformat()
                }
            }

    except Exception as e:
        logger.error(f"Error in transcript analysis: {str(e)}")
        # Return a valid, though empty, structure to allow the pipeline to complete
        return {
            "semesters": [],
            "academic_summary": {
                "institution": "Unknown Institution",
                "student_name": "Unknown Student",
                "birth_date": "",
                "degree": "",
                "minor": "",
                "transcript_stated_gpa": None,
                "cumulative_gpa": 0.0,
                "gpa_source": "calculated",
                "total_credits_attempted": 0,
                "total_credits_earned": 0,
                "quality_points": 0,
            },
            "metadata": {
                "validation_status": ValidationStatus.FAILED.value,
                "analysis_timestamp": datetime.now(timezone.utc).isoformat(),
                "error": str(e)
            }
        }



def calculate_academic_metrics(semester_data):
    """Calculate academic metrics including GPA"""
    total_attempted = 0
    total_earned = 0
    quality_points = 0
    
    grade_points = {
        'A+': 4.0, 'A': 4.0, 'A-': 3.7,
        'B+': 3.3, 'B': 3.0, 'B-': 2.7,
        'C+': 2.3, 'C': 2.0, 'C-': 1.7,
        'D+': 1.3, 'D': 1.0, 'D-': 0.7,
        'F': 0.0
    }
    
    try:
        # Calculate term GPAs and accumulate overall metrics
        for semester in semester_data:
            if not isinstance(semester, dict) or 'courses' not in semester:
                continue
            
            term_attempted = 0
            term_points = 0
                
            for course in semester['courses']:
                if not isinstance(course, dict):
                    continue
                    
                try:
                    credits = float(course.get('credits', 0))
                    grade = course.get('grade', '').upper().strip()
                    
                    # Skip courses with no credits or invalid grades
                    if credits <= 0 or grade not in grade_points:
                        continue
                        
                    # Add to term totals
                    term_attempted += credits
                    if grade in grade_points:
                        term_points += credits * grade_points[grade]
                    
                    # Add to cumulative totals
                    total_attempted += credits
                    if grade not in ['F', 'W']:
                        total_earned += credits
                        if grade in grade_points:
                            quality_points += credits * grade_points[grade]
                            
                except (ValueError, TypeError) as e:
                    logger.warning(f"Error processing course: {e}")
                    continue
            
            # Calculate and set term GPA
            semester['term_gpa'] = round(term_points / term_attempted, 3) if term_attempted > 0 else 0.0
            # Calculate and set cumulative GPA up to this term
            semester['cumulative_gpa'] = round(quality_points / total_attempted, 3) if total_attempted > 0 else 0.0
                    
        cumulative_gpa = round(quality_points / total_attempted, 3) if total_attempted > 0 else 0.0
        
        return {
            "total_credits_attempted": total_attempted,
            "total_credits_earned": total_earned,
            "quality_points": quality_points,
            "cumulative_gpa": cumulative_gpa
        }
        
    except Exception as e:
        logger.error(f"Error calculating academic metrics: {e}")
        return {
            "total_credits_attempted": 0,
            "total_credits_earned": 0,
            "quality_points": 0,
            "cumulative_gpa": 0.0
        }

def extraction_node(state: TranscriptState) -> TranscriptState:
    """Extract text from PDF using PDFplumber"""
    try:
        logger.info("Starting PDF text extraction")
        state['current_stage'] = 'extracting'
        
        s3_manager = S3Manager()
        # Use load_file instead of load_file_text to handle columns
        documents = s3_manager.load_file(state['pdf_key'])
        
        if not documents:
            raise ValueError("No text extracted from PDF")
            
        # Combine documents into raw text while preserving structure
        raw_text = "\n\n".join(doc.page_content for doc in documents)
        state['raw_text'] = raw_text
        
        logger.info("Text extraction completed successfully")
        return state
        
    except Exception as e:
        logger.error(f"Error in text extraction: {str(e)}")
        state['error_message'] = str(e)
        return state

def structuring_node(state: TranscriptState) -> TranscriptState:
    """Structure raw text into JSON using LLM"""
    try:
        logger.info("Starting text structuring")
        state['current_stage'] = 'structuring'
        
        if not state.get('raw_text'):
            raise ValueError("No raw text available for structuring")
            
        try:
            structured_data = extract_transcript_data(state['raw_text'])
            logger.info("Successfully extracted transcript data")
        except Exception as extraction_error:
            logger.error(f"Error in transcript data extraction: {str(extraction_error)}")
            # Create a minimal valid structure as fallback
            logger.warning("Creating default fallback structure for transcript data")
            structured_data = {
                "student_info": {"name": "Unknown Student"},
                "degree_info": {"institution": "Unknown Institution"},
                "semesters": []
            }
        
        # Additional safeguards
        if not isinstance(structured_data, dict):
            logger.warning(f"Structured data is not a dictionary, converting to dictionary: {type(structured_data).__name__}")
            if isinstance(structured_data, list):
                structured_data = {"semesters": structured_data}
            else:
                structured_data = {"semesters": []}
                
        if 'semesters' not in structured_data:
            logger.warning("Structured data is missing semesters key, adding empty list")
            structured_data['semesters'] = []
            
        state['structured_data'] = structured_data
        logger.info("Text structuring completed")
        return state
        
    except Exception as e:
        logger.error(f"Error in text structuring: {str(e)}")
        state['error_message'] = str(e)
        return state

def analysis_node(state: TranscriptState) -> TranscriptState:
    """
    Node that calls analyze_transcript(), populates the state with
    the resulting analysis, and updates validation status.
    """
    try:
        logger.info("Starting transcript analysis")
        state['current_stage'] = 'analyzing'
        
        if not state.get('structured_data'):
            raise ValueError("No structured data available for analysis")
            
        # Log what we received
        logger.info(f"Analysis input type: {type(state['structured_data']).__name__}")

        # Ensure structured_data is properly formatted
        structured_data = state['structured_data']
        if isinstance(structured_data, list):
            logger.warning("Converting structured_data from list to dictionary with semesters key")
            structured_data = {"semesters": structured_data}
            state['structured_data'] = structured_data

        # Call the function that returns semesters + academic_summary + metadata
        analysis = analyze_transcript(
            state['structured_data'],
            state['llm'],
            validation_status=state.get('validation_status'),
            undergrad_start_date=state.get('undergrad_start_date')
        )

        # Check if analysis has the expected format
        if not isinstance(analysis, dict):
            raise ValueError(f"Analysis returned unexpected type: {type(analysis).__name__}")
            
        if "academic_summary" not in analysis:
            raise ValueError("Analysis missing academic_summary key")
            
        # Fill out state['analysis'] with the data you want to store permanently
        # Create with safe accessors to avoid errors with missing keys
        state['analysis'] = {
            "semesters": analysis.get("semesters", []),
            "academic_summary": {
                "institution": analysis.get("academic_summary", {}).get("institution", ""),
                "student_name": analysis.get("academic_summary", {}).get("student_name", ""),
                "birth_date": analysis.get("academic_summary", {}).get("birth_date", ""),
                "degree": analysis.get("academic_summary", {}).get("degree", ""),
                "minor": analysis.get("academic_summary", {}).get("minor", ""),
                "transcript_stated_gpa": analysis.get("academic_summary", {}).get("transcript_stated_gpa"),
                "cumulative_gpa": analysis.get("academic_summary", {}).get("cumulative_gpa"),
                "total_credits_attempted": analysis.get("academic_summary", {}).get("total_credits_attempted", 0),
                "total_credits_earned": analysis.get("academic_summary", {}).get("total_credits_earned", 0),
                "quality_points": analysis.get("academic_summary", {}).get("quality_points", 0)
            }
        }

        # Example: Mark the transcript as "approved" here for simplicity
        state['validation_status'] = ValidationStatus.APPROVED.value
        state['is_verified'] = True
        
        return state

    except Exception as e:
        logger.error(f"Error in analysis: {str(e)}")
        state['error_message'] = str(e)
        state['validation_status'] = ValidationStatus.FAILED.value
        return state


def storage_node(state: TranscriptState) -> TranscriptState:
    """Store processed transcript data"""
    try:
        logger.info(f"Starting storage for session {state['session_id']}")
        state['current_stage'] = 'storing'
        
        # Add detailed logging about state contents
        logger.info(f"Storage node received state keys: {list(state.keys())}")
        
        # Check if analysis exists and log its structure
        if 'analysis' not in state or state['analysis'] is None:
            logger.error("Analysis key is missing or None in state")
            raise ValueError("No analysis data available for storage")
            
        logger.info(f"Analysis type: {type(state['analysis']).__name__}")
        logger.info(f"Analysis keys: {list(state['analysis'].keys()) if isinstance(state['analysis'], dict) else 'Not a dict'}")
        
        # Create minimal valid analysis if missing components
        if not isinstance(state['analysis'], dict):
            logger.warning(f"Analysis is not a dictionary, creating default structure")
            state['analysis'] = {
                "semesters": [],
                "academic_summary": {
                    "institution": "",
                    "student_name": "",
                    "birth_date": "",
                    "degree": "",
                    "minor": "",
                    "transcript_stated_gpa": None,
                    "cumulative_gpa": 0.0,
                    "total_credits_attempted": 0,
                    "total_credits_earned": 0,
                    "quality_points": 0
                }
            }
        elif "academic_summary" not in state['analysis']:
            logger.warning("Analysis missing academic_summary, adding default")
            state['analysis']["academic_summary"] = {
                "institution": "",
                "student_name": "",
                "birth_date": "",
                "degree": "",
                "minor": "",
                "transcript_stated_gpa": None,
                "cumulative_gpa": 0.0,
                "total_credits_attempted": 0,
                "total_credits_earned": 0,
                "quality_points": 0
            }
            
        # Create document for storage
        doc = Document(
            page_content=json.dumps(state['analysis']),
            metadata={
                "session_id": state['session_id'],
                "type": "transcript_analysis",
                "is_final": state.get('validation_status') == 'approved',
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        )
        
        # Store in vector database
        try:
            success = vectorstore.add_documents(
                documents=[doc],
                namespace="transcript_analyses"
            )
            
            if success:
                state['is_verified'] = True
                logger.info("Storage completed successfully")
            else:
                raise ValueError("Failed to store analysis data")
        except Exception as e:
            logger.error(f"Vector store error: {str(e)}")
            raise ValueError(f"Vector store error: {str(e)}")
        
        return state
        
    except Exception as e:
        logger.error(f"Error in storage: {str(e)}")
        state['error_message'] = str(e)
        return state

def create_transcript_graph() -> StateGraph:
    """Create transcript processing workflow graph"""
    workflow = StateGraph(state_schema=TranscriptState)
    
    # Add nodes
    workflow.add_node("extract", extraction_node)
    workflow.add_node("structure", structuring_node)
    workflow.add_node("analyze", analysis_node)
    workflow.add_node("store", storage_node)
    
    # Define edges
    workflow.set_entry_point("extract")
    workflow.add_edge("extract", "structure")
    workflow.add_edge("structure", "analyze")
    workflow.add_edge("analyze", "store")
    workflow.add_edge("store", END)
    
    return workflow

def create_initial_state(session_id: str, user_id: int, transcript_id: int) -> TranscriptState:
    """Create initial state for transcript processing workflow"""
    try:
        # Get the transcript record
        from .models import UserTranscript
        from users.models import UserProfile  # Change from relative import to absolute
        transcript = UserTranscript.objects.get(id=transcript_id, user_id=user_id)
        # Fetch user's undergrad start date for academic level detection
        try:
            profile = UserProfile.objects.get(user_id=user_id)
            ug_start = profile.undergrad_start_date
        except UserProfile.DoesNotExist:
            ug_start = None
        
        if not transcript.is_active:
            raise ValueError("Transcript is not active")
            
        return {
            # Core data
            "raw_text": None,
            "structured_data": None,
            "analysis": None,
            
            # Status tracking
            "current_stage": "start",
            "is_verified": False,
            "validation_status": None,
            
            # Error handling
            "error_message": None,
            "validation_feedback": None,
            
            # Session info
            "session_id": session_id,
            "user_id": user_id,
            "transcript_id": transcript_id,
            "pdf_key": transcript.s3_key,
            
            # Processing tools
            "llm": llm,
            "undergrad_start_date": ug_start
        }
    except UserTranscript.DoesNotExist:
        raise ValueError(f"Transcript {transcript_id} not found for user {user_id}")

def create_approval_graph() -> StateGraph:
    """Create graph for handling transcript approvals"""
    workflow = StateGraph(state_schema=TranscriptState)
    
    # Only need analysis and storage for approvals
    workflow.add_node("analyze", analysis_node)
    workflow.add_node("store", storage_node)
    
    # Define edges
    workflow.set_entry_point("analyze")
    workflow.add_edge("analyze", "store")
    workflow.add_edge("store", END)
    
    return workflow

@traceable
def process_transcript_button(
    session_id: str,
    user_id: int,
    transcript_id: int,
    is_approval: bool = False,
    edited_data: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """Main entry point for transcript processing"""
    try:
        initial_state = create_initial_state(
            session_id=session_id,
            user_id=user_id,
            transcript_id=transcript_id
        )
        # Ensure user's undergrad start date is passed into workflow
        initial_state['undergrad_start_date'] = initial_state.get('undergrad_start_date')
        
        # Update transcript status to processing
        from .models import UserTranscript
        transcript = UserTranscript.objects.get(id=transcript_id)
        transcript.status = 'processing'
        transcript.save()
        
        if is_approval:
            if not edited_data:
                raise ValueError("No data provided for approval")
        
            initial_state.update({
                "structured_data": edited_data,
                "current_stage": "analyzing",
                "validation_status": ValidationStatus.VALIDATING.value
            })
            workflow = create_approval_graph()
        else:
            initial_state["validation_status"] = ValidationStatus.PROCESSING.value
            workflow = create_transcript_graph()
        
        app = workflow.compile(checkpointer=MemorySaver())
        config = {
            "configurable": {
                "thread_id": session_id,
                "checkpoint_ns": "transcript_processing",
                "checkpoint_id": f"transcript_{session_id}_{uuid.uuid4()}"
            }
        }
        
        try:
            logger.info("Starting workflow execution")
            final_state = app.invoke(initial_state, config=config)
            logger.info(f"Workflow completed with status: {final_state.get('validation_status')}")
        except Exception as workflow_error:
            logger.error(f"Workflow execution failed: {str(workflow_error)}")
            transcript.status = 'failed'
            transcript.save()
            return {
                "status": "error",
                "validation_status": ValidationStatus.FAILED.value,
                "message": f"Error in workflow execution: {str(workflow_error)}"
            }
        
        # Update transcript status based on final state
        if final_state.get("error_message"):
            transcript.status = 'failed'
            transcript.save()
            
            logger.error(f"Error in workflow: {final_state['error_message']}")
            return {
                "status": "error",
                "validation_status": ValidationStatus.FAILED.value,
                "message": final_state["error_message"]
            }
        
        # Set appropriate validation status
        validation_status = (ValidationStatus.APPROVED.value if is_approval 
                           else ValidationStatus.PENDING.value)
        transcript.status = 'validated' if is_approval else 'processing'
        transcript.save()
        
        return {
            "status": "success",
            "message": "Transcript processed successfully",
            "data": {
                "analysis": final_state.get("analysis", {}),
                "structured_data": final_state.get("structured_data", {}),
                "validation_status": validation_status
            }
        }
        
    except Exception as e:
        # Update transcript status on error
        if 'transcript' in locals():
            transcript.status = 'failed'
            transcript.save()
            
        logger.error(f"Error in process_transcript_button: {str(e)}")
        return {
            "status": "error",
            "validation_status": ValidationStatus.FAILED.value,
            "message": f"Error processing transcript: {str(e)}"
        }

def extract_transcript_data(raw_text: str) -> Dict:
    """Extract structured transcript data from raw text using LLM"""
    try:
        logger.info("Starting LLM-based transcript data extraction")
        
        prompt = ChatPromptTemplate.from_template("""
        You are a transcript processing assistant. Extract structured information from this academic transcript.
        
        Your task is to parse this transcript into a well-structured JSON object with these components:
        
        1. student_info: Extract student identification (name, ID, birth date if available)
        2. degree_info: Extract degree program, major, minor, institution, final GPA (if stated)
        3. semesters: For each semester/term, extract:
           - term: The semester (e.g., Fall, Spring, Summer)
           - year: The year (e.g., 2021)
           - courses: For each course, extract:
              * code: Course code (e.g., MATH 101)
              * name: Course name (e.g., Introduction to Calculus)
              * credits: Credit hours as a number
              * grade: Letter grade (A, B+, etc.)
              * has_lab: Boolean indicating if the course has a lab component. Detect this by looking for 
                         lab-related keywords in the course name (e.g., "lab", "laboratory", "& lab", "with lab", etc.)
                         or if the course code contains an "L" suffix or similar lab indicators.
        
        RULES:
        1. Include ALL courses even if withdrawn or failed.
        2. If columns are present, ensure you process ALL content (scan for both left and right sides).
        3. Format credit hours as numbers (e.g., 3 not "3 credits").
        4. If multiple transcripts appear, process ALL of them as separate semesters.
        5. Handle special grades like W (Withdraw), P (Pass), etc.
        
        Return ONLY the parsed JSON with no additional text.
        
        Transcript:
        {raw_text}
        """)
        
        # Invoke the LLM with timeout and error handling
        try:
            chain = prompt | llm | StrOutputParser()
            response = chain.invoke({"raw_text": raw_text})
            logger.debug(f"LLM extraction response length: {len(response) if response else 0}")
        except Exception as llm_error:
            logger.error(f"LLM call failed: {str(llm_error)}")
            # Create a minimal valid structure as fallback when LLM fails completely
            logger.warning("Using fallback structure due to LLM failure")
            return {
                "student_info": {"name": "Unknown Student"},
                "degree_info": {"institution": "Unknown Institution"},
                "semesters": [{
                    "year": "2000",
                    "term": "Fall",
                    "courses": []
                }]
            }
        
        # Parse JSON response
        try:
            # Try direct JSON parsing first
            data = json.loads(response)
            logger.info("Successfully parsed LLM response as JSON directly")
        except json.JSONDecodeError as e:
            logger.warning(f"Initial JSON parsing failed: {e}")
            # Try to find just the JSON part with regex
            try:
                import re
                json_match = re.search(r'({.*})', response, re.DOTALL)
                if json_match:
                    data = json.loads(json_match.group(1))
                    logger.info("Successfully parsed JSON after extracting with regex")
                else:
                    # Try looking for array response
                    array_match = re.search(r'(\[.*\])', response, re.DOTALL)
                    if array_match:
                        data = json.loads(array_match.group(1))
                        logger.info("Successfully parsed JSON array after extracting with regex")
                    else:
                        logger.error("No JSON-like structure found in response")
                        # Create minimal valid structure as fallback
                        data = {
                            "student_info": {"name": "Unknown Student"},
                            "degree_info": {"institution": "Unknown Institution"},
                            "semesters": [{
                                "year": "2000",
                                "term": "Fall",
                                "courses": []
                            }]
                        }
            except json.JSONDecodeError as e2:
                logger.error(f"Failed to parse extracted JSON: {e2}")
                # Create minimal valid structure as fallback
                logger.warning("Creating default minimal structure as fallback")
                data = {
                    "student_info": {"name": "Unknown Student"},
                    "degree_info": {"institution": "Unknown Institution"},
                    "semesters": [{
                        "year": "2000",
                        "term": "Fall",
                        "courses": []
                    }]
                }
        
        # Ensure data is a dictionary
        if isinstance(data, list):
            logger.warning("LLM returned a list instead of a dictionary. Converting to dictionary structure.")
            # Convert list to a dictionary with semesters key
            data = {"semesters": data}
        
        # Verify data structure
        if not isinstance(data, dict):
            raise ValueError(f"Invalid data structure. Expected dictionary, got {type(data).__name__}")
        
        # Initialize semesters if missing
        if 'semesters' not in data:
            data['semesters'] = []
        
        # Add default has_lab field to courses based on name/code patterns if not already present
        for semester in data.get('semesters', []):
            if not isinstance(semester, dict):
                logger.warning(f"Skipping invalid semester format: {semester}")
                continue
                
            if 'courses' not in semester:
                logger.warning(f"Semester has no courses: {semester}")
                semester['courses'] = []
                continue
                
            for course in semester.get('courses', []):
                if not isinstance(course, dict):
                    logger.warning(f"Skipping invalid course format: {course}")
                    continue
                    
                if 'has_lab' not in course:
                    course_name = course.get('name', '').lower() if course.get('name') else ''
                    course_code = course.get('code', '').lower() if course.get('code') else ''
                    
                    # Debug logging to see what we're checking
                    logger.info(f"Checking lab for course: {course_code} - {course_name}")
                    
                    # Simple explicit check: look for the word "lab" in the name or code
                    has_lab = False
                    
                    if 'lab' in course_name or 'lab' in course_code:
                        has_lab = True
                        logger.info(f"✓ MATCH: Found lab in course {course_code} - {course_name}")
                    else:
                        logger.info(f"✗ NO MATCH: No lab found in course {course_code} - {course_name}")
                    
                    course['has_lab'] = has_lab
        
        logger.info("LLM extraction and structuring completed successfully")
        return data
        
    except Exception as e:
        logger.error(f"Error in transcript extraction: {str(e)}")
        # Return a minimal valid structure to allow the pipeline to continue
        return {
            "student_info": {"name": "Unknown Student"},
            "degree_info": {"institution": "Unknown Institution"},
            "semesters": [{
                "year": "2000",
                "term": "Fall",
                "courses": []
            }]
        }

class StorageError(Exception):
    pass