from django.core.management.base import BaseCommand
import json
from gradapp.models import School, Program

class Command(BaseCommand):
    help = 'Import data from a JSON file into the database'

    def handle(self, *args, **options):
        file_path = r'C:\Users\<USER>\OneDrive\Desktop\RFUandUNRPA.json'
        
        try:
            with open(file_path, 'r') as file:
                schools_data = json.load(file)
                
                if not isinstance(schools_data, list):
                    self.stdout.write(self.style.ERROR(f'Expected a list of schools, got {type(schools_data)}'))
                    return

                self.stdout.write(f'Found {len(schools_data)} schools in file')
                
                for school_data in schools_data:
                    try:
                        # Print the school data for debugging
                        self.stdout.write(f'Processing school: {school_data}')
                        
                        school, created = School.objects.get_or_create(
                            name=school_data['name'],
                            defaults={
                                'location': school_data.get('location', 'Unknown'),
                                'mission_statement': school_data.get('mission_statement', 'No mission statement available'),
                                'website': school_data.get('website', 'http://example.com')  # Default URL
                            }
                        )
                        
                        if created:
                            self.stdout.write(self.style.SUCCESS(f'Created new school: {school.name}'))
                        else:
                            self.stdout.write(self.style.WARNING(f'School already exists: {school.name}'))
                            
                    except KeyError as e:
                        self.stdout.write(self.style.ERROR(f'Missing required field {e} in school data: {school_data}'))
                    except Exception as e:
                        self.stdout.write(self.style.ERROR(f'Error processing school {school_data}: {str(e)}'))
                        
        except FileNotFoundError:
            self.stdout.write(self.style.ERROR(f'File not found: {file_path}'))
        except json.JSONDecodeError as e:
            self.stdout.write(self.style.ERROR(f'Invalid JSON in file: {str(e)}'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Unexpected error: {str(e)}')) 