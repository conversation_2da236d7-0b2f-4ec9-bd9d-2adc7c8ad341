import React from 'react';
import ReactDOM from 'react-dom/client'; // Correct import for React 18
import './index.css';
import App from './App';
import reportWebVitals from './reportWebVitals';
import { AuthProvider } from './contexts/AuthContext'; // Ensure this path is correct

// Create a root.
const root = ReactDOM.createRoot(document.getElementById('root'));

// Render app with AuthProvider for context
root.render(
    <React.StrictMode>
        <AuthProvider>
            <App />
        </AuthProvider>
    </React.StrictMode>
);

// Performance reporting
reportWebVitals();
