

from django.core.management.base import BaseCommand
import json
from your_app.models import School, Program, ...  # Import your models here

class Command(BaseCommand):
    help = 'Imports data from JSON file into the database'

    def handle(self, *args, **kwargs):
        with open('path/to/your/json_file.json', 'r') as file:
            data = json.load(file)
            # Logic to iterate over data and create model instances
