import { useState, useEffect, useCallback, useRef } from 'react';
import { ProgramMatchingCache, createMatchingWorker } from '../utils/programMatchingOptimized';

export const useProgramMatching = (programs, userTranscript, profileData, manuallyApprovedPrograms = []) => {
    const [matchingResults, setMatchingResults] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [progress, setProgress] = useState(0);
    const [error, setError] = useState(null);
    
    const workerRef = useRef(null);
    const cacheRef = useRef(new ProgramMatchingCache());
    
    const startMatching = useCallback(async () => {
        if (!programs || !userTranscript || programs.length === 0) {
            setMatchingResults(null);
            return;
        }
        
        setIsLoading(true);
        setProgress(0);
        setError(null);
        
        try {
            // Check cache first
            const cached = cacheRef.current.get(userTranscript, profileData);
            if (cached && (Date.now() - cached.timestamp) < 5 * 60 * 1000) { // 5 minute cache
                setMatchingResults(cached.results);
                setIsLoading(false);
                return;
            }
            
            // Use Web Worker for heavy computation
            if (workerRef.current) {
                workerRef.current.terminate();
            }
            
            workerRef.current = createMatchingWorker();
            
            workerRef.current.onmessage = (e) => {
                const { type, results, counts, processed, total } = e.data;
                
                if (type === 'progress') {
                    setProgress((processed / total) * 100);
                } else if (type === 'complete') {
                    const finalResults = {
                        eligibilityCache: results,
                        counts
                    };
                    
                    setMatchingResults(finalResults);
                    setProgress(100);
                    setIsLoading(false);
                    
                    // Cache the results
                    cacheRef.current.set(userTranscript, profileData, finalResults);
                    
                    // Clean up worker
                    workerRef.current.terminate();
                    workerRef.current = null;
                }
            };
            
            workerRef.current.onerror = (error) => {
                console.error('Worker error:', error);
                setError('Failed to process program matching');
                setIsLoading(false);
                workerRef.current = null;
            };
            
            // Start the worker
            workerRef.current.postMessage({
                programs,
                userTranscript,
                profileData,
                manuallyApprovedPrograms
            });
            
        } catch (err) {
            console.error('Matching error:', err);
            setError('Failed to start program matching');
            setIsLoading(false);
        }
    }, [programs, userTranscript, profileData, manuallyApprovedPrograms]);
    
    // Auto-start matching when dependencies change
    useEffect(() => {
        startMatching();
        
        // Cleanup on unmount
        return () => {
            if (workerRef.current) {
                workerRef.current.terminate();
                workerRef.current = null;
            }
        };
    }, [startMatching]);
    
    const clearCache = useCallback(() => {
        cacheRef.current.clear();
        startMatching();
    }, [startMatching]);
    
    const retryMatching = useCallback(() => {
        startMatching();
    }, [startMatching]);
    
    return {
        matchingResults,
        isLoading,
        progress,
        error,
        clearCache,
        retryMatching
    };
};

// Hook for backend-powered matching (alternative approach)
export const useBackendMatching = (token) => {
    const [backendResults, setBackendResults] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    
    const fetchMatches = useCallback(async () => {
        if (!token) return;
        
        setIsLoading(true);
        setError(null);
        
        try {
            const response = await fetch('/api/match-programs/', {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            
            if (!response.ok) {
                throw new Error('Failed to fetch program matches');
            }
            
            const data = await response.json();
            setBackendResults(data);
        } catch (err) {
            console.error('Backend matching error:', err);
            setError(err.message);
        } finally {
            setIsLoading(false);
        }
    }, [token]);
    
    return {
        backendResults,
        isLoading,
        error,
        fetchMatches
    };
};