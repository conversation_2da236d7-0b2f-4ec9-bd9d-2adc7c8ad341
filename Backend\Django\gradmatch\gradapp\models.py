from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from django.contrib.auth import get_user_model

from langchain_core.prompts import ChatPromptTemplate
from langchain_community.utilities import SQLDatabase
from langchain_core.output_parsers import StrOut<PERSON>Parser
from langchain_core.runnables import RunnablePassthrough
from langchain_openai import ChatOpenAI

User = get_user_model()



class School(models.Model):
    name = models.TextField()
    location = models.TextField()
    mission_statement = models.TextField()
    website = models.URLField()

    def __str__(self):
        return self.name



class Program(models.Model):
    school = models.ForeignKey(School, on_delete=models.CASCADE)
    name = models.TextField()
    description = models.TextField(null=True, blank=True)  # Allow null values
    url = models.URLField()
    application_deadline = models.DateField(null=True, blank=True)
    program_start_date = models.DateField(null=True, blank=True)
    average_gpa = models.DecimalField(max_digits=3, decimal_places=2, null=True, blank=True)
    class_size = models.IntegerField()
    
    # Enhanced fields from comprehensive JSON data
    phone = models.CharField(max_length=50, null=True, blank=True)  # Increased from 20 to handle international formats
    address = models.TextField(null=True, blank=True)
    mission_statement = models.TextField(null=True, blank=True)
    unique_features = models.TextField(null=True, blank=True)
    curriculum_focus = models.TextField(null=True, blank=True)
    credentials_offered = models.CharField(max_length=200, null=True, blank=True)  # Increased from 100
    bridge_dual_degree = models.CharField(max_length=50, null=True, blank=True)
    doctorate_offered = models.CharField(max_length=100, null=True, blank=True)  # Fixed for any response
    masters_degree_type = models.CharField(max_length=200, null=True, blank=True)  # Increased from 100
    program_length = models.CharField(max_length=50, null=True, blank=True)  # Increased from 20 to handle descriptive lengths
    start_month = models.CharField(max_length=50, null=True, blank=True)  # Increased from 20 to handle "September 2026" format
    estimated_class_size = models.CharField(max_length=100, null=True, blank=True)  # Fixed for any response
    part_time_option = models.CharField(max_length=100, null=True, blank=True)  # Fixed for any response
    distance_learning = models.CharField(max_length=100, null=True, blank=True)  # Fixed for any response  
    on_campus_housing = models.CharField(max_length=100, null=True, blank=True)  # Fixed for any response
    admission_type = models.CharField(max_length=200, null=True, blank=True)  # Increased from 100
    
    # Enhanced JSON storage - stores all program data flexibly
    enhanced_data = models.JSONField(null=True, blank=True, help_text="Complete program data from enhanced JSON source")
    
    # New fields from enhanced JSON structure - with safe field lengths
    city_state = models.CharField(max_length=200, null=True, blank=True)  # Increased from 100
    caspa_member = models.BooleanField(default=True)
    upcoming_caspa_cycle = models.BooleanField(default=True)
    caspa_deadline = models.DateField(null=True, blank=True)
    caspa_deadline_requirement = models.TextField(null=True, blank=True)
    program_website = models.URLField(null=True, blank=True)
    program_social = models.CharField(max_length=200, null=True, blank=True)
    email = models.EmailField(null=True, blank=True)
    last_updated = models.CharField(max_length=50, null=True, blank=True)
    international_application_link = models.URLField(null=True, blank=True)
    curriculum_focus_json = models.JSONField(null=True, blank=True)  # Store as array
    credentials_offered_json = models.JSONField(null=True, blank=True)  # Store as array
    type_of_masters_degree = models.JSONField(null=True, blank=True)  # Store as array
    program_length_months = models.CharField(max_length=50, null=True, blank=True)  # Increased from 20
    required_onsite_interview = models.CharField(max_length=100, null=True, blank=True)  # Fixed for any response
    types_of_interviews = models.JSONField(null=True, blank=True)  # Store as array

    # --- NORMALIZED SEARCH FIELDS ---
    # Critical numeric fields for efficient searching/filtering
    
    # Class size as integer for proper numeric comparisons
    class_size_min = models.IntegerField(null=True, blank=True, db_index=True, 
                                         help_text="Minimum class size as integer")
    class_size_max = models.IntegerField(null=True, blank=True, db_index=True,
                                         help_text="Maximum class size as integer")
    
    # Program length in months for consistent comparisons
    program_length_months_min = models.IntegerField(null=True, blank=True, db_index=True,
                                                    help_text="Program length in months as integer")
    program_length_months_max = models.IntegerField(null=True, blank=True, db_index=True,
                                                    help_text="Maximum program length if range")
    
    # Boolean fields for filtering
    has_part_time_option = models.BooleanField(null=True, blank=True, db_index=True,
                                               help_text="True if part-time option available")
    has_distance_learning = models.BooleanField(null=True, blank=True, db_index=True,
                                                help_text="True if distance learning available")
    has_on_campus_housing = models.BooleanField(null=True, blank=True, db_index=True,
                                                help_text="True if on-campus housing available")
    requires_onsite_interview = models.BooleanField(null=True, blank=True, db_index=True,
                                                    help_text="True if onsite interview required")
    offers_doctorate = models.BooleanField(null=True, blank=True, db_index=True,
                                           help_text="True if doctorate degree offered")
    
    # Tuition as decimal for proper currency handling
    tuition_in_state = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True,
                                           db_index=True, help_text="In-state tuition amount")
    tuition_out_of_state = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True,
                                               db_index=True, help_text="Out-of-state tuition amount")
    
    # GPA requirements as decimals
    min_gpa_overall = models.DecimalField(max_digits=3, decimal_places=2, null=True, blank=True,
                                          db_index=True, help_text="Minimum overall GPA required")
    min_gpa_science = models.DecimalField(max_digits=3, decimal_places=2, null=True, blank=True,
                                          db_index=True, help_text="Minimum science GPA required")
    min_gpa_prereq = models.DecimalField(max_digits=3, decimal_places=2, null=True, blank=True,
                                         db_index=True, help_text="Minimum prerequisite GPA required")
    
    # Experience requirements as integers
    min_patient_care_hours = models.IntegerField(null=True, blank=True, db_index=True,
                                                  help_text="Minimum patient care hours required")
    min_shadowing_hours = models.IntegerField(null=True, blank=True, db_index=True,
                                              help_text="Minimum shadowing hours required")
    
    # Location fields for geographic searches
    state = models.CharField(max_length=2, null=True, blank=True, db_index=True,
                             help_text="Two-letter state code")
    city = models.CharField(max_length=100, null=True, blank=True, db_index=True,
                            help_text="City name")

    def __str__(self):
        return self.name


class PrerequisiteCourse(models.Model):
    program = models.ForeignKey(Program, on_delete=models.CASCADE, related_name='prerequisite_courses')
    course_name = models.TextField()
    credits = models.TextField()
    lab_required = models.BooleanField(default=False)
    time_limit = models.TextField()

    def __str__(self):
        return f"{self.program.name} - {self.course_name}"


class HealthcareExperience(models.Model):
    program = models.ForeignKey(Program, on_delete=models.CASCADE, related_name='healthcare_experience')
    required = models.BooleanField(default=False, null=True, blank=True)
    hours_needed = models.IntegerField(null=True, blank=True)
    time_limit = models.TextField(null=True, blank=True)  # Allow null values
    paid_accepted = models.BooleanField(default=True)
    volunteer_accepted = models.BooleanField(default=True)
    virtual_accepted = models.BooleanField(default=False)
    status = models.TextField()


class PatientCareExperience(models.Model):
    program = models.ForeignKey(Program, on_delete=models.CASCADE, related_name='patient_care_experience')
    required = models.BooleanField(default=False, null=True, blank=True)
    hours_needed = models.IntegerField(null=True, blank=True)
    time_limit = models.TextField()
    paid_accepted = models.BooleanField(default=True)
    volunteer_accepted = models.BooleanField(default=True)
    paid_status = models.TextField()
    volunteer_status = models.TextField()


class ShadowingRequirement(models.Model):
    program = models.ForeignKey(Program, on_delete=models.CASCADE, related_name='shadowing_requirements')
    pa_shadowing = models.TextField()
    physician_shadowing = models.TextField()
    other_shadowing = models.TextField()
    virtual_accepted = models.BooleanField(default=False)


class GRERequirement(models.Model):
    program = models.ForeignKey(Program, on_delete=models.CASCADE, related_name='gre_requirements')
    required = models.BooleanField(null=True, blank=True) # Allow null if inference is uncertain
    verbal_score = models.TextField()
    quantitative_score = models.TextField()
    analytical_score = models.TextField()
    verbal_percentile = models.TextField()
    quantitative_percentile = models.TextField()
    analytical_percentile = models.TextField()


class GPARequirement(models.Model):
    program = models.ForeignKey(Program, on_delete=models.CASCADE, related_name='gpa_requirements')
    minimum_overall = models.TextField(null=True, blank=True)  # Allow null values
    minimum_prereq = models.TextField(null=True, blank=True)  # Allow null values
    minimum_science = models.TextField(null=True, blank=True)  # Allow null values


class InterviewRequirement(models.Model):
    program = models.ForeignKey(Program, on_delete=models.CASCADE, related_name='interview_requirements')
    required = models.BooleanField(default=True)
    onsite_required = models.BooleanField(default=True)
    mmi_used = models.BooleanField(default=False)
    interview_types = models.JSONField()  # This will store the array of interview types


class ApplicationRequirement(models.Model):
    program = models.ForeignKey(Program, on_delete=models.CASCADE, related_name='application_requirements')
    caspa_required = models.BooleanField(default=True)
    caspa_deadline = models.DateField(null=True)
    caspa_verification_requirement = models.TextField()
    supplemental_required = models.BooleanField(default=False)
    supplemental_deadline = models.DateField(null=True)
    supplemental_fee = models.TextField()
    fee_waiver_available = models.BooleanField(default=False)
    admission_type = models.TextField()
    
    # Enhanced fields for tuition and deposit information
    tuition_separate_rates = models.CharField(max_length=20, null=True, blank=True)  # Increased from 10
    tuition_amount = models.CharField(max_length=50, null=True, blank=True)
    resident_tuition = models.CharField(max_length=50, null=True, blank=True)
    non_resident_tuition = models.CharField(max_length=50, null=True, blank=True)
    seat_deposit_required = models.CharField(max_length=20, null=True, blank=True)  # Increased from 10
    seat_deposit_amount = models.CharField(max_length=50, null=True, blank=True)  # Increased from 20 to handle larger amounts
    seat_deposit_refundable = models.CharField(max_length=20, null=True, blank=True)  # Increased from 10


class CASPARequirement(models.Model):
    """
    Stores CASPA application deadlines and requirements
    """
    program = models.ForeignKey(Program, on_delete=models.CASCADE, related_name='caspa_requirements')
    caspa_member = models.BooleanField(default=True)
    upcoming_caspa_cycle = models.BooleanField(default=True)
    application_deadline = models.DateField(null=True, blank=True)
    deadline_requirement = models.TextField(null=True, blank=True)
    supplemental_application = models.BooleanField(default=False)
    supplemental_deadline = models.DateField(null=True, blank=True)
    supplemental_application_fee = models.CharField(max_length=50, null=True, blank=True)
    supplemental_application_fee_waiver = models.BooleanField(default=False)
    view_supplemental_application = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.program.name} - CASPA Requirements"


class TuitionInformation(models.Model):
    """
    Stores detailed tuition and deposit information
    """
    program = models.ForeignKey(Program, on_delete=models.CASCADE, related_name='tuition_information')
    separate_tuition_rates = models.BooleanField(default=False)
    tuition = models.CharField(max_length=50, null=True, blank=True)
    resident_tuition = models.CharField(max_length=50, null=True, blank=True)
    non_resident_tuition = models.CharField(max_length=50, null=True, blank=True)
    seat_deposit = models.BooleanField(default=False)
    seat_deposit_cost = models.CharField(max_length=50, null=True, blank=True)  # Increased from 20
    refundable_seat_deposit = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.program.name} - Tuition Information"


class OtherRequirement(models.Model):
    program = models.ForeignKey(Program, on_delete=models.CASCADE, related_name='other_requirements')
    daca_accepted = models.BooleanField(default=False)
    veteran_support = models.TextField()
    transfer_accepted = models.BooleanField(default=False)
    out_of_state_accepted = models.BooleanField(default=True)
    international_accepted = models.BooleanField(default=False)
    international_requirements = models.TextField()


class RecommendationRequirement(models.Model):
    program = models.ForeignKey(Program, on_delete=models.CASCADE, related_name='recommendation_requirements')
    number_required = models.TextField()
    types_required = models.JSONField()  # This will store the array of required types
    specific_requirements = models.TextField()


class ClassProfile(models.Model):
    program = models.ForeignKey(Program, on_delete=models.CASCADE, related_name='class_profiles')
    entering_class_year = models.IntegerField()
    number_of_applicants = models.IntegerField()
    average_gpa = models.DecimalField(max_digits=3, decimal_places=2)
    average_age = models.IntegerField()
    in_state_students_percentage = models.DecimalField(max_digits=5, decimal_places=2)

    def __str__(self):
        return f"{self.program.name} - {self.entering_class_year}"


class PANCEPassRate(models.Model):
    """
    Stores PANCE pass rate data for programs by year and group type
    """
    program = models.ForeignKey(Program, on_delete=models.CASCADE, related_name='pance_pass_rates')
    year = models.CharField(max_length=50)  # Increased to handle any year format
    group = models.CharField(max_length=50)  # "All Takers" or "First Time Takers"
    candidates_took_pance = models.IntegerField(null=True, blank=True)
    exam_attempts = models.IntegerField(null=True, blank=True)
    exams_passed = models.IntegerField(null=True, blank=True)
    program_pass_rate = models.CharField(max_length=50, null=True, blank=True)  # Increased to handle longer values
    national_pass_rate = models.CharField(max_length=50, null=True, blank=True)  # Increased to handle longer values
    ultimate_pass_rate = models.CharField(max_length=20, null=True, blank=True)  # Increased from 10

    class Meta:
        unique_together = ['program', 'year', 'group']
        ordering = ['-year', 'group']

    def __str__(self):
        return f"{self.program.name} - {self.year} ({self.group})"


class AttritionData(models.Model):
    """
    Stores attrition and graduation data for program classes
    """
    program = models.ForeignKey(Program, on_delete=models.CASCADE, related_name='attrition_data')
    class_year = models.CharField(max_length=50)  # Increased to handle any year format
    max_entering_class_size = models.IntegerField(null=True, blank=True)
    entering_class_size = models.IntegerField(null=True, blank=True)
    graduates = models.IntegerField(null=True, blank=True)
    attrition_rate = models.CharField(max_length=50, null=True, blank=True)  # Increased to handle longer values
    graduation_rate = models.CharField(max_length=50, null=True, blank=True)  # Increased to handle longer values

    class Meta:
        unique_together = ['program', 'class_year']
        ordering = ['-class_year']

    def __str__(self):
        return f"{self.program.name} - Class of {self.class_year}"


class EnhancedClassProfile(models.Model):
    """
    Stores comprehensive class profile data including GPA, test scores, and application statistics
    """
    program = models.ForeignKey(Program, on_delete=models.CASCADE, related_name='enhanced_class_profiles')
    year = models.CharField(max_length=50)
    total_applications = models.CharField(max_length=50, null=True, blank=True)
    total_interviewed = models.CharField(max_length=50, null=True, blank=True)
    total_matriculants = models.CharField(max_length=50, null=True, blank=True)
    average_overall_gpa = models.CharField(max_length=50, null=True, blank=True)  # Increased from 20
    average_science_gpa = models.CharField(max_length=50, null=True, blank=True)  # Increased from 20
    average_healthcare_hours = models.CharField(max_length=50, null=True, blank=True)
    gre_verbal_score = models.CharField(max_length=50, null=True, blank=True)  # Increased from 20
    gre_quantitative_score = models.CharField(max_length=50, null=True, blank=True)  # Increased from 20
    gre_analytical_score = models.CharField(max_length=50, null=True, blank=True)  # Increased from 20
    gre_verbal_percentile = models.CharField(max_length=50, null=True, blank=True)  # Increased from 20
    gre_quantitative_percentile = models.CharField(max_length=50, null=True, blank=True)  # Increased from 20
    gre_analytical_percentile = models.CharField(max_length=50, null=True, blank=True)  # Increased from 20

    class Meta:
        unique_together = ['program', 'year']
        ordering = ['-year']

    def __str__(self):
        return f"{self.program.name} - Enhanced Profile {self.year}"


class MatriculantDemographics(models.Model):
    """
    Stores demographic information about matriculated students
    """
    program = models.ForeignKey(Program, on_delete=models.CASCADE, related_name='matriculant_demographics')
    year = models.CharField(max_length=50, null=True, blank=True)
    female_count = models.CharField(max_length=50, null=True, blank=True)  # Increased to handle longer values
    male_count = models.CharField(max_length=50, null=True, blank=True)  # Increased to handle longer values
    non_binary_count = models.CharField(max_length=50, null=True, blank=True)  # Increased to handle longer values
    gender_unknown_count = models.CharField(max_length=50, null=True, blank=True)  # Increased to handle longer values
    american_indian_count = models.CharField(max_length=50, null=True, blank=True)  # Increased to handle longer values
    asian_count = models.CharField(max_length=50, null=True, blank=True)  # Increased to handle longer values
    black_count = models.CharField(max_length=50, null=True, blank=True)  # Increased to handle longer values
    hispanic_count = models.CharField(max_length=50, null=True, blank=True)  # Increased to handle longer values
    hawaiian_pacific_count = models.CharField(max_length=50, null=True, blank=True)  # Increased to handle longer values
    white_count = models.CharField(max_length=50, null=True, blank=True)  # Increased to handle longer values
    other_count = models.CharField(max_length=50, null=True, blank=True)  # Increased to handle longer values

    class Meta:
        unique_together = ['program', 'year']
        ordering = ['-year']

    def __str__(self):
        return f"{self.program.name} - Demographics {self.year or 'N/A'}"


class Curriculum(models.Model):
    program = models.ForeignKey(Program, on_delete=models.CASCADE)
    year = models.TextField()
    courses = models.JSONField()  # Stores course details as JSON
    
    # Enhanced fields for detailed curriculum data
    didactic_term_type = models.CharField(max_length=50, null=True, blank=True)  # Increased from 20
    didactic_terms = models.JSONField(null=True, blank=True)  # Detailed term/course structure
    clinical_rotations = models.JSONField(null=True, blank=True)  # Required rotations


class ProgramCurriculum(models.Model):
    """
    Stores detailed curriculum structure for both didactic and clinical phases
    """
    program = models.ForeignKey(Program, on_delete=models.CASCADE, related_name='program_curriculum')
    curriculum_focus = models.JSONField(null=True, blank=True)  # Array of focus areas
    didactic_phase_length = models.CharField(max_length=50, null=True, blank=True)
    clinical_phase_length = models.CharField(max_length=50, null=True, blank=True)
    total_program_length = models.CharField(max_length=50, null=True, blank=True)
    didactic_courses = models.JSONField(null=True, blank=True)  # Detailed course structure
    clinical_rotations = models.JSONField(null=True, blank=True)  # Required clinical rotations
    elective_rotations = models.JSONField(null=True, blank=True)  # Available elective rotations

    def __str__(self):
        return f"{self.program.name} - Curriculum Structure"


class AdmissionRequirement(models.Model):
    program = models.ForeignKey(Program, on_delete=models.CASCADE)
    requirements = models.JSONField()  # Stores various requirements as JSON


class ProgramCompetency(models.Model):
    program = models.ForeignKey(Program, on_delete=models.CASCADE)
    competencies = models.TextField()  # A descriptive text of competencies


class TranscriptRecord(models.Model):
    """
    Stores processed transcript data (courses, semesters, calculated GPAs)
    
    The transcript_data JSONField structure should now include:
    {
        'semesters': [
            {
                'term': str,
                'year': str,
                'courses': [
                    {
                        'id': str, // Unique identifier for the course
                        'code': str,
                        'name': str,
                        'credits': float,
                        'grade': str,
                        'is_science': bool,
                        'has_lab': bool,  // NEW: Indicates if the course includes a lab component
                        'manual_academic_level': str // Optional field for user-specified level
                    }
                ],
                'term_gpa': float,
                'cumulative_gpa': float
            }
        ],
        'academic_summary': {
            'institution': str,
            'student_name': str,
            'cumulative_gpa': float,
            'science_gpa': float,
            'science_credits': float,
            'total_credits': float
        }
    }
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='transcripts')
    institution = models.CharField(max_length=255)
    student_name = models.CharField(max_length=255, blank=True)
    calculated_gpa = models.DecimalField(max_digits=3, decimal_places=2, default=0.0)
    science_gpa = models.DecimalField(max_digits=3, decimal_places=2, default=0.0, null=True)
    transcript_data = models.JSONField(null=True)
    validation_status = models.CharField(
        max_length=20,
        choices=[
            ('pending', 'Pending Validation'),
            ('approved', 'Approved'),
            ('rejected', 'Rejected')
        ],
        default='pending'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Transcript for {self.student_name} from {self.institution}"


class UserTranscript(models.Model):
    STATUS_CHOICES = [
        ('uploaded', 'Uploaded'),
        ('processing', 'Processing'),
        ('validated', 'Validated'),
        ('failed', 'Failed'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='raw_transcripts')
    original_filename = models.CharField(max_length=255)
    s3_key = models.CharField(max_length=512)  # Full S3 path
    upload_date = models.DateTimeField(default=timezone.now)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='uploaded')
    is_active = models.BooleanField(default=True)  # Current active transcript
    file_size = models.IntegerField(default=0)  # Size in bytes
    mime_type = models.CharField(max_length=100, default='application/pdf')
    
    class Meta:
        ordering = ['-upload_date']
        
    def __str__(self):
        return f"{self.user.username} - {self.original_filename} ({self.status})"
        
    def get_s3_path(self):
        """Generate the full S3 path for this transcript"""
        return f"transcripts/user_{self.user.id}/{self.s3_key}"


class PrerequisiteSelection(models.Model):
    """
    Stores user-selected courses for prerequisites
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='prerequisite_selections')
    prerequisite_name = models.CharField(max_length=255)
    course_code = models.CharField(max_length=50)
    course_name = models.CharField(max_length=255)
    credits = models.DecimalField(max_digits=5, decimal_places=2)
    grade = models.CharField(max_length=5)
    institution = models.CharField(max_length=255)
    term = models.CharField(max_length=50, blank=True, null=True)
    year = models.CharField(max_length=50, blank=True, null=True)  # Increased from 20
    has_lab = models.BooleanField(default=False)  # New field to track lab component
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-updated_at']

    def __str__(self):
        return f"{self.prerequisite_name}: {self.course_code} - {self.course_name}"


class ProgramMatchingResult(models.Model):
    """Store program matching results for each user"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='matching_results')
    timestamp = models.DateTimeField(auto_now=True)
    matching_results = models.JSONField(null=True)  # Store the complete matching results
    data_hash = models.CharField(max_length=64)  # Store hash of user data to detect changes
    
    class Meta:
        ordering = ['-timestamp']
        verbose_name = 'Program Matching Result'
        verbose_name_plural = 'Program Matching Results'
    
    def __str__(self):
        return f"{self.user.username}'s matching results from {self.timestamp}"
