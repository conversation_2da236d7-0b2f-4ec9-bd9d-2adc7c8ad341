"""
Program Data Migration Service

Handles the complete migration process from JSON files to database models.
Integrates with the JSON parser and data transformer utilities.
"""

import json
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

from django.db import transaction
from django.utils import timezone

from gradapp.utils.json_parser import PAJSONParser
from gradapp.utils.data_transformer import ProgramDataTransformer
from gradapp.models import Program, School


class MigrationError(Exception):
    """Custom exception for migration-related errors"""
    pass


class ProgramDataMigrationProcessor:
    """
    Main processor for migrating PA program data from JSON files to database
    """
    
    def __init__(self, json_directory_path: str, log_level: int = logging.INFO):
        self.json_directory_path = Path(json_directory_path)
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(log_level)
        
        # Initialize utilities
        self.json_parser = PAJSONParser()
        self.data_transformer = ProgramDataTransformer()
        
        # Migration statistics
        self.stats = {
            'total_files': 0,
            'processed_successfully': 0,
            'failed_processing': 0,
            'programs_created': 0,
            'programs_updated': 0,
            'schools_created': 0,
            'schools_updated': 0,
            'errors': [],
            'warnings': []
        }
    
    def process_all_programs(self, batch_size: int = 50) -> Dict[str, Any]:
        """
        Process all JSON files in the directory
        
        Args:
            batch_size: Number of programs to process in each transaction batch
            
        Returns:
            Dict containing migration statistics and results
        """
        json_files = list(self.json_directory_path.glob("*.json"))
        self.stats['total_files'] = len(json_files)
        
        self.logger.info(f"Starting migration of {len(json_files)} JSON files")
        
        # Process files in batches
        for i in range(0, len(json_files), batch_size):
            batch_files = json_files[i:i + batch_size]
            batch_num = (i // batch_size) + 1
            total_batches = (len(json_files) + batch_size - 1) // batch_size
            
            self.logger.info(f"Processing batch {batch_num}/{total_batches} ({len(batch_files)} files)")
            
            try:
                with transaction.atomic():
                    self._process_batch(batch_files)
            except Exception as e:
                self.logger.error(f"Batch {batch_num} failed: {str(e)}")
                # Continue with next batch
                continue
        
        # Calculate success rate
        success_rate = (self.stats['processed_successfully'] / self.stats['total_files'] * 100) if self.stats['total_files'] > 0 else 0
        
        return {
            'statistics': self.stats,
            'success_rate': success_rate,
            'errors': self.stats['errors'],
            'warnings': self.stats['warnings'],
            'timestamp': timezone.now().isoformat()
        }
    
    def _process_batch(self, json_files: List[Path]):
        """Process a batch of JSON files within a transaction"""
        for json_file in json_files:
            try:
                self._process_single_file(json_file)
                self.stats['processed_successfully'] += 1
            except Exception as e:
                self.stats['failed_processing'] += 1
                error_info = {
                    'file': json_file.name,
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                }
                self.stats['errors'].append(error_info)
                self.logger.error(f"Failed to process {json_file.name}: {str(e)}")
    
    def _process_single_file(self, json_file: Path):
        """
        Process a single JSON file and create/update database records.
        
        Args:
            json_file: Path to the JSON file to process
            
        Raises:
            MigrationError: If file processing fails
        """
        self.logger.debug(f"Processing file: {json_file.name}")
        
        # Read and parse JSON file
        raw_data = self.json_parser.read_json_file(json_file)
        
        if not raw_data:
            raise MigrationError(f"Failed to read JSON data from {json_file.name}")
        
        # Detect JSON structure type and process accordingly
        if 'program_data' in raw_data:
            # New comprehensive structure
            self.logger.info(f"Processing comprehensive structure: {json_file.name}")
            program = self.data_transformer.transform_comprehensive_program_data(raw_data)
        else:
            # Legacy structure - extract program data
            program_data = raw_data
            
            # Add program name from the top level if available
            if 'program_name' in raw_data:
                program_data['program_name'] = raw_data['program_name']
            
            # Validate structure
            if not self._validate_json_structure(program_data, json_file.name):
                raise MigrationError(f"Invalid JSON structure in {json_file.name}")
            
            # Transform data to database models
            program = self.data_transformer.transform_program_data(program_data)
        
        if program is None:
            raise MigrationError(f"Failed to transform data in {json_file.name}")
        
        # Update statistics
        if program.pk:  # Program was updated
            self.stats['programs_updated'] += 1
        else:  # Program was created
            self.stats['programs_created'] += 1
        
        self.logger.debug(f"Successfully processed: {program.name} at {program.school.name}")
    
    def _validate_json_structure(self, data: Dict[str, Any], filename: str) -> bool:
        """Validate that JSON has required structure"""
        # Check if we have the basic program information
        if 'program_name' in data:
            return True
        
        # Check for nested structure with required fields
        required_fields = ['name', 'school']
        
        for field in required_fields:
            if field not in data:
                self.logger.warning(f"Missing required field '{field}' in {filename}")
                return False
        
        return True
    
