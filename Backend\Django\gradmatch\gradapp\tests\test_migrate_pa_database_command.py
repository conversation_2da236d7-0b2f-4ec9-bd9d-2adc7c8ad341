"""
Tests for the migrate_pa_database management command

This test suite validates the comprehensive migration command functionality
including backup creation, progress tracking, error handling, and rollback capabilities.
"""

import json
import os
import tempfile
import shutil
from pathlib import Path
from unittest.mock import patch, MagicMock, mock_open
from django.test import TestCase
from django.core.management import call_command
from django.core.management.base import CommandError
from django.db import connection
from django.conf import settings
from io import StringIO

from gradapp.management.commands.migrate_pa_database import Command
from gradapp.models import Program, School


class MigratePADatabaseCommandTest(TestCase):
    """Test cases for the migrate_pa_database management command"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.test_json_dir = None
        self.command = Command()
        
    def tearDown(self):
        """Clean up test fixtures"""
        if self.test_json_dir and os.path.exists(self.test_json_dir):
            shutil.rmtree(self.test_json_dir)
    
    def create_test_json_directory(self, num_files=3):
        """Create a temporary directory with test JSON files"""
        self.test_json_dir = tempfile.mkdtemp()
        
        # Sample program data
        sample_program = {
            "program_name": "Test PA Program",
            "program_website": "https://test.edu/pa",
            "phone_number": "************",
            "email": "<EMAIL>",
            "mission_statement": "Test mission statement",
            "address": {
                "street": "123 Test St",
                "city": "Test City",
                "state": "TS",
                "zip_code": "12345"
            },
            "estimated_incoming_class_size": "30",
            "caspa_member": "Yes",
            "application_deadline": "2024-01-15"
        }
        
        # Create test JSON files
        for i in range(num_files):
            program_data = sample_program.copy()
            program_data["program_name"] = f"Test PA Program {i+1}"
            
            file_path = os.path.join(self.test_json_dir, f"program_{i+1}.json")
            with open(file_path, 'w') as f:
                json.dump(program_data, f)
        
        return self.test_json_dir
    
    def test_command_requires_json_directory_argument(self):
        """Test that command requires json_directory argument"""
        out = StringIO()
        err = StringIO()
        
        with self.assertRaises(CommandError):
            call_command('migrate_pa_database', stdout=out, stderr=err)
    
    def test_command_validates_json_directory_exists(self):
        """Test that command validates JSON directory exists"""
        out = StringIO()
        err = StringIO()
        
        with self.assertRaises(CommandError) as cm:
            call_command('migrate_pa_database', '/nonexistent/path', stdout=out, stderr=err)
        
        self.assertIn("does not exist", str(cm.exception))
    
    def test_command_validates_directory_contains_json_files(self):
        """Test that command validates directory contains JSON files"""
        # Create empty directory
        empty_dir = tempfile.mkdtemp()
        try:
            out = StringIO()
            err = StringIO()
            
            with self.assertRaises(CommandError) as cm:
                call_command('migrate_pa_database', empty_dir, stdout=out, stderr=err)
            
            self.assertIn("No JSON files found", str(cm.exception))
        finally:
            shutil.rmtree(empty_dir)
    
    @patch('gradapp.management.commands.migrate_pa_database.ProgramDataMigrationProcessor')
    def test_dry_run_mode(self, mock_processor):
        """Test dry run mode functionality"""
        json_dir = self.create_test_json_directory(2)
        
        out = StringIO()
        err = StringIO()
        
        # Mock processor for validation
        mock_instance = MagicMock()
        mock_instance._validate_json_structure.return_value = True
        mock_processor.return_value = mock_instance
        
        call_command(
            'migrate_pa_database', 
            json_dir, 
            '--dry-run',
            '--skip-backup',
            stdout=out,
            stderr=err
        )
        
        # Verify dry run output
        output = out.getvalue()
        self.assertIn("Dry Run Validation Results", output)
        self.assertIn("Total JSON files: 2", output)
    
    @patch('gradapp.management.commands.migrate_pa_database.ProgramDataMigrationProcessor')
    @patch('gradapp.management.commands.migrate_pa_database.shutil.copy2')
    def test_backup_creation_sqlite(self, mock_copy, mock_processor):
        """Test SQLite database backup creation"""
        json_dir = self.create_test_json_directory(1)
        
        # Mock processor
        mock_instance = MagicMock()
        mock_instance.process_all_programs.return_value = {
            'statistics': {'total_files': 1, 'processed_successfully': 1, 'failed_processing': 0},
            'success_rate': 100.0,
            'errors': [],
            'warnings': []
        }
        mock_processor.return_value = mock_instance
        
        out = StringIO()
        err = StringIO()
        
        with patch('django.conf.settings.DATABASES', {'default': {'ENGINE': 'django.db.backends.sqlite3', 'NAME': 'test.db'}}):
            call_command(
                'migrate_pa_database',
                json_dir,
                '--batch-size', '10',
                stdout=out,
                stderr=err
            )
        
        # Verify backup was attempted
        mock_copy.assert_called_once()
        
        # Verify output contains backup information
        output = out.getvalue()
        self.assertIn("MIGRATION COMPLETED", output)
    
    @patch('gradapp.management.commands.migrate_pa_database.ProgramDataMigrationProcessor')
    def test_migration_with_errors(self, mock_processor):
        """Test migration handling with errors"""
        json_dir = self.create_test_json_directory(2)
        
        # Mock processor with errors
        mock_instance = MagicMock()
        mock_instance.process_all_programs.return_value = {
            'statistics': {
                'total_files': 2, 
                'processed_successfully': 1, 
                'failed_processing': 1,
                'programs_created': 1,
                'programs_updated': 0,
                'schools_created': 1,
                'schools_updated': 0
            },
            'success_rate': 50.0,
            'errors': [{'file': 'program_2.json', 'error': 'Test error'}],
            'warnings': []
        }
        mock_processor.return_value = mock_instance
        
        out = StringIO()
        err = StringIO()
        
        call_command(
            'migrate_pa_database',
            json_dir,
            '--skip-backup',
            '--continue-on-error',
            stdout=out,
            stderr=err
        )
        
        # Verify error handling in output
        output = out.getvalue()
        self.assertIn("ERRORS: 1 errors encountered", output)
        self.assertIn("Success rate: 50.00%", output)
    
    def test_command_arguments_validation(self):
        """Test command argument validation"""
        json_dir = self.create_test_json_directory(1)
        
        out = StringIO()
        err = StringIO()
        
        # Test batch size validation
        call_command(
            'migrate_pa_database',
            json_dir,
            '--batch-size', '100',
            '--skip-backup',
            '--dry-run',
            stdout=out,
            stderr=err
        )
        
        # Should complete without error
        output = out.getvalue()
        self.assertIn("Batch Size: 100", output)
    
    @patch('gradapp.management.commands.migrate_pa_database.subprocess.run')
    def test_postgresql_backup(self, mock_subprocess):
        """Test PostgreSQL backup functionality"""
        json_dir = self.create_test_json_directory(1)
        
        # Mock successful subprocess call
        mock_result = MagicMock()
        mock_result.returncode = 0
        mock_subprocess.return_value = mock_result
        
        command = Command()
        
        with patch('django.conf.settings.DATABASES', {
            'default': {
                'ENGINE': 'django.db.backends.postgresql',
                'NAME': 'test_db',
                'USER': 'test_user',
                'PASSWORD': 'test_pass',
                'HOST': 'localhost',
                'PORT': '5432'
            }
        }):
            # Test backup creation
            command._backup_postgresql_database('test_backup', '20240101_120000')
            
            # Verify pg_dump was called
            mock_subprocess.assert_called_once()
            call_args = mock_subprocess.call_args[0][0]
            self.assertIn('pg_dump', call_args[0])
    
    def test_logging_setup(self):
        """Test logging setup functionality"""
        command = Command()
        
        options = {
            'log_level': 'INFO',
            'log_file': None
        }
        
        # Test logging setup
        command._setup_logging(options)
        
        # Verify logger was created
        self.assertIsNotNone(command.logger)
        self.assertIsNotNone(command.progress_handler)
    
    @patch('gradapp.management.commands.migrate_pa_database.ProgramDataMigrationProcessor')
    def test_progress_tracking(self, mock_processor):
        """Test progress tracking functionality"""
        json_dir = self.create_test_json_directory(3)
        
        # Mock processor with detailed statistics
        mock_instance = MagicMock()
        mock_instance.process_all_programs.return_value = {
            'statistics': {
                'total_files': 3,
                'processed_successfully': 3,
                'failed_processing': 0,
                'programs_created': 2,
                'programs_updated': 1,
                'schools_created': 1,
                'schools_updated': 0
            },
            'success_rate': 100.0,
            'errors': [],
            'warnings': []
        }
        mock_processor.return_value = mock_instance
        
        out = StringIO()
        err = StringIO()
        
        call_command(
            'migrate_pa_database',
            json_dir,
            '--skip-backup',
            stdout=out,
            stderr=err
        )
        
        # Verify progress tracking in output
        output = out.getvalue()
        self.assertIn("Total files processed: 3", output)
        self.assertIn("Successfully processed: 3", output)
        self.assertIn("Programs created: 2", output)
        self.assertIn("Programs updated: 1", output)
        self.assertIn("Success rate: 100.00%", output)
  