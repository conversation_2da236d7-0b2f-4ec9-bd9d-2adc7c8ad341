import React, { useState } from 'react';
import { motion } from 'framer-motion';
import LoginForm from './LoginForm';
import RegisterForm from './RegisterForm';
import styled from '@emotion/styled';

const Container = styled.div`
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-image: url("C:\\Users\\<USER>\\MasterGradMatch\\NatureJapan.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
`;

const MainContent = styled.main`
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem;
  gap: 2rem;
`;

const Hero = styled.div`
  text-align: center;
  max-width: 800px;
  margin-bottom: 3rem;
`;

const Title = styled.h1`
  font-size: 3rem;
  color: #2c3e50;
  margin-bottom: 1rem;
`;

const Subtitle = styled.p`
  font-size: 1.2rem;
  color: #34495e;
  line-height: 1.6;
`;

const AuthContainer = styled.div`
  background: white;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
`;

const TabContainer = styled.div`
  display: flex;
  margin-bottom: 2rem;
  border-bottom: 2px solid #eee;
`;

const Tab = styled.button`
  flex: 1;
  padding: 1rem;
  border: none;
  background: none;
  color: ${props => props.active ? '#3498db' : '#7f8c8d'};
  font-weight: ${props => props.active ? 'bold' : 'normal'};
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    color: #3498db;
  }
`;

const Features = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  width: 100%;
  margin-top: 4rem;
`;

const FeatureCard = styled(motion.div)`
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  text-align: center;
`;

const LandingPage = () => {
  const [activeTab, setActiveTab] = useState('login');

  const features = [
    {
      title: "Smart Matching",
      description: "Get matched with PA programs based on your academic profile"
    },
    {
      title: "Transcript Analysis",
      description: "Automated analysis of your academic transcripts"
    },
    {
      title: "Program Insights",
      description: "Detailed insights into PA program requirements"
    }
  ];

  return (
    <Container>
      <MainContent>
        <Hero>
          <Title>Welcome to GradMatch</Title>
          <Subtitle>
            Your intelligent companion for matching with the perfect PA program.
            We analyze your academic profile and help you find the best fit for your future.
          </Subtitle>
        </Hero>

        <AuthContainer>
          <TabContainer>
            <Tab 
              active={activeTab === 'login'} 
              onClick={() => setActiveTab('login')}
            >
              Login
            </Tab>
            <Tab 
              active={activeTab === 'register'} 
              onClick={() => setActiveTab('register')}
            >
              Sign Up
            </Tab>
          </TabContainer>

          {activeTab === 'login' ? <LoginForm /> : <RegisterForm />}
        </AuthContainer>

        <Features>
          {features.map((feature, index) => (
            <FeatureCard
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.2 }}
            >
              <h3>{feature.title}</h3>
              <p>{feature.description}</p>
            </FeatureCard>
          ))}
        </Features>
      </MainContent>
    </Container>
  );
};

export default LandingPage; 