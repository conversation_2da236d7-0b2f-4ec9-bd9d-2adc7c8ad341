"""
Demo script for the PA JSON Parser utility

This script demonstrates how to use the PAJSONParser class to process
PA program data from JSON files.
"""

import os
import sys
from pathlib import Path

# Add the Django project to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from json_parser import PAJSONParser


def demo_json_parser():
    """
    Demonstrate the JSON parser functionality with the structured PA programs file
    """
    print("=== PA JSON Parser Demo ===\n")
    
    # Initialize parser with the current directory (where structured_pa_programs.json is located)
    current_dir = Path(__file__).parent.parent.parent  # Go up to gradmatch directory
    parser = PAJSONParser(data_directory=str(current_dir))
    
    print(f"Initialized parser with directory: {parser.data_directory}")
    
    try:
        # Discover JSON files
        print("\n1. Discovering JSON files...")
        json_files = parser.discover_json_files()
        print(f"Found {len(json_files)} JSON files:")
        for file in json_files:
            print(f"  - {file.name}")
        
        # Process the structured PA programs file specifically
        structured_file = current_dir / "structured_pa_programs.json"
        if structured_file.exists():
            print(f"\n2. Processing structured PA programs file...")
            print(f"File path: {structured_file}")
            
            # Read the JSON data
            data = parser.read_json_file(structured_file)
            
            if isinstance(data, list) and len(data) > 0:
                print(f"Successfully loaded {len(data)} program records")
                
                # Validate the first few programs
                print("\n3. Validating sample programs...")
                valid_count = 0
                invalid_count = 0
                
                # Test first 5 programs
                for i, program in enumerate(data[:5]):
                    print(f"\nValidating program {i+1}: {program.get('program_name', 'Unknown')}")
                    
                    is_valid, errors = parser.validate_json_data(program, Path(f"program_{i+1}"))
                    
                    if is_valid:
                        valid_count += 1
                        print("  ✓ Valid")
                    else:
                        invalid_count += 1
                        print(f"  ✗ Invalid ({len(errors)} errors)")
                        for error in errors[:3]:  # Show first 3 errors
                            print(f"    - {error}")
                        if len(errors) > 3:
                            print(f"    ... and {len(errors) - 3} more errors")
                
                print(f"\nValidation Summary (first 5 programs):")
                print(f"  Valid: {valid_count}")
                print(f"  Invalid: {invalid_count}")
                
                # Show sample program structure
                print("\n4. Sample program structure:")
                sample_program = data[0]
                print("Key fields found:")
                for key in sorted(sample_program.keys())[:10]:  # Show first 10 keys
                    value = sample_program[key]
                    if isinstance(value, str) and len(value) > 50:
                        value = value[:50] + "..."
                    print(f"  {key}: {value}")
                print("  ... (showing first 10 fields)")
                
            else:
                print("Error: Expected a list of programs")
                
        else:
            print(f"Error: structured_pa_programs.json not found at {structured_file}")
            
    except Exception as e:
        print(f"Error during demo: {str(e)}")
        import traceback
        traceback.print_exc()


def demo_validation_features():
    """
    Demonstrate specific validation features
    """
    print("\n=== Validation Features Demo ===\n")
    
    parser = PAJSONParser()
    
    # Test data with various validation scenarios
    test_cases = [
        {
            "name": "Valid Program",
            "data": {
                "program_name": "Test University PA Program",
                "program_url": "https://example.com/program",
                "mission_statement": "To educate competent physician assistants",
                "caspa_member": True,
                "upcoming_caspa_cycle": True,
                "curriculum_focus": ["Problem-Based Learning"],
                "minimum_overall_gpa_required": "3.0",
                "estimated_incoming_class_size": "50",
                "application_deadline": "10/01/2025"
            }
        },
        {
            "name": "Missing Required Fields",
            "data": {
                "program_url": "https://example.com/program",
                # Missing program_name, mission_statement, etc.
            }
        },
        {
            "name": "Invalid Data Types",
            "data": {
                "program_name": "Test Program",
                "program_url": "https://example.com/program",
                "mission_statement": "Test mission",
                "caspa_member": "invalid_boolean",  # Should be boolean
                "upcoming_caspa_cycle": True,
                "curriculum_focus": "not_an_array",  # Should be array
            }
        },
        {
            "name": "Invalid Business Logic",
            "data": {
                "program_name": "Test Program",
                "program_url": "https://example.com/program",
                "mission_statement": "Test mission",
                "caspa_member": True,
                "upcoming_caspa_cycle": True,
                "minimum_overall_gpa_required": "5.0",  # Invalid GPA range
                "estimated_incoming_class_size": "-10",  # Invalid class size
                "application_deadline": "invalid-date"  # Invalid date format
            }
        }
    ]
    
    for test_case in test_cases:
        print(f"Testing: {test_case['name']}")
        is_valid, errors = parser.validate_json_data(test_case['data'], Path("test.json"))
        
        if is_valid:
            print("  ✓ Valid")
        else:
            print(f"  ✗ Invalid ({len(errors)} errors)")
            for error in errors:
                print(f"    - {error}")
        print()


def demo_error_handling():
    """
    Demonstrate error handling capabilities
    """
    print("\n=== Error Handling Demo ===\n")
    
    parser = PAJSONParser("/nonexistent/directory")
    
    print("1. Testing nonexistent directory...")
    try:
        parser.discover_json_files()
    except Exception as e:
        print(f"  Caught expected error: {type(e).__name__}: {str(e)}")
    
    print("\n2. Testing malformed JSON...")
    # This would normally test with a real malformed file
    print("  (Would test with malformed JSON file in real scenario)")
    
    print("\n3. Testing processing statistics...")
    stats = parser.get_processing_stats()
    print(f"  Processing stats: {stats}")


if __name__ == "__main__":
    print("PA JSON Parser Utility Demo")
    print("=" * 50)
    
    # Run the main demo
    demo_json_parser()
    
    # Run validation features demo
    demo_validation_features()
    
    # Run error handling demo
    demo_error_handling()
    
    print("\n" + "=" * 50)
    print("Demo completed!")