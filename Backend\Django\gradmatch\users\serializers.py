from rest_framework import serializers
from .models import UserProfile

class UserProfileSerializer(serializers.ModelSerializer):
    email = serializers.EmailField(source='user.email', read_only=True)
    first_name = serializers.Char<PERSON>ield(required=False, allow_blank=True)
    last_name = serializers.CharField(required=False, allow_blank=True)
    date_of_birth = serializers.DateField(required=False, allow_null=True)
    undergrad_start_date = serializers.DateField(required=False, allow_null=True)
    gender = serializers.CharField(required=False, allow_blank=True)
    ethnicity = serializers.CharField(required=False, allow_blank=True)
    direct_patient_care_hours = serializers.IntegerField(required=False, min_value=0)
    shadowing_hours = serializers.IntegerField(required=False, min_value=0)
    # Add prerequisite class fields
    human_anatomy = serializers.CharField(required=False, allow_blank=True)
    human_physiology = serializers.Char<PERSON>ield(required=False, allow_blank=True)
    microbiology_with_lab = serializers.CharField(required=False, allow_blank=True)
    medical_terminology = serializers.CharField(required=False, allow_blank=True)
    statistics = serializers.CharField(required=False, allow_blank=True)
    organic_chemistry = serializers.CharField(required=False, allow_blank=True)
    genetics = serializers.CharField(required=False, allow_blank=True)
    psychology = serializers.CharField(required=False, allow_blank=True)
    biochemistry = serializers.CharField(required=False, allow_blank=True)
    english = serializers.CharField(required=False, allow_blank=True)
    social_science = serializers.CharField(required=False, allow_blank=True)
    humanities = serializers.CharField(required=False, allow_blank=True)
    behavioral_science = serializers.CharField(required=False, allow_blank=True)
    inorganic_chemistry = serializers.CharField(required=False, allow_blank=True)
    physics = serializers.CharField(required=False, allow_blank=True)
    sociology = serializers.CharField(required=False, allow_blank=True)
    # GRE Fields
    gre_verbal_score = serializers.IntegerField(required=False, allow_null=True)
    gre_quantitative_score = serializers.IntegerField(required=False, allow_null=True)
    gre_analytical_writing_score = serializers.DecimalField(max_digits=2, decimal_places=1, required=False, allow_null=True)
    gre_verbal_percentile = serializers.IntegerField(required=False, allow_null=True)
    gre_quantitative_percentile = serializers.IntegerField(required=False, allow_null=True)
    gre_analytical_writing_percentile = serializers.IntegerField(required=False, allow_null=True)
    # Other Tests
    has_taken_casper = serializers.BooleanField(required=False, default=False)
    has_taken_pa_cat = serializers.BooleanField(required=False, default=False)
    # PA-CAT Scores
    pa_cat_anatomy_ss = serializers.IntegerField(required=False, allow_null=True)
    pa_cat_anatomy_pr = serializers.IntegerField(required=False, allow_null=True)
    pa_cat_physiology_ss = serializers.IntegerField(required=False, allow_null=True)
    pa_cat_physiology_pr = serializers.IntegerField(required=False, allow_null=True)
    pa_cat_biology_ss = serializers.IntegerField(required=False, allow_null=True)
    pa_cat_biology_pr = serializers.IntegerField(required=False, allow_null=True)
    pa_cat_chemistry_ss = serializers.IntegerField(required=False, allow_null=True)
    pa_cat_chemistry_pr = serializers.IntegerField(required=False, allow_null=True)
    pa_cat_composite_ss = serializers.IntegerField(required=False, allow_null=True)
    pa_cat_composite_pr = serializers.IntegerField(required=False, allow_null=True)
    # Test Dates
    gre_test_date = serializers.DateField(required=False, allow_null=True)
    pa_cat_test_date = serializers.DateField(required=False, allow_null=True)
    casper_test_date = serializers.DateField(required=False, allow_null=True)

    class Meta:
        model = UserProfile
        fields = ['email', 'first_name', 'last_name', 'date_of_birth', 'undergrad_start_date',
                 'gender', 'ethnicity', 'direct_patient_care_hours', 
                 'shadowing_hours', 'human_anatomy', 'human_physiology',
                 'microbiology_with_lab', 'medical_terminology', 'statistics',
                 'organic_chemistry', 'genetics', 'psychology', 'biochemistry',
                 'english', 'social_science', 'humanities', 'behavioral_science',
                 'inorganic_chemistry', 'physics', 'sociology',
                 # Add GRE fields to Meta
                 'gre_verbal_score', 'gre_quantitative_score', 'gre_analytical_writing_score',
                 'gre_verbal_percentile', 'gre_quantitative_percentile', 'gre_analytical_writing_percentile',
                 # Add other test fields to Meta
                 'has_taken_casper', 'has_taken_pa_cat',
                 # Add PA-CAT score fields to Meta
                 'pa_cat_anatomy_ss', 'pa_cat_anatomy_pr', 'pa_cat_physiology_ss', 'pa_cat_physiology_pr',
                 'pa_cat_biology_ss', 'pa_cat_biology_pr', 'pa_cat_chemistry_ss', 'pa_cat_chemistry_pr',
                 'pa_cat_composite_ss', 'pa_cat_composite_pr',
                 # Add test date fields to Meta
                 'gre_test_date', 'pa_cat_test_date', 'casper_test_date']
        
    def update(self, instance, validated_data):
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        return instance 