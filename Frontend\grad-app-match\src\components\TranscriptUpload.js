import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import axios from 'axios';
import './TranscriptUpload.css';
import TranscriptValidation from './TranscriptValidation';

const TranscriptUpload = ({ sessionId, transcripts, setTranscripts, onTranscriptUpdate }) => {
    // Get currentUser and supabase client from AuthContext
    const { currentUser, supabase } = useAuth();
    const [selectedFile, setSelectedFile] = useState(null);
    const [uploadStatus, setUploadStatus] = useState('');
    const [error, setError] = useState('');
    const [processingTranscript, setProcessingTranscript] = useState(null);
    const [transcriptData, setTranscriptData] = useState(null);
    const [validationStatus, setValidationStatus] = useState('initial');
    const [isProcessing, setIsProcessing] = useState(false);

    useEffect(() => {
        // Check if currentUser is available (user is logged in via Supabase)
        if (!currentUser) {
            // If no user, set an error indicating login is needed
            setError('Authentication required. Please log in to manage transcripts.');
            // Optionally clear transcripts list if user logs out
            setTranscripts([]);
            return;
        }

        // If currentUser is available, clear any previous auth errors
        if (error && (error.includes('Authentication required') || error.includes('User information'))) {
             setError('');
        }

        // Initial fetch of transcripts when user logs in
        // This will be handled by the fetchValidatedTranscripts and fetchS3Transcripts calls
        // in the Dashboard component's useEffect, which pass the token.
        // We don't need to fetch them again here on mount, but we need the token for actions.

    }, [currentUser, error, setTranscripts]); // Depend on currentUser and error state

    const handleProcessTranscript = async (transcriptKey) => {
        try {
            setIsProcessing(true);
            setError(''); // Clear previous errors
            setUploadStatus('Processing transcript...'); // Update status

            // Get current session token
            const { data: { session }, error: sessionError } = await supabase.auth.getSession();
            if (sessionError || !session?.access_token) {
                throw new Error("User session not found or invalid for processing.");
            }
            const apiToken = session.access_token;

            // First, get the transcript ID from the database
            const response = await axios.get(
                'http://127.0.0.1:8000/transcripts/',
                {
                    headers: {
                        'Authorization': `Bearer ${apiToken}` // Use the obtained token
                    }
                }
            );

            console.log('Database transcripts:', response.data);
            console.log('Looking for transcript with key:', transcriptKey);

            // Extract filename from the S3 key
            const filename = transcriptKey.split('/').pop();
            console.log('Looking for filename:', filename);

            // Find the active transcript that matches the filename
            const transcript = response.data.find(t =>
                t.filename === filename && t.is_active
            );

            if (!transcript) {
                console.log('No matching transcript found in:', response.data);
                throw new Error('Could not find matching transcript in database');
            }

            console.log('Found matching transcript:', transcript);

            // Now send the processing request
            const processResponse = await axios.post(
                'http://127.0.0.1:8000/api/transcript/',
                {
                    sessionId: sessionId,
                    transcript_id: transcript.id
                },
                {
                    headers: {
                        'Authorization': `Bearer ${apiToken}`, // Use the obtained token
                        'Content-Type': 'application/json'
                    }
                }
            );

            if (processResponse.data.status === 'success' && processResponse.data.data?.structured_data) {
                const structuredData = processResponse.data.data.structured_data;
                console.log('Received structured data:', structuredData);

                if (!structuredData.semesters || !Array.isArray(structuredData.semesters)) {
                    throw new Error('Invalid transcript data: missing or invalid semesters array');
                }

                // Create academic summary from student_info and degree_info
                const academicSummary = {
                    institution: structuredData.degree_info.institution,
                    student_name: structuredData.student_info.name,
                    birth_date: structuredData.student_info.birth_date,
                    degree: structuredData.degree_info.major,
                    minor: structuredData.degree_info.minor,
                    transcript_stated_gpa: structuredData.degree_info.final_gpa
                };

                // Set the complete transcript data structure
                const completeTranscriptData = {
                    semesters: structuredData.semesters,
                    academic_summary: academicSummary
                };

                // Set both states with the complete data structure
                setTranscriptData(completeTranscriptData);
                setProcessingTranscript(completeTranscriptData);
                setValidationStatus('processing');
            } else {
                throw new Error(processResponse.data.message || 'Failed to process transcript: No structured data received');
            }
        } catch (err) {
            console.error('Error processing transcript:', err);
            setError(err.response?.data?.message || err.message || 'Failed to process transcript');
            // Reset processing state on error
            setProcessingTranscript(null);
            setTranscriptData(null);
            setValidationStatus('initial');
        } finally {
            setIsProcessing(false);
        }
    };

    const handleValidation = async (validationData) => {
        try {
            setIsProcessing(true);
            setError(''); // Clear previous errors
            setUploadStatus('Validating transcript...'); // Update status

            // Get current session token
            const { data: { session }, error: sessionError } = await supabase.auth.getSession();
            if (sessionError || !session?.access_token) {
                throw new Error("User session not found or invalid for validation.");
            }
            const apiToken = session.access_token;

            const response = await axios.post(
                'http://127.0.0.1:8000/api/validate-transcript/',
                validationData,
                {
                    headers: {
                        'Authorization': `Bearer ${apiToken}`, // Use the obtained token
                        'Content-Type': 'application/json'
                    }
                }
            );

            if (response.data.status === 'success') {
                setValidationStatus('approved');
                setProcessingTranscript(null);
                await onTranscriptUpdate(); // Refresh the transcripts list
            } else {
                throw new Error(response.data.message || 'Validation failed');
            }
        } catch (err) {
            console.error('Error validating transcript:', err);
            setError(err.response?.data?.message || 'Failed to validate transcript');
        } finally {
            setIsProcessing(false);
        }
    };

    const handleFileSelect = (event) => {
        const file = event.target.files[0];
        if (file) {
            if (file.type !== 'application/pdf') {
                setError('Please select a PDF file');
                setSelectedFile(null);
                event.target.value = '';
                return;
            }
            if (file.size > 10 * 1024 * 1024) { // 10MB limit
                setError('File size must be less than 10MB');
                setSelectedFile(null);
                event.target.value = '';
                return;
            }
            setSelectedFile(file);
            setError('');
        }
    };

    const handleUpload = async () => {
        // Validate auth state first
        if (!currentUser) {
            setError('User not authenticated. Please log in to upload transcripts.');
            return;
        }

        // Get current session token using the supabase client from context
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        if (sessionError || !session?.access_token) {
            setError("User session not found or invalid for upload.");
            return;
        }
        const apiToken = session.access_token;

        if (!selectedFile) {
            setError('Please select a file first');
            return;
        }

        if (!sessionId) {
            setError('Session ID is missing. Please try refreshing the page.');
            return;
        }

        setUploadStatus('Uploading transcript...');
        setError('');

        const formData = new FormData();
        formData.append('file', selectedFile);
        formData.append('user_id', currentUser.id.toString());
        formData.append('session_id', sessionId);

        try { // Correct start of try block
            // Step 1: Upload the transcript
            console.log('Step 1: Uploading transcript with:', {
                userId: currentUser.id,
                sessionId,
                fileName: selectedFile.name
            });

            const uploadResponse = await axios.post(
                'http://127.0.0.1:8000/transcripts/upload/',
                formData,
                {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                        'Authorization': `Bearer ${apiToken}` // Use the obtained token
                    }
                }
            );

            console.log('Upload response:', uploadResponse.data);

            if (uploadResponse.data.transcript_id) {
                setUploadStatus('Transcript uploaded successfully!');
                setSelectedFile(null);

                // Clear the file input
                const fileInput = document.getElementById('transcript-file');
                if (fileInput) fileInput.value = '';

                // Refresh the transcripts list
                await onTranscriptUpdate();
            } else {
                throw new Error('Upload successful but no transcript ID received');
            }

        } catch (err) { // Correct start of catch block
            console.error('Operation error:', {
                message: err.message,
                response: err.response ? {
                    status: err.response.status,
                    statusText: err.response.statusText,
                    data: err.response.data,
                } : 'No response'
            });

            const errorMessage = err.response?.data?.error || err.message || 'Failed to upload transcript';
            setError(`Error: ${errorMessage}. Please try again.`);
            setUploadStatus('');
        } finally { // Correct start of finally block
            setIsProcessing(false); // Assuming upload also uses this state
            setUploadStatus(''); // Clear status on completion/error
        }
    };

    return (
        <div className="transcript-upload-section">
            <h3>Upload New Transcript</h3>
            <p>Upload transcripts from each school you've attended. Files must be in PDF format and under 10MB.</p>

            <div className="upload-controls">
                <input
                    type="file"
                    id="transcript-file"
                    accept=".pdf"
                    onChange={handleFileSelect}
                    className="file-input"
                    disabled={!currentUser} // Disable if not logged in
                />
                <button
                    onClick={handleUpload}
                    disabled={!selectedFile || !currentUser} // Disable if no file or not logged in
                    className="upload-button"
                >
                    Upload Transcript
                </button>
            </div>

            {error && <div className="error-message">{error}</div>}
            {uploadStatus && <div className="status-message">{uploadStatus}</div>}

            {selectedFile && (
                <div className="selected-file">
                    Selected file: {selectedFile.name}
                </div>
            )}

            {processingTranscript ? (
                <TranscriptValidation
                    transcriptData={transcriptData}
                    validationStatus={validationStatus}
                    isProcessing={isProcessing}
                    onValidate={handleValidation}
                />
            ) : (
                <div className="transcripts-list">
                    <h3>Transcript Management</h3>
                    {!Array.isArray(transcripts) || transcripts.length === 0 ? (
                        <div className="no-transcripts">No transcripts uploaded yet</div>
                    ) : (
                        <table>
                            <thead>
                                <tr>
                                    <th>Filename</th>
                                    <th>Upload Date</th>
                                    <th>Size</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {transcripts.map((transcript) => (
                                    <tr key={transcript.key}>
                                        <td>{transcript.filename}</td>
                                        <td>{new Date(transcript.upload_date).toLocaleDateString()}</td>
                                        <td>{Math.round(transcript.size / 1024)} KB</td>
                                        <td>
                                            <button
                                                onClick={() => handleProcessTranscript(transcript.key)}
                                                className="process-button"
                                                disabled={isProcessing}
                                            >
                                                {isProcessing ? 'Processing...' : 'Process Transcript'}
                                            </button>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    )}
                </div>
            )}
        </div>
    );
};

export default TranscriptUpload;