# Generated by Django 5.0.1 on 2025-01-26 07:34

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='userprofile',
            name='behavioral_science',
            field=models.CharField(blank=True, max_length=50),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='biochemistry',
            field=models.Char<PERSON>ield(blank=True, max_length=50),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='english',
            field=models.CharField(blank=True, max_length=50),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='genetics',
            field=models.CharField(blank=True, max_length=50),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='human_anatomy',
            field=models.Char<PERSON><PERSON>(blank=True, max_length=50),
        ),
        migrations.Add<PERSON>ield(
            model_name='userprofile',
            name='human_physiology',
            field=models.<PERSON>r<PERSON><PERSON>(blank=True, max_length=50),
        ),
        migrations.Add<PERSON>ield(
            model_name='userprofile',
            name='humanities',
            field=models.CharField(blank=True, max_length=50),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='inorganic_chemistry',
            field=models.CharField(blank=True, max_length=50),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='medical_terminology',
            field=models.CharField(blank=True, max_length=50),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='microbiology_with_lab',
            field=models.CharField(blank=True, max_length=50),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='organic_chemistry',
            field=models.CharField(blank=True, max_length=50),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='physics',
            field=models.CharField(blank=True, max_length=50),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='psychology',
            field=models.CharField(blank=True, max_length=50),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='social_science',
            field=models.CharField(blank=True, max_length=50),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='sociology',
            field=models.CharField(blank=True, max_length=50),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='statistics',
            field=models.CharField(blank=True, max_length=50),
        ),
    ]
