"""
Django management command for comprehensive PA program database migration

This command orchestrates the complete migration process including:
- Database backup before migration starts
- Progress tracking and detailed logging throughout the process
- Data migration from JSON files to enhanced database schema
- Validation and error reporting
- Rollback capabilities in case of failure

Usage:
    python manage.py migrate_pa_database /path/to/json/files [options]
"""

import json
import logging
import os
import shutil
import subprocess
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional

from django.core.management.base import BaseCommand, CommandError
from django.core.management import call_command
from django.conf import settings
from django.db import connection, transaction
from django.utils import timezone

from gradapp.services.program_data_migration import ProgramDataMigrationProcessor, MigrationError
from gradapp.utils.migration_logging import (
    setup_migration_logging, 
    create_migration_log_file,
    MigrationTimer,
    log_system_info,
    create_error_report
)


class Command(BaseCommand):
    help = 'Comprehensive PA program database migration with backup and progress tracking'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.logger = None
        self.progress_handler = None
        self.backup_path = None
        self.migration_start_time = None
        self.migration_stats = {
            'backup_created': False,
            'migration_completed': False,
            'rollback_performed': False,
            'total_duration': 0,
            'backup_size': 0
        }

    def add_arguments(self, parser):
        """Add command line arguments"""
        parser.add_argument(
            'json_directory',
            type=str,
            help='Path to directory containing JSON program files (e.g., D:\\ScrapperPAEAReal)'
        )
        
        parser.add_argument(
            '--batch-size',
            type=int,
            default=50,
            help='Number of programs to process in each batch (default: 50)'
        )
        
        parser.add_argument(
            '--log-level',
            choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
            default='INFO',
            help='Set the logging level (default: INFO)'
        )
        
        parser.add_argument(
            '--backup-dir',
            type=str,
            default='database_backups',
            help='Directory to store database backups (default: database_backups)'
        )
        
        parser.add_argument(
            '--skip-backup',
            action='store_true',
            help='Skip database backup (NOT RECOMMENDED for production)'
        )
        
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Perform validation and backup without making database changes'
        )
        
        parser.add_argument(
            '--continue-on-error',
            action='store_true',
            help='Continue processing even if individual programs fail'
        )
        
        parser.add_argument(
            '--rollback-on-failure',
            action='store_true',
            help='Automatically rollback database if migration fails'
        )
        
        parser.add_argument(
            '--report-file',
            type=str,
            help='Path to save detailed migration report (JSON format)'
        )
        
        parser.add_argument(
            '--log-file',
            type=str,
            help='Path to save detailed logs (auto-generated if not specified)'
        )

    def handle(self, *args, **options):
        """Main command handler"""
        self.migration_start_time = timezone.now()
        
        try:
            # Setup logging
            self._setup_logging(options)
            
            # Log system information
            log_system_info(self.logger)
            
            # Validate inputs
            self._validate_inputs(options)
            
            # Display migration configuration
            self._display_configuration(options)
            
            # Create database backup
            if not options['skip_backup']:
                self._create_database_backup(options['backup_dir'])
            else:
                self.logger.warning("Skipping database backup as requested")
            
            # Perform dry run validation if requested
            if options['dry_run']:
                self._perform_dry_run_validation(options)
                return
            
            # Execute the migration
            migration_report = self._execute_migration(options)
            
            # Generate and save reports
            self._generate_reports(migration_report, options)
            
            # Display final results
            self._display_final_results(migration_report)
            
            self.migration_stats['migration_completed'] = True
            
        except Exception as e:
            self.logger.error(f"Migration failed: {str(e)}")
            
            # Perform rollback if requested and backup exists
            if options.get('rollback_on_failure') and self.backup_path:
                self._perform_rollback()
            
            # Create error report
            if self.progress_handler:
                error_report_path = f"migration_error_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                create_error_report(self.progress_handler, error_report_path)
                self.logger.info(f"Error report saved to: {error_report_path}")
            
            raise CommandError(f"Migration failed: {str(e)}")
        
        finally:
            # Calculate total duration
            if self.migration_start_time:
                duration = timezone.now() - self.migration_start_time
                self.migration_stats['total_duration'] = duration.total_seconds()
            
            # Log final statistics
            self._log_final_statistics()

    def _setup_logging(self, options: Dict[str, Any]):
        """Setup comprehensive logging for the migration process"""
        log_file = options.get('log_file')
        if not log_file:
            log_file = create_migration_log_file('pa_database_migration')
        
        self.logger, self.progress_handler = setup_migration_logging(
            log_level=options['log_level'],
            log_file=log_file,
            console_output=True,
            use_colors=True
        )
        
        self.logger.info("="*60)
        self.logger.info("PA PROGRAM DATABASE MIGRATION STARTED")
        self.logger.info("="*60)

    def _validate_inputs(self, options: Dict[str, Any]):
        """Validate command inputs and prerequisites"""
        json_directory = options['json_directory']
        
        # Validate JSON directory
        if not os.path.exists(json_directory):
            raise CommandError(f"JSON directory does not exist: {json_directory}")
        
        if not os.path.isdir(json_directory):
            raise CommandError(f"Path is not a directory: {json_directory}")
        
        # Check for JSON files
        json_files = list(Path(json_directory).glob("*.json"))
        if not json_files:
            raise CommandError(f"No JSON files found in directory: {json_directory}")
        
        self.logger.info(f"Found {len(json_files)} JSON files to process")
        
        # Validate backup directory
        backup_dir = options['backup_dir']
        if not options['skip_backup']:
            try:
                Path(backup_dir).mkdir(parents=True, exist_ok=True)
            except Exception as e:
                raise CommandError(f"Cannot create backup directory {backup_dir}: {str(e)}")
        
        # Check database connectivity
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
        except Exception as e:
            raise CommandError(f"Database connection failed: {str(e)}")

    def _display_configuration(self, options: Dict[str, Any]):
        """Display migration configuration"""
        self.stdout.write(self.style.SUCCESS("Migration Configuration:"))
        self.stdout.write(f"  JSON Directory: {options['json_directory']}")
        self.stdout.write(f"  Batch Size: {options['batch_size']}")
        self.stdout.write(f"  Log Level: {options['log_level']}")
        self.stdout.write(f"  Backup Directory: {options['backup_dir']}")
        self.stdout.write(f"  Skip Backup: {options['skip_backup']}")
        self.stdout.write(f"  Dry Run: {options['dry_run']}")
        self.stdout.write(f"  Continue on Error: {options['continue_on_error']}")
        self.stdout.write(f"  Rollback on Failure: {options['rollback_on_failure']}")
        
        if options['dry_run']:
            self.stdout.write(self.style.WARNING("DRY RUN MODE - No database changes will be made"))

    def _create_database_backup(self, backup_dir: str):
        """Create a comprehensive database backup before migration"""
        with MigrationTimer(self.logger, "database backup"):
            backup_timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # Determine database type and create appropriate backup
            db_engine = settings.DATABASES['default']['ENGINE']
            
            if 'sqlite' in db_engine:
                self._backup_sqlite_database(backup_dir, backup_timestamp)
            elif 'postgresql' in db_engine:
                self._backup_postgresql_database(backup_dir, backup_timestamp)
            elif 'mysql' in db_engine:
                self._backup_mysql_database(backup_dir, backup_timestamp)
            else:
                self.logger.warning(f"Unsupported database engine for backup: {db_engine}")
                self._backup_django_fixtures(backup_dir, backup_timestamp)
            
            self.migration_stats['backup_created'] = True
            self.logger.info(f"Database backup completed: {self.backup_path}")

    def _backup_sqlite_database(self, backup_dir: str, timestamp: str):
        """Create SQLite database backup"""
        db_path = settings.DATABASES['default']['NAME']
        backup_filename = f"database_backup_{timestamp}.sqlite3"
        self.backup_path = Path(backup_dir) / backup_filename
        
        # Copy the SQLite file
        shutil.copy2(db_path, self.backup_path)
        
        # Get backup size
        self.migration_stats['backup_size'] = self.backup_path.stat().st_size
        
        self.logger.info(f"SQLite backup created: {self.backup_path}")

    def _backup_postgresql_database(self, backup_dir: str, timestamp: str):
        """Create PostgreSQL database backup using pg_dump"""
        db_config = settings.DATABASES['default']
        backup_filename = f"database_backup_{timestamp}.sql"
        self.backup_path = Path(backup_dir) / backup_filename
        
        # Build pg_dump command
        cmd = [
            'pg_dump',
            f"--host={db_config.get('HOST', 'localhost')}",
            f"--port={db_config.get('PORT', '5432')}",
            f"--username={db_config['USER']}",
            f"--dbname={db_config['NAME']}",
            '--verbose',
            '--clean',
            '--no-owner',
            '--no-privileges',
            f"--file={self.backup_path}"
        ]
        
        # Set password environment variable
        env = os.environ.copy()
        env['PGPASSWORD'] = db_config['PASSWORD']
        
        # Execute backup
        result = subprocess.run(cmd, env=env, capture_output=True, text=True)
        
        if result.returncode != 0:
            raise CommandError(f"PostgreSQL backup failed: {result.stderr}")
        
        self.migration_stats['backup_size'] = self.backup_path.stat().st_size
        self.logger.info(f"PostgreSQL backup created: {self.backup_path}")

    def _backup_mysql_database(self, backup_dir: str, timestamp: str):
        """Create MySQL database backup using mysqldump"""
        db_config = settings.DATABASES['default']
        backup_filename = f"database_backup_{timestamp}.sql"
        self.backup_path = Path(backup_dir) / backup_filename
        
        # Build mysqldump command
        cmd = [
            'mysqldump',
            f"--host={db_config.get('HOST', 'localhost')}",
            f"--port={db_config.get('PORT', '3306')}",
            f"--user={db_config['USER']}",
            f"--password={db_config['PASSWORD']}",
            '--single-transaction',
            '--routines',
            '--triggers',
            db_config['NAME']
        ]
        
        # Execute backup
        with open(self.backup_path, 'w') as backup_file:
            result = subprocess.run(cmd, stdout=backup_file, stderr=subprocess.PIPE, text=True)
        
        if result.returncode != 0:
            raise CommandError(f"MySQL backup failed: {result.stderr}")
        
        self.migration_stats['backup_size'] = self.backup_path.stat().st_size
        self.logger.info(f"MySQL backup created: {self.backup_path}")

    def _backup_django_fixtures(self, backup_dir: str, timestamp: str):
        """Create Django fixtures backup as fallback"""
        backup_filename = f"database_backup_{timestamp}.json"
        self.backup_path = Path(backup_dir) / backup_filename
        
        # Use Django's dumpdata command
        with open(self.backup_path, 'w') as backup_file:
            call_command('dumpdata', stdout=backup_file, indent=2)
        
        self.migration_stats['backup_size'] = self.backup_path.stat().st_size
        self.logger.info(f"Django fixtures backup created: {self.backup_path}")

    def _perform_dry_run_validation(self, options: Dict[str, Any]):
        """Perform dry run validation without making database changes"""
        self.logger.info("Starting dry run validation...")
        
        try:
            # Initialize processor for validation
            processor = ProgramDataMigrationProcessor(
                json_directory_path=options['json_directory'],
                log_level=getattr(logging, options['log_level'])
            )
            
            # Validate JSON files
            json_files = list(Path(options['json_directory']).glob("*.json"))
            validation_errors = []
            
            for json_file in json_files[:10]:  # Validate first 10 files as sample
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        program_data = json.load(f)
                    
                    # Basic structure validation
                    if not processor._validate_json_structure(program_data, json_file.name):
                        validation_errors.append(f"Invalid structure: {json_file.name}")
                        
                except json.JSONDecodeError as e:
                    validation_errors.append(f"JSON decode error in {json_file.name}: {str(e)}")
                except Exception as e:
                    validation_errors.append(f"Validation error in {json_file.name}: {str(e)}")
            
            # Display validation results
            self.stdout.write(self.style.SUCCESS("Dry Run Validation Results:"))
            self.stdout.write(f"  Total JSON files: {len(json_files)}")
            self.stdout.write(f"  Sample validated: 10")
            self.stdout.write(f"  Validation errors: {len(validation_errors)}")
            
            if validation_errors:
                self.stdout.write(self.style.WARNING("Validation Errors Found:"))
                for error in validation_errors[:5]:
                    self.stdout.write(f"    - {error}")
                if len(validation_errors) > 5:
                    self.stdout.write(f"    ... and {len(validation_errors) - 5} more")
            
            self.stdout.write(self.style.SUCCESS("Dry run validation completed"))
            
        except Exception as e:
            raise CommandError(f"Dry run validation failed: {str(e)}")

    def _execute_migration(self, options: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the main migration process"""
        with MigrationTimer(self.logger, "program data migration"):
            try:
                # Initialize migration processor
                processor = ProgramDataMigrationProcessor(
                    json_directory_path=options['json_directory'],
                    log_level=getattr(logging, options['log_level'])
                )
                
                # Execute migration with progress tracking
                self.logger.info("Starting program data migration...")
                
                migration_report = processor.process_all_programs(
                    batch_size=options['batch_size']
                )
                
                # Check for critical errors
                if migration_report['errors'] and not options['continue_on_error']:
                    error_count = len(migration_report['errors'])
                    raise MigrationError(
                        f"Migration completed with {error_count} errors. "
                        "Use --continue-on-error to ignore errors."
                    )
                
                return migration_report
                
            except Exception as e:
                self.logger.error(f"Migration execution failed: {str(e)}")
                raise

    def _perform_rollback(self):
        """Perform database rollback using the backup"""
        if not self.backup_path or not self.backup_path.exists():
            self.logger.error("Cannot perform rollback: backup file not found")
            return
        
        with MigrationTimer(self.logger, "database rollback"):
            try:
                db_engine = settings.DATABASES['default']['ENGINE']
                
                if 'sqlite' in db_engine:
                    self._rollback_sqlite_database()
                elif 'postgresql' in db_engine:
                    self._rollback_postgresql_database()
                elif 'mysql' in db_engine:
                    self._rollback_mysql_database()
                else:
                    self._rollback_django_fixtures()
                
                self.migration_stats['rollback_performed'] = True
                self.logger.info("Database rollback completed successfully")
                
            except Exception as e:
                self.logger.error(f"Rollback failed: {str(e)}")

    def _rollback_sqlite_database(self):
        """Rollback SQLite database"""
        db_path = settings.DATABASES['default']['NAME']
        
        # Close all connections
        connection.close()
        
        # Replace current database with backup
        shutil.copy2(self.backup_path, db_path)
        
        self.logger.info("SQLite database rolled back successfully")

    def _rollback_postgresql_database(self):
        """Rollback PostgreSQL database"""
        db_config = settings.DATABASES['default']
        
        # Build psql command to restore
        cmd = [
            'psql',
            f"--host={db_config.get('HOST', 'localhost')}",
            f"--port={db_config.get('PORT', '5432')}",
            f"--username={db_config['USER']}",
            f"--dbname={db_config['NAME']}",
            f"--file={self.backup_path}"
        ]
        
        # Set password environment variable
        env = os.environ.copy()
        env['PGPASSWORD'] = db_config['PASSWORD']
        
        # Execute restore
        result = subprocess.run(cmd, env=env, capture_output=True, text=True)
        
        if result.returncode != 0:
            raise Exception(f"PostgreSQL rollback failed: {result.stderr}")

    def _rollback_mysql_database(self):
        """Rollback MySQL database"""
        db_config = settings.DATABASES['default']
        
        # Build mysql command to restore
        cmd = [
            'mysql',
            f"--host={db_config.get('HOST', 'localhost')}",
            f"--port={db_config.get('PORT', '3306')}",
            f"--user={db_config['USER']}",
            f"--password={db_config['PASSWORD']}",
            db_config['NAME']
        ]
        
        # Execute restore
        with open(self.backup_path, 'r') as backup_file:
            result = subprocess.run(cmd, stdin=backup_file, capture_output=True, text=True)
        
        if result.returncode != 0:
            raise Exception(f"MySQL rollback failed: {result.stderr}")

    def _rollback_django_fixtures(self):
        """Rollback using Django fixtures"""
        # Flush database and load fixtures
        call_command('flush', '--noinput')
        call_command('loaddata', str(self.backup_path))

    def _generate_reports(self, migration_report: Dict[str, Any], options: Dict[str, Any]):
        """Generate comprehensive migration reports"""
        # Add migration statistics to report
        migration_report['migration_stats'] = self.migration_stats
        migration_report['configuration'] = {
            'json_directory': options['json_directory'],
            'batch_size': options['batch_size'],
            'log_level': options['log_level'],
            'backup_created': self.migration_stats['backup_created'],
            'backup_path': str(self.backup_path) if self.backup_path else None
        }
        
        # Save detailed report if requested
        if options.get('report_file'):
            self._save_detailed_report(migration_report, options['report_file'])
        
        # Create error report if there were issues
        if self.progress_handler and (migration_report.get('errors') or migration_report.get('warnings')):
            error_report_path = f"migration_issues_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            create_error_report(self.progress_handler, error_report_path)
            self.logger.info(f"Issues report saved to: {error_report_path}")

    def _save_detailed_report(self, report: Dict[str, Any], report_file: str):
        """Save detailed migration report to JSON file"""
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False, default=str)
            
            self.logger.info(f"Detailed migration report saved to: {report_file}")
            
        except Exception as e:
            self.logger.error(f"Failed to save detailed report: {str(e)}")

    def _display_final_results(self, migration_report: Dict[str, Any]):
        """Display comprehensive final results"""
        stats = migration_report.get('statistics', {})
        
        self.stdout.write("\n" + "="*60)
        self.stdout.write(self.style.SUCCESS("MIGRATION COMPLETED"))
        self.stdout.write("="*60)
        
        # Migration statistics
        self.stdout.write("Migration Statistics:")
        self.stdout.write(f"  Total files processed: {stats.get('total_files', 0)}")
        self.stdout.write(f"  Successfully processed: {stats.get('processed_successfully', 0)}")
        self.stdout.write(f"  Failed processing: {stats.get('failed_processing', 0)}")
        self.stdout.write(f"  Programs created: {stats.get('programs_created', 0)}")
        self.stdout.write(f"  Programs updated: {stats.get('programs_updated', 0)}")
        self.stdout.write(f"  Schools created: {stats.get('schools_created', 0)}")
        self.stdout.write(f"  Schools updated: {stats.get('schools_updated', 0)}")
        
        # Success rate
        success_rate = migration_report.get('success_rate', 0)
        if success_rate >= 90:
            style = self.style.SUCCESS
        elif success_rate >= 70:
            style = self.style.WARNING
        else:
            style = self.style.ERROR
        
        self.stdout.write(style(f"  Success rate: {success_rate:.2f}%"))
        
        # Backup information
        if self.migration_stats['backup_created']:
            backup_size_mb = self.migration_stats['backup_size'] / (1024 * 1024)
            self.stdout.write(f"  Backup created: {self.backup_path}")
            self.stdout.write(f"  Backup size: {backup_size_mb:.2f} MB")
        
        # Duration
        duration = self.migration_stats['total_duration']
        self.stdout.write(f"  Total duration: {duration:.2f} seconds")
        
        # Error summary
        if migration_report.get('errors'):
            error_count = len(migration_report['errors'])
            self.stdout.write(f"\n{self.style.ERROR(f'ERRORS: {error_count} errors encountered')}")
            
            # Show first few errors
            for error in migration_report['errors'][:3]:
                file_name = error.get('file', 'Unknown')
                error_msg = error.get('error', 'Unknown error')
                self.stdout.write(f"  - {file_name}: {error_msg}")
            
            if error_count > 3:
                self.stdout.write(f"  ... and {error_count - 3} more errors")
        
        # Warning summary
        if migration_report.get('warnings'):
            warning_count = len(migration_report['warnings'])
            self.stdout.write(f"\n{self.style.WARNING(f'WARNINGS: {warning_count} warnings')}")

    def _log_final_statistics(self):
        """Log final migration statistics"""
        self.logger.info("="*60)
        self.logger.info("FINAL MIGRATION STATISTICS")
        self.logger.info("="*60)
        
        for key, value in self.migration_stats.items():
            self.logger.info(f"{key}: {value}")
        
        if self.progress_handler:
            progress_stats = self.progress_handler.get_stats()
            self.logger.info("Logging Statistics:")
            for key, value in progress_stats.items():
                self.logger.info(f"  {key}: {value}")