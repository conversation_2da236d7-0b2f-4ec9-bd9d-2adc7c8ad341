# Generated by Django 5.0.1 on 2025-04-07 03:21

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0002_userprofile_behavioral_science_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='userprofile',
            name='gre_analytical_writing_percentile',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='gre_analytical_writing_score',
            field=models.DecimalField(blank=True, decimal_places=1, max_digits=2, null=True),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='gre_quantitative_percentile',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='gre_quantitative_score',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='gre_verbal_percentile',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='gre_verbal_score',
            field=models.IntegerField(blank=True, null=True),
        ),
    ]
