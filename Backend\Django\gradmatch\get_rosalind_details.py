#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gradmatch.settings')
django.setup()

from gradapp.models import *

# Find Rosalind Franklin University
program = Program.objects.filter(name__icontains='<PERSON><PERSON>').first()

if program:
    print('=== ROSALIND FRANKLIN UNIVERSITY PA PROGRAM ===')
    print(f'Program Name: {program.name}')
    print(f'School: {program.school.name}')
    print(f'Location: {program.school.location}')
    print(f'Website: {program.school.website}')
    print()
    
    print('=== BASIC PROGRAM INFO ===')
    print(f'Program URL: {program.url}')
    print(f'Description: {program.description[:200]}...' if program.description else 'N/A')
    print(f'Application Deadline: {program.application_deadline}')
    print(f'Program Start Date: {program.program_start_date}')
    print(f'Class Size: {program.class_size}')
    print(f'Average GPA: {program.average_gpa}')
    print()
    
    print('=== ENHANCED PROGRAM DATA ===')
    print(f'Phone: {program.phone}')
    print(f'Address: {program.address}')
    print(f'City/State: {program.city_state}')
    print(f'Email: {program.email}')
    print(f'CASPA Member: {program.caspa_member}')
    print(f'Program Length: {program.program_length}')
    print(f'Start Month: {program.start_month}')
    print(f'Part Time Option: {program.part_time_option}')
    print(f'Distance Learning: {program.distance_learning}')
    print(f'On Campus Housing: {program.on_campus_housing}')
    print()
    
    print('=== CASPA REQUIREMENTS ===')
    caspa_req = program.caspa_requirements.first()
    if caspa_req:
        print(f'CASPA Member: {caspa_req.caspa_member}')
        print(f'Upcoming CASPA Cycle: {caspa_req.upcoming_caspa_cycle}')
        print(f'Application Deadline: {caspa_req.application_deadline}')
        print(f'Deadline Requirement: {caspa_req.deadline_requirement}')
        print(f'Supplemental Application: {caspa_req.supplemental_application}')
        print(f'Supplemental Deadline: {caspa_req.supplemental_deadline}')
        print(f'Supplemental Fee: {caspa_req.supplemental_application_fee}')
    else:
        print('No CASPA requirements data available')
    print()
    
    print('=== TUITION INFORMATION ===')
    tuition = program.tuition_information.first()
    if tuition:
        print(f'Separate Tuition Rates: {tuition.separate_tuition_rates}')
        print(f'Tuition: {tuition.tuition}')
        print(f'Resident Tuition: {tuition.resident_tuition}')
        print(f'Non-Resident Tuition: {tuition.non_resident_tuition}')
        print(f'Seat Deposit Required: {tuition.seat_deposit}')
        print(f'Seat Deposit Cost: {tuition.seat_deposit_cost}')
        print(f'Refundable Seat Deposit: {tuition.refundable_seat_deposit}')
    else:
        print('No tuition information available')
    print()
    
    print('=== PREREQUISITE COURSES ===')
    prereqs = program.prerequisite_courses.all()
    if prereqs:
        for prereq in prereqs:
            print(f'- {prereq.course_name}: {prereq.credits} credits')
            print(f'  Lab Required: {prereq.lab_required}')
            print(f'  Time Limit: {prereq.time_limit}')
    else:
        print('No prerequisite courses data available')
    print()
    
    print('=== GPA REQUIREMENTS ===')
    gpa_req = program.gpa_requirements.first()
    if gpa_req:
        print(f'Minimum Overall GPA: {gpa_req.minimum_overall}')
        print(f'Minimum Prerequisite GPA: {gpa_req.minimum_prereq}')
        print(f'Minimum Science GPA: {gpa_req.minimum_science}')
    else:
        print('No GPA requirements data available')
    print()
    
    print('=== GRE REQUIREMENTS ===')
    gre_req = program.gre_requirements.first()
    if gre_req:
        print(f'GRE Required: {gre_req.required}')
        print(f'Verbal Score: {gre_req.verbal_score}')
        print(f'Quantitative Score: {gre_req.quantitative_score}')
        print(f'Analytical Score: {gre_req.analytical_score}')
    else:
        print('No GRE requirements data available')
    print()
    
else:
    print('Rosalind Franklin University PA program not found')