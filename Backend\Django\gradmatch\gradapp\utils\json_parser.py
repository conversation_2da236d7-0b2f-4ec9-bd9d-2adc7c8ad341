"""
JSON Data Parser and Validator for PA Program Migration

This utility class handles reading, parsing, and validating JSON files from the
"D:\\ScrapperPAEAReal" directory for the database schema update migration.
"""

import json
import os
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime


class JSONParserError(Exception):
    """Custom exception for JSON parsing errors"""
    pass


class PAJSONParser:
    """
    Utility class to read and parse JSON files from PA program data directory.
    
    Provides validation logic for required fields and data types, with error
    handling for malformed or incomplete JSON files.
    """
    
    def __init__(self, data_directory: str = r"D:\ScrapperPAEAReal"):
        """
        Initialize the JSON parser with the data directory path.
        
        Args:
            data_directory: Path to directory containing JSON files
        """
        self.data_directory = Path(data_directory)
        self.logger = self._setup_logger()
        self.processed_files = []
        self.failed_files = []
        
    def _setup_logger(self) -> logging.Logger:
        """Set up logging for the parser"""
        logger = logging.getLogger('pa_json_parser')
        logger.setLevel(logging.INFO)
        
        # Create console handler if not already exists
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def discover_json_files(self) -> List[Path]:
        """
        Discover all JSON files in the data directory.
        
        Returns:
            List of Path objects for JSON files
            
        Raises:
            JSONParserError: If directory doesn't exist or can't be accessed
        """
        try:
            if not self.data_directory.exists():
                raise JSONParserError(f"Directory does not exist: {self.data_directory}")
            
            if not self.data_directory.is_dir():
                raise JSONParserError(f"Path is not a directory: {self.data_directory}")
            
            json_files = list(self.data_directory.glob("*.json"))
            self.logger.info(f"Found {len(json_files)} JSON files in {self.data_directory}")
            
            return json_files
            
        except PermissionError:
            raise JSONParserError(f"Permission denied accessing directory: {self.data_directory}")
        except Exception as e:
            raise JSONParserError(f"Error accessing directory {self.data_directory}: {str(e)}")
    
    def read_json_file(self, file_path: Path) -> Dict[str, Any]:
        """
        Read and parse a single JSON file.
        
        Args:
            file_path: Path to the JSON file
            
        Returns:
            Parsed JSON data as dictionary
            
        Raises:
            JSONParserError: If file can't be read or parsed
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                data = json.load(file)
                self.logger.debug(f"Successfully read JSON file: {file_path.name}")
                return data
                
        except FileNotFoundError:
            raise JSONParserError(f"File not found: {file_path}")
        except json.JSONDecodeError as e:
            raise JSONParserError(f"Invalid JSON in file {file_path}: {str(e)}")
        except UnicodeDecodeError as e:
            raise JSONParserError(f"Encoding error in file {file_path}: {str(e)}")
        except PermissionError:
            raise JSONParserError(f"Permission denied reading file: {file_path}")
        except Exception as e:
            raise JSONParserError(f"Unexpected error reading file {file_path}: {str(e)}")
    
    def validate_required_fields(self, data: Dict[str, Any], file_path: Path) -> List[str]:
        """
        Validate that required fields are present in the JSON data.
        
        Args:
            data: JSON data dictionary
            file_path: Path to the file being validated
            
        Returns:
            List of validation error messages
        """
        errors = []
        
        # Define required fields
        required_fields = [
            'program_name',
            'program_data'
        ]
        
        # Optional but important fields that should be checked
        important_fields = [
            'program_url',
            'mission_statement',
            'caspa_member',
            'upcoming_caspa_cycle'
        ]
        
        # Check required fields
        for field in required_fields:
            if field not in data:
                errors.append(f"Missing required field: {field}")
            elif not data[field] or (isinstance(data[field], str) and not data[field].strip()):
                errors.append(f"Required field is empty: {field}")
        
        # Check important fields (warnings, not errors)
        for field in important_fields:
            if field not in data:
                self.logger.warning(f"Missing important field '{field}' in {file_path.name}")
        
        return errors
    
    def validate_data_types(self, data: Dict[str, Any], file_path: Path) -> List[str]:
        """
        Validate data types for known fields.
        
        Args:
            data: JSON data dictionary
            file_path: Path to the file being validated
            
        Returns:
            List of validation error messages
        """
        errors = []
        
        # Define expected data types
        type_validations = {
            'name': str,
            'school': str,
            'program_url': str,
            'mission_statement': str,
            'caspa_member': bool,
            'upcoming_caspa_cycle': bool,
            'curriculum_focus': list,
            'minimum_overall_gpa_required': (str, float, int),
            'estimated_incoming_class_size': (str, int),
            'application_deadline': str
        }
        
        for field, expected_type in type_validations.items():
            if field in data and data[field] is not None:
                if not isinstance(data[field], expected_type):
                    errors.append(f"Field '{field}' should be {expected_type.__name__}, got {type(data[field]).__name__}")
        
        return errors
    
    def validate_business_logic(self, data: Dict[str, Any], file_path: Path) -> List[str]:
        """
        Validate business logic rules for the data.
        
        Args:
            data: JSON data dictionary
            file_path: Path to the file being validated
            
        Returns:
            List of validation error messages
        """
        errors = []
        
        # Validate GPA range
        if 'minimum_overall_gpa_required' in data and data['minimum_overall_gpa_required']:
            try:
                gpa = float(data['minimum_overall_gpa_required'])
                if gpa < 0.0 or gpa > 4.0:
                    errors.append(f"GPA should be between 0.0 and 4.0: {gpa}")
            except (ValueError, TypeError):
                errors.append(f"GPA should be numeric: {data['minimum_overall_gpa_required']}")
        
        # Validate class size
        if 'estimated_incoming_class_size' in data and data['estimated_incoming_class_size']:
            try:
                class_size = int(data['estimated_incoming_class_size'])
                if class_size <= 0:
                    errors.append(f"Class size should be positive: {class_size}")
            except (ValueError, TypeError):
                errors.append(f"Class size should be numeric: {data['estimated_incoming_class_size']}")
        
        # Validate tuition format
        if 'tuition' in data and data['tuition'] and data['tuition'] != "No information provided":
            tuition = data['tuition'].replace('$', '').replace(',', '')
            try:
                tuition_value = float(tuition)
                if tuition_value < 0:
                    errors.append(f"Tuition should be non-negative: {tuition_value}")
            except (ValueError, TypeError):
                errors.append(f"Tuition should be numeric: {data['tuition']}")
        
        # Validate URL format (basic check)
        if 'program_url' in data and data['program_url']:
            url = data['program_url']
            if not (url.startswith('http://') or url.startswith('https://')):
                errors.append(f"Program URL should start with http:// or https://: {url}")
        
        return errors
    
    def validate_json_data(self, data: Dict[str, Any], file_path: Path) -> Tuple[bool, List[str]]:
        """
        Comprehensive validation of JSON data.
        
        Args:
            data: JSON data dictionary
            file_path: Path to the file being validated
            
        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        all_errors = []
        
        # Run all validation checks
        all_errors.extend(self.validate_required_fields(data, file_path))
        all_errors.extend(self.validate_data_types(data, file_path))
        all_errors.extend(self.validate_business_logic(data, file_path))
        
        is_valid = len(all_errors) == 0
        
        if not is_valid:
            self.logger.warning(f"Validation failed for {file_path.name}: {len(all_errors)} errors")
        
        return is_valid, all_errors
    
    def process_single_file(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """
        Process a single JSON file with error handling.
        
        Args:
            file_path: Path to the JSON file
            
        Returns:
            Parsed and validated JSON data, or None if processing failed
        """
        try:
            # Read the JSON file
            data = self.read_json_file(file_path)
            
            # Validate the data
            is_valid, errors = self.validate_json_data(data, file_path)
            
            if is_valid:
                self.processed_files.append(file_path)
                self.logger.debug(f"Successfully processed: {file_path.name}")
                return data
            else:
                self.failed_files.append({
                    'file': file_path,
                    'errors': errors,
                    'timestamp': datetime.now()
                })
                self.logger.error(f"Validation failed for {file_path.name}: {errors}")
                return None
                
        except JSONParserError as e:
            self.failed_files.append({
                'file': file_path,
                'errors': [str(e)],
                'timestamp': datetime.now()
            })
            self.logger.error(f"Failed to process {file_path.name}: {str(e)}")
            return None
        except Exception as e:
            self.failed_files.append({
                'file': file_path,
                'errors': [f"Unexpected error: {str(e)}"],
                'timestamp': datetime.now()
            })
            self.logger.error(f"Unexpected error processing {file_path.name}: {str(e)}")
            return None
    
    def process_all_files(self) -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
        """
        Process all JSON files in the data directory.
        
        Returns:
            Tuple of (valid_data_list, processing_summary)
        """
        try:
            json_files = self.discover_json_files()
        except JSONParserError as e:
            self.logger.error(f"Failed to discover files: {str(e)}")
            return [], {'error': str(e)}
        
        valid_data = []
        
        self.logger.info(f"Processing {len(json_files)} JSON files...")
        
        for file_path in json_files:
            data = self.process_single_file(file_path)
            if data is not None:
                valid_data.append(data)
        
        # Create processing summary
        summary = {
            'total_files': len(json_files),
            'processed_successfully': len(self.processed_files),
            'failed_processing': len(self.failed_files),
            'success_rate': len(self.processed_files) / len(json_files) * 100 if json_files else 0,
            'failed_files': self.failed_files
        }
        
        self.logger.info(f"Processing complete: {summary['processed_successfully']}/{summary['total_files']} files successful")
        
        return valid_data, summary
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """
        Get current processing statistics.
        
        Returns:
            Dictionary with processing statistics
        """
        return {
            'processed_files_count': len(self.processed_files),
            'failed_files_count': len(self.failed_files),
            'processed_files': [str(f) for f in self.processed_files],
            'failed_files': self.failed_files
        }