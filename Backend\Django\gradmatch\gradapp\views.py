import os
import logging
from django.http import JsonResponse, HttpResponse
from django.conf import settings
from django.views.decorators.csrf import csrf_exempt
from rest_framework import status, views
from rest_framework.response import Response
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from django.contrib.auth import get_user_model # Removed authenticate
import json
import uuid # Add uuid import
from langchain_core.documents import Document
from django.core.exceptions import ValidationError
from .services.s3_service import S3Service
from .models import School, TranscriptRecord, Program, UserTranscript, PrerequisiteSelection, ProgramMatchingResult
# Removed UserSerializer, RegisterSerializer, LoginSerializer
# from .serializers import UserSerializer, RegisterSerializer, LoginSerializer # Original line commented out
from .ProcessTranscript import process_transcript_button
from .langchain_service import process_input_with_langchain, vectorstore
from .ProcessTranscript import process_transcript_button, ValidationStatus
from .program_matching import find_matching_programs
from django.db import transaction
import hashlib
from django.utils import timezone

# Get the User model
User = get_user_model()

# Initialize logging
logger = logging.getLogger(__name__)

def get_stored_analysis(session_id, user_id):
    """
    Retrieve the stored analysis for a given session ID from the vector store.
    """
    try:
        # Search for the final analysis document
        results = vectorstore.similarity_search(
            "final transcript analysis",
            filter={
                "session_id": session_id,
                "is_final": True,
                "type": "transcript_analysis",
                "user_id": user_id
            },
            k=1,
            namespace="transcript_analyses"
        )
        
        if results and len(results) > 0:
            # Parse the stored JSON content
            return json.loads(results[0].page_content)
        return None
    except Exception as e:
        logger.error(f"Error retrieving stored analysis: {str(e)}")
        return None

class TranscriptValidationError(Exception):
    pass

def list_all_schools(request):
    schools = School.objects.all().values('name', 'location')
    return JsonResponse(list(schools), safe=False)

def home(request):
    return HttpResponse('Welcome to the GradMatch homepage!')


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def process_transcript(request):
    """API endpoint to process transcript"""
    try:
        # Generate a session ID
        session_id = str(uuid.uuid4())
        user_id = request.user.id
        # Extract transcript_id from request
        if not request.data.get('transcript_id'):
            return Response({"error": "No transcript_id provided"}, status=400)
        transcript_id = request.data.get('transcript_id')
        
        # Call ProcessTranscript.py process_transcript_button
        try:
            result = process_transcript_button(
                session_id=session_id or request.data.get('sessionId'), 
                user_id=user_id,
                transcript_id=transcript_id
            )
            
            # Handle error in result
            if result.get('status') == 'error':
                return Response({"error": result.get('message')}, status=500)
                
            return Response(result)
        except Exception as e:
            logger.error(f"Error in transcript processing: {str(e)}")
            return Response({"error": str(e)}, status=500)
    except Exception as e:
        logger.error(f"Error in process_transcript view: {str(e)}")
        return Response({"error": str(e)}, status=500)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def validate_transcript(request):
    try:
        logger.info("Starting transcript validation...")
        data = request.data
        structured_data = data.get('editedData', [])
        academic_summary = data.get('studentInfo', {})

        # Use the GPAs calculated in the frontend
        cumulative_gpa = academic_summary.get('cumulative_gpa', 0.0)
        science_gpa = academic_summary.get('science_gpa', 0.0)
        science_credits = academic_summary.get('science_credits', 0)
        institution = academic_summary.get('institution')

        # Get student name with fallback to user's name or username
        student_name = academic_summary.get('student_name')
        if not student_name:
            # Fallback to user's full name or username
            student_name = request.user.get_full_name() or request.user.username
            logger.info(f"Using fallback student name: {student_name}")
            # Also update the academic summary with this name
            academic_summary['student_name'] = student_name

        logger.info(f"""
        Validating transcript for:
        Institution: {institution}
        Student: {student_name}
        Cumulative GPA: {cumulative_gpa}
        Science GPA: {science_gpa}
        Science Credits: {science_credits}
        """)

        if not institution:
            logger.error("Institution is missing from validation data")
            return Response({
                'status': 'error',
                'message': 'Institution is required'
            }, status=400)

        # Use transaction to ensure database operations are atomic
        with transaction.atomic():
            # Delete any pending records for this institution
            deleted_pending = TranscriptRecord.objects.filter(
                user=request.user,
                institution=institution,
                validation_status='pending'
            ).delete()
            logger.info(f"Deleted {deleted_pending[0]} pending records for {institution}")

            # Delete any previous approved records for this institution
            deleted_approved = TranscriptRecord.objects.filter(
                user=request.user,
                institution=institution,
                validation_status='approved'
            ).delete()
            logger.info(f"Deleted {deleted_approved[0]} previous approved records for {institution}")

            # Process each course in the structured data
            for semester in structured_data:
                if 'courses' in semester and isinstance(semester['courses'], list):
                    for course in semester['courses']:
                        # Add unique ID if not already present
                        if 'id' not in course:
                            course['id'] = uuid.uuid4().hex
                        
                        # Process has_lab flag
                        course_name = course.get('name', '').lower()
                        course_code = course.get('code', '').lower()
                        
                        # Auto-detect lab courses if has_lab is not explicitly set
                        if 'has_lab' not in course:
                            # Get course name and code with proper null handling
                            course_name_lower = course_name.lower() if course_name else ''
                            course_code_lower = course_code.lower() if course_code else ''
                            
                            # Debug logging to see what we're checking
                            logger.info(f"Checking lab for course: {course_code_lower} - {course_name_lower}")
                            
                            # Simple explicit check: look for the word "lab" in the name or code
                            has_lab = False
                            
                            if 'lab' in course_name_lower or 'lab' in course_code_lower:
                                has_lab = True
                                logger.info(f"✓ MATCH: Found lab in course {course_code_lower} - {course_name_lower}")
                            else:
                                logger.info(f"✗ NO MATCH: No lab found in course {course_code_lower} - {course_name_lower}")
                            
                            course['has_lab'] = has_lab

            # Create new validated transcript record
            transcript = TranscriptRecord.objects.create(
                user=request.user,
                institution=institution,
                student_name=student_name,  # Use our variable with fallback
                calculated_gpa=cumulative_gpa,
                science_gpa=science_gpa,  # Add science_gpa to the record
                validation_status='approved',
                transcript_data={
                    'semesters': structured_data, # Save data with course IDs and has_lab flags
                    'academic_summary': {
                        **academic_summary,
                        'cumulative_gpa': cumulative_gpa,
                        'science_gpa': science_gpa,
                        'science_credits': science_credits
                    }
                }
            )

            logger.info(f"""
            Created new validated transcript record:
            ID: {transcript.id}
            Institution: {institution}
            Student: {student_name}
            Status: approved
            Semesters: {len(structured_data)}
            """)

            # Get count of remaining validated transcripts for the user
            remaining_count = TranscriptRecord.objects.filter(
                user=request.user,
                validation_status='approved'
            ).count()
            logger.info(f"Total validated transcripts for user after cleanup: {remaining_count}")

        return Response({
            'status': 'success',
            'message': 'Transcript validated successfully',
            'data': {
                'semesters': structured_data,
                'academic_summary': {
                    **academic_summary,
                    'cumulative_gpa': cumulative_gpa,
                    'science_gpa': science_gpa,
                    'science_credits': science_credits
                }
            }
        })

    except Exception as e:
        logger.error(f"Error in validate_transcript: {str(e)}")
        return Response({
            'status': 'error',
            'message': str(e)
        }, status=500)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_transcripts(request):
    """Get all transcripts for the current user"""
    transcripts = TranscriptRecord.objects.filter(user=request.user)
    
    # Add logging to see the transcript data
    for t in transcripts:
        logger.debug(f"Transcript ID {t.id} data structure:")
        logger.debug(f"Raw transcript_data: {json.dumps(t.transcript_data, indent=2)}")
    
    return Response({
        'transcripts': [{
            'id': t.id,
            'institution': t.institution,
            'student_name': t.student_name,
            'calculated_gpa': float(t.calculated_gpa),
            'validation_status': t.validation_status,
            'updated_at': t.updated_at,
            'transcript_data': t.transcript_data
        } for t in transcripts]
    })


# Removed register_user and login_user functions previously here

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def chat(request):
    try:
        logger.debug(f"Received chat request data: {request.data}")
        message = request.data.get('message')
        session_id = request.data.get('session_id')
        
        if not message or not session_id:
            return Response({
                'error': 'Message and session_id are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Check for validated transcript
        stored_analysis = get_stored_analysis(session_id, request.user.id)
        if not stored_analysis:
            return Response({
                'response': "Please complete the transcript validation process before asking questions. Go to the dashboard and click 'Process Transcript' to begin.",
                'sources': {
                    'transcript': [],
                    'programs': {'unr': [], 'rfums': []}
                }
            }, status=status.HTTP_200_OK)  # Return 200 with guidance message

        # Get user profile
        user_profile = {
            'id': request.user.id,
            'email': request.user.email,
            'user_id': request.user.id  # Add user_id explicitly
        }

        # Use langchain service to process the message
        result = process_input_with_langchain(
            user_input=message,
            user_profile=user_profile,
            session_id=session_id
        )

        return Response({
            'response': result['answer'],
            'sources': {
                'transcript': result['sources']['transcript'],
                'programs': {
                    'unr': result['sources']['programs']['unr'],
                    'rfums': result['sources']['programs']['rfums']
                }
            }
        })

    except Exception as e:
        logger.error(f"Error in chat endpoint: {str(e)}")
        return Response({
            'error': 'An error occurred while processing your message'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def match_programs(request):
    """
    Match the user's transcript against PA programs
    """
    try:
        results = find_matching_programs(request.user.id)
        
        if 'error' in results:
            return Response(
                results,
                status=status.HTTP_400_BAD_REQUEST
            )
            
        return Response(results)
        
    except Exception as e:
        logger.error(f"Error in match_programs view: {str(e)}")
        return Response(
            {'error': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

from rest_framework.permissions import IsAuthenticated # Ensure this import exists (it does on line 9)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_program_list(request):
    """Get all programs with their comprehensive enhanced data structure"""
    try:
        programs = Program.objects.select_related('school').prefetch_related(
            'prerequisite_courses',
            'gpa_requirements',
            'healthcare_experience',
            'patient_care_experience',
            'shadowing_requirements',
            'gre_requirements',
            'caspa_requirements',
            'tuition_information',
            'enhanced_class_profiles',
            'program_curriculum',
            'matriculant_demographics',
            'attrition_data',
            'pance_pass_rates',
            'other_requirements',
            'recommendation_requirements'
        ).all()
        
        program_data = []
        for program in programs:
            # Extract school location data
            school_city = program.school.city or None
            school_state = program.school.state or None
            
            # Fallback location parsing if city/state not populated
            if not school_city or not school_state:
                if program.city_state:
                    location_parts = program.city_state.split(', ')
                    if len(location_parts) >= 2:
                        school_city = school_city or location_parts[0].strip()
                        school_state = school_state or location_parts[1].strip()[:2]
                elif program.school.location:
                    location_parts = program.school.location.split(', ')
                    if len(location_parts) >= 2:
                        school_city = school_city or location_parts[-2].strip()
                        school_state = school_state or location_parts[-1].strip()[:2]
            
            # Build school object
            school_data = {
                'name': program.school.name,
                'city': school_city,
                'state': school_state,
                'location': program.school.location,
                'website': program.school.website,
                'mission_statement': program.school.mission_statement
            }
            
            # === GENERAL INFORMATION ===
            general_information = {
                'program_name': program.name if program.name else f"{program.school.name} PA Program",
                'phone': program.phone,
                'address': program.address,
                'mission_statement': program.mission_statement,
                'unique_program_features': program.unique_features,
                'curriculum_focus': program.curriculum_focus_json if program.curriculum_focus_json else program.curriculum_focus,
                'credentials_offered': program.credentials_offered_json if program.credentials_offered_json else program.credentials_offered,
                'bridge_or_dual_degree': program.bridge_dual_degree,
                'doctorate_degree_offered': program.doctorate_offered,
                'type_of_masters_degree': program.type_of_masters_degree if program.type_of_masters_degree else program.masters_degree_type,
                'program_length': program.program_length_months if program.program_length_months else program.program_length,
                'start_month': program.start_month,
                'estimated_incoming_class_size': program.estimated_class_size,
                'part_time_option': program.part_time_option,
                'distance_learning': program.distance_learning,
                'on_campus_housing': program.on_campus_housing,
                'required_onsite_interview': program.required_onsite_interview,
                'types_of_interviews': program.types_of_interviews if program.types_of_interviews else [],
                'admission_type': program.admission_type
            }
            
            # === CLASS PROFILE ===
            class_profile = {}
            enhanced_profile = program.enhanced_class_profiles.first()
            if enhanced_profile:
                class_profile = {
                    'year': enhanced_profile.year,
                    'total_applications': enhanced_profile.total_applications,
                    'total_interviewed': enhanced_profile.total_interviewed,
                    'total_matriculants': enhanced_profile.total_matriculants,
                    'average_overall_gpa': enhanced_profile.average_overall_gpa,
                    'average_science_gpa': enhanced_profile.average_science_gpa,
                    'mean_gre_percentiles': {
                        'verbal': enhanced_profile.gre_verbal_percentile,
                        'quantitative': enhanced_profile.gre_quantitative_percentile,
                        'analytical': enhanced_profile.gre_analytical_percentile
                    },
                    'average_healthcare_hours': enhanced_profile.average_healthcare_hours
                }
            
            # === ATTRITION ===
            attrition = {}
            attrition_data = program.attrition_data.first()
            if attrition_data:
                attrition = {
                    'class_year': attrition_data.class_year,
                    'maximum_entering_class_size': attrition_data.max_entering_class_size,
                    'entering_class_size': attrition_data.entering_class_size,
                    'graduates': attrition_data.graduates,
                    'attrition_rate': attrition_data.attrition_rate,
                    'graduation_rate': attrition_data.graduation_rate
                }
            
            # === PANCE PASS RATES ===
            pance_pass_rates = {}
            pance_data = program.pance_pass_rates.first()
            if pance_data:
                pance_pass_rates = {
                    'year': pance_data.year,
                    'group': pance_data.group,
                    'program_pass_rate': pance_data.program_pass_rate,
                    'national_pass_rate': pance_data.national_pass_rate,
                    'candidates_took_pance': pance_data.candidates_took_pance
                }

            # === PROGRAM CURRICULUM ===
            curriculum = {}
            program_curriculum = program.program_curriculum.first()
            if program_curriculum:
                curriculum = {
                    'didactic_phase': {
                        'term_type': program_curriculum.didactic_terms,
                        'terms': program_curriculum.didactic_courses
                    },
                    'clinical_phase': {
                        'required_rotations': program_curriculum.clinical_rotations
                    }
                }
            
            # === CASPA DEADLINES ===
            caspa_deadlines = {}
            caspa_req = program.caspa_requirements.first()
            if caspa_req:
                caspa_deadlines = {
                    'caspa_member': 'Yes' if caspa_req.caspa_member else 'No',
                    'upcoming_caspa_cycle': 'Yes' if caspa_req.upcoming_caspa_cycle else 'No',
                    'application_deadline': caspa_req.application_deadline.strftime('%m/%d/%Y') if caspa_req.application_deadline else None,
                    'deadline_requirement': caspa_req.deadline_requirement,
                    'supplemental_application': caspa_req.supplemental_application,
                    'supplemental_deadline': caspa_req.supplemental_deadline.strftime('%m/%d/%Y') if caspa_req.supplemental_deadline else None,
                    'supplemental_application_fee': caspa_req.supplemental_application_fee,
                    'supplemental_application_fee_waiver': caspa_req.supplemental_application_fee_waiver
                }
            
            # === TUITION DEPOSITS ===
            tuition_deposits = {}
            tuition_info = program.tuition_information.first()
            if tuition_info:
                tuition_deposits = {
                    'separate_tuition_rates': 'Yes' if tuition_info.separate_tuition_rates else 'No',
                    'tuition': tuition_info.tuition,
                    'seat_deposit': 'Yes' if tuition_info.seat_deposit else 'No',
                    'seat_deposit_cost': tuition_info.seat_deposit_cost,
                    'refundable_seat_deposit': 'Yes' if tuition_info.refundable_seat_deposit else 'No'
                }
            
            # === MATRICULANTS ===
            matriculants = {}
            demographics = program.matriculant_demographics.first()
            other_req = program.other_requirements.first()
            
            matriculants_data = {}
            if other_req:
                matriculants_data.update({
                    'out_of_state_students_accepted': 'Yes' if other_req.out_of_state_accepted else 'No',
                    'transfer_students_accepted': 'Yes' if other_req.transfer_accepted else 'No',
                    'daca_status_applicants_considered': 'Yes' if other_req.daca_accepted else 'No',
                    'international_applicants_accepted': 'Yes' if other_req.international_accepted else 'No',
                    'support_for_veterans': other_req.veteran_support
                })
            
            if demographics:
                matriculants_data.update({
                    'gender': {
                        'female': demographics.female_count,
                        'male': demographics.male_count,
                        'non_binary': demographics.non_binary_count,
                        'gender_unknown': demographics.gender_unknown_count
                    },
                    'ethnicity': {
                        'american_indian_or_alaskan_native': demographics.american_indian_count,
                        'asian': demographics.asian_count,
                        'black_or_african_american': demographics.black_count,
                        'hispanic_latino_or_spanish': demographics.hispanic_count,
                        'native_hawaiian_or_pacific_islander': demographics.hawaiian_pacific_count,
                        'white': demographics.white_count,
                        'other': demographics.other_count
                    }
                })
            
            matriculants = matriculants_data
            
            # === GRE SCORES ===
            gre_scores = {}
            if enhanced_profile:
                gre_scores = {
                    'average_scores': {
                        'verbal_reasoning': enhanced_profile.gre_verbal_score,
                        'analytical_writing': enhanced_profile.gre_analytical_score,
                        'quantitative_reasoning': enhanced_profile.gre_quantitative_score
                    },
                    'average_percentiles': {
                        'verbal_reasoning': enhanced_profile.gre_verbal_percentile,
                        'analytical_writing': enhanced_profile.gre_analytical_percentile,
                        'quantitative_reasoning': enhanced_profile.gre_quantitative_percentile
                    }
                }
            
            # === REQUIREMENTS ===
            requirements = {}
            
            # References
            rec_req = program.recommendation_requirements.first()
            if rec_req:
                requirements['references'] = {
                    'minimum_number_required': rec_req.number_required,
                    'types_of_references_required': rec_req.types_required
                }
            
            # GPA Requirements
            gpa_req = program.gpa_requirements.first()
            if gpa_req:
                requirements['gpa_requirements'] = {
                    'minimum_overall_gpa': gpa_req.minimum_overall,
                    'minimum_prereq_gpa': gpa_req.minimum_prereq,
                    'minimum_science_gpa': gpa_req.minimum_science
                }
            
            # Standardized Testing
            gre_req = program.gre_requirements.first()
            if gre_req:
                requirements['standardized_testing'] = {
                    'tests_required': 'GRE' if gre_req.required else 'None'
                }
            
            # Healthcare Experience
            healthcare_exp = program.healthcare_experience.first()
            patient_care_exp = program.patient_care_experience.first()
            
            if healthcare_exp or patient_care_exp:
                requirements['healthcare_experience'] = {}
                if healthcare_exp:
                    requirements['healthcare_experience'].update({
                        'required_or_recommended': 'Required' if healthcare_exp.required else 'Recommended',
                        'hours_needed': healthcare_exp.hours_needed,
                        'time_limit': healthcare_exp.time_limit,
                        'accepted_experience_types': {
                            'paid_health_care': 'Accepted' if healthcare_exp.paid_accepted else 'Not Accepted',
                            'volunteer_community_service': 'Accepted' if healthcare_exp.volunteer_accepted else 'Not Accepted',
                            'virtual_health_care_accepted': 'Yes' if healthcare_exp.virtual_accepted else 'No'
                        }
                    })
                
                if patient_care_exp:
                    if 'accepted_experience_types' not in requirements['healthcare_experience']:
                        requirements['healthcare_experience']['accepted_experience_types'] = {}
                    requirements['healthcare_experience']['accepted_experience_types'].update({
                        'paid_direct_patient_care': 'Accepted' if patient_care_exp.paid_accepted else 'Not Accepted',
                        'volunteer_direct_patient_care': 'Accepted' if patient_care_exp.volunteer_accepted else 'Not Accepted',
                        'direct_patient_care_hours_needed': patient_care_exp.hours_needed,
                        'direct_patient_care_time_limit': patient_care_exp.time_limit
                    })
            
            # Shadowing
            shadowing_req = program.shadowing_requirements.first()
            if shadowing_req:
                requirements['shadowing'] = {
                    'shadowing_pa': shadowing_req.pa_shadowing,
                    'shadowing_physician': shadowing_req.physician_shadowing,
                    'shadowing_other_health_care_provider': shadowing_req.other_shadowing,
                    'virtual_shadowing_accepted': 'Yes' if shadowing_req.virtual_accepted else 'No'
                }
            
            # === PREREQUISITES ===
            prerequisites = {
                'prereq_time_limit': '10 Years',  # Default, can be made dynamic
                'courses': []
            }
            
            for prereq in program.prerequisite_courses.all():
                prerequisites['courses'].append({
                    'name': prereq.course_name,
                    'credits': prereq.credits
                })
            
            # Build the comprehensive program data object
            program_data.append({
                'program_name': general_information['program_name'],
                'program_data': {
                    'general_information': general_information,
                    'class_profile': class_profile,
                    'attrition': attrition,
                    'program_curriculum': curriculum,
                    'caspa_deadlines': caspa_deadlines,
                    'tuition_deposits': tuition_deposits,
                    'matriculants': matriculants,
                    'gre_scores': gre_scores,
                    'pance_pass_rates': pance_pass_rates,
                    'requirements': requirements,
                    'prerequisites': prerequisites
                },
                'url': program.program_website if program.program_website else program.url,
                'timestamp': program.last_updated,
                
                # Keep the legacy structure for backward compatibility
                'name': general_information['program_name'],
                'school': school_data,
                'program_url': program.program_website if program.program_website else program.url,
                'requirements': {
                    'gpa': {
                        'overall': float(gpa_req.minimum_overall) if gpa_req and gpa_req.minimum_overall and gpa_req.minimum_overall != 'N/A' else 0.0,
                        'science': float(gpa_req.minimum_science) if gpa_req and gpa_req.minimum_science and gpa_req.minimum_science != 'N/A' else 0.0
                    },
                    'prerequisites': {prereq.course_name: {'credits': prereq.credits, 'timeframe': prereq.time_limit, 'lab_required': prereq.lab_required, 'courses': []} for prereq in program.prerequisite_courses.all()},
                    'experience': {
                        'Patient Care Hours': int(patient_care_exp.hours_needed) if patient_care_exp and patient_care_exp.hours_needed and patient_care_exp.hours_needed != 'N/A' else 0,
                        'Healthcare Hours': int(healthcare_exp.hours_needed) if healthcare_exp and healthcare_exp.hours_needed and healthcare_exp.hours_needed != 'N/A' else 0,
                        'Shadowing Hours': 0  # Can be enhanced from shadowing requirements
                    },
                    'standardized_tests': {
                        'tests_required': 'GRE' if gre_req and gre_req.required else 'None'
                    }
                }
            })
        
        return JsonResponse({'programs': program_data})
        
    except Exception as e:
        logger.error(f"Error fetching comprehensive program list: {str(e)}")
        return JsonResponse(
            {'error': 'Failed to fetch program list'},
            status=500
        )

def sync_transcripts_with_s3(user_id: int):
    """Synchronize database records with S3 files - only keep records with actual files"""
    try:
        s3_service = S3Service()
        # Get actual files from S3
        s3_files = s3_service.list_user_transcripts(user_id)
        s3_keys = {file['key'] for file in s3_files}
        
        # Get all database records for user
        db_transcripts = UserTranscript.objects.filter(user_id=user_id)
        
        # Deactivate ALL records first
        db_transcripts.update(is_active=False, status='failed')
        
        # Only activate records that have actual files in S3
        UserTranscript.objects.filter(
            user_id=user_id,
            s3_key__in=s3_keys
        ).update(is_active=True, status='uploaded')
        
        # Delete any records that don't have files
        UserTranscript.objects.filter(
            user_id=user_id,
            is_active=False
        ).delete()
        
        logger.info(f"Synchronized transcripts for user {user_id}: Found {len(s3_keys)} actual files in S3")
        
    except Exception as e:
        logger.error(f"Error synchronizing transcripts: {str(e)}")
        raise

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def upload_transcript(request):
    """Handle transcript file upload with proper transaction handling"""
    try:
        if 'file' not in request.FILES:
            return Response(
                {'error': 'No file provided'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
            
        file_obj = request.FILES['file']
        
        # Validate file type
        if not file_obj.name.lower().endswith('.pdf'):
            return Response(
                {'error': 'Only PDF files are accepted'}, 
                status=status.HTTP_400_BAD_REQUEST
            )

        # First, sync existing records with S3
        sync_transcripts_with_s3(request.user.id)

        # Use transaction to ensure both S3 and database operations succeed or fail together
        with transaction.atomic():
            # Initialize S3 service
            s3_service = S3Service()
            
            try:
                # Upload file to S3 and get normalized key
                s3_key, file_size = s3_service.upload_transcript(
                    user_id=request.user.id,
                    file_obj=file_obj,
                    original_filename=file_obj.name
                )
                
                logger.info(f"File uploaded to S3 with key: {s3_key}")
                
            except Exception as e:
                logger.error(f"S3 upload failed: {str(e)}")
                raise ValidationError("Failed to upload file to storage")

            try:
                # Deactivate ALL previous transcripts
                UserTranscript.objects.filter(
                    user=request.user
                ).update(is_active=False)
                
                # Create transcript record with normalized key
                transcript = UserTranscript.objects.create(
                    user=request.user,
                    original_filename=file_obj.name.lower(),  # Store lowercase for consistency
                    s3_key=s3_key,  # Store the normalized key
                    file_size=file_size,
                    mime_type='application/pdf',
                    is_active=True,
                    status='uploaded'
                )
                
                # Verify the record was created with the correct key
                if not UserTranscript.objects.filter(
                    id=transcript.id,
                    s3_key=s3_key,
                    is_active=True
                ).exists():
                    raise ValidationError("Failed to create transcript record with correct key")

            except Exception as e:
                logger.error(f"Database operation failed: {str(e)}")
                # If database operation fails, attempt to clean up S3
                try:
                    s3_service.delete_transcript(request.user.id, s3_key)
                except Exception as cleanup_error:
                    logger.error(f"Failed to clean up S3 file: {str(cleanup_error)}")
                raise ValidationError("Failed to create transcript record")

        logger.info(f"Successfully created transcript record with ID {transcript.id} and key {s3_key}")
        
        return Response({
            'message': 'Transcript uploaded successfully',
            'transcript_id': transcript.id,
            'filename': file_obj.name.lower(),
            'status': transcript.status,
            's3_key': s3_key  # Return the normalized key for verification
        }, status=status.HTTP_201_CREATED)
        
    except ValidationError as e:
        return Response(
            {'error': str(e)}, 
            status=status.HTTP_400_BAD_REQUEST
        )
    except Exception as e:
        logger.error(f"Unexpected error in upload_transcript: {str(e)}")
        return Response(
            {'error': 'An unexpected error occurred while processing your request'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_transcripts(request):
    """Get list of user's transcripts that actually exist in S3"""
    try:
        # First sync to ensure we only have real files
        sync_transcripts_with_s3(request.user.id)
        
        # Now get only active transcripts (ones with actual files)
        transcripts = UserTranscript.objects.filter(
            user=request.user,
            is_active=True
        )
        
        return Response([{
            'id': t.id,
            'filename': t.original_filename,
            'upload_date': t.upload_date,
            'status': t.status,
            'is_active': t.is_active,
            's3_key': t.s3_key
        } for t in transcripts])
        
    except Exception as e:
        logger.error(f"Error listing transcripts: {str(e)}")
        return Response(
            {'error': 'Failed to retrieve transcripts'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def delete_transcript(request, transcript_id):
    """Delete a transcript"""
    try:
        transcript = UserTranscript.objects.get(
            id=transcript_id, 
            user=request.user
        )
        
        # Initialize S3 service
        s3_service = S3Service()
        
        # Delete from S3
        if s3_service.delete_transcript(request.user.id, transcript.s3_key):
            # Delete from database
            transcript.delete()
            return Response(status=status.HTTP_204_NO_CONTENT)
        else:
            return Response(
                {'error': 'Failed to delete transcript from storage'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
            
    except UserTranscript.DoesNotExist:
        return Response(
            {'error': 'Transcript not found'}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        logger.error(f"Error deleting transcript: {str(e)}")
        return Response(
            {'error': 'Failed to delete transcript'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_s3_transcripts(request):
    """List all transcripts in the user's S3 folder"""
    try:
        s3_service = S3Service()
        files = s3_service.list_user_transcripts(request.user.id)
        
        return Response(files)
        
    except Exception as e:
        logger.error(f"Error listing S3 transcripts: {str(e)}")
        return Response(
            {'error': 'Failed to retrieve transcripts'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def manual_transcript(request):
    try:
        logger.info("Processing manual transcript submission...")
        data = request.data.get('transcript', {})
        
        if not data:
            return Response({
                'status': 'error',
                'message': 'No transcript data provided'
            }, status=400)

        institution = data.get('institution')
        transcript_data = data.get('transcript_data', {})
        
        if not institution or not transcript_data:
            return Response({
                'status': 'error',
                'message': 'Institution and transcript data are required'
            }, status=400)

        # Get or set student name
        student_name = data.get('student_name')
        if not student_name:
            # Fallback to user's name
            student_name = request.user.get_full_name() or request.user.username
            logger.info(f"Using fallback student name for manual transcript: {student_name}")
            # Update the data with this name
            data['student_name'] = student_name

        # Calculate GPAs
        total_points = 0.0
        total_credits = 0.0
        science_points = 0.0
        science_credits = 0.0

        # Grade points mapping
        GRADE_POINTS = {
            'A': 4.0, 'A-': 3.7,
            'B+': 3.3, 'B': 3.0, 'B-': 2.7,
            'C+': 2.3, 'C': 2.0, 'C-': 1.7,
            'D+': 1.3, 'D': 1.0, 'D-': 0.7,
            'F': 0.0
        }

        semesters = transcript_data.get('semesters', [])
        if not isinstance(semesters, list):
            return Response({
                'status': 'error',
                'message': 'Invalid semester data format'
            }, status=400)

        # Process each semester and mark science courses
        processed_semesters = []
        for semester in semesters:
            if not isinstance(semester.get('courses', []), list):
                continue

            processed_courses = []
            semester_points = 0.0
            semester_credits = 0.0

            for course in semester.get('courses', []):
                try:
                    # Convert credits to float, defaulting to 0 if invalid
                    try:
                        credits = float(str(course.get('credits', '0')).strip())
                    except (ValueError, TypeError):
                        credits = 0.0

                    grade = str(course.get('grade', '')).strip().upper()
                    course_code = str(course.get('code', '')).strip()
                    course_name = str(course.get('name', '')).strip()
                    is_science = bool(course.get('is_science', False))
                    
                    # Determine if course has a lab component (auto-detection)
                    # First check if has_lab was explicitly provided
                    if 'has_lab' in course:
                        has_lab = bool(course['has_lab'])
                    else:
                        # Get course name and code with proper null handling
                        course_name_lower = course_name.lower() if course_name else ''
                        course_code_lower = course_code.lower() if course_code else ''
                        
                        # Debug logging to see what we're checking
                        logger.info(f"Checking lab for course: {course_code_lower} - {course_name_lower}")
                        
                        # Simple explicit check: look for the word "lab" in the name or code
                        has_lab = False
                        
                        if 'lab' in course_name_lower or 'lab' in course_code_lower:
                            has_lab = True
                            logger.info(f"✓ MATCH: Found lab in course {course_code_lower} - {course_name_lower}")
                        else:
                            logger.info(f"✗ NO MATCH: No lab found in course {course_code_lower} - {course_name_lower}")
                        
                        course['has_lab'] = has_lab

                    # Get grade points value
                    grade_points = GRADE_POINTS.get(grade, 0.0)

                    # Calculate points if valid grade and credits
                    if credits > 0 and grade in GRADE_POINTS:
                        points = credits * grade_points
                        total_points += points
                        total_credits += credits
                        semester_points += points
                        semester_credits += credits

                        if is_science:
                            science_points += points
                            science_credits += credits

                    # Create processed course with a unique ID and has_lab flag
                    processed_course = {
                        'id': uuid.uuid4().hex, # Add unique ID
                        'code': course_code,
                        'name': course_name,
                        'credits': credits,
                        'grade': grade,
                        'is_science': is_science,
                        'has_lab': has_lab  # Add has_lab flag
                    }
                    processed_courses.append(processed_course)

                except Exception as e:
                    logger.error(f"Error processing course: {course}, Error: {str(e)}")
                    continue

            # Calculate semester GPA
            semester_gpa = round(semester_points / semester_credits, 2) if semester_credits > 0 else 0.0
            cumulative_gpa = round(total_points / total_credits, 2) if total_credits > 0 else 0.0

            processed_semesters.append({
                'term': semester.get('term', ''),
                'year': semester.get('year', ''),
                'courses': processed_courses, # Courses now have IDs and has_lab flags
                'term_gpa': semester_gpa,
                'cumulative_gpa': cumulative_gpa
            })

        # Calculate final GPAs
        calculated_gpa = round(total_points / total_credits, 2) if total_credits > 0 else 0.0
        science_gpa = round(science_points / science_credits, 2) if science_credits > 0 else 0.0

        # Use transaction to ensure database operations are atomic
        with transaction.atomic():
            # Delete any existing records for this institution
            TranscriptRecord.objects.filter(
                user=request.user,
                institution=institution
            ).delete()

            # Create new transcript record
            transcript = TranscriptRecord.objects.create(
                user=request.user,
                institution=institution,
                student_name=student_name,  # Use our variable with fallback
                calculated_gpa=calculated_gpa,
                science_gpa=science_gpa,
                validation_status='approved',  # Manual entries are automatically approved
                transcript_data={
                    'semesters': processed_semesters,
                    'academic_summary': {
                        'institution': institution,
                        'student_name': student_name,  # Use our variable
                        'cumulative_gpa': calculated_gpa,
                        'science_gpa': science_gpa,
                        'science_credits': science_credits,
                        'total_credits': total_credits
                    }
                }
            )

        return Response({
            'status': 'success',
            'message': 'Manual transcript saved successfully',
            'data': {
                'transcript_id': transcript.id,
                'calculated_gpa': calculated_gpa,
                'science_gpa': science_gpa,
                'transcript_data': {
                    'semesters': processed_semesters,
                    'academic_summary': {
                        'institution': institution,
                        'student_name': student_name,  # Use our variable
                        'cumulative_gpa': calculated_gpa,
                        'science_gpa': science_gpa,
                        'science_credits': science_credits,
                        'total_credits': total_credits
                    }
                }
            }
        })

    except Exception as e:
        logger.error(f"Error in manual_transcript: {str(e)}")
        return Response({
            'status': 'error',
            'message': str(e)
        }, status=500)

@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def delete_transcript_record(request, transcript_id):
    """Delete a transcript record"""
    try:
        transcript = TranscriptRecord.objects.get(
            id=transcript_id,
            user=request.user
        )
        
        # Delete the transcript record
        transcript.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)
            
    except TranscriptRecord.DoesNotExist:
        return Response(
            {'error': 'Transcript not found'}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        logger.error(f"Error deleting transcript record: {str(e)}")
        return Response(
            {'error': 'Failed to delete transcript record'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['PATCH'])
@permission_classes([IsAuthenticated])
def update_course_academic_level(request, course_id): # Note: course_id here is actually transcript_id from frontend
            """
            Update the manual academic level for a specific course within a transcript record.
            The frontend sends transcript_id as course_id in the URL.
            """
            try:
                transcript_id = course_id # Rename for clarity in backend
                new_level = request.data.get('manual_academic_level')
                # Get the unique course ID from the request body
                course_unique_id = request.data.get('course_unique_id')

                # Remove unused variables that were used for text matching
                # course_code = request.data.get('code')
                # course_name = request.data.get('name')
                # course_term = request.data.get('term')
                # course_year = request.data.get('year')
                # course_institution = request.data.get('institution')

                # Check for required data: new level and the unique course ID
                if new_level is None or course_unique_id is None:
                    return Response(
                        {'error': 'Missing required data (manual_academic_level, course_unique_id) for course update'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
        
                try:
                    transcript = TranscriptRecord.objects.get(id=transcript_id, user=request.user)
                except TranscriptRecord.DoesNotExist:
                    return Response(
                        {'error': 'Transcript record not found'},
                        status=status.HTTP_404_NOT_FOUND
                    )
        
                transcript_data = transcript.transcript_data
                semesters = transcript_data.get('semesters', [])
                course_updated = False
        
                # Iterate through semesters and courses to find and update the specific course using its unique ID
                for semester in semesters:
                    courses = semester.get('courses', [])
                    for course in courses:
                        # Match course based on the unique ID
                        if course.get('id') == course_unique_id:
                            # Update the manual_academic_level
                            course['manual_academic_level'] = new_level
                            # Optionally ensure other fields like institution are present if needed elsewhere
                            if 'institution' not in course:
                                course['institution'] = transcript.institution # Use transcript's institution if missing on course
                            course_updated = True
                            logger.info(f"Found course with ID {course_unique_id} in transcript {transcript_id}")
                            break # Found and updated the course, exit inner loop
                    if course_updated:
                        break # Exit outer loop if course was updated
        
                if course_updated:
                    # Save the modified transcript_data back to the model
                    transcript.transcript_data = transcript_data
                    transcript.save()
                    logger.info(f"Successfully updated academic level for course {course_unique_id} in transcript {transcript_id}")
                    return Response({'status': 'success', 'message': 'Academic level updated successfully'})
                else:
                    logger.warning(f"Course with unique ID {course_unique_id} not found in transcript {transcript_id}. Request data: {request.data}")
                    return Response(
                        {'error': f'Course with unique ID {course_unique_id} not found in transcript'},
                        status=status.HTTP_404_NOT_FOUND
                    )
        
            except Exception as e:
                logger.error(f"Error updating course academic level: {str(e)}", exc_info=True)
                return Response(
                    {'error': 'An error occurred while updating the academic level'},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
        
@api_view(['POST', 'GET'])
@permission_classes([IsAuthenticated])
def handle_prerequisites(request):
    if request.method == 'POST':
        try:
            prerequisites_data = request.data.get('prerequisites', [])
            
            # Clear existing prerequisites for this user
            PrerequisiteSelection.objects.filter(user=request.user).delete()
            
            # Create new prerequisite selections
            for prereq in prerequisites_data:
                prereq_name = prereq['prerequisite_name']
                for course in prereq['courses']:
                    # Check if course has lab information, default to False if missing
                    has_lab = bool(course.get('has_lab', False))
                    
                    PrerequisiteSelection.objects.create(
                        user=request.user,
                        prerequisite_name=prereq_name,
                        course_code=course['code'],
                        course_name=course['name'],
                        credits=course['credits'],
                        grade=course['grade'],
                        institution=course['institution'],
                        term=course.get('term'),
                        year=course.get('year'),
                        has_lab=has_lab
                    )
            
            return Response({'message': 'Prerequisites saved successfully'}, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response(
                {'error': f'Failed to save prerequisites: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )
            
    elif request.method == 'GET':
        try:
            prerequisites = PrerequisiteSelection.objects.filter(user=request.user)
            # Group prerequisites by name
            prereq_dict = {}
            for prereq in prerequisites:
                if prereq.prerequisite_name not in prereq_dict:
                    prereq_dict[prereq.prerequisite_name] = []
                prereq_dict[prereq.prerequisite_name].append({
                    'code': prereq.course_code,
                    'name': prereq.course_name,
                    'credits': prereq.credits,
                    'grade': prereq.grade,
                    'institution': prereq.institution,
                    'term': prereq.term,
                    'year': prereq.year,
                    'has_lab': prereq.has_lab  # Include has_lab in response
                })
            
            # Convert to list format
            data = [
                {
                    'prerequisite_name': name,
                    'courses': courses
                }
                for name, courses in prereq_dict.items()
            ]
            
            return Response(data, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response(
                {'error': f'Failed to fetch prerequisites: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_saved_prerequisites(request):
    """
    Get all saved prerequisites for the current user.
    """
    try:
        # Get all prerequisite selections for the user
        prereq_selections = PrerequisiteSelection.objects.filter(user=request.user)
        
        # Group prerequisites by category
        prereqs_by_category = {}
        for selection in prereq_selections:
            if selection.prerequisite_name not in prereqs_by_category:
                prereqs_by_category[selection.prerequisite_name] = {
                    'courses': []
                }
            
            # Add course details
            course_data = {
                'code': selection.course_code,
                'name': selection.course_name,
                'credits': selection.credits,
                'grade': selection.grade,
                'institution': selection.institution,
                'term': selection.term,
                'year': selection.year,
                'has_lab': selection.has_lab  # Include has_lab in response
            }
            prereqs_by_category[selection.prerequisite_name]['courses'].append(course_data)
        
        # Calculate GPAs if needed
        gpa = 0.0  # You may want to calculate this from saved courses
        science_gpa = 0.0  # You may want to calculate this from saved science courses
        
        response_data = {
            'prerequisites': prereqs_by_category,
            'gpa': gpa,
            'science_gpa': science_gpa
        }
        
        return Response(response_data)
    except Exception as e:
        logger.error(f"Error getting saved prerequisites: {str(e)}")
        return Response(
            {"error": "Failed to get saved prerequisites"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

def calculate_data_hash(user):
    """
    Calculate a hash based on user's transcript, profile, and prerequisite data
    to determine if saved matching results are still valid
    """
    hash_input = ""
    
    # Add transcript data
    transcripts = TranscriptRecord.objects.filter(user=user).order_by('-updated_at')
    if transcripts.exists():
        latest_transcript = transcripts.first()
        hash_input += str(latest_transcript.updated_at)
    
    # Add profile data
    user_profile = user.profile if hasattr(user, 'profile') else None
    if user_profile:
        hash_input += str(user_profile.updated_at) if hasattr(user_profile, 'updated_at') else ""
        
        # Add test scores if they exist
        hash_input += str(user_profile.gre_verbal_score or '')
        hash_input += str(user_profile.gre_quantitative_score or '')
        hash_input += str(user_profile.pa_cat_composite_ss or '')
        hash_input += str(user_profile.direct_patient_care_hours or '')
        hash_input += str(user_profile.shadowing_hours or '')
    
    # Add prerequisite data
    prereq_count = PrerequisiteSelection.objects.filter(user=user).count()
    latest_prereq = PrerequisiteSelection.objects.filter(user=user).order_by('-updated_at').first()
    hash_input += str(prereq_count)
    hash_input += str(latest_prereq.updated_at) if latest_prereq else ""
    
    # Create hash
    return hashlib.sha256(hash_input.encode()).hexdigest()

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def save_matching_results(request):
    """Save program matching results to database"""
    user = request.user
    
    # Get matching results from request body
    matching_results = request.data.get('matching_results')
    if not matching_results:
        return Response({'status': 'error', 'message': 'No matching results provided'}, status=400)
    
    # Calculate hash from user's current data
    data_hash = calculate_data_hash(user)
    
    # Save to database (update if exists, create if not)
    ProgramMatchingResult.objects.update_or_create(
        user=user,
        defaults={
            'matching_results': matching_results,
            'data_hash': data_hash
        }
    )
    
    return Response({
        'status': 'success',
        'message': 'Matching results saved successfully',
        'timestamp': timezone.now().isoformat()
    })

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_matching_results(request):
    """Retrieve saved program matching results if available"""
    user = request.user
    
    # Get latest matching result
    result = ProgramMatchingResult.objects.filter(user=user).first()
    
    if not result:
        return Response({'status': 'no_results', 'message': 'No saved results found'})
    
    # Check if results are still valid by comparing data hash
    current_hash = calculate_data_hash(user)
    if current_hash != result.data_hash:
        return Response({
            'status': 'stale',
            'message': 'Your data has changed since these results were calculated',
            'timestamp': result.timestamp.isoformat()
        })
    
    return Response({
        'status': 'success',
        'message': 'Retrieved saved matching results',
        'timestamp': result.timestamp.isoformat(),
        'matching_results': result.matching_results
    })