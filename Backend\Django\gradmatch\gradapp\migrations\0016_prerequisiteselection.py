# Generated by Django 5.0.1 on 2025-01-31 09:07

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('gradapp', '0015_remove_transcriptrecord_programs_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PrerequisiteSelection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('prerequisite_name', models.Char<PERSON><PERSON>(max_length=100)),
                ('course_code', models.Char<PERSON>ield(max_length=20)),
                ('course_name', models.CharField(max_length=200)),
                ('credits', models.DecimalField(decimal_places=2, max_digits=4)),
                ('grade', models.CharField(max_length=2)),
                ('institution', models.<PERSON>r<PERSON><PERSON>(max_length=200)),
                ('term', models.Char<PERSON>ield(max_length=20)),
                ('year', models.CharField(max_length=4)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('user', 'prerequisite_name')},
            },
        ),
    ]
