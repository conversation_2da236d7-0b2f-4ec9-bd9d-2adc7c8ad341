"""
Tests for the PA JSON Parser utility
"""

import json
import tempfile
import unittest
from pathlib import Path
from unittest.mock import patch, mock_open

from django.test import TestCase
from gradapp.utils.json_parser import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, JSONValidationError


class TestPAJSONParser(TestCase):
    """Test cases for PAJSONParser class"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        self.parser = PAJSONParser(data_directory=self.temp_dir)
        
        # Sample valid JSON data
        self.valid_json_data = {
            "program_name": "Test University PA Program",
            "program_url": "https://example.com/program",
            "mission_statement": "To educate competent physician assistants",
            "caspa_member": True,
            "upcoming_caspa_cycle": True,
            "address": {
                "street": "123 Main St",
                "city": "Test City",
                "state": "TS",
                "zip_code": "12345"
            },
            "curriculum_focus": ["Problem-Based Learning", "Case-Based Learning"],
            "credentials_offered": ["Master's degree"],
            "types_of_interviews": ["Traditional Interview"],
            "estimated_incoming_class_size": "50",
            "tuition": "$50,000",
            "application_deadline": "10/01/2025",
            "minimum_overall_gpa_required": "3.0"
        }
        
        # Sample invalid JSON data
        self.invalid_json_data = {
            "program_url": "https://example.com/program",
            # Missing required program_name
            "caspa_member": "invalid_boolean",  # Should be boolean
            "curriculum_focus": "not_an_array",  # Should be array
            "minimum_overall_gpa_required": "5.0",  # Invalid GPA range
            "estimated_incoming_class_size": "-10"  # Invalid class size
        }
    
    def tearDown(self):
        """Clean up test fixtures"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def create_test_json_file(self, filename: str, data: dict) -> Path:
        """Helper method to create a test JSON file"""
        file_path = Path(self.temp_dir) / filename
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f)
        return file_path
    
    def test_init(self):
        """Test parser initialization"""
        parser = PAJSONParser()
        self.assertEqual(parser.data_directory, Path(r"D:\ScrapperPAEAReal"))
        
        custom_parser = PAJSONParser("/custom/path")
        self.assertEqual(custom_parser.data_directory, Path("/custom/path"))
    
    def test_discover_json_files_success(self):
        """Test successful JSON file discovery"""
        # Create test JSON files
        self.create_test_json_file("test1.json", self.valid_json_data)
        self.create_test_json_file("test2.json", self.valid_json_data)
        
        # Create non-JSON file (should be ignored)
        non_json_path = Path(self.temp_dir) / "test.txt"
        with open(non_json_path, 'w') as f:
            f.write("not json")
        
        files = self.parser.discover_json_files()
        self.assertEqual(len(files), 2)
        self.assertTrue(all(f.suffix == '.json' for f in files))
    
    def test_discover_json_files_nonexistent_directory(self):
        """Test file discovery with nonexistent directory"""
        parser = PAJSONParser("/nonexistent/path")
        with self.assertRaises(JSONParserError) as context:
            parser.discover_json_files()
        self.assertIn("does not exist", str(context.exception))
    
    def test_read_json_file_success(self):
        """Test successful JSON file reading"""
        file_path = self.create_test_json_file("valid.json", self.valid_json_data)
        
        data = self.parser.read_json_file(file_path)
        self.assertEqual(data["program_name"], "Test University PA Program")
        self.assertEqual(data["caspa_member"], True)
    
    def test_read_json_file_invalid_json(self):
        """Test reading invalid JSON file"""
        file_path = Path(self.temp_dir) / "invalid.json"
        with open(file_path, 'w') as f:
            f.write("{ invalid json }")
        
        with self.assertRaises(JSONParserError) as context:
            self.parser.read_json_file(file_path)
        self.assertIn("Invalid JSON", str(context.exception))
    
    def test_read_json_file_not_found(self):
        """Test reading nonexistent file"""
        file_path = Path(self.temp_dir) / "nonexistent.json"
        
        with self.assertRaises(JSONParserError) as context:
            self.parser.read_json_file(file_path)
        self.assertIn("File not found", str(context.exception))
    
    def test_validate_required_fields_success(self):
        """Test validation with all required fields present"""
        file_path = Path("test.json")
        errors = self.parser.validate_required_fields(self.valid_json_data, file_path)
        self.assertEqual(len(errors), 0)
    
    def test_validate_required_fields_missing(self):
        """Test validation with missing required fields"""
        incomplete_data = {
            "program_url": "https://example.com"
            # Missing program_name, mission_statement, etc.
        }
        
        file_path = Path("test.json")
        errors = self.parser.validate_required_fields(incomplete_data, file_path)
        self.assertGreater(len(errors), 0)
        self.assertTrue(any("program_name" in error for error in errors))
    
    def test_validate_data_types_success(self):
        """Test data type validation with correct types"""
        file_path = Path("test.json")
        errors = self.parser.validate_data_types(self.valid_json_data, file_path)
        self.assertEqual(len(errors), 0)
    
    def test_validate_data_types_incorrect(self):
        """Test data type validation with incorrect types"""
        invalid_types_data = {
            "program_name": 123,  # Should be string
            "caspa_member": "not_boolean",  # Should be boolean
            "curriculum_focus": "not_array",  # Should be array
            "address": "not_object"  # Should be object
        }
        
        file_path = Path("test.json")
        errors = self.parser.validate_data_types(invalid_types_data, file_path)
        self.assertGreater(len(errors), 0)
    
    def test_validate_business_logic_success(self):
        """Test business logic validation with valid data"""
        file_path = Path("test.json")
        errors = self.parser.validate_business_logic(self.valid_json_data, file_path)
        self.assertEqual(len(errors), 0)
    
    def test_validate_business_logic_invalid_gpa(self):
        """Test business logic validation with invalid GPA"""
        invalid_data = self.valid_json_data.copy()
        invalid_data["minimum_overall_gpa_required"] = "5.0"  # Invalid GPA range
        
        file_path = Path("test.json")
        errors = self.parser.validate_business_logic(invalid_data, file_path)
        self.assertTrue(any("GPA value out of range" in error for error in errors))
    
    def test_validate_business_logic_invalid_class_size(self):
        """Test business logic validation with invalid class size"""
        invalid_data = self.valid_json_data.copy()
        invalid_data["estimated_incoming_class_size"] = "-10"  # Negative class size
        
        file_path = Path("test.json")
        errors = self.parser.validate_business_logic(invalid_data, file_path)
        self.assertTrue(any("Class size should be positive" in error for error in errors))
    
    def test_validate_business_logic_invalid_date(self):
        """Test business logic validation with invalid date format"""
        invalid_data = self.valid_json_data.copy()
        invalid_data["application_deadline"] = "invalid-date"
        
        file_path = Path("test.json")
        errors = self.parser.validate_business_logic(invalid_data, file_path)
        self.assertTrue(any("Invalid date format" in error for error in errors))
    
    def test_validate_json_data_valid(self):
        """Test comprehensive validation with valid data"""
        file_path = Path("test.json")
        is_valid, errors = self.parser.validate_json_data(self.valid_json_data, file_path)
        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)
    
    def test_validate_json_data_invalid(self):
        """Test comprehensive validation with invalid data"""
        file_path = Path("test.json")
        is_valid, errors = self.parser.validate_json_data(self.invalid_json_data, file_path)
        self.assertFalse(is_valid)
        self.assertGreater(len(errors), 0)
    
    def test_process_single_file_success(self):
        """Test processing a single valid file"""
        file_path = self.create_test_json_file("valid.json", self.valid_json_data)
        
        result = self.parser.process_single_file(file_path)
        self.assertIsNotNone(result)
        self.assertEqual(result["program_name"], "Test University PA Program")
        self.assertIn(str(file_path), self.parser.processed_files)
    
    def test_process_single_file_invalid(self):
        """Test processing a single invalid file"""
        file_path = self.create_test_json_file("invalid.json", self.invalid_json_data)
        
        result = self.parser.process_single_file(file_path)
        self.assertIsNone(result)
        self.assertEqual(len(self.parser.failed_files), 1)
        self.assertEqual(self.parser.failed_files[0]['file'], str(file_path))
    
    def test_process_single_file_malformed_json(self):
        """Test processing a file with malformed JSON"""
        file_path = Path(self.temp_dir) / "malformed.json"
        with open(file_path, 'w') as f:
            f.write("{ malformed json }")
        
        result = self.parser.process_single_file(file_path)
        self.assertIsNone(result)
        self.assertEqual(len(self.parser.failed_files), 1)
        self.assertIn("Invalid JSON", self.parser.failed_files[0]['errors'][0])
    
    def test_process_all_files_success(self):
        """Test processing all files with mixed valid/invalid data"""
        # Create valid files
        self.create_test_json_file("valid1.json", self.valid_json_data)
        self.create_test_json_file("valid2.json", self.valid_json_data)
        
        # Create invalid file
        self.create_test_json_file("invalid.json", self.invalid_json_data)
        
        valid_data, summary = self.parser.process_all_files()
        
        self.assertEqual(len(valid_data), 2)
        self.assertEqual(summary['total_files'], 3)
        self.assertEqual(summary['processed_successfully'], 2)
        self.assertEqual(summary['failed_files'], 1)
        self.assertAlmostEqual(summary['success_rate'], 66.67, places=1)
    
    def test_process_all_files_empty_directory(self):
        """Test processing with no JSON files"""
        valid_data, summary = self.parser.process_all_files()
        
        self.assertEqual(len(valid_data), 0)
        self.assertEqual(summary['total_files'], 0)
        self.assertEqual(summary['processed_successfully'], 0)
        self.assertEqual(summary['failed_files'], 0)
    
    def test_get_processing_stats(self):
        """Test getting processing statistics"""
        # Process some files first
        self.create_test_json_file("valid.json", self.valid_json_data)
        self.create_test_json_file("invalid.json", self.invalid_json_data)
        
        self.parser.process_all_files()
        
        stats = self.parser.get_processing_stats()
        self.assertEqual(stats['processed_files_count'], 1)
        self.assertEqual(stats['failed_files_count'], 1)
        self.assertIsInstance(stats['processed_files'], list)
        self.assertIsInstance(stats['failed_files'], list)
    
    def test_logger_setup(self):
        """Test logger setup"""
        logger = self.parser._setup_logger()
        self.assertEqual(logger.name, 'pa_json_parser')
        self.assertEqual(logger.level, 20)  # INFO level
    
    def test_edge_cases_no_information_provided(self):
        """Test handling of 'No information provided' values"""
        data_with_no_info = self.valid_json_data.copy()
        data_with_no_info.update({
            "phone_number": "No information provided",
            "email": "No information provided",
            "application_deadline": "No information provided"
        })
        
        file_path = Path("test.json")
        is_valid, errors = self.parser.validate_json_data(data_with_no_info, file_path)
        self.assertTrue(is_valid)  # Should still be valid
    
    def test_boolean_string_conversion(self):
        """Test handling of Yes/No string values for boolean fields"""
        data_with_string_bools = self.valid_json_data.copy()
        data_with_string_bools.update({
            "caspa_member": "Yes",
            "upcoming_caspa_cycle": "No",
            "part_time_option": "yes",
            "distance_learning": "no"
        })
        
        file_path = Path("test.json")
        errors = self.parser.validate_data_types(data_with_string_bools, file_path)
        # Should not have errors for Yes/No values
        boolean_errors = [e for e in errors if "should be boolean" in e]
        self.assertEqual(len(boolean_errors), 0)


if __name__ == '__main__':
    unittest.main()