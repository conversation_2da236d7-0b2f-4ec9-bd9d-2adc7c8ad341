import os
import json
from decimal import Decimal, InvalidOperation
from datetime import datetime
import re
import logging
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from gradapp.models import (
    School, Program, PrerequisiteCourse, HealthcareExperience,
    PatientCareExperience, ShadowingRequirement, GRERequirement,
    GPARequirement, InterviewRequirement, ApplicationRequirement,
    OtherRequirement, RecommendationRequirement
    # Add other models like ClassProfile if data becomes available later
)

# Configure logging (can keep for detailed logs if needed)
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- Helper Functions ---
# [Keep all helper functions as they were: safe_get, parse_boolean, parse_int, etc.]
# ... (Previous helper functions omitted for brevity) ...

def safe_get(data, key, default=None):
    """Gets a value from dict, returning default if key missing or value is 'No information provided' or empty."""
    value = data.get(key, default)
    if isinstance(value, str):
        val_lower = value.strip().lower()
        if val_lower in ('no information provided', 'none', ''):
            return default
    elif value is None:
         return default
    return value

def parse_boolean(value):
    """Converts 'Yes'/'No'/True/False strings/bools to Python boolean, handling variations."""
    if isinstance(value, bool):
        return value
    if isinstance(value, str):
        val_lower = value.strip().lower()
        if val_lower.startswith('yes'): # Handles "Yes" and "Yes, with exceptions..."
            return True
        if val_lower == 'no':
            return False
    # Return None if it's not clearly True/False (e.g., "Recommended")
    # Specific logic elsewhere might interpret None differently based on context
    return None

def parse_int(value, default=None):
    """Safely parses a string to an integer."""
    if isinstance(value, int):
        return value
    if isinstance(value, str):
        try:
            # Remove commas, spaces, etc.
            cleaned_value = re.sub(r'[^\d-]', '', value)
            if cleaned_value:
                return int(cleaned_value)
        except (ValueError, TypeError):
            pass
    return default

def parse_decimal(value, default=None):
    """Safely parses a string to a Decimal."""
    if isinstance(value, (int, float)):
         return Decimal(value)
    if isinstance(value, str):
        try:
            # Remove currency symbols, commas, etc.
            cleaned_value = re.sub(r'[^\d.-]', '', value)
            if cleaned_value:
                return Decimal(cleaned_value)
        except (InvalidOperation, TypeError):
            pass
    return default if default is None else Decimal(default)

def parse_date(value, formats=["%m/%d/%Y", "%B %Y"]):
    """Safely parses a string into a date object using multiple formats."""
    if isinstance(value, str):
        cleaned_value = value.strip()
        for fmt in formats:
            try:
                # Handle cases like "May 2026" - default to 1st day
                if "%d" not in fmt and len(cleaned_value.split()) == 2:
                     dt = datetime.strptime(cleaned_value, fmt)
                     return dt.date().replace(day=1) # Use 1st day of month
                elif "%d" in fmt:
                     dt = datetime.strptime(cleaned_value, fmt)
                     return dt.date()
            except (ValueError, TypeError):
                continue # Try next format
    return None # Return None if parsing fails for all formats

def parse_credits(credit_str):
    """Extracts credit number from strings like '3 Credit(s)'."""
    if isinstance(credit_str, str):
        match = re.search(r'(\d+(\.\d+)?)', credit_str)
        if match:
            return match.group(1)
    return 'Not specified' # Default value if parsing fails

def parse_required_status(status_str):
    """Parses requirement status strings like 'Yes, required', 'Recommended...', 'No'."""
    if not isinstance(status_str, str):
        return None # Or False, depending on desired default for ambiguity
    status_lower = status_str.strip().lower()
    if 'required' in status_lower and 'not required' not in status_lower:
        return True
    if 'recommended' in status_lower or 'not required' in status_lower or status_lower == 'no':
        return False
    # Could return None if status is ambiguous (e.g., just "Yes")
    # For healthcare/patient care, let's default ambiguity to False unless explicitly required
    return False # Defaulting to False for ambiguity based on previous model changes

def parse_list_from_string(value):
    """Parses a list or a semicolon-separated string into a Python list."""
    if isinstance(value, list):
        return [str(item).strip() for item in value if str(item).strip()]
    if isinstance(value, str):
        # Split by semicolon, trim whitespace, remove empty strings
        return [item.strip() for item in value.split(';') if item.strip()]
    return [] # Return empty list if input is not list or string

def infer_gre_required(data):
    """Infers if GRE is required based on presence of score or percentile data."""
    fields_to_check = [
        'gre_average_verbal_reasoning_score',
        'gre_average_quantitative_reasoning_score',
        'gre_average_analytical_writing_score',
        'gre_average_verbal_reasoning_percentile',
        'gre_average_quantitative_reasoning_percentile',
        'gre_average_analytical_writing_percentile'
    ]
    for field in fields_to_check:
        value = safe_get(data, field)
        if value is not None: # If any field has a value (not 'No info provided')
            # Check if the value itself isn't just text indicating absence
            if isinstance(value, str) and value.strip().lower() not in ['no information provided', 'none', '']:
                 # Check if it looks like a number (score or percentile)
                 if re.search(r'\d', value):
                     return True
            elif isinstance(value, (int, float)): # Direct numeric value
                 return True

    # Also check the explicit text field as a fallback, though less reliable
    std_test_text = safe_get(data, 'standardized_tests_required', '').lower()
    if 'gre' in std_test_text or 'graduate record examination' in std_test_text:
        # Avoid cases like "GRE not required"
        if 'not required' not in std_test_text and 'will not accept' not in std_test_text:
             return True

    return False # Default to False if no scores/percentiles found


# --- Main Command ---

class Command(BaseCommand):
    help = 'Loads program data from JSON files in a specified directory into the database, overwriting existing data.'

    def add_arguments(self, parser):
        parser.add_argument('json_dir', type=str, help='The directory containing the JSON files.')

    def handle(self, *args, **options):
        json_dir = options['json_dir']

        if not os.path.isdir(json_dir):
            raise CommandError(f"Directory '{json_dir}' does not exist.")

        logger.info(f"Starting program data load from directory: {json_dir}")
        program_count = 0
        error_count = 0

        # Get list of JSON files to process for progress reporting
        json_files = [f for f in os.listdir(json_dir) if f.endswith(".json")]
        total_files = len(json_files)
        self.stdout.write(f"Found {total_files} JSON files to process.")

        try:
            with transaction.atomic():
                # Clear existing data - Use with caution!
                self.stdout.write(self.style.WARNING("Deleting existing School and related Program data..."))
                logger.warning("Deleting existing School and related Program data...")
                # Deleting Schools should cascade to Programs and their related requirements
                School.objects.all().delete()
                self.stdout.write("Existing data deleted.")
                logger.info("Existing data deleted.")

                # Process JSON files
                for i, filename in enumerate(json_files):
                    file_path = os.path.join(json_dir, filename)
                    # Print progress to standard output
                    self.stdout.write(f"Processing file {i + 1}/{total_files}: {filename}")
                    # Keep logger info as well for log files
                    logger.info(f"Processing file {i + 1}/{total_files}: {filename}")

                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)

                        # --- Create School ---
                        school_name = safe_get(data, 'program_name_from_list', f'Unknown School from {filename}')
                        school_location = safe_get(data, 'address_full', 'Unknown Location')
                        school_website = safe_get(data, 'program_website')
                        school_mission = safe_get(data, 'mission_statement', '')

                        school, created = School.objects.get_or_create(
                            name=school_name,
                            defaults={
                                'location': school_location,
                                'mission_statement': school_mission,
                                'website': school_website or ''
                            }
                        )
                        log_prefix = "  "
                        if created:
                            logger.debug(f"{log_prefix}Created School: {school.name}")
                        else:
                            # Update existing school details if needed (optional)
                            school.location = school_location
                            school.mission_statement = school_mission
                            school.website = school_website or ''
                            school.save()
                            logger.debug(f"{log_prefix}Found/Updated School: {school.name}")


                        # --- Create Program ---
                        program_name = safe_get(data, 'program_name_scraped', f'Unknown Program from {filename}')
                        program_url = safe_get(data, 'detail_page_url')
                        app_deadline_str = safe_get(data, 'application_deadline')
                        start_month_str = safe_get(data, 'start_month')
                        class_size_str = safe_get(data, 'estimated_incoming_class_size')

                        program, created = Program.objects.update_or_create(
                            school=school,
                            name=program_name, # Use scraped name as potentially more specific
                            defaults={
                                'description': safe_get(data, 'unique_program_features', ''),
                                'url': program_url or '',
                                'application_deadline': parse_date(app_deadline_str),
                                'program_start_date': parse_date(start_month_str),
                                'class_size': parse_int(class_size_str, 0),
                                'average_gpa': None, # Set explicitly to None as we added null=True
                            }
                        )
                        if created:
                            logger.debug(f"{log_prefix}  Created Program: {program.name}")
                        else:
                            logger.debug(f"{log_prefix}  Updated Program: {program.name}")
                        program_count += 1


                        # --- Create Prerequisite Courses ---
                        prereqs_data = safe_get(data, 'prerequisites', {})
                        prereq_time_limit = safe_get(data, 'prereq_time_limit', 'Not specified')
                        PrerequisiteCourse.objects.filter(program=program).delete() # Clear old ones first
                        if isinstance(prereqs_data, dict):
                            for course_name, details in prereqs_data.items():
                                if isinstance(details, dict):
                                    PrerequisiteCourse.objects.create(
                                        program=program,
                                        course_name=course_name.strip(),
                                        credits=parse_credits(safe_get(details, 'credits_info')),
                                        lab_required=parse_boolean(safe_get(details, 'lab_required', False)) or False, # Default False if None
                                        time_limit=prereq_time_limit
                                    )
                            logger.debug(f"{log_prefix}    Created {len(prereqs_data)} Prerequisite Courses.")


                        # --- Create Healthcare Experience ---
                        hc_exp_status_str = safe_get(data, 'healthcare_experience')
                        hc_hours = safe_get(data, 'healthcare_experience_hours_needed')
                        hc_time_limit = safe_get(data, 'healthcare_experience_time_limit')
                        hc_paid_status_str = safe_get(data, 'paid_health_care_status')
                        hc_volunteer_status_str = safe_get(data, 'volunteer_health_care_status')
                        hc_virtual_accepted_str = safe_get(data, 'virtual_health_care_accepted')

                        HealthcareExperience.objects.update_or_create(
                            program=program,
                            defaults={
                                'required': parse_required_status(hc_exp_status_str), # This now returns True/False/None
                                'hours_needed': parse_int(hc_hours),
                                'time_limit': hc_time_limit or 'Not specified',
                                'paid_accepted': parse_required_status(hc_paid_status_str) is not False, # Assume accepted if not explicitly 'Not Required'
                                'volunteer_accepted': parse_required_status(hc_volunteer_status_str) is not False,
                                'virtual_accepted': parse_boolean(hc_virtual_accepted_str) or False, # Default False if None
                                'status': hc_exp_status_str or 'Not specified'
                            }
                        )
                        logger.debug(f"{log_prefix}    Created/Updated Healthcare Experience.")


                        # --- Create Patient Care Experience ---
                        pc_hours = safe_get(data, 'direct_patient_care_hours_needed')
                        pc_time_limit = safe_get(data, 'direct_patient_care_time_limit')
                        pc_paid_status_str = safe_get(data, 'paid_direct_patient_care_status')
                        pc_volunteer_status_str = safe_get(data, 'volunteer_direct_patient_care_status')
                        pc_paid_required = parse_required_status(pc_paid_status_str)
                        pc_volunteer_required = parse_required_status(pc_volunteer_status_str)
                        # Infer overall requirement, allowing None if both inputs are None
                        pc_required = None
                        if pc_paid_required is True or pc_volunteer_required is True:
                             pc_required = True
                        elif pc_paid_required is False or pc_volunteer_required is False:
                             # If at least one is explicitly False (or Recommended), overall is False unless the other is True
                             pc_required = False
                        # If both are None (ambiguous), pc_required remains None

                        PatientCareExperience.objects.update_or_create(
                            program=program,
                            defaults={
                                'required': pc_required, # Can be True, False, or None now
                                'hours_needed': parse_int(pc_hours),
                                'time_limit': pc_time_limit or 'Not specified',
                                'paid_accepted': pc_paid_required is not False,
                                'volunteer_accepted': pc_volunteer_required is not False,
                                'paid_status': pc_paid_status_str or 'Not specified',
                                'volunteer_status': pc_volunteer_status_str or 'Not specified'
                            }
                        )
                        logger.debug(f"{log_prefix}    Created/Updated Patient Care Experience.")


                        # --- Create Shadowing Requirement ---
                        shadow_pa_str = safe_get(data, 'shadowing_pa_status')
                        shadow_physician_str = safe_get(data, 'shadowing_physician_status')
                        shadow_other_str = safe_get(data, 'shadowing_other_provider_status')
                        shadow_virtual_str = safe_get(data, 'virtual_shadowing_accepted')

                        ShadowingRequirement.objects.update_or_create(
                            program=program,
                            defaults={
                                'pa_shadowing': shadow_pa_str or 'Not specified',
                                'physician_shadowing': shadow_physician_str or 'Not specified',
                                'other_shadowing': shadow_other_str or 'Not specified',
                                'virtual_accepted': parse_boolean(shadow_virtual_str) or False # Default False if None
                            }
                        )
                        logger.debug(f"{log_prefix}    Created/Updated Shadowing Requirement.")


                        # --- Create GRE Requirement ---
                        GRERequirement.objects.update_or_create(
                            program=program,
                            defaults={
                                'required': infer_gre_required(data), # Use inferred boolean
                                'verbal_score': safe_get(data, 'gre_average_verbal_reasoning_score', ''),
                                'quantitative_score': safe_get(data, 'gre_average_quantitative_reasoning_score', ''),
                                'analytical_score': safe_get(data, 'gre_average_analytical_writing_score', ''),
                                'verbal_percentile': safe_get(data, 'gre_average_verbal_reasoning_percentile', ''),
                                'quantitative_percentile': safe_get(data, 'gre_average_quantitative_reasoning_percentile', ''),
                                'analytical_percentile': safe_get(data, 'gre_average_analytical_writing_percentile', '')
                            }
                        )
                        logger.debug(f"{log_prefix}    Created/Updated GRE Requirement.")


                        # --- Create GPA Requirement ---
                        GPARequirement.objects.update_or_create(
                            program=program,
                            defaults={
                                'minimum_overall': safe_get(data, 'minimum_overall_gpa_required', 'Not specified'),
                                'minimum_prereq': safe_get(data, 'minimum_prereq_gpa_required', 'Not specified'),
                                'minimum_science': safe_get(data, 'minimum_science_gpa_required', 'Not specified')
                            }
                        )
                        logger.debug(f"{log_prefix}    Created/Updated GPA Requirement.")


                        # --- Create Interview Requirement ---
                        interview_types_raw = safe_get(data, 'types_of_interviews', [])
                        interview_types_list = parse_list_from_string(interview_types_raw)
                        onsite_req_str = safe_get(data, 'required_onsite_interview')
                        onsite_required = parse_boolean(onsite_req_str)

                        InterviewRequirement.objects.update_or_create(
                            program=program,
                            defaults={
                                # Infer required if onsite is Yes OR if there's anything in the types list
                                'required': onsite_required or bool(interview_types_list),
                                'onsite_required': onsite_required or False, # Default False if None
                                'mmi_used': any('mmi' in item.lower() or 'multiple mini' in item.lower() for item in interview_types_list),
                                'interview_types': interview_types_list
                            }
                        )
                        logger.debug(f"{log_prefix}    Created/Updated Interview Requirement.")


                        # --- Create Application Requirement ---
                        supp_app_str = safe_get(data, 'supplemental_application')
                        supp_fee_waiver_str = safe_get(data, 'supplemental_application_fee_waiver')

                        ApplicationRequirement.objects.update_or_create(
                            program=program,
                            defaults={
                                'caspa_required': parse_boolean(safe_get(data, 'caspa_member')) or False, # Default False if None
                                'caspa_deadline': parse_date(safe_get(data, 'application_deadline')),
                                'caspa_verification_requirement': safe_get(data, 'deadline_requirement', 'Not specified'),
                                'supplemental_required': parse_boolean(supp_app_str) is not False, # True if 'Yes' or ambiguous, False only if 'No'
                                'supplemental_deadline': parse_date(safe_get(data, 'supplemental_deadline')),
                                'supplemental_fee': safe_get(data, 'supplemental_application_fee', 'Not specified'),
                                'fee_waiver_available': parse_boolean(supp_fee_waiver_str) or False, # Default False if None or 'No'
                                'admission_type': safe_get(data, 'admission_type', 'Not specified')
                            }
                        )
                        logger.debug(f"{log_prefix}    Created/Updated Application Requirement.")


                        # --- Create Other Requirement ---
                        daca_str = safe_get(data, 'daca_status_applicants_considered')
                        transfer_str = safe_get(data, 'transfer_students_accepted')
                        out_of_state_str = safe_get(data, 'out_of_state_students_accepted')
                        international_str = safe_get(data, 'international_applicants_accepted')
                        international_link = safe_get(data, 'international_application_link')
                        international_req_text = international_link if international_link else international_str

                        OtherRequirement.objects.update_or_create(
                            program=program,
                            defaults={
                                'daca_accepted': parse_boolean(daca_str) or False, # Default False if None
                                'veteran_support': safe_get(data, 'support_for_veterans', 'Not specified'),
                                'transfer_accepted': parse_boolean(transfer_str) or False, # Default False if None
                                'out_of_state_accepted': parse_boolean(out_of_state_str) or False, # Default False if None
                                'international_accepted': parse_boolean(international_str) or False, # Default False if None
                                'international_requirements': international_req_text or 'Not specified'
                            }
                        )
                        logger.debug(f"{log_prefix}    Created/Updated Other Requirement.")


                        # --- Create Recommendation Requirement ---
                        ref_types_raw = safe_get(data, 'types_of_references_required', [])
                        ref_types_list = parse_list_from_string(ref_types_raw)

                        RecommendationRequirement.objects.update_or_create(
                            program=program,
                            defaults={
                                'number_required': safe_get(data, 'minimum_reference_letters_required', 'Not specified'),
                                'types_required': ref_types_list,
                                'specific_requirements': '' # No source data found
                            }
                        )
                        logger.debug(f"{log_prefix}    Created/Updated Recommendation Requirement.")


                    except json.JSONDecodeError as e:
                        logger.error(f"Error decoding JSON from file {filename}: {e}")
                        self.stdout.write(self.style.ERROR(f"Error decoding JSON from file {filename}: {e}"))
                        error_count += 1
                    except Exception as e:
                        logger.error(f"Error processing file {filename}: {e}", exc_info=True) # Log traceback
                        self.stdout.write(self.style.ERROR(f"Error processing file {filename}: {e}"))
                        error_count += 1

        except Exception as e:
            logger.error(f"An error occurred during the database transaction: {e}", exc_info=True)
            raise CommandError(f"Failed to load program data due to error: {e}")

        logger.info(f"Program data load process finished.")
        logger.info(f"Successfully processed {program_count} programs.")
        if error_count > 0:
            logger.warning(f"Encountered errors while processing {error_count} files.")
        else:
            logger.info("No errors encountered during file processing.")

        self.stdout.write(self.style.SUCCESS(f'Finished loading data.'))
        self.stdout.write(self.style.SUCCESS(f'Successfully processed {program_count} programs.'))
        if error_count > 0:
             self.stdout.write(self.style.WARNING(f'Encountered errors processing {error_count} files. Check logs for details.'))