# Generated by Django 5.0.1 on 2025-07-30 21:25

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('gradapp', '0034_add_normalized_search_fields'),
    ]

    operations = [
        migrations.AlterField(
            model_name='attritiondata',
            name='attrition_rate',
            field=models.Char<PERSON>ield(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='attritiondata',
            name='graduation_rate',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='matriculantdemographics',
            name='american_indian_count',
            field=models.Char<PERSON>ield(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='matriculantdemographics',
            name='asian_count',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='matriculantdemographics',
            name='black_count',
            field=models.Char<PERSON><PERSON>(blank=True, max_length=50, null=True),
        ),
        migrations.<PERSON><PERSON><PERSON>ield(
            model_name='matriculantdemographics',
            name='female_count',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='matriculantdemographics',
            name='gender_unknown_count',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='matriculantdemographics',
            name='hawaiian_pacific_count',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='matriculantdemographics',
            name='hispanic_count',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='matriculantdemographics',
            name='male_count',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='matriculantdemographics',
            name='non_binary_count',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='matriculantdemographics',
            name='other_count',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='matriculantdemographics',
            name='white_count',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='pancepassrate',
            name='national_pass_rate',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='pancepassrate',
            name='program_pass_rate',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
    ]
