{"name": "grad-app-match", "version": "0.1.0", "private": true, "dependencies": {"@emotion/styled": "^11.14.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "framer-motion": "^11.18.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^7.1.1", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://127.0.0.1:8000"}