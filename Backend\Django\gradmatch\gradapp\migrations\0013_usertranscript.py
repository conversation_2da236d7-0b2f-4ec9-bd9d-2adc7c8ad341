# Generated by Django 5.0.1 on 2025-01-27 00:28

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('gradapp', '0012_convert_remaining_to_textfield'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='UserTranscript',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('original_filename', models.CharField(max_length=255)),
                ('s3_key', models.CharField(max_length=512)),
                ('upload_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('status', models.CharField(choices=[('uploaded', 'Uploaded'), ('processing', 'Processing'), ('validated', 'Validated'), ('failed', 'Failed')], default='uploaded', max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('file_size', models.IntegerField(default=0)),
                ('mime_type', models.CharField(default='application/pdf', max_length=100)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transcripts', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-upload_date'],
            },
        ),
    ]
