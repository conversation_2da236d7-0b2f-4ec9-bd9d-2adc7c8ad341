# Generated by Django 5.0.1 on 2025-01-24 21:00

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('gradapp', '0007_delete_userprofile'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='program',
            name='prerequisites',
        ),
        migrations.AlterField(
            model_name='classprofile',
            name='program',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='class_profiles', to='gradapp.program'),
        ),
        migrations.CreateModel(
            name='ApplicationRequirement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('caspa_required', models.BooleanField(default=True)),
                ('caspa_deadline', models.DateField(null=True)),
                ('caspa_verification_requirement', models.TextField()),
                ('supplemental_required', models.BooleanField(default=False)),
                ('supplemental_deadline', models.DateField(null=True)),
                ('supplemental_fee', models.CharField(max_length=50)),
                ('fee_waiver_available', models.BooleanField(default=False)),
                ('admission_type', models.CharField(max_length=100)),
                ('program', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='application_requirements', to='gradapp.program')),
            ],
        ),
        migrations.CreateModel(
            name='GPARequirement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('minimum_overall', models.CharField(max_length=50)),
                ('minimum_prereq', models.CharField(max_length=50)),
                ('minimum_science', models.CharField(max_length=50)),
                ('program', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='gpa_requirements', to='gradapp.program')),
            ],
        ),
        migrations.CreateModel(
            name='GRERequirement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('required', models.CharField(max_length=100)),
                ('verbal_score', models.CharField(max_length=50)),
                ('quantitative_score', models.CharField(max_length=50)),
                ('analytical_score', models.CharField(max_length=50)),
                ('verbal_percentile', models.CharField(max_length=50)),
                ('quantitative_percentile', models.CharField(max_length=50)),
                ('analytical_percentile', models.CharField(max_length=50)),
                ('program', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='gre_requirements', to='gradapp.program')),
            ],
        ),
        migrations.CreateModel(
            name='HealthcareExperience',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('required', models.BooleanField(default=False)),
                ('hours_needed', models.IntegerField(blank=True, null=True)),
                ('time_limit', models.CharField(max_length=100)),
                ('paid_accepted', models.BooleanField(default=True)),
                ('volunteer_accepted', models.BooleanField(default=True)),
                ('virtual_accepted', models.BooleanField(default=False)),
                ('status', models.CharField(max_length=100)),
                ('program', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='healthcare_experience', to='gradapp.program')),
            ],
        ),
        migrations.CreateModel(
            name='InterviewRequirement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('required', models.BooleanField(default=True)),
                ('onsite_required', models.BooleanField(default=True)),
                ('mmi_used', models.BooleanField(default=False)),
                ('interview_types', models.JSONField()),
                ('program', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='interview_requirements', to='gradapp.program')),
            ],
        ),
        migrations.CreateModel(
            name='OtherRequirement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('daca_accepted', models.BooleanField(default=False)),
                ('veteran_support', models.CharField(max_length=100)),
                ('transfer_accepted', models.BooleanField(default=False)),
                ('out_of_state_accepted', models.BooleanField(default=True)),
                ('international_accepted', models.BooleanField(default=False)),
                ('international_requirements', models.TextField()),
                ('program', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='other_requirements', to='gradapp.program')),
            ],
        ),
        migrations.CreateModel(
            name='PatientCareExperience',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('required', models.BooleanField(default=False)),
                ('hours_needed', models.IntegerField(blank=True, null=True)),
                ('time_limit', models.CharField(max_length=100)),
                ('paid_accepted', models.BooleanField(default=True)),
                ('volunteer_accepted', models.BooleanField(default=True)),
                ('paid_status', models.CharField(max_length=100)),
                ('volunteer_status', models.CharField(max_length=100)),
                ('program', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='patient_care_experience', to='gradapp.program')),
            ],
        ),
        migrations.CreateModel(
            name='PrerequisiteCourse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('course_name', models.CharField(max_length=100)),
                ('credits', models.CharField(max_length=50)),
                ('lab_required', models.BooleanField(default=False)),
                ('time_limit', models.CharField(max_length=100)),
                ('program', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='prerequisite_courses', to='gradapp.program')),
            ],
        ),
        migrations.CreateModel(
            name='RecommendationRequirement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('number_required', models.CharField(max_length=50)),
                ('types_required', models.JSONField()),
                ('specific_requirements', models.TextField()),
                ('program', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='recommendation_requirements', to='gradapp.program')),
            ],
        ),
        migrations.CreateModel(
            name='ShadowingRequirement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('pa_shadowing', models.CharField(max_length=100)),
                ('physician_shadowing', models.CharField(max_length=100)),
                ('other_shadowing', models.CharField(max_length=100)),
                ('virtual_accepted', models.BooleanField(default=False)),
                ('program', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='shadowing_requirements', to='gradapp.program')),
            ],
        ),
    ]
