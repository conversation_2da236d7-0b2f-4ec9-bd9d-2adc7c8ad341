// App.js

import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'; // Removed Navigate, useAuth
import './App.css';
import LandingPage from './components/LandingPage';
import HomeHub from './components/HomeHub';
import UserProfileDashboard from './components/Dashboard';

import PAMatch from './components/PAMatch';
import { AuthProvider } from './contexts/AuthContext'; // Removed useAuth import
// import TranscriptUpload from './components/TranscriptUpload'; // Assuming TranscriptUpload is part of another component or route
import ProtectedRoute from './components/ProtectedRoute';
import LoginForm from './components/LoginForm'; // Import LoginForm
import RegisterForm from './components/RegisterForm'; // Import RegisterForm
import UpdatePassword from './components/UpdatePassword'; // Import UpdatePassword
import EmailVerification from './components/EmailVerification'; // Import EmailVerification
import { v4 as uuidv4 } from 'uuid';

function App() {
    // Session ID logic remains unchanged for now
    const [sessionId, setSessionId] = useState(() => {
        const existingSessionId = localStorage.getItem('sessionId');
        if (existingSessionId) {
            return existingSessionId;
        }
        const newSessionId = uuidv4();
        localStorage.setItem('sessionId', newSessionId);
        return newSessionId;
    });

    return (
        <Router>
            <AuthProvider> {/* AuthProvider wraps everything */}
                <Routes>
                    {/* Public Routes */}
                    <Route path="/" element={<LandingPage />} />
                    <Route path="/login" element={<LoginForm />} />
                    <Route path="/register" element={<RegisterForm />} />
                    {/* Route for handling password update after email link click */}
                    {/* Supabase handles the token verification from the URL */}
                    <Route path="/update-password" element={<UpdatePassword />} />
                    {/* Route for email verification */}
                    <Route path="/verify-email" element={<EmailVerification />} />

                    {/* Protected Routes */}
                    <Route
                        path="/home"
                        element={
                            <ProtectedRoute>
                                <HomeHub />
                            </ProtectedRoute>
                        }
                    />
                    <Route
                        path="/dashboard/:tabName?"
                        element={
                            <ProtectedRoute>
                                <UserProfileDashboard sessionId={sessionId} setSessionId={setSessionId} />
                            </ProtectedRoute>
                        }
                    />

                    <Route
                        path="/pamatch"
                        element={
                            <ProtectedRoute>
                                <PAMatch />
                            </ProtectedRoute>
                        }
                    />
                     {/* Add other protected routes here */}
                     {/* Example:
                     <Route
                         path="/transcript-upload"
                         element={
                             <ProtectedRoute>
                                 <TranscriptUpload />
                             </ProtectedRoute>
                         }
                     />
                     */}

                    {/* Optional: Add a catch-all route for 404 Not Found */}
                    {/* <Route path="*" element={<NotFoundComponent />} /> */}
                </Routes>
            </AuthProvider>
        </Router>
    );
}

export default App;
