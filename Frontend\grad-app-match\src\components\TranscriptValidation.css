.academic-summary {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.academic-summary h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.2em;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.summary-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.summary-item label {
    font-weight: 600;
    color: #566573;
    font-size: 0.9em;
}

.summary-item span {
    color: #2c3e50;
    font-size: 1em;
    padding: 5px 0;
}

.science-course {
    background-color: #e8f5e9;
}

.semester-section {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.semester-section h4 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.1em;
}

.semester-summary {
    margin-top: 15px;
    display: flex;
    gap: 20px;
}

.semester-summary p {
    color: #566573;
    font-size: 0.9em;
    margin: 0;
}

.transcript-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.transcript-table th,
.transcript-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #e1e8ed;
}

.transcript-table th {
    background-color: #f8f9fa;
    color: #2c3e50;
    font-weight: 600;
    font-size: 0.9em;
}

.transcript-table td {
    color: #2c3e50;
    font-size: 0.9em;
} 