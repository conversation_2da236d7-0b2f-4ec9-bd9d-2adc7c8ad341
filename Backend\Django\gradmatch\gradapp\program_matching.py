import os
import logging
from typing import List, Dict, Any
from .models import Program, TranscriptRecord, PrerequisiteSelection
from django.db.models import Q
import json
from langchain_openai import ChatOpenAI

# Initialize logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize LLM
llm = ChatOpenAI(
    model_name="o1-mini-2024-09-12",
    api_key=os.getenv("OPENAI_API_KEY")
)

def filter_eligible_programs(transcript_gpa: float, total_credits: float) -> List[Program]:
    """
    First pass filter using basic criteria from PostgreSQL.
    Shows programs where student's GPA is within 0.3 points of the average GPA.
    """
    try:
        logger.info(f"Filtering programs for GPA: {transcript_gpa}, Credits: {total_credits}")
        
        # Allow GPA within 0.3 points below average GPA
        gpa_threshold = 0.3
        eligible_programs = Program.objects.filter(
            Q(average_gpa__lte=transcript_gpa + gpa_threshold) & 
            Q(average_gpa__gte=transcript_gpa - gpa_threshold) |
            Q(average_gpa__isnull=True)
        ).select_related('school').all()
        
        logger.info(f"Found {len(eligible_programs)} programs after initial filtering")
        return eligible_programs
        
    except Exception as e:
        logger.error(f"Error in filter_eligible_programs: {str(e)}")
        return []

def check_lab_requirements(courses: List[Dict], prerequisite_name: str, lab_required: bool) -> bool:
    """
    Check if the courses satisfy a lab requirement for a given prerequisite
    
    Args:
        courses: List of course dictionaries
        prerequisite_name: Name of the prerequisite to check
        lab_required: Whether the prerequisite requires a lab
        
    Returns:
        bool: True if the lab requirement is satisfied, False otherwise
    """
    if not lab_required:
        # No lab required, so requirement is automatically satisfied
        return True
        
    # Check if any of the courses have a lab component
    for course in courses:
        if course.get('has_lab', False):
            # Found a course with lab component
            return True
            
    # If we're here, no course with lab was found but it was required
    return False

def check_credit_total(courses: List[Dict], required_credits: float) -> bool:
    """
    Check if the courses meet the required credit total
    
    Args:
        courses: List of course dictionaries
        required_credits: Required credit amount
        
    Returns:
        bool: True if total credits meets or exceeds required credits
    """
    # If no credit requirement, consider it met
    if not required_credits or required_credits <= 0:
        return True
        
    # Calculate total credits from courses
    total_credits = sum(float(course.get('credits', 0)) for course in courses)
    
    # Provide a 1-credit flexibility for lab requirements
    # (since labs sometimes get counted as part of the course)
    if any(course.get('has_lab', False) for course in courses):
        return total_credits >= (required_credits - 1)
    
    # Otherwise, require exact match or higher
    return total_credits >= required_credits

def batch_prerequisite_screening(transcript_courses: List[Dict], programs: List[Program]) -> List[Program]:
    """
    Quick screening of prerequisites using string matching with improved lab and credit checking
    """
    try:
        potential_matches = []
        logger.info(f"Screening {len(programs)} programs for prerequisite matches")
        
        for program in programs:
            prereqs_met = 0
            prereq_courses = program.prerequisite_courses.all()
            if not prereq_courses:
                continue
                
            required_prereqs = len(prereq_courses)
            
            # Check each prerequisite
            for prereq in prereq_courses:
                # Find matching courses for this prerequisite
                matching_courses = []
                prereq_name = prereq.course_name.lower()
                prereq_terms = set(term.lower() for term in prereq_name.split())
                
                for course in transcript_courses:
                    course_name = course['name'].lower()
                    if all(term in course_name for term in prereq_terms):
                        # Convert dictionary course to object-like structure for is_course_match
                        class CourseObj:
                            def __init__(self, course_dict):
                                self.course_name = course_dict['name']
                                self.course_code = course_dict.get('code', '')
                                self.credits = course_dict.get('credits', 0)
                                self.has_lab = course_dict.get('has_lab', False)
                                
                        matching_courses.append(CourseObj(course))
                
                # Check if matching courses meet the prerequisite using is_course_match
                if matching_courses and is_course_match(prereq, matching_courses):
                    prereqs_met += 1
            
            # If meets 70% threshold
            if required_prereqs > 0 and (prereqs_met / required_prereqs) >= 0.7:
                potential_matches.append(program)
                
        logger.info(f"Found {len(potential_matches)} potential program matches")
        return potential_matches
        
    except Exception as e:
        logger.error(f"Error in batch_prerequisite_screening: {str(e)}")
        return []

def parse_llm_response(response: str) -> List[Dict]:
    """
    Parse the LLM response into structured data
    """
    try:
        # Basic parsing - can be enhanced based on actual LLM response format
        return json.loads(response)
    except json.JSONDecodeError:
        logger.error("Failed to parse LLM response as JSON")
        return []

def detailed_analysis(transcript_data: Dict, shortlisted_programs: List[Program], batch_size: int = 5) -> List[Dict]:
    """
    Detailed LLM analysis for shortlisted programs
    """
    try:
        results = []
        total_programs = len(shortlisted_programs)
        logger.info(f"Starting detailed analysis for {total_programs} programs")
        
        # Process programs in batches
        for i in range(0, total_programs, batch_size):
            batch = shortlisted_programs[i:i + batch_size]
            
            # Create efficient prompt for batch
            prompt = f"""
            Analyze student eligibility for these PA programs.
            
            STUDENT TRANSCRIPT SUMMARY:
            - Overall GPA: {transcript_data['academic_summary']['cumulative_gpa']}
            - Science GPA: {transcript_data['academic_summary'].get('science_gpa', 'Not Available')}
            - Total Credits: {transcript_data['academic_summary'].get('total_credits_earned', 0)}
            
            Key Courses Completed:
            {json.dumps([{
                'name': course['name'],
                'grade': course['grade'],
                'credits': course['credits'],
                'has_lab': course.get('has_lab', False)  # Include lab information in prompt
            } for semester in transcript_data['semesters'] 
              for course in semester['courses']], indent=2)}
            
            PROGRAMS TO ANALYZE:
            {json.dumps([{
                'name': p.name,
                'prerequisites': [
                    {
                        'name': prereq.course_name,
                        'credits': prereq.credits,
                        'lab_required': prereq.lab_required,  # Include lab requirement info
                        'timeframe': prereq.time_limit
                    } for prereq in p.prerequisite_courses.all()
                ],
                'required_gpa': p.average_gpa,
                'location': p.school.location,
                'class_size': p.class_size
            } for p in batch], indent=2)}
            
            For each program, provide a JSON response with:
            1. "program_name": string
            2. "prerequisites_met": list of met prerequisites (include which courses meet each prerequisite)
            3. "prerequisites_missing": list of unmet prerequisites (specify why they're not met - missing lab, insufficient credits, etc.)
            4. "gpa_requirement_met": boolean (compare with required_gpa)
            5. "overall_eligible": boolean
            6. "recommendations": list of suggestions if not eligible
            
            Pay special attention to lab requirements and credit totals when determining if prerequisites are met.
            """
            
            try:
                # Get LLM analysis for batch
                response = llm.invoke(prompt)
                batch_results = parse_llm_response(response)
                results.extend(batch_results)
                logger.info(f"Processed batch of {len(batch)} programs")
                
            except Exception as batch_error:
                logger.error(f"Error processing batch: {str(batch_error)}")
                continue
        
        return results
        
    except Exception as e:
        logger.error(f"Error in detailed_analysis: {str(e)}")
        return []

def find_matching_programs(user_id: int) -> Dict[str, Any]:
    """
    Main function to find matching PA programs for a user
    """
    try:
        # 1. Get user's transcript
        transcript = TranscriptRecord.objects.filter(
            user_id=user_id,
            validation_status='approved'
        ).order_by('-updated_at').first()
        
        if not transcript:
            logger.warning(f"No approved transcript found for user {user_id}")
            return {
                'error': 'No approved transcript found',
                'matches': []
            }
        
        # 2. Initial SQL filtering
        eligible_programs = filter_eligible_programs(
            transcript.calculated_gpa,
            transcript.transcript_data['academic_summary'].get('total_credits', 0)
        )
        
        if not eligible_programs:
            logger.info(f"No programs found meeting basic criteria for user {user_id}")
            return {
                'message': 'No programs found meeting basic GPA and credit requirements',
                'matches': []
            }
        
        # 3. Get combined course information
        # Combine transcript courses and any saved prerequisite selections
        transcript_courses = [
            course for semester in transcript.transcript_data['semesters']
            for course in semester['courses']
        ]
        
        # Add saved prerequisite information
        saved_prereqs = PrerequisiteSelection.objects.filter(user_id=user_id)
        for prereq in saved_prereqs:
            # Check if this is a new course not in transcript_courses
            is_new = True
            for course in transcript_courses:
                if (course.get('code') == prereq.course_code and 
                    course.get('name') == prereq.course_name and
                    course.get('institution') == prereq.institution):
                    is_new = False
                    # Update the course with any additional info from prereq
                    if not course.get('has_lab') and prereq.has_lab:
                        course['has_lab'] = prereq.has_lab
                    break
            
            # If this is a new course, add it
            if is_new:
                transcript_courses.append({
                    'code': prereq.course_code,
                    'name': prereq.course_name,
                    'credits': prereq.credits,
                    'grade': prereq.grade,
                    'institution': prereq.institution,
                    'has_lab': prereq.has_lab if hasattr(prereq, 'has_lab') else False
                })
        
        # 4. Quick prerequisite screening with improved lab checking
        potential_matches = batch_prerequisite_screening(
            transcript_courses,
            eligible_programs
        )
        
        if not potential_matches:
            logger.info(f"No programs found after prerequisite screening for user {user_id}")
            return {
                'message': 'No programs found meeting prerequisite requirements',
                'matches': []
            }
        
        # 5. Detailed LLM analysis
        detailed_results = detailed_analysis(
            transcript.transcript_data,
            potential_matches
        )
        
        return {
            'message': 'Successfully analyzed program matches',
            'matches': detailed_results
        }
        
    except Exception as e:
        logger.error(f"Error in find_matching_programs: {str(e)}")
        return {
            'error': str(e),
            'matches': []
        }

def is_course_match(target_prereq, selected_courses):
    """Check if selected courses match a prerequisite requirement"""
    
    prereq_name = target_prereq.name.lower()
    prereq_credits = target_prereq.credits
    lab_required = target_prereq.lab_required
    
    # If no selected courses, not a match
    if not selected_courses:
        return False
        
    # Calculate total credits from selected courses
    total_credits = sum(float(course.credits) for course in selected_courses)
    
    # Check if total credits meet or exceed requirement
    if total_credits < prereq_credits:
        return False
        
    # Check if lab is required and exists in at least one course
    if lab_required:
        # First try to use the dedicated has_lab field if it exists
        has_lab_courses = [course for course in selected_courses if getattr(course, 'has_lab', False)]
        
        # If has_lab fields aren't found, fall back to name-based detection
        if not has_lab_courses:
            has_lab_courses = [
                course for course in selected_courses 
                if 'lab' in course.course_name.lower() or 
                   'laboratory' in course.course_name.lower() or 
                   course.course_code.lower().endswith('l')
            ]
            
        if not has_lab_courses:
            return False
            
    # If we reached this point, the selected courses match the prerequisite
    return True 