import React, { useEffect } from 'react';
import axios from 'axios';

const ProgramSearch = ({ onResults }) => {
    const handleShowAllSchools = async () => {
        console.log("1. Starting fetch...");
        try {
            const response = await axios.get('http://127.0.0.1:8000/api/schools/all');
            console.log("2. Response received:", response.data);
            
            if (response.data.status === 'success' && response.data.schools) {
                console.log("3. Valid schools data found:", response.data.schools);
                onResults(response.data.schools);
                console.log("4. Called onResults with schools data");
            }
        } catch (error) {
            console.error('Failed to fetch schools:', error);
        }
    };

    useEffect(() => {
        console.log("0. ProgramSearch mounted - calling handleShowAllSchools");
        handleShowAllSchools();
    }, []);

    return (
        <div className="program-search-form">
            <button onClick={handleShowAllSchools}>Refresh Schools List</button>
        </div>
    );
};

export default ProgramSearch;
