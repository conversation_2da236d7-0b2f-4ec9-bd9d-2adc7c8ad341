.dashboard-container {
    max-width: 1300px; /* Increase max-width slightly */
    margin: 0.5rem auto; /* Reduce top/bottom margin */
    padding: 1rem; /* Further reduce padding */
    background-color: #ffffff;
    min-height: 100vh;
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    font-size: 14px; /* Revert base font size back to previous value */
    position: relative; /* Make container the positioning context for absolute children */
}

.dashboard-header {
    text-align: center;
    margin-bottom: 3rem;
    color: #2c3e50;
}

.dashboard-header h1 {
    font-size: 1.8rem; /* Further reduce main heading */
    margin-bottom: 0.4rem;
}

.dashboard-header p {
    color: #6c757d;
    font-size: 0.9rem; /* Further reduce subheading */
}

.profile-form {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 2rem;
}

.profile-section {
    background-color: white;
    padding: 0.6rem 1rem; /* Even smaller section padding */
    margin-bottom: 0.8rem; /* Even smaller space between sections */
    border-radius: 6px; /* Slightly less rounded */
    border: 1px solid #e2e8f0; /* Add subtle border */
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05); /* Make shadow much subtler */
}

.profile-section h2 {
    color: #1a202c;
    font-size: 1.15rem; /* Revert section heading */
    font-weight: 600;
    margin-bottom: 1rem; /* Reduce space below heading */
    padding-bottom: 0; /* Remove padding */
    border-bottom: none; /* Remove border */
}

/* Transcript Management Section */
.profile-section .transcript-upload-container {
    padding: 0;
    max-width: 100%;
    margin: 0;
    box-shadow: none;
}

.profile-section .transcript-upload-container h2 {
    display: none; /* Hide the component's own heading since we're using the section heading */
}

.profile-section .upload-section {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
}

/* Ensure consistent styling with other form elements */
.profile-section .file-input-container {
    margin-bottom: 15px;
}

.profile-section .transcripts-list {
    background-color: transparent;
    padding: 0;
    box-shadow: none;
}

.profile-section .transcripts-list table {
    width: 100%;
    margin-top: 10px;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(210px, 1fr)); /* Even smaller columns */
    gap: 0.6rem; /* Further reduce grid gap */
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.2rem; /* Further reduce gap within form group */
}

.form-group label {
    font-weight: 500;
    color: #2d3748;
    font-size: 0.75rem; /* Even smaller label */
}

.form-group input,
.form-group select {
    padding: 0.3rem 0.5rem; /* Further reduce input padding */
    border: 1px solid #cbd5e0;
    border-radius: 3px; /* Slightly less rounded */
    font-size: 0.8rem; /* Smaller input font */
    font-size: 1rem;
    background-color: #fff; /* Ensure white background */
    transition: border-color 0.2s, box-shadow 0.2s; /* Add box-shadow transition */
}

.form-group input:focus,
.form-group select:focus {
    border-color: #4a5568; /* Darker gray focus border */
    outline: none;
    box-shadow: 0 0 0 1px #a0aec0; /* Subtle focus shadow */
}

.form-group input:disabled {
    background-color: #e9ecef;
    cursor: not-allowed;
}

.form-group small {
    color: #6c757d;
    font-size: 0.875rem;
}

.transcript-status {
    background-color: #f8f9fa;
    padding: 1.5rem;
    border-radius: 6px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.transcript-status h3 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.1rem;
}

.status-validated {
    color: #28a745;
    background-color: #d4edda;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.9rem;
}

.status-pending {
    color: #dc3545;
    background-color: #f8d7da;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.9rem;
}

/* General Button Styling */
button {
    padding: 0.3rem 0.8rem; /* Further reduce button padding */
    border-radius: 3px; /* Slightly less rounded */
    font-size: 0.8rem; /* Smaller button font */
    cursor: pointer;
    font-weight: 500;
    font-size: 0.95rem;
    transition: background-color 0.2s, transform 0.1s;
    border: 1px solid transparent; /* Base border */
}

button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Primary Action Button (e.g., Save Profile, Save Scores) */
.save-profile-btn, .save-gre-btn, .save-pa-cat-btn, .save-transcript-btn, .add-semester-btn, .add-course-btn {
    background-color: #2d3748; /* Charcoal */
    color: white;
    border-color: #2d3748;
}

.save-profile-btn:hover:not(:disabled),
.save-gre-btn:hover:not(:disabled),
.save-pa-cat-btn:hover:not(:disabled),
.save-transcript-btn:hover:not(:disabled),
.add-semester-btn:hover:not(:disabled),
.add-course-btn:hover:not(:disabled) {
    background-color: #1a202c; /* Darker Charcoal */
    border-color: #1a202c;
}

/* Secondary/Neutral Action Button (e.g., Add/Edit, Cancel, Back) */
.home-btn, .add-gre-btn, .edit-gre-btn, .cancel-gre-btn,
.add-pa-cat-btn, .edit-pa-cat-btn, .cancel-pa-cat-btn,
.toggle-manual-entry-btn, .add-course-btn /* Revisit add-course if needed */ {
    background-color: #edf2f7; /* Light Gray */
    color: #2d3748; /* Charcoal text */
    border-color: #cbd5e0; /* Gray border */
}

.home-btn:hover:not(:disabled),
.add-gre-btn:hover:not(:disabled),
.edit-gre-btn:hover:not(:disabled),
.cancel-gre-btn:hover:not(:disabled),
.add-pa-cat-btn:hover:not(:disabled),
.edit-pa-cat-btn:hover:not(:disabled),
.cancel-pa-cat-btn:hover:not(:disabled),
.toggle-manual-entry-btn:hover:not(:disabled),
.add-course-btn:hover:not(:disabled) {
    background-color: #e2e8f0; /* Slightly darker gray */
    border-color: #a0aec0;
}


/* Remove specific upload button style */
/* .upload-transcript-btn { ... } */
/* .upload-transcript-btn:hover { ... } */

.form-actions {
    margin-top: 2rem;
    display: flex;
    justify-content: flex-end;
}

/* Adjust save profile button size if needed, inherits base styles now */
.save-profile-btn {
     padding: 0.5rem 1.1rem; /* Smaller save button */
     font-size: 0.85rem;
}

/* Remove old hover style */
/* .save-profile-btn:hover { ... } */


.save-profile-btn:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
    transform: none;
}

.error-message {
    background-color: #f8d7da;
    color: #721c24;
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1.5rem;
}

.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.2rem;
    color: #6c757d;
}

.loading-indicator {
    text-align: center;
    padding: 20px;
    color: #666;
    font-style: italic;
}

/* Styling for read-only score display */
.display-grid.test-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); /* Adjust column width */
    gap: 0.8rem; /* Adjust gap */
    margin-bottom: 1rem; /* Space before actions */
    align-items: start; /* Align items to the top */
}

.display-grid.test-grid .display-item {
    display: flex;
    flex-direction: column; /* Stack label and value */
    gap: 0.2rem; /* Space between label and value */
}

.display-grid.test-grid .display-item label {
    font-weight: 500;
    color: #4a5568;
    font-size: 0.8rem; /* Smaller label font */
    margin-bottom: 0;
}

.display-grid.test-grid .display-item span {
    font-weight: 400;
    color: #1a202c;
    font-size: 0.9rem; /* Slightly larger value font */
}

.display-grid.test-grid .full-width-grid-item {
    grid-column: 1 / -1; /* Make test date span all columns */
}

/* Styling for test subsection actions (Edit/Save/Cancel buttons) */
.test-actions {
    margin-top: 0.5rem; /* Space above buttons */
    display: flex;
    gap: 0.8rem; /* Space between buttons */
    justify-content: flex-start; /* Align buttons to the left */
    flex-wrap: wrap; /* Allow buttons to wrap on smaller screens */
}

.test-actions button {
    padding: 0.4rem 0.9rem; /* Adjust button padding */
    font-size: 0.85rem; /* Adjust button font size */
}

/* Specific styles for test subsections */
.test-subsection {
    border: 1px solid #e2e8f0; /* Add border around each test section */
    border-radius: 6px;
    padding: 1rem; /* Add padding inside test section */
    margin-bottom: 1rem; /* Space between test sections */
    background-color: #f8f9fa; /* Light background for test sections */
}

.test-subsection h3 {
    font-size: 1rem; /* Smaller heading for test subsections */
    margin-top: 0;
    margin-bottom: 1rem; /* Space below heading */
    color: #2d3748;
    border-bottom: 1px solid #cbd5e0; /* Separator below heading */
    padding-bottom: 0.5rem;
}

/* Adjust form grid for editing state */
.form-grid.test-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); /* Match display grid columns */
    gap: 0.8rem;
    margin-bottom: 1rem;
}

.form-grid.test-grid .form-group {
    flex-direction: column; /* Stack label and input */
    gap: 0.2rem;
}

.form-grid.test-grid .form-group label {
    font-size: 0.8rem; /* Match display label font size */
}

.form-grid.test-grid .form-group input {
     font-size: 0.9rem; /* Match display value font size */
     padding: 0.4rem 0.6rem; /* Adjust input padding */
}

.form-grid.test-grid .full-width-grid-item {
    grid-column: 1 / -1; /* Make test date span all columns */
}

/* Specific styles for CASPer section */
.casper-grid {
    grid-template-columns: 1fr auto; /* Adjust grid for checkbox and date */
    align-items: center;
}

.casper-grid .form-group.casper-checkbox {
    flex-direction: row; /* Keep checkbox and label inline */
    align-items: center;
    gap: 0.5rem;
}

.casper-grid .form-group.casper-checkbox label {
    font-size: 0.9rem; /* Adjust font size */
    font-weight: 400;
    color: #1a202c;
}

.casper-grid .form-group.casper-date {
    flex-direction: row; /* Keep label and input inline */
    align-items: center;
    gap: 0.5rem;
}

.casper-grid .form-group.casper-date label {
    font-size: 0.8rem; /* Match other labels */
    font-weight: 500;
    color: #4a5568;
}

.casper-grid .display-item.casper-status,
.casper-grid .display-item.casper-date {
    flex-direction: row; /* Keep label and value inline for display */
    align-items: center;
    gap: 0.5rem;
}

.casper-grid .display-item.casper-status label,
.casper-grid .display-item.casper-date label {
     font-size: 0.8rem; /* Match other labels */
     font-weight: 500;
     color: #4a5568;
}

.casper-grid .display-item.casper-status span,
.casper-grid .display-item.casper-date span {
    font-size: 0.9rem; /* Match other values */
}

/* Specific styles for PA-CAT display items */
.display-item.pa-cat-item div {
    display: flex;
    flex-direction: column; /* Stack the spans vertically */
    gap: 0.1rem; /* Small space between the two lines */
}

.display-item.pa-cat-item div {
    display: flex;
    flex-direction: column; /* Stack the spans vertically */
    gap: 0.1rem; /* Small space between the two lines */
}

.no-transcripts-message {
    text-align: center;
    padding: 2rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    color: #6c757d;
}

.no-transcripts-message p {
    margin: 0;
    font-style: italic;
}

@media (max-width: 768px) {
    .dashboard-container {
        padding: 1rem;
    }

    .profile-form {
        padding: 1.5rem;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .transcript-status {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .form-actions {
        justify-content: center;
    }

    .save-profile-btn {
        width: 100%;
    }
}

.course-option {
    padding: 16px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    margin-bottom: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.course-option:hover {
    background-color: #f7fafc;
    border-color: #4299e1;
}

.course-option-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.course-option-header span:first-child {
    font-weight: 600;
    color: #2d3748;
}

.course-option-header span:last-child {
    color: #4a5568;
    font-size: 0.9em;
}

.course-option-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #718096;
    font-size: 0.9em;
}

.course-institution {
    color: #4a5568;
    font-size: 0.9em;
    margin-top: 4px;
    display: block;
}

.manual-transcript-section {
    margin-top: 20px;
    padding: 24px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.manual-transcript-section h3 {
    margin-top: 0;
    font-weight: 600;
    color: #2d3748;
    font-size: 1.3rem;
    margin-bottom: 1rem;
}

.toggle-manual-entry-btn {
    background-color: #4299e1;
    color: white;
    padding: 10px 18px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    margin-bottom: 16px;
    transition: all 0.2s ease;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

.toggle-manual-entry-btn:hover {
    background-color: #3182ce;
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.toggle-manual-entry-btn:active {
    transform: translateY(0);
    box-shadow: none;
}

.toggle-manual-entry-btn svg {
    width: 16px;
    height: 16px;
}

.manual-entry-form {
    padding: 24px;
    background-color: #f8fafc;
    border-radius: 8px;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
}

.institution-section {
    margin-bottom: 24px;
}

.institution-input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 16px;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.institution-input:focus {
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.2);
    outline: none;
}

.semester-section {
    margin-bottom: 24px;
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.semester-section h4 {
    margin-top: 0;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 16px;
    font-size: 1.1rem;
    border-bottom: 2px solid #e2e8f0;
    padding-bottom: 8px;
}

.semester-section h5 {
    font-weight: 500;
    color: #4a5568;
    margin-bottom: 12px;
    font-size: 1rem;
}

.semester-header {
    display: flex;
    gap: 16px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.semester-select,
.year-input,
.calendar-type-select {
    padding: 10px 14px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 15px;
    flex: 1;
    min-width: 120px;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.semester-select:focus,
.year-input:focus,
.calendar-type-select:focus {
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.2);
    outline: none;
}

.course-entry {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
    margin-bottom: 20px;
    align-items: center;
    background-color: #f8fafc;
    padding: 16px;
    border-radius: 8px;
    border: 1px dashed #cbd5e0;
}

.course-input,
.credits-input,
.grade-select {
    padding: 10px 14px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 15px;
    width: 100%;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.course-input:focus,
.credits-input:focus,
.grade-select:focus {
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.2);
    outline: none;
}

.add-course-btn,
.add-semester-btn,
.save-transcript-btn {
    background-color: #48bb78;
    color: white;
    padding: 10px 18px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: fit-content;
}

.add-course-btn:hover,
.add-semester-btn:hover,
.save-transcript-btn:hover {
    background-color: #38a169;
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.add-course-btn:active,
.add-semester-btn:active,
.save-transcript-btn:active {
    transform: translateY(0);
    box-shadow: none;
}

.science-toggle {
    display: flex;
    align-items: center;
    background-color: white;
    padding: 10px 14px;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.science-toggle label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    user-select: none;
    color: #4a5568;
    font-size: 15px;
    width: 100%;
}

.science-toggle input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
    accent-color: #4299e1;
}

.courses-list {
    margin: 16px 0;
    padding: 16px;
    background-color: white;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.course-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid #e2e8f0;
    transition: background-color 0.2s ease;
    border-radius: 4px;
}

.course-item:hover {
    background-color: #f7fafc;
}

.course-item:last-child {
    border-bottom: none;
}

.remove-course-btn {
    background-color: #f56565;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 6px 10px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.remove-course-btn:hover {
    background-color: #e53e3e;
    transform: translateY(-1px);
}

.remove-course-btn:active {
    transform: translateY(0);
}

.transcript-preview {
    margin-top: 30px;
    padding: 24px;
    background-color: white;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.transcript-preview h3 {
    color: #2d3748;
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 1.2rem;
}

.semester-preview {
    margin: 20px 0;
    padding: 20px;
    background-color: #f8fafc;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    border-left: 4px solid #4299e1;
}

.semester-preview h4 {
    color: #2d3748;
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 16px;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.semester-preview h4::before {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    background-color: #4299e1;
    border-radius: 50%;
}

.course-preview {
    display: flex;
    justify-content: space-between;
    padding: 12px;
    border-bottom: 1px solid #e2e8f0;
    transition: background-color 0.2s ease;
    border-radius: 4px;
}

.course-preview:hover {
    background-color: #f7fafc;
}

.course-preview:last-child {
    border-bottom: none;
}

.remove-semester-btn {
    background-color: #f56565;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 14px;
    cursor: pointer;
    margin-top: 16px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.remove-semester-btn:hover {
    background-color: #e53e3e;
    transform: translateY(-1px);
}

.remove-semester-btn:active {
    transform: translateY(0);
}

.transcript-summary {
    margin-top: 30px;
    padding: 20px;
    background-color: #ebf8ff;
    border-radius: 8px;
    border: 1px solid #bee3f8;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.transcript-summary h4 {
    color: #2b6cb0;
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 12px;
    font-size: 1.1rem;
}

.gpa-summary {
    margin-top: 12px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 16px;
}

.gpa-summary p {
    margin: 0;
    color: #2d3748;
    font-weight: 500;
    padding: 12px;
    background-color: white;
    border-radius: 6px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
}

.science-course {
    background-color: #f0f9ff;
    border-left: 3px solid #3182ce;
}

/* Add styles for the back button */
.back-to-home-btn {
  position: absolute; /* Position relative to the container */
  top: 1.5rem; /* Adjust vertical position */
  left: 1.5rem; /* Adjust horizontal position */
  background-color: #f8f9fa; /* Light background */
  color: #495057; /* Dark text */
  border: 1px solid #dee2e6; /* Subtle border */
  padding: 0.4rem 0.8rem; /* Adjust padding */
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.2s, border-color 0.2s, color 0.2s;
}

.back-to-home-btn:hover {
  background-color: #e9ecef;
  border-color: #ced4da;
  color: #212529;
}

/* Lab course highlighting */
.transcript-table tr.has-lab td,
.science-course.has-lab {
    background-color: #e6f7ff; /* Light blue background for lab courses */
}

/* Style for "Yes" in the Lab Included column */
.transcript-table .lab-yes {
    font-weight: bold;
    color: #0066cc;
}

/* Apply both styles for science courses with labs */
.transcript-table tr.science-course.has-lab td {
    background: linear-gradient(to right, #f0f7e6, #e6f7ff); /* Gradient from light green to light blue */
}

.semester-input-group, .course-input-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.semester-input-group label, .course-input-group label {
    font-size: 14px;
    font-weight: 500;
    color: #4a5568;
}

.institution-section label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #4a5568;
    margin-bottom: 6px;
}

.science-badge {
    display: inline-block;
    background-color: #ebf8ff;
    color: #3182ce;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-left: 8px;
}

/* Fix box-sizing for the course entry grid */
.course-entry {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
}

/* Allow the Add Course button to take full width on small screens */
@media (max-width: 768px) {
    .course-entry {
        grid-template-columns: 1fr;
    }
    
    .add-course-btn {
        width: 100%;
    }
}

/* CASPA GPA Tab Styling */
.caspa-gpa-card {
    background-color: #f8fafc;
    border: 1px solid #4299e1;
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.15);
}

.caspa-card-header {
    color: #2c5282;
    font-size: 1.3rem;
    border-bottom: 2px solid #4299e1;
    padding-bottom: 0.8rem;
    margin-bottom: 1.2rem;
}

.caspa-intro-text {
    font-size: 0.95rem;
    line-height: 1.5;
    color: #4a5568;
    margin-bottom: 1.2rem;
    padding: 0.8rem;
    background-color: #ebf8ff;
    border-radius: 6px;
    border-left: 4px solid #3182ce;
}

.important-note {
    display: block;
    margin-top: 0.5rem;
    font-weight: 500;
    color: #2c5282;
}

.caspa-gpa-legend {
    background-color: #f0f9ff;
    padding: 0.8rem;
    border-radius: 6px;
    margin-bottom: 1.2rem;
    border: 1px solid #bee3f8;
}

.legend-title {
    margin-top: 0;
    margin-bottom: 0.5rem;
    color: #2b6cb0;
}

.legend-list {
    margin: 0;
    padding-left: 1.5rem;
}

.legend-term {
    font-weight: 600;
    color: #2c5282;
}

.caspa-gpa-table-container {
    margin-bottom: 1.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.enhanced-table {
    width: 100%;
    border-collapse: collapse;
}

.enhanced-thead th {
    background-color: #4299e1;
    color: white;
    padding: 0.8rem;
    text-align: center;
    font-weight: 600;
}

.group-header {
    background-color: #63b3ed;
}

.science-header {
    background-color: #3182ce;
}

.non-science-header {
    background-color: #4299e1;
}

.total-header {
    background-color: #2b6cb0;
}

.sub-header {
    font-weight: 500;
    font-size: 0.9rem;
}

.gpa-header {
    font-weight: 600;
}

.data-cell {
    padding: 0.7rem;
    text-align: center;
    border: 1px solid #e2e8f0;
    background-color: white;
}

.category-header {
    font-weight: 600;
    color: #2d3748;
    background-color: #f7fafc;
    text-align: left;
    padding-left: 1rem;
}

/* Style for Overall row to make it stand out */
tr.summary-row.overall-row {
    background-color: #ebf8ff;
    font-weight: 600;
}

tr.summary-row.overall-row td {
    background-color: #ebf8ff;
    border-top: 2px solid #4299e1;
    border-bottom: 2px solid #4299e1;
}

tr.summary-row.overall-row td:first-child {
    color: #2b6cb0;
    font-size: 1.05rem;
}

tr.summary-row.overall-row .gpa-cell {
    background-color: #e6f7ff;
}

.gpa-detail-button {
    background: none;
    border: none;
    color: #2b6cb0;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    padding: 0.3rem 0.6rem;
    border-radius: 4px;
}

.gpa-detail-button:hover {
    background-color: #ebf8ff;
    text-decoration: underline;
}

.gpa-detail-button.primary-gpa {
    color: #2b5cb0;
    font-size: 1.1rem;
    font-weight: 700;
    background-color: #ebf8ff;
    border: 2px solid #3182ce;
    box-shadow: 0 2px 5px rgba(49, 130, 206, 0.15);
    padding: 0.4rem 0.8rem;
    border-radius: 6px;
    position: relative;
}

.gpa-detail-button.primary-gpa:after {
    content: "📊 Home";
    display: block;
    font-size: 0.7rem;
    font-weight: normal;
    color: #3182ce;
    margin-top: 0.2rem;
}

.gpa-detail-button.primary-gpa:hover {
    background-color: #e6f7ff;
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(49, 130, 206, 0.2);
}

tr.summary-row.overall-row .gpa-detail-button {
    color: #2c5282;
    font-size: 1.05rem;
}

/* Details Container Styling */
.caspa-gpa-details-container {
    background-color: #f8fafc;
    border: 1px solid #4299e1;
    border-radius: 8px;
    padding: 1.2rem;
    margin-top: 1.5rem;
    box-shadow: 0 4px 10px rgba(66, 153, 225, 0.15);
    position: relative;
}

/* Special styling for Overall total section */
.caspa-gpa-details-container.overall-total {
    background-color: #ebf8ff;
    border: 2px solid #3182ce;
    box-shadow: 0 6px 16px rgba(49, 130, 206, 0.2);
}

.caspa-gpa-details-container.overall-total::before {
    content: "Home Page for Academic Level Adjustments";
    display: block;
    background-color: #3182ce;
    color: white;
    padding: 0.5rem 1rem;
    margin: -1.2rem -1.2rem 1rem -1.2rem;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    font-weight: 600;
    text-align: center;
}

.caspa-gpa-details-header {
    color: #2c5282;
    font-size: 1.2rem;
    margin-top: 0;
    margin-bottom: 1rem;
    padding-bottom: 0.6rem;
    border-bottom: 1px solid #bee3f8;
}

.caspa-gpa-details-container.overall-total .caspa-gpa-details-header {
    color: #2b6cb0;
    font-size: 1.3rem;
    border-bottom: 2px solid #3182ce;
}

.caspa-gpa-calculation-highlight {
    background-color: #e6f7ff;
    padding: 0.8rem;
    border-radius: 6px;
    margin: 1rem 0;
    border-left: 4px solid #4299e1;
    font-size: 0.95rem;
    color: #2d3748;
}

.caspa-gpa-details-container.overall-total .caspa-gpa-calculation-highlight {
    background-color: #ebf8ff;
    border-left: 4px solid #3182ce;
    font-weight: 500;
}

.close-details-btn {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background-color: #e2e8f0;
    color: #4a5568;
    border: none;
    padding: 0.4rem 0.8rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.close-details-btn:hover {
    background-color: #cbd5e0;
    color: #2d3748;
}

/* Academic level override section styling */
.academic-level-override {
    background-color: #f0f9ff;
    border: 1px solid #bee3f8;
    border-radius: 6px;
    padding: 1rem;
    margin: 1.5rem 0;
}

.academic-level-override h4 {
    color: #2b6cb0;
    margin-top: 0;
    margin-bottom: 0.8rem;
    font-size: 1.1rem;
}

.caspa-gpa-details-container.overall-total .academic-level-override {
    background-color: #e6f7ff;
    border: 1px solid #90cdf4;
    border-left: 4px solid #3182ce;
}

.semester-group-header {
    font-weight: 600;
    color: #2c5282;
    margin-top: 1.5rem;
    margin-bottom: 0.6rem;
    padding-bottom: 0.4rem;
    border-bottom: 1px solid #bee3f8;
}

.caspa-gpa-details-container.overall-total .semester-group-header {
    color: #2b6cb0;
    border-bottom: 2px solid #90cdf4;
}