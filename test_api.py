import requests
import json

def test_api():
    try:
        print("Testing API endpoint...")
        
        # Test if server is running
        response = requests.get('http://localhost:8000/api/programs/', timeout=5)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ API endpoint is working!")
            data = response.json()
            if 'programs' in data:
                print(f"✅ Found {len(data['programs'])} programs")
                if len(data['programs']) > 0:
                    first_program = data['programs'][0]
                    print(f"✅ First program: {first_program.get('name', 'Unknown')}")
                    if 'program_data' in first_program:
                        print("✅ Enhanced data structure found")
                        program_data = first_program['program_data']
                        for section in program_data.keys():
                            print(f"   - {section}")
                    else:
                        print("❌ No enhanced data structure found")
            else:
                print("❌ No programs key in response")
        elif response.status_code == 401:
            print("❌ Authentication required")
        elif response.status_code == 403:
            print("❌ Permission denied")
        elif response.status_code == 500:
            print("❌ Internal server error")
            try:
                error_data = response.json()
                print(f"Error details: {error_data}")
            except:
                print(f"Raw response: {response.text}")
        else:
            print(f"❌ Unexpected status code: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Is Django running on localhost:8000?")
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    test_api()
