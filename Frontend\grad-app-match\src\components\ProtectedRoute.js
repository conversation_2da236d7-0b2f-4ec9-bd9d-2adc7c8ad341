import React from 'react'; // Import React
import { Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const ProtectedRoute = ({ children }) => {
  // Get currentUser and the loading state from the updated context
  const { currentUser, loading } = useAuth();

  // While checking the auth state, don't render anything or show a loader
  if (loading) {
    // Optional: return a loading spinner component here
    return <div>Loading...</div>; // Or return null;
  }

  // If not loading and no user is logged in, redirect to login
  if (!currentUser) {
    // Redirect to login page (assuming '/' is your public landing/login page)
    // Use replace to avoid adding the protected route to history when not logged in
    return <Navigate to="/" replace />; // Reverted redirect path back to /
  }

  // If not loading and user is logged in, render the child components
  return children;
};

export default ProtectedRoute;