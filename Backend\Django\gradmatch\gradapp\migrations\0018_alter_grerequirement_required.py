# Generated by Django 5.0.1 on 2025-03-31 22:33
import re
from django.db import migrations, models

def convert_gre_required_text_to_bool(apps, schema_editor):
    """
    Converts existing text values in GRERequirement.required to appropriate
    boolean values (defaulting to False for unknowns) before the field type is changed.
    Handles the existing NOT NULL constraint by avoiding NULL updates.
    """
    GRERequirement = apps.get_model('gradapp', 'GRERequirement')
    db_alias = schema_editor.connection.alias

    for req in GRERequirement.objects.using(db_alias).all():
        current_value = getattr(req, 'required', None)
        # Default to False instead of None to satisfy NOT NULL constraint during this step
        new_bool_value = False

        if isinstance(current_value, str):
            text_value = current_value.strip().lower()
            # Check for explicit requirement mentions, avoiding negations
            if ('required' in text_value and 'not required' not in text_value):
                 new_bool_value = True
            # Check for GRE mentions, avoiding negations
            elif ('gre' in text_value or 'graduate record' in text_value) and \
                 ('not required' not in text_value and 'will not accept' not in text_value):
                 new_bool_value = True
            # If text indicates not required or is known non-required text
            elif text_value in ('no information provided', 'none', 'not required', ''):
                 new_bool_value = False
            # Add other specific text checks if needed
            # else: # Keep default False for any other unrecognized text

        elif isinstance(current_value, bool):
             # If it's somehow already a boolean, keep it
             new_bool_value = current_value
        # else: Keep default False if current_value is None or not str/bool

        # Update the field directly using update()
        GRERequirement.objects.using(db_alias).filter(pk=req.pk).update(required=new_bool_value)


def reverse_convert_gre_required_bool_to_text(apps, schema_editor):
    """
    Reverts boolean values back to a default text representation if needed.
    """
    GRERequirement = apps.get_model('gradapp', 'GRERequirement')
    db_alias = schema_editor.connection.alias

    # Convert True back to 'Inferred Required', False/None back to 'Inferred Not Required'
    for req in GRERequirement.objects.using(db_alias).all():
         current_value = getattr(req, 'required', None)
         new_text_value = 'Inferred Not Required' # Default text for False/NULL
         if current_value is True:
             new_text_value = 'Inferred Required'

         GRERequirement.objects.using(db_alias).filter(pk=req.pk).update(required=new_text_value)


class Migration(migrations.Migration):

    dependencies = [
        ('gradapp', '0017_alter_prerequisiteselection_unique_together_and_more'),
    ]

    operations = [
        # Step 1: Run Python code to convert existing text data to True/False (defaulting False)
        migrations.RunPython(convert_gre_required_text_to_bool, reverse_code=reverse_convert_gre_required_bool_to_text),

        # Step 2: Alter the field type now that the data is compatible (True/False)
        # It also sets null=True, blank=True for future flexibility, even though we avoided NULLs here.
        migrations.AlterField(
            model_name='grerequirement',
            name='required',
            field=models.BooleanField(blank=True, null=True),
        ),
    ]
