.program-matching-skeleton {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.matching-progress {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.progress-header h3 {
    margin: 0;
    color: #1e40af;
    font-size: 18px;
    font-weight: 600;
}

.progress-text {
    color: #6b7280;
    font-weight: 500;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background-color: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 12px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    border-radius: 4px;
    transition: width 0.3s ease;
    animation: shimmer 2s infinite;
}

.progress-description {
    color: #6b7280;
    margin: 0;
    font-size: 14px;
}

.skeleton-sections {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.skeleton-section {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.skeleton-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e5e7eb;
}

.skeleton-title {
    height: 24px;
    width: 200px;
    background: linear-gradient(90deg, #f3f4f6, #e5e7eb, #f3f4f6);
    border-radius: 4px;
    animation: shimmer 2s infinite;
}

.skeleton-count {
    height: 20px;
    width: 60px;
    background: linear-gradient(90deg, #f3f4f6, #e5e7eb, #f3f4f6);
    border-radius: 12px;
    animation: shimmer 2s infinite;
}

.skeleton-programs {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.skeleton-program {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    background: #fafafa;
}

.skeleton-program-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.skeleton-program-name {
    height: 20px;
    width: 250px;
    background: linear-gradient(90deg, #f3f4f6, #e5e7eb, #f3f4f6);
    border-radius: 4px;
    animation: shimmer 2s infinite;
}

.skeleton-status-badge {
    height: 24px;
    width: 80px;
    background: linear-gradient(90deg, #f3f4f6, #e5e7eb, #f3f4f6);
    border-radius: 12px;
    animation: shimmer 2s infinite;
}

.skeleton-requirements {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
}

.skeleton-requirement {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
}

.skeleton-req-label {
    height: 16px;
    width: 120px;
    background: linear-gradient(90deg, #f3f4f6, #e5e7eb, #f3f4f6);
    border-radius: 4px;
    animation: shimmer 2s infinite;
}

.skeleton-req-value {
    height: 16px;
    width: 40px;
    background: linear-gradient(90deg, #f3f4f6, #e5e7eb, #f3f4f6);
    border-radius: 4px;
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

/* Responsive design */
@media (max-width: 768px) {
    .program-matching-skeleton {
        padding: 16px;
    }
    
    .matching-progress {
        padding: 20px;
    }
    
    .progress-header {
        flex-direction: column;
        gap: 8px;
        text-align: center;
    }
    
    .skeleton-requirements {
        grid-template-columns: 1fr;
    }
    
    .skeleton-program-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
}