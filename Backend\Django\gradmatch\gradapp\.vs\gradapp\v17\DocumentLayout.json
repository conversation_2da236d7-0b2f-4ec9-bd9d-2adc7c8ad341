{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\asdfasdfasdfasdfasdfasdf\\gradmatch\\gradapp\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\source\\repos\\asdfasdfasdfasdfasdfasdf\\gradmatch\\gradapp\\langchain_service.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:langchain_service.py||{8B382828-6202-11D1-8870-0000F87579D2}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "langchain_service.py", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\asdfasdfasdfasdfasdfasdf\\gradmatch\\gradapp\\langchain_service.py", "RelativeDocumentMoniker": "langchain_service.py", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\asdfasdfasdfasdfasdfasdf\\gradmatch\\gradapp\\langchain_service.py", "RelativeToolTip": "langchain_service.py", "ViewState": "AQIAAO0BAAAAAAAAAAAAAAICAAANAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2024-11-24T01:28:09.781Z", "EditorCaption": ""}]}]}]}