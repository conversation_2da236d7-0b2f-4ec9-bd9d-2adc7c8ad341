import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import ChangeEmail from './ChangeEmail';
import DeleteAccount from './DeleteAccount';
import styled from '@emotion/styled';

const Container = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
`;

const Section = styled.div`
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  padding: 1.5rem;
`;

const SectionTitle = styled.h3`
  margin: 0 0 1rem 0;
  color: #1f2937;
`;

const Button = styled.button`
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.375rem;
  font-size: 1rem;
  cursor: pointer;
  margin-right: 1rem;
  margin-bottom: 0.5rem;
  transition: background-color 0.2s;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const PrimaryButton = styled(Button)`
  background-color: #3b82f6;
  color: white;

  &:hover:not(:disabled) {
    background-color: #2563eb;
  }
`;

const DangerButton = styled(Button)`
  background-color: #dc2626;
  color: white;

  &:hover:not(:disabled) {
    background-color: #b91c1c;
  }
`;

const SecondaryButton = styled(Button)`
  background-color: #6b7280;
  color: white;

  &:hover:not(:disabled) {
    background-color: #4b5563;
  }
`;

const InfoText = styled.p`
  color: #6b7280;
  margin: 0.5rem 0;
  font-size: 0.875rem;
`;

const StatusBadge = styled.span`
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  
  ${props => props.verified ? `
    background-color: #dcfce7;
    color: #166534;
  ` : `
    background-color: #fef3c7;
    color: #92400e;
  `}
`;

const Modal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const AccountSecurity = () => {
  const [activeModal, setActiveModal] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');
  
  const { currentUser, supabase, resetPasswordForEmail } = useAuth();

  const handlePasswordReset = async () => {
    setIsLoading(true);
    setMessage('');

    try {
      await resetPasswordForEmail(currentUser.email);
      setMessage('Password reset email sent! Check your inbox.');
    } catch (err) {
      console.error('Password reset error:', err);
      setMessage('Failed to send password reset email. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendVerification = async () => {
    setIsLoading(true);
    setMessage('');

    try {
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: currentUser.email
      });

      if (error) throw error;
      
      setMessage('Verification email sent! Check your inbox.');
    } catch (err) {
      console.error('Resend verification error:', err);
      setMessage('Failed to send verification email. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Container>
      <h2>Account Security</h2>

      {message && (
        <div style={{ 
          padding: '1rem', 
          backgroundColor: '#ecfdf5', 
          color: '#059669',
          borderRadius: '6px',
          marginBottom: '1rem'
        }}>
          {message}
        </div>
      )}

      {/* Account Information */}
      <Section>
        <SectionTitle>Account Information</SectionTitle>
        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginBottom: '1rem' }}>
          <strong>Email:</strong> {currentUser?.email}
          <StatusBadge verified={currentUser?.email_confirmed_at}>
            {currentUser?.email_confirmed_at ? 'Verified' : 'Unverified'}
          </StatusBadge>
        </div>
        
        <div style={{ marginBottom: '1rem' }}>
          <strong>Account Created:</strong> {new Date(currentUser?.created_at).toLocaleDateString()}
        </div>

        <div style={{ marginBottom: '1rem' }}>
          <strong>Last Sign In:</strong> {currentUser?.last_sign_in_at ? 
            new Date(currentUser.last_sign_in_at).toLocaleDateString() : 'Never'}
        </div>
      </Section>

      {/* Email Management */}
      <Section>
        <SectionTitle>Email Settings</SectionTitle>
        <InfoText>Manage your email address and verification status.</InfoText>
        
        <PrimaryButton 
          onClick={() => setActiveModal('changeEmail')}
          disabled={isLoading}
        >
          Change Email Address
        </PrimaryButton>

        {!currentUser?.email_confirmed_at && (
          <SecondaryButton 
            onClick={handleResendVerification}
            disabled={isLoading}
          >
            {isLoading ? 'Sending...' : 'Resend Verification Email'}
          </SecondaryButton>
        )}
      </Section>

      {/* Password Management */}
      <Section>
        <SectionTitle>Password Settings</SectionTitle>
        <InfoText>Reset your password or update security settings.</InfoText>
        
        <PrimaryButton 
          onClick={handlePasswordReset}
          disabled={isLoading}
        >
          {isLoading ? 'Sending...' : 'Send Password Reset Email'}
        </PrimaryButton>
      </Section>

      {/* Account Actions */}
      <Section>
        <SectionTitle>Account Actions</SectionTitle>
        <InfoText>Manage your account data and access.</InfoText>
        
        <DangerButton 
          onClick={() => setActiveModal('deleteAccount')}
          disabled={isLoading}
        >
          Delete Account
        </DangerButton>
        
        <InfoText style={{ color: '#dc2626' }}>
          ⚠️ Account deletion is permanent and cannot be undone.
        </InfoText>
      </Section>

      {/* Modals */}
      {activeModal === 'changeEmail' && (
        <Modal>
          <ChangeEmail onCancel={() => setActiveModal(null)} />
        </Modal>
      )}

      {activeModal === 'deleteAccount' && (
        <Modal>
          <DeleteAccount onCancel={() => setActiveModal(null)} />
        </Modal>
      )}
    </Container>
  );
};

export default AccountSecurity;