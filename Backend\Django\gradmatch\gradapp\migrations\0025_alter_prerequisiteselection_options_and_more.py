# Generated by Django 5.0.1 on 2025-05-09 03:00

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('gradapp', '0024_programmatchingresult'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='prerequisiteselection',
            options={'ordering': ['-updated_at']},
        ),
        migrations.AlterModelOptions(
            name='transcriptrecord',
            options={},
        ),
        migrations.RemoveIndex(
            model_name='prerequisiteselection',
            name='gradapp_pre_user_id_b32cb5_idx',
        ),
        migrations.AddField(
            model_name='prerequisiteselection',
            name='has_lab',
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name='prerequisiteselection',
            name='course_code',
            field=models.Char<PERSON><PERSON>(max_length=50),
        ),
        migrations.AlterField(
            model_name='prerequisiteselection',
            name='course_name',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='prerequisiteselection',
            name='credits',
            field=models.DecimalField(decimal_places=2, max_digits=5),
        ),
        migrations.AlterField(
            model_name='prerequisiteselection',
            name='grade',
            field=models.CharField(max_length=5),
        ),
        migrations.AlterField(
            model_name='prerequisiteselection',
            name='institution',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='prerequisiteselection',
            name='prerequisite_name',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='prerequisiteselection',
            name='term',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='prerequisiteselection',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='prerequisite_selections', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='prerequisiteselection',
            name='year',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='transcriptrecord',
            name='calculated_gpa',
            field=models.DecimalField(decimal_places=2, default=0.0, max_digits=3),
        ),
        migrations.AlterField(
            model_name='transcriptrecord',
            name='science_gpa',
            field=models.DecimalField(decimal_places=2, default=0.0, max_digits=3, null=True),
        ),
        migrations.AlterField(
            model_name='transcriptrecord',
            name='student_name',
            field=models.CharField(blank=True, max_length=255),
        ),
        migrations.AlterField(
            model_name='transcriptrecord',
            name='transcript_data',
            field=models.JSONField(null=True),
        ),
        migrations.AlterField(
            model_name='transcriptrecord',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transcripts', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='transcriptrecord',
            name='validation_status',
            field=models.CharField(choices=[('pending', 'Pending Validation'), ('approved', 'Approved'), ('rejected', 'Rejected')], default='pending', max_length=20),
        ),
        migrations.AlterField(
            model_name='usertranscript',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='raw_transcripts', to=settings.AUTH_USER_MODEL),
        ),
    ]
