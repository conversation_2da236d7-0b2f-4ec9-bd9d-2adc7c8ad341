# Generated by Django 5.0.1 on 2025-05-09 02:26

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('gradapp', '0023_delete_programmatchresult'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ProgramMatchingResult',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('timestamp', models.DateTimeField(auto_now=True)),
                ('matching_results', models.J<PERSON><PERSON>ield(null=True)),
                ('data_hash', models.Char<PERSON>ield(max_length=64)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='matching_results', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Program Matching Result',
                'verbose_name_plural': 'Program Matching Results',
                'ordering': ['-timestamp'],
            },
        ),
    ]
