.academic-summary {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.academic-summary h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.2em;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 8px;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.summary-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.summary-item label {
    font-weight: 600;
    color: #566573;
    font-size: 0.9em;
}

.summary-item span {
    color: #2c3e50;
    font-size: 1.1em;
    padding: 5px 0;
    font-weight: 500;
} 