from django.db import models
from django.contrib.auth import get_user_model
import uuid

User = get_user_model()

class UserProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    supabase_user_id = models.UUIDField(unique=True, null=True, blank=True, db_index=True, help_text="Supabase Auth User ID")
    first_name = models.CharField(max_length=100, blank=True)
    last_name = models.CharField(max_length=100, blank=True)
    date_of_birth = models.DateField(null=True, blank=True)
    undergrad_start_date = models.DateField(null=True, blank=True)
    gender = models.CharField(max_length=20, blank=True)
    ethnicity = models.CharField(max_length=50, blank=True)
    direct_patient_care_hours = models.IntegerField(default=0)
    shadowing_hours = models.IntegerField(default=0)
    # Add prerequisite class fields
    human_anatomy = models.Char<PERSON>ield(max_length=50, blank=True)
    human_physiology = models.CharField(max_length=50, blank=True)
    microbiology_with_lab = models.CharField(max_length=50, blank=True)
    medical_terminology = models.CharField(max_length=50, blank=True)
    statistics = models.CharField(max_length=50, blank=True)
    organic_chemistry = models.CharField(max_length=50, blank=True)
    genetics = models.CharField(max_length=50, blank=True)
    psychology = models.CharField(max_length=50, blank=True)
    biochemistry = models.CharField(max_length=50, blank=True)
    english = models.CharField(max_length=50, blank=True)
    social_science = models.CharField(max_length=50, blank=True)
    humanities = models.CharField(max_length=50, blank=True)
    behavioral_science = models.CharField(max_length=50, blank=True)
    inorganic_chemistry = models.CharField(max_length=50, blank=True)
    physics = models.CharField(max_length=50, blank=True)
    sociology = models.CharField(max_length=50, blank=True)

    # GRE Scores
    gre_verbal_score = models.IntegerField(null=True, blank=True)
    gre_quantitative_score = models.IntegerField(null=True, blank=True)
    gre_analytical_writing_score = models.DecimalField(max_digits=2, decimal_places=1, null=True, blank=True) # e.g., 4.5
    gre_verbal_percentile = models.IntegerField(null=True, blank=True)
    gre_quantitative_percentile = models.IntegerField(null=True, blank=True)
    gre_analytical_writing_percentile = models.IntegerField(null=True, blank=True)

    # Other Standardized Tests Taken?
    has_taken_casper = models.BooleanField(default=False)
    has_taken_pa_cat = models.BooleanField(default=False)

    # PA-CAT Scores
    pa_cat_anatomy_ss = models.IntegerField(null=True, blank=True)
    pa_cat_anatomy_pr = models.IntegerField(null=True, blank=True)
    pa_cat_physiology_ss = models.IntegerField(null=True, blank=True) # Physiology separate? Or combined A&P? Assuming separate for now based on example.
    pa_cat_physiology_pr = models.IntegerField(null=True, blank=True)
    pa_cat_biology_ss = models.IntegerField(null=True, blank=True)
    pa_cat_biology_pr = models.IntegerField(null=True, blank=True)
    pa_cat_chemistry_ss = models.IntegerField(null=True, blank=True)
    pa_cat_chemistry_pr = models.IntegerField(null=True, blank=True)
    pa_cat_composite_ss = models.IntegerField(null=True, blank=True)
    pa_cat_composite_pr = models.IntegerField(null=True, blank=True)

    # Test Dates
    gre_test_date = models.DateField(null=True, blank=True)
    pa_cat_test_date = models.DateField(null=True, blank=True)
    casper_test_date = models.DateField(null=True, blank=True)
    # Let's add separate Anatomy and Physiology fields for now. If they are always reported combined, we can adjust.

    def __str__(self):
        return f"{self.user.email}'s Profile" 