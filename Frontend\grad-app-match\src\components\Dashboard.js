import React, { useState, useEffect, useMemo } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate, useLocation, useParams } from 'react-router-dom'; // Import useParams
import axios from 'axios';
import '../styles/Dashboard.css';
import TranscriptValidation from './TranscriptValidation';
import TranscriptUpload from './TranscriptUpload';
import PrerequisiteClasses from './PrerequisiteClasses';
// Import a potential utility file if created later
// import { calculateCaspaGpas } from '../utils/gpaCalculations';

// --- CASPA Subject Categorization Data ---
const caspaSubjects = {
    'Biochemistry': { category: 'Biochemistry', isScience: true, isBCP: true, keywords: ['BIOCHEMISTRY', 'BIOLOGICAL CHEMISTRY', 'BIOMOLECULES', 'METABOLISM', 'PHYSICAL BIOCHEMISTRY', 'PHYSIOLOGICAL CHEMISTRY'] },
    'Biology/Zoology': { category: 'Biology/Zoology', isScience: true, isBCP: true, keywords: ['ANATOMY', 'ANATOMY & PHYSIOLOGY', 'BACTERIOLOGY', 'BIODIVERSITY', 'BIOLOGY', 'BIOMEDICAL SCIENCE', 'BIOPHYSICS', 'BOTANY', 'CELLULAR BIOLOGY', 'CELLULAR & MOLECULAR BIOLOGY', 'CELLULAR PHYSIOLOGY', 'ECOLOGY', 'EMBRYOLOGY', 'ENDOCRINOLOGY', 'ENTOMOLOGY', 'EUKARYOTICS', 'EVOLUTION', 'GENETICS', 'GENOMICS', 'HEMATOLOGY', 'HISTOLOGY', 'IMMUNOLOGY', 'MICROBIOLOGY', 'MICRO-ORGANISMS', 'MOLECULAR BIOLOGY', 'NEURAL MECHANISMS', 'NEUROBIOLOGY', 'NEUROLOGY', 'NEUROSCIENCE', 'PARASITOLOGY', 'PATHOLOGY', 'PATHOPHYSIOLOGY', 'PHYSIOLOGICAL SCIENCE', 'PHYSIOLOGY', 'PLANT BIOLOGY', 'POPULATION BIOLOGY', 'PROKARYOTE PHYSIOLOGY', 'VIROLOGY', 'ZOOLOGY'] },
    'Inorganic Chemistry': { category: 'Inorganic Chemistry', isScience: true, isBCP: true, keywords: ['ANALYTIC CHEMISTRY', 'ARMCHAIR CHEMISTRY', 'BASIC CHEMISTRY', 'CHEMISTRY', 'INORGANIC CHEMISTRY', 'INTRO TO CHEMISTRY', 'MEDICAL CHEMISTRY', 'PHARMACEUTICAL CHEMISTRY', 'PHYSICAL CHEMISTRY'] },
    'Organic Chemistry': { category: 'Organic Chemistry', isScience: true, isBCP: true, keywords: ['BIOORGANIC CHEMISTRY', 'ORGANIC CHEMISTRY', 'STRUCTURE & BONDS', 'STRUCTURE & REACTION'] },
    'Physics': { category: 'Physics', isScience: true, isBCP: true, keywords: ['ELECTRICITY', 'ELECTRICITY & LIGHT', 'HEAT', 'LIGHT', 'MAGNETISM', 'MECHANICAL HEAT', 'MECHANICS', 'NUCLEAR PHYSICS', 'PHYSICS', 'THERMODYNAMICS'] },
    'Other Science': { category: 'Other Science', isScience: true, isBCP: false, keywords: ['ACUPUNCTURE', 'AEROSPACE STUDIES', 'ANIMAL BEHAVIOR', 'ANIMAL SCIENCE', 'ASTRONOMY', 'ATHLETIC TRAINING', 'ATMOSPHERIC SCIENCE', 'AUDIOLOGY', 'BIOBEHAVIORAL HEALTH', 'BIOENGINEERING', 'BIOETHICS', 'BIOLOGICAL ANTHROPOLOGY', 'BIOMECHANICS', 'BIOMEDICAL ENGINEERING', 'BIOTECHNOLOGY', 'CHEMICAL ENGINEERING', 'CHINESE INTERNAL MEDICINE', 'CHIROPRACTIC MEDICINE', 'CIVIL ENGINEERING', 'CLINICAL LABORATORY SCIENCES', 'CLINICAL LABORATORY TECHNICIAN', 'CLINICAL SCIENCES', 'COMMUNICATION SCIENCE DISORDERS', 'CONSERVATION', 'CPR', 'CYTOLOGY/CYTOTECHNOLOGY', 'DENTAL ASSISTING', 'DENTAL HYGIENE', 'DENTISTRY', 'DIETETICS', 'DISEASE', 'EARTH SCIENCE', 'ELECTRONICS', 'EMERGENCY MED-TECH', 'ENGINEERING', 'ENVIRONMENTAL HEALTH', 'ENVIRONMENTAL SCIENCE', 'EPIDEMIOLOGY', 'EXERCISE PHYSIOLOGY', 'EXERCISE SCIENCE', 'FIRST AID', 'FOOD SCIENCE', 'FORENSIC SCIENCES', 'GEOLOGY', 'GYNECOLOGY', 'HEALTH', 'HEALTH SCIENCE', 'HEALTH SERVICES', 'HEALTH TECHNOLOGY', 'HERBOLOGY', 'HUMAN GEOGRAPHY', 'HUMAN MOVEMENT', 'KINESIOLOGY', 'LIMNOLOGY', 'LOCOMOTION STUDIES', 'MARINE BIOLOGY', 'MARINE SCIENCE', 'MEDICAL LABORATORY SCIENCES', 'MEDICAL LABORATORY TECHNICIAN', 'MEDICAL TECHNOLOGY', 'MEDICINE', 'METEOROLOGY', 'MOTOR CONTROL', 'MOVEMENT SCIENCE', 'NATURAL RESOURCES', 'NATURAL SCIENCE', 'NUCLEAR', 'NURSING', 'NUTRITION', 'OCCUPATIONAL THERAPY', 'OCEANOGRAPHY', 'OPTICS', 'OPTOMETRY', 'ORIENTAL MEDICINE', 'OTHER HEALTH PROF', 'PARAMEDIC', 'PHARMACOLOGY', 'PHARMACY', 'PHLEBOTOMY', 'PHYSICAL ANTHROPOLOGY', 'PHYSICAL GEOGRAPHY', 'PHYSICAL SCIENCES', 'PHYSICAL THERAPY', 'PHYSICAL THERAPY ASSISTANT', 'PHYSICIAN ASSISTANT', 'PLANT', 'PLANT SCIENCES', 'PODIATRY', 'PUBLIC HEALTH', 'RADIATION THERAPY', 'RADIOLOGY', 'REHABILITATION', 'RESPIRATORY THERAPY', 'SONOGRAPHY', 'SPEECH AND HEARING DISORDERS', 'SPEECH AND HEARING SCIENCE', 'SPORTS MEDICINE', 'SPORTS SCIENCES', 'SURGERY TECH', 'THERAPEUTIC EXERCISE', 'TOXICOLOGY', 'VETERINARY'] },
    'Behavioral Sciences': { category: 'Behavioral Sciences', isScience: false, isBCP: false, keywords: ['ABNORMAL PSYCHOLOGY', 'ADOLESCENT PSYCHOLOGY', 'AFRICAN AMERICAN STUDIES', 'ANTHROPOLOGY', 'ASIAN AMERICAN STUDIES', 'BEHAVIORAL SCIENCES', 'BIOLOGICAL PSYCHOLOGY', 'BRAIN AND BEHAVIOR', 'CHILD PSYCHOLOGY', 'COGNITIVE PSYCHOLOGY', 'COGNITIVE SCIENCE', 'COMMUNITY HEALTH', 'COUNSELING', 'CRIMINAL JUSTICE', 'CRIMINOLOGY', 'CULTURAL GEOGRAPHY', 'CULTURAL STUDIES', 'DEATH & DYING', 'DEVELOPMENTAL BIOLOGY', 'DEVELOPMENTAL PSYCHOLOGY', 'EDUCATIONAL PSYCHOLOGY', 'ETHNIC STUDIES', 'FAMILY STUDIES', 'GROWTH & DEVELOPMENT', 'HUMAN BEHAVIOR', 'HUMAN DEVELOPMENT', 'HUMAN SEXUALITY', 'LATINA/O AMERICAN STUDIES', 'LIFE SPAN DEVELOPMENT', 'MARRIAGE/FAMILY', 'MULTICULTURAL STUDIES', 'NATIVE AMERICAN STUDIES', 'NEUROPSYCHOLOGY', 'PSYCHOBIOLOGY', 'PSYCHOLOGY', 'PSYCHOPHARMACOLOGY', 'SENSORIMOTOR DEVELOPMENT', 'SOCIAL ECOLOGY', 'SOCIAL JUSTICE STUDIES', 'SOCIAL PSYCHOLOGY', 'SOCIAL SCIENCE', 'SOCIAL SCIENCES', 'SOCIAL WELFARE', 'SOCIAL WORK', 'SOCIOLOGY', 'SPORTS PSYCHOLOGY'] },
    'English': { category: 'English', isScience: false, isBCP: false, keywords: ['BASIC COMPOSITION', 'COMPOSITION', 'CREATIVE WRITING', 'ENGLISH', 'ENGLISH AS A SECOND LANGUAGE', 'JOURNALISM', 'LITERATURE', 'MEDICAL TERMINOLOGY', 'POETRY', 'RHETORIC', 'TECHNICAL WRITING', 'WRITING'] },
    'Mathematics': { category: 'Mathematics', isScience: false, isBCP: false, keywords: ['ALGEBRA', 'ANALYTICAL GEOMETRY', 'BEHAVIORAL STATISTICS', 'BIOINFORMATICS', 'BIOMETRY/BIOMETRICS', 'BIOSTATISTICS', 'CALCULUS', 'CHEMICAL MATH', 'COMPUTER PROGRAMING', 'COMPUTER SCIENCE', 'INFORMATICS', 'LINEAR ALGEBRA', 'MATH ANALYSIS', 'MATHEMATICS', 'QUANTITATIVE ANALYSIS', 'STATISTICS', 'TRIGONOMETRY'] },
    'Other Non-Science': { category: 'Other Non-Science', isScience: false, isBCP: false, keywords: ['ACCOUNTING', 'ACTING', 'ADMINISTRATION OF JUSTICE', 'AGRIBUSINESS', 'AGRICULTURE', 'AMERICAN SIGN LANGUAGE', 'AMERICAN STUDIES', 'ARABIC', 'ARCHEOLOGY', 'ARCHITECTURE', 'ART', 'ART HISTORY', 'BANKING', 'BASIC COMPUTER SKILLS/COMPUTER BASICS', 'BIBLE LITERATURE', 'BILINGUAL/BICULTURAL STUDIES', 'BUSINESS', 'BUSINESS ADMINISTRATION', 'CLASSICS', 'COMMUNICATIONS', 'COMMUNITY PLANNING', 'COMPUTER APPLICATIONS', 'DANCE', 'DEBATE', 'DRAMA', 'EARLY CHILDHOOD EDUCATION', 'ECONOMICS', 'EDUCATION', 'ENVIRONMENTAL STUDIES', 'ERGONOMICS', 'ETHICS', 'EXCEL', 'FAMILY AND CONSUMER SCIENCES', 'FASHION DESIGN', 'FILM/MEDIA', 'FINANCE', 'FIRE AID/FIREFIGHTING', 'FOLKLORE', 'FOOD MANAGEMENT', 'FOREIGN LANGUAGES', 'FORESTRY', 'FRENCH', 'GENDER STUDIES', 'GEOGRAPHIC INFORMATION SYSTEMS', 'GEOGRAPHY', 'GERMAN', 'GERONTOLOGY/AGING', 'GLOBAL STUDIES', 'GOVERNMENT', 'GRAPHIC DESIGN', 'HEALTH EDUCATION', 'HEALTH SCIENCE ADMINISTRATION', 'HISTORY', 'HONORS CORE', 'HORTICULTURE', 'HUMANITIES', 'HYGIENE', 'INFORMATION SYSTEMS', 'INFORMATION TECHNOLOGY', 'INTELLECTUAL HERITAGE', 'INTERNET', 'LAW', 'LAW ENFORCEMENT', 'LIBRARY SCIENCE', 'LINGUISTICS', 'LOGIC', 'MANAGEMENT', 'MARKETING', 'MASSAGE', 'MEDIA STUDIES', 'MEDICAL ETHICS', 'MILITARY SCIENCE', 'MOSAIC', 'MUSIC', 'OFFICE TECHNOLOGY', 'ORGANIZATIONAL STUDIES', 'ORIENTATION', 'PERSONAL HEALTH', 'PHILOSOPHY', 'PHONETICS', 'PHOTOGRAPHY', 'PHYSICAL EDUCATION', 'POLITICAL SCIENCE', 'PUBLIC ADMINISTRATION', 'PUBLIC AFFAIRS/ADMINISTRATION', 'PUBLIC POLICY', 'PUBLIC RELATIONS', 'PUBLIC SPEAKING', 'QUALITATIVE ANALYSIS', 'QUEER STUDIES', 'RANGE MANAGEMENT', 'READING', 'RECREATION', 'RELIGION', 'SCIENCE READING/WRITING', 'SEXUALITY STUDIES', 'SPANISH', 'SPECIAL TOPICS', 'SPORTS ADMINISTRATION', 'TEACHING SCIENCE', 'TEST CREDIT - NO SUBJECT', 'THEATER', 'THEATER LITERATURE', 'THEOLOGY', 'TYPING', 'URBAN PLANNING', 'WESTERN CIVILIZATION', 'WILDLIFE', 'WOMEN\'S STUDIES', 'WORD PROCESSING'] }
};

// Pre-process keywords for faster lookup
const subjectKeywordMap = new Map();
Object.values(caspaSubjects).forEach(subjectInfo => {
    subjectInfo.keywords.forEach(keyword => {
        subjectKeywordMap.set(keyword, subjectInfo); // Map keyword string to the subject info object
    });
});
// --- End CASPA Data ---


const UserProfileDashboard = ({ sessionId, setSessionId }) => {
    // Get currentUser AND supabase client instance from the updated AuthContext
    const { currentUser, supabase, loading: authLoading } = useAuth(); // Get auth loading state
    const navigate = useNavigate();
    const params = useParams(); // Get route parameters
    const location = useLocation(); // Keep for now, might be used elsewhere
    const [profileLoading, setProfileLoading] = useState(true); // Renamed state
    const [transcriptsLoading, setTranscriptsLoading] = useState(true);
    const [error, setError] = useState(null);
    const [isProcessing, setIsProcessing] = useState(false);
    const [transcriptData, setTranscriptData] = useState(null); // For data passed to validation
    const [processingTranscript, setProcessingTranscript] = useState(null); // Transcript being processed/validated
    const [s3Transcripts, setS3Transcripts] = useState([]); // Uploaded files list
    const [existingTranscripts, setExistingTranscripts] = useState([]); // Validated/saved transcript records
    const [validationStatus, setValidationStatus] = useState(null);
    const [validatedTranscript, setValidatedTranscript] = useState(null); // Data returned from validation
    const [isTranscriptExpanded, setIsTranscriptExpanded] = useState(true); // For verified analysis section - expanded by default
    // Initialize profileData with empty defaults
    const [profileData, setProfileData] = useState({
        firstName: '',
        lastName: '',
        email: '', // Will be populated by fetchProfileData or currentUser effect
        dateOfBirth: '',
        gender: '',
        address: '',
        city: '',
        state: '',
        zipCode: '',
        // Academic History
        primary_undergrad_institution: '', // New field
        undergrad_start_year: '', // New field (expecting YYYY format)
        undergrad_end_date: '', // New field (Undergraduate end date, YYYY-MM-DD)
        // Experience
        directPatientCareHours: '',
        shadowingHours: '',
        // Initialize GRE fields
        gre_verbal_score: '',
        gre_quantitative_score: '',
        gre_analytical_writing_score: '',
        gre_verbal_percentile: '',
        gre_quantitative_percentile: '',
        gre_analytical_writing_percentile: '',
        gre_test_date: '',
        // Initialize PA-CAT fields
        pa_cat_anatomy_ss: '',
        pa_cat_anatomy_pr: '',
        pa_cat_physiology_ss: '',
        pa_cat_physiology_pr: '',
        pa_cat_biology_ss: '',
        pa_cat_biology_pr: '',
        pa_cat_chemistry_ss: '',
        pa_cat_chemistry_pr: '',
        pa_cat_composite_ss: '',
        pa_cat_composite_pr: '',
        pa_cat_test_date: '',
        // Initialize CASPer fields
        has_taken_casper: false,
        casper_test_date: ''
    });
    const [expandedSchools, setExpandedSchools] = useState({}); // For expanding individual school transcripts
    const [showManualEntry, setShowManualEntry] = useState(false);
    const [manualTranscript, setManualTranscript] = useState({
        institution: '',
        semesters: []
    });
    const [manualTranscriptSaveMessage, setManualTranscriptSaveMessage] = useState('');
    const [isManualTranscriptSaving, setIsManualTranscriptSaving] = useState(false);
    const [currentSemester, setCurrentSemester] = useState({
        term: '',
        year: '',
        calendarType: 'Semester',
        courses: []
    });
    const [currentCourse, setCurrentCourse] = useState({
        code: '',
        name: '',
        credits: '',
        grade: 'A',
        is_science: false // Keep for manual entry fallback
    });
    const [saveMessage, setSaveMessage] = useState(''); // General profile save message
    const [isSaving, setIsSaving] = useState(false); // General saving indicator
    const [isEditingGREScores, setIsEditingGREScores] = useState(false);
    const [isEditingPaCatScores, setIsEditingPaCatScores] = useState(false);
    const [isEditingCasperDate, setIsEditingCasperDate] = useState(false);
    // Initialize activeTab state possibilities
    const getInitialTab = () => {
        const tabFromUrl = params.tabName; // Get tabName from route params
        const validTabs = ['profile', 'transcripts', 'caspa', 'prerequisites'];
        if (tabFromUrl && validTabs.includes(tabFromUrl)) {
            return tabFromUrl;
        } else if (!tabFromUrl) {
            // If no tabName is in the path (e.g., /dashboard), default to profile
            return 'profile';
        }
        // If tabName is invalid, default to profile (could also redirect to /dashboard)
        console.warn(`Invalid tab name in URL: ${tabFromUrl}. Defaulting to profile.`);
        // Optional: navigate('/dashboard', { replace: true }); // Redirect on invalid tab
        return 'profile';
    };

    const [activeTab, setActiveTab] = useState(getInitialTab); // Initialize with simplified function call
    const [caspaGpas, setCaspaGpas] = useState(null); // State for calculated CASPA GPAs
    const [detailedViewKey, setDetailedViewKey] = useState(null); // e.g., 'Freshman-Science'
    const [detailedViewCourses, setDetailedViewCourses] = useState([]); // Courses for the detailed view
    // Manual override categories for academic levels
    const [overrideCategories, setOverrideCategories] = useState({});
    const [manualCourseLevels, setManualCourseLevels] = useState({}); // State for individual course level overrides
    const [savedCourseKey, setSavedCourseKey] = useState(null); // State to track the most recently saved course key - WILL BE REMOVED
    const [hasUnsavedOverrides, setHasUnsavedOverrides] = useState(false); // Track unsaved manual level changes
    const [isSavingOverrides, setIsSavingOverrides] = useState(false); // Loading state for bulk save
    const [overrideSaveMessage, setOverrideSaveMessage] = useState(''); // Message for bulk save status

    const levelGroupingMap = {
        'Transfer/Pre-College': 'Pre-Undergraduate',
        'Undergraduate': 'Undergraduate',
        'Post-Baccalaureate/Graduate': 'Post-Baccalaureate',
        'Unknown': 'Pre-Undergraduate'
    };
    const groupingOptions = ['Pre-Undergraduate', 'Undergraduate', 'Post-Baccalaureate', 'Graduate Coursework']; // Used for dropdowns
    const academicLevelOptions = ['Undergraduate', 'Post-Baccalaureate', 'Pre-Undergraduate', 'Graduate Coursework', 'Do not count towards CASPA GPA']; // Options for course override
    const DO_NOT_COUNT_VALUE = "DO_NOT_COUNT"; // Backend value for exclusion

    // Helper function to identify AP/IB classes
    const isAPorIBCourse = (course) => {
        // Check course code for AP or IB prefix
        if (course.code && (
            course.code.toUpperCase().startsWith('AP ') || 
            course.code.toUpperCase().includes(' AP') || 
            course.code.toUpperCase().startsWith('IB ') || 
            course.code.toUpperCase().includes(' IB')
        )) {
            return true;
        }
        
        // Check course name for AP or IB indicators
        if (course.name && (
            course.name.toUpperCase().includes('ADVANCED PLACEMENT') || 
            course.name.toUpperCase().includes('AP ') ||
            course.name.toUpperCase().includes(' AP ') ||
            course.name.toUpperCase().includes(' AP-') ||
            course.name.toUpperCase().includes('INTERNATIONAL BACCALAUREATE') || 
            course.name.toUpperCase().includes('IB ') ||
            course.name.toUpperCase().includes(' IB ') ||
            course.name.toUpperCase().includes(' IB-')
        )) {
            return true;
        }
        
        // Check if course has an AP/IB flag (might be added in future)
        if (course.is_ap || course.is_ib) {
            return true;
        }
        
        return false;
    };

    const handleOverrideChange = (key, value) => {
        setOverrideCategories(prev => ({ ...prev, [key]: value }));
    };

    // Handler for changing individual course level
    const handleManualCourseLevelChange = async (courseKey, newLevel) => {
        // Update local state immediately for responsiveness
        setManualCourseLevels(prev => ({
            ...prev,
            [courseKey]: newLevel // Store the display value in local state
        }));
        setHasUnsavedOverrides(true); // Mark that there are changes to save

        // --- REMOVED API CALL - Will be handled by bulk save ---
        // Find the course object based on courseKey to get its backend ID
        // This assumes course objects in caspaGpas have an 'id' field from the backend
        // const courseToUpdate = Object.values(caspaGpas)
        //     .flatMap(levelData => Object.values(levelData).flatMap(categoryData => categoryData.courses))
        //     .find(course => `${course.institution}-${course.code}-${course.term}-${course.year}` === courseKey);
        //
        // if (courseToUpdate && courseToUpdate.id) { // Ensure course and its backend ID exist
        //     try {
        //         const response = await axios.patch(
        //             `http://127.0.0.1:8000/api/courses/${courseToUpdate.id}/`, // Use the new backend endpoint URL
        //             { manual_academic_level: newLevel === 'Unknown' ? null : newLevel }, // Send null if setting back to Unknown
        //             { headers: { 'Authorization': `Bearer ${token}` } }
        //         );
        //         if (response.status === 200) {
        //             console.log(`Successfully updated academic level for course ${courseKey}`);
        //             // setSavedCourseKey(courseKey); // REMOVED
        //             // setTimeout(() => setSavedCourseKey(null), 2000); // REMOVED
        //         } else {
        //             console.error(`Failed to save academic level for course ${courseKey}. Status: ${response.status}`);
        //             // Handle error: maybe revert local state or show error message
        //         }
        //     } catch (error) {
        //         console.error(`Error saving academic level for course ${courseKey}:`, error);
        //          // Handle error
        //     }
        // } else {
        //      console.error(`Could not find course with key ${courseKey} or its ID for saving.`);
        // }
        // --- END REMOVED API CALL ---

        // Force detailed view update if it's currently open for 'Overall'
        // This should still happen automatically due to state change triggering useMemo recalculation
        if (detailedViewKey && detailedViewKey.startsWith('Overall')) {
            // Recalculating caspaGpas (triggered by manualCourseLevels state change)
            // should eventually update detailedViewCourses if the data structure allows.
            // No explicit update needed here if useMemo is set up correctly.
        }
    };


    // --- Fetching Data ---

    // Modified to accept token as argument
    const fetchS3Transcripts = async (apiToken) => {
        if (!apiToken) return;
        console.log("Dashboard: Fetching S3 transcripts..."); // Add log
        try {
            const response = await axios.get(`${process.env.REACT_APP_API_URL || 'http://127.0.0.1:8000'}/api/s3-transcripts/`, {
                headers: { 'Authorization': `Bearer ${apiToken}` } // Use passed token
            });
            setS3Transcripts(Array.isArray(response.data) ? response.data : []);
        } catch (err) {
            console.error('Error fetching S3 transcripts:', err);
            setError('Failed to load uploaded transcripts list');
            setS3Transcripts([]);
        }
    };

    // Modified to accept token as argument
    const fetchValidatedTranscripts = async (apiToken) => {
        if (!apiToken) return;
        console.log("Dashboard: Fetching validated transcripts..."); // Add log
        try {
            setTranscriptsLoading(true);
            // console.log('Fetching validated transcripts...'); // Original log, keep or remove
            const response = await axios.get(`${process.env.REACT_APP_API_URL || 'http://127.0.0.1:8000'}/api/transcript/get/`, {
                headers: { 'Authorization': `Bearer ${apiToken}` } // Use passed token
            });
            if (response.data?.transcripts) {
                // Filter for both 'approved' and manually entered transcripts
                const validTranscripts = response.data.transcripts.filter(
                    t => t.validation_status === 'approved' || t.entry_method === 'manual'
                );
                console.log('Received validated/manual transcripts:', validTranscripts);
                setExistingTranscripts(validTranscripts);

                // Initialize expandedSchools state to show all schools expanded by default
                const initialExpandedState = {};
                validTranscripts.forEach(transcript => {
                    // Use institution name as key, could be null/undefined if data is bad
                    if (transcript.institution) {
                        initialExpandedState[transcript.institution] = true;
                    }
                });
                setExpandedSchools(initialExpandedState);

                // Populate initial manualCourseLevels from fetched data
                const initialOverrides = {};
                validTranscripts.forEach(transcript => {
                    (transcript.transcript_data?.semesters || []).forEach(semester => {
                        semester.courses.forEach(course => {
                            const backendLevel = course.manual_academic_level;
                            if (backendLevel) {
                                const courseKey = `${transcript.institution}-${course.code}-${semester.term}-${semester.year}`;
                                // Map backend value to display value for initial state
                                initialOverrides[courseKey] = backendLevel === DO_NOT_COUNT_VALUE ? 'Do not count towards CASPA GPA' : backendLevel;
                            }
                        });
                    });
                });
                setManualCourseLevels(initialOverrides);
                setHasUnsavedOverrides(false); // Reset unsaved flag after loading

            } else {
                 setExistingTranscripts([]); // Ensure it's an empty array if no data
                 setManualCourseLevels({}); // Reset overrides if no transcripts
                 setHasUnsavedOverrides(false);
                 setExpandedSchools({}); // Clear expanded state if no transcripts
            }
        } catch (error) {
            console.error("Failed to fetch transcript data:", error);
             setError('Failed to load verified transcripts');
             setExistingTranscripts([]); // Ensure it's an empty array on error
             setManualCourseLevels({}); // Reset overrides on error
             setHasUnsavedOverrides(false);
             setExpandedSchools({}); // Clear expanded state on error
         } finally {
             setTranscriptsLoading(false);
         }
     };

     // Modified to accept token as argument
     const fetchProfileData = async (apiToken) => {
         if (!apiToken) return; // Check the passed token
         setProfileLoading(true); // Use renamed state setter
         console.log("Dashboard: Attempting to fetch profile data...");
         try {
             console.log("Dashboard: Sending GET request to /api/profile/ with token:", apiToken ? "Present" : "Absent"); // Log request details using apiToken
             const response = await axios.get(`${process.env.REACT_APP_API_URL || 'http://127.0.0.1:8000'}/api/profile/`, {
                 headers: { 'Authorization': `Bearer ${apiToken}` } // Use passed token
             });
             console.log("Dashboard: Received profile response status:", response.status); // Log response status
             console.log("Dashboard: Received profile data:", response.data); // Log received data
             console.log("Dashboard: Specific fields from response:", {
                 date_of_birth: response.data.date_of_birth,
                 gender: response.data.gender
             });
             
             if (response.data) {
                 // Helper to safely convert to string or return empty string
                 const toStringOrEmpty = (value) => value?.toString() || '';

                 setProfileData(prevData => {
                     const newProfileData = {
                         ...prevData,
                         ...response.data,
                         firstName: response.data.first_name || prevData.firstName,
                         lastName: response.data.last_name || prevData.lastName,
                         email: response.data.email || prevData.email,
                                              dateOfBirth: response.data.date_of_birth || '',
                     gender: response.data.gender || '',
                     address: response.data.address || '',
                     city: response.data.city || '',
                     state: response.data.state || '',
                     zipCode: response.data.zip_code || '',
                     // Academic History
                     primary_undergrad_institution: response.data.primary_undergrad_institution || '', // Fetch new field
                     undergrad_start_year: toStringOrEmpty(response.data.undergrad_start_year), // Fetch new field
                     undergrad_end_date: response.data.undergrad_end_date || '', // Fetch new field
                     // Experience
                     directPatientCareHours: toStringOrEmpty(response.data.direct_patient_care_hours),
                     shadowingHours: toStringOrEmpty(response.data.shadowing_hours),
                     // GRE
                     gre_verbal_score: toStringOrEmpty(response.data.gre_verbal_score),
                     gre_quantitative_score: toStringOrEmpty(response.data.gre_quantitative_score),
                     gre_analytical_writing_score: toStringOrEmpty(response.data.gre_analytical_writing_score),
                     gre_verbal_percentile: toStringOrEmpty(response.data.gre_verbal_percentile),
                     gre_quantitative_percentile: toStringOrEmpty(response.data.gre_quantitative_percentile),
                     gre_analytical_writing_percentile: toStringOrEmpty(response.data.gre_analytical_writing_percentile),
                     gre_test_date: response.data.gre_test_date || '',
                     // PA-CAT
                     pa_cat_anatomy_ss: toStringOrEmpty(response.data.pa_cat_anatomy_ss),
                     pa_cat_anatomy_pr: toStringOrEmpty(response.data.pa_cat_anatomy_pr),
                     pa_cat_physiology_ss: toStringOrEmpty(response.data.pa_cat_physiology_ss),
                     pa_cat_physiology_pr: toStringOrEmpty(response.data.pa_cat_physiology_pr),
                     pa_cat_biology_ss: toStringOrEmpty(response.data.pa_cat_biology_ss),
                     pa_cat_biology_pr: toStringOrEmpty(response.data.pa_cat_biology_pr),
                     pa_cat_chemistry_ss: toStringOrEmpty(response.data.pa_cat_chemistry_ss),
                     pa_cat_chemistry_pr: toStringOrEmpty(response.data.pa_cat_chemistry_pr),
                     pa_cat_composite_ss: toStringOrEmpty(response.data.pa_cat_composite_ss),
                     pa_cat_composite_pr: toStringOrEmpty(response.data.pa_cat_composite_pr),
                     pa_cat_test_date: response.data.pa_cat_test_date || '',
                         // CASPer
                         has_taken_casper: response.data.has_taken_casper || false,
                         casper_test_date: response.data.casper_test_date || ''
                     };
                     
                     console.log("About to set profile data:", {
                         dateOfBirth: newProfileData.dateOfBirth,
                         gender: newProfileData.gender
                     });
                     
                     return newProfileData;
                 });
                 
                 // Set initial edit states based on whether data exists
                 setIsEditingGREScores(!response.data.gre_test_date && !response.data.gre_verbal_score);
                 setIsEditingPaCatScores(!response.data.pa_cat_test_date && !response.data.pa_cat_composite_ss);
                 setIsEditingCasperDate(!response.data.casper_test_date && !response.data.has_taken_casper);
             }
         } catch (err) {
             console.error('Dashboard: Error fetching profile:', err); // Log the full error object
             if (err.response) {
                 // The request was made and the server responded with a status code
                 // that falls out of the range of 2xx
                 console.error("Dashboard: Error response data:", err.response.data);
                 console.error("Dashboard: Error response status:", err.response.status);
                 console.error("Dashboard: Error response headers:", err.response.headers);
                 setError(`Failed to load profile data. Status: ${err.response.status}. ${err.response.data?.detail || err.response.data?.error || ''}`);
             } else if (err.request) {
                 // The request was made but no response was received
                 console.error("Dashboard: Error request data:", err.request);
                 setError('Failed to load profile data. No response from server.');
             } else {
                 // Something happened in setting up the request that triggered an Error
                 console.error('Dashboard: Error message:', err.message);
                 setError(`Failed to load profile data: ${err.message}`);
             }
         } finally {
             console.log("Dashboard: fetchProfileData finally block reached. Setting loading to false."); // Log finally block
             setProfileLoading(false); // Use renamed state setter
         }
     };

    // Fetch data when currentUser is available (logged in)
    useEffect(() => {
        // Check if currentUser exists (meaning user is logged in via Supabase)
        if (currentUser && supabase) { // Also check if supabase client is available
            console.log("Dashboard: currentUser detected, fetching data...");
            const fetchTokenAndData = async () => {
                try {
                    // Use the supabase client from context to get the session
                    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

                    if (sessionError) {
                         console.error("Dashboard: Supabase error getting session:", sessionError);
                         throw new Error("Failed to get authentication session."); // Throw error
                    }
                    if (!session?.access_token) {
                         console.warn("Dashboard: No active session token found.");
                         // This might happen briefly during logout or if session expires,
                         // setting loading false might be appropriate if not logged in.
                         setError("No active session found. Please log in.");
                         setLoading(false);
                         setTranscriptsLoading(false);
                         return; // Exit if no token
                    }

                    const apiToken = session.access_token;
                    console.log("Dashboard: Got session token, proceeding to fetch data.");

                    // Now make the calls using the obtained token
                    // Ensure these functions are defined to accept the token
                    fetchProfileData(apiToken);
                    fetchS3Transcripts(apiToken);
                    fetchValidatedTranscripts(apiToken);

                } catch (err) {
                     console.error("Dashboard: Error fetching token or initial data:", err);
                     setError(err.message || "Failed to initialize session or fetch data. Please try logging in again.");
                     setProfileLoading(false); // Ensure loading stops on error
                     setTranscriptsLoading(false);
                 }
             };
            fetchTokenAndData();

        } else {
             console.log("Dashboard: No currentUser detected or supabase client not ready.");
             // Handle case where user logs out or context isn't ready
             setProfileLoading(false); // Not loading if not logged in
             setTranscriptsLoading(false);
             // Optionally clear profile/transcript data here
             // setProfileData({...initialProfileState}); // Resetting state might be needed here too
             // setExistingTranscripts([]);
        }
    }, [currentUser?.id, supabase]); // Rerun effect only when user ID or supabase client changes

    // Effect to potentially redirect or switch tab if URL tab becomes disabled
    useEffect(() => {
        const tabFromUrl = params.tabName || 'profile'; // Use param, default to profile if path is just /dashboard

        // Automatically expand transcript analysis section if going to transcripts tab
        if (tabFromUrl === 'transcripts') {
            setIsTranscriptExpanded(true);
        }

        // If the URL specifies caspa/prereqs but they should be disabled...
        if ((tabFromUrl === 'caspa' || tabFromUrl === 'prerequisites') && !transcriptsLoading && existingTranscripts.length === 0) {
            // If the active tab state doesn't match the corrected state ('profile'), update it
            if (activeTab !== 'profile') {
                 console.log(`Switching tab back to profile because ${tabFromUrl} is disabled.`);
                 setActiveTab('profile');
                 // Update URL silently to the base dashboard path
                 navigate('/dashboard', { replace: true });
             }
        } else if (activeTab !== tabFromUrl && !( (tabFromUrl === 'caspa' || tabFromUrl === 'prerequisites') && !transcriptsLoading && existingTranscripts.length === 0)) {
             // If the URL tab is valid and enabled, make sure the activeTab state matches it
             // This handles cases where the initial check might have defaulted but transcripts loaded allowing the tab
             // Or if the user manually changes the URL
             const validTabs = ['profile', 'transcripts', 'caspa', 'prerequisites'];
             if (validTabs.includes(tabFromUrl)) {
                 // console.log(`Syncing activeTab state to match URL parameter: ${tabFromUrl}`);
                 setActiveTab(tabFromUrl);
             }
        }
    }, [transcriptsLoading, existingTranscripts, params.tabName, navigate, activeTab]);

    // --- Input Handlers ---

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        console.log("Input change:", { name, value });
        setProfileData(prev => ({ ...prev, [name]: value }));
    };

    const handleCheckboxChange = (e) => {
        const { name, checked } = e.target;
        setProfileData(prev => ({ ...prev, [name]: checked }));
    };

    // --- Save Handlers ---

    const showSaveMessage = (message, isError = false) => {
        setSaveMessage(message);
        setTimeout(() => setSaveMessage(''), isError ? 5000 : 3000);
    };

    // Save general profile info (excluding tests)
    const handleProfileSubmit = async (e) => {
        e.preventDefault();
        e.stopPropagation();
        console.log("handleProfileSubmit called");
        setError(null);
        setIsSaving(true);

        const profilePayload = {
            first_name: profileData.firstName,
            last_name: profileData.lastName,
            date_of_birth: profileData.dateOfBirth || null, // Send snake_case for backend
            dateOfBirth: profileData.dateOfBirth || null, // Send camelCase as backup
            gender: profileData.gender,
            address: profileData.address,
            city: profileData.city,
            state: profileData.state,
            zip_code: profileData.zipCode,
            // Academic History
            primary_undergrad_institution: profileData.primary_undergrad_institution || null, // Send new field
            undergrad_start_year: parseInt(profileData.undergrad_start_year) || null, // Send new field as int or null
            undergrad_end_date: profileData.undergrad_end_date || null, // Send new field
            // Experience - Use snake_case field names to match backend expectations
            direct_patient_care_hours: parseInt(profileData.directPatientCareHours) || 0,
            shadowing_hours: parseInt(profileData.shadowingHours) || 0,
        };

        console.log("Saving Profile Data:", profilePayload);
        console.log("Current profileData state:", {
            dateOfBirth: profileData.dateOfBirth,
            gender: profileData.gender
        });

        try {
            // Get current token using the supabase client from context
            const { data: { session }, error: sessionError } = await supabase.auth.getSession();
            if (sessionError || !session?.access_token) {
                throw new Error("User session not found or invalid for saving profile.");
            }
            const apiToken = session.access_token;

            const response = await axios.patch(`${process.env.REACT_APP_API_URL || 'http://127.0.0.1:8000'}/api/profile/`, profilePayload, {
                headers: { 'Authorization': `Bearer ${apiToken}` }
            });
            console.log("Save response:", response.data);
            
            if (response.status === 200) {
                showSaveMessage('Profile updated successfully!');
                // Re-trigger GPA calculation if start year changed
                setCaspaGpas(calculatedCaspaGpas);
                // No need to refresh - the form already has the correct data
            } else {
                throw new Error(`Failed to save profile. Status: ${response.status}`);
            }
        } catch (err) {
            setError('Failed to save profile data');
            console.error('Error saving profile:', err.response ? err.response.data : err);
            showSaveMessage('Error saving profile.', true);
        } finally {
            setIsSaving(false);
        }
    };

    const handleSaveGREScores = async () => {
        setError(null);
        setIsSaving(true);
        const greData = {
            gre_verbal_score: parseInt(profileData.gre_verbal_score) || null,
            gre_quantitative_score: parseInt(profileData.gre_quantitative_score) || null,
            gre_analytical_writing_score: parseFloat(profileData.gre_analytical_writing_score) || null,
            gre_verbal_percentile: parseInt(profileData.gre_verbal_percentile) || null,
            gre_quantitative_percentile: parseInt(profileData.gre_quantitative_percentile) || null,
            gre_analytical_writing_percentile: parseInt(profileData.gre_analytical_writing_percentile) || null,
            gre_test_date: profileData.gre_test_date || null
        };
        console.log("Saving GRE Data:", greData);
        try {
            const { data: { session }, error: sessionError } = await supabase.auth.getSession();
            if (sessionError || !session?.access_token) {
                throw new Error("User session not found or invalid for saving GRE scores.");
            }
            const apiToken = session.access_token;
            const response = await axios.patch(`${process.env.REACT_APP_API_URL || 'http://127.0.0.1:8000'}/api/profile/`, greData, {
                headers: { 'Authorization': `Bearer ${apiToken}` }
            });
            if (response.status === 200) {
                showSaveMessage('GRE scores saved successfully!');
                setIsEditingGREScores(false); // Exit edit mode
            } else {
                 throw new Error(`Failed to save GRE scores. Status: ${response.status}`);
            }
        } catch (err) {
            setError('Failed to save GRE scores');
            console.error('Error saving GRE scores:', err.response ? err.response.data : err);
            showSaveMessage('Error saving GRE scores.', true);
        } finally {
            setIsSaving(false);
        }
    };

    const handleSavePaCatScores = async () => {
        setError(null);
        setIsSaving(true);
        const paCatData = {
            pa_cat_anatomy_ss: parseInt(profileData.pa_cat_anatomy_ss) || null,
            pa_cat_anatomy_pr: parseInt(profileData.pa_cat_anatomy_pr) || null,
            pa_cat_physiology_ss: parseInt(profileData.pa_cat_physiology_ss) || null,
            pa_cat_physiology_pr: parseInt(profileData.pa_cat_physiology_pr) || null,
            pa_cat_biology_ss: parseInt(profileData.pa_cat_biology_ss) || null,
            pa_cat_biology_pr: parseInt(profileData.pa_cat_biology_pr) || null,
            pa_cat_chemistry_ss: parseInt(profileData.pa_cat_chemistry_ss) || null,
            pa_cat_chemistry_pr: parseInt(profileData.pa_cat_chemistry_pr) || null,
            pa_cat_composite_ss: parseInt(profileData.pa_cat_composite_ss) || null,
            pa_cat_composite_pr: parseInt(profileData.pa_cat_composite_pr) || null,
            pa_cat_test_date: profileData.pa_cat_test_date || null
        };
        console.log("Saving PA-CAT Data:", paCatData);
        try {
            const { data: { session }, error: sessionError } = await supabase.auth.getSession();
            if (sessionError || !session?.access_token) {
                throw new Error("User session not found or invalid for saving PA-CAT scores.");
            }
            const apiToken = session.access_token;
            const response = await axios.patch(`${process.env.REACT_APP_API_URL || 'http://127.0.0.1:8000'}/api/profile/`, paCatData, {
                headers: { 'Authorization': `Bearer ${apiToken}` }
            });
            if (response.status === 200) {
                showSaveMessage('PA-CAT scores saved successfully!');
                setIsEditingPaCatScores(false); // Exit edit mode
            } else {
                 throw new Error(`Failed to save PA-CAT scores. Status: ${response.status}`);
            }
        } catch (err) {
            setError('Failed to save PA-CAT scores');
            console.error('Error saving PA-CAT scores:', err.response ? err.response.data : err);
            showSaveMessage('Error saving PA-CAT scores.', true);
        } finally {
            setIsSaving(false);
        }
    };

    const handleSaveCasperDate = async () => {
        setError(null);
        setIsSaving(true);
        const casperData = {
            has_taken_casper: profileData.has_taken_casper || false,
            casper_test_date: profileData.casper_test_date || null
        };
        console.log("Saving CASPer Data:", casperData);
        try {
            const { data: { session }, error: sessionError } = await supabase.auth.getSession();
            if (sessionError || !session?.access_token) {
                throw new Error("User session not found or invalid for saving CASPer data.");
            }
            const apiToken = session.access_token;
            const response = await axios.patch(`${process.env.REACT_APP_API_URL || 'http://127.0.0.1:8000'}/api/profile/`, casperData, {
                headers: { 'Authorization': `Bearer ${apiToken}` }
            });
            if (response.status === 200) {
                showSaveMessage('CASPer information saved successfully!');
                setIsEditingCasperDate(false); // Exit edit mode
            } else {
                 throw new Error(`Failed to save CASPer data. Status: ${response.status}`);
            }
        } catch (err) {
            setError('Failed to save CASPer data');
            console.error('Error saving CASPer data:', err.response ? err.response.data : err);
            showSaveMessage('Error saving CASPer data.', true);
        } finally {
            setIsSaving(false);
        }
    };

    // --- Transcript Processing & Validation ---

    const handleValidation = async (validationData) => {
        try {
            setIsProcessing(true);
            // Calculate GPAs based on the validated data
            const allCourses = validationData.editedData.flatMap(semester => semester.courses);
            let totalPoints = 0;
            let totalCredits = 0;
            let sciencePoints = 0;
            let scienceCredits = 0;

            allCourses.forEach(course => {
                const credits = parseFloat(course.credits) || 0;
                const gradePoints = caspaGradeToPoints(course.grade); // Use CASPA conversion

                if (gradePoints !== null && credits > 0) { // Only count graded courses with credits
                    totalPoints += credits * gradePoints;
                    totalCredits += credits;

                    // Use refined subject categorization
                    const subjectInfo = getCourseSubjectInfo(course);
                    if (subjectInfo.isScience) {
                        sciencePoints += credits * gradePoints;
                        scienceCredits += credits;
                    }
                }
            });

            const cumulativeGPA = totalCredits > 0 ? (totalPoints / totalCredits).toFixed(2) : '0.00';
            const scienceGPA = scienceCredits > 0 ? (sciencePoints / scienceCredits).toFixed(2) : '0.00';

            // Normalize institution name to ensure case-insensitive matching
            const normalizedInstitution = validationData.studentInfo?.institution?.trim();
            
            // Check if we already have a transcript from this institution
            // First, get a normalized version of each existing institution
            const existingNormalizedInstitutions = existingTranscripts.map(t => ({
                id: t.id,
                normalizedName: t.institution?.trim()
            }));
            
            // Try to find a match (check if any existing institution is similar to our current one)
            const similarInstitution = existingNormalizedInstitutions.find(t => 
                t.normalizedName && 
                normalizedInstitution && 
                (t.normalizedName.toLowerCase() === normalizedInstitution.toLowerCase() ||
                 t.normalizedName.toLowerCase().includes(normalizedInstitution.toLowerCase()) ||
                 normalizedInstitution.toLowerCase().includes(t.normalizedName.toLowerCase()))
            );
            
            // If we found a similar institution, delete it first
            if (similarInstitution) {
                console.log(`Found similar institution: ${similarInstitution.normalizedName}. Deleting before saving new transcript.`);
                try {
                    await handleDeleteTranscript(similarInstitution.id, similarInstitution.normalizedName, false);
                    // Wait a moment for the deletion to complete
                    await new Promise(resolve => setTimeout(resolve, 500));
                } catch (error) {
                    console.error("Error deleting similar institution:", error);
                    // Continue anyway - the backend should handle this
                }
            }

            const updatedValidationData = {
                ...validationData,
                studentInfo: {
                    ...validationData.studentInfo,
                    institution: normalizedInstitution, // Use normalized institution name
                    cumulative_gpa: cumulativeGPA,
                    science_gpa: scienceGPA,
                    science_credits: scienceCredits // Keep track of total science credits attempted
                }
            };

            console.log('Sending validation data:', updatedValidationData);
            const { data: { session }, error: sessionError } = await supabase.auth.getSession();
             if (sessionError || !session?.access_token) {
                throw new Error("User session not found or invalid for transcript validation.");
            }
            const apiToken = session.access_token;
            const response = await axios.post(
                'http://127.0.0.1:8000/api/validate-transcript/',
                updatedValidationData,
                { headers: { 'Authorization': `Bearer ${apiToken}` } }
            );

            if (response.data.status === "success") {
                setValidationStatus('approved');
                setProcessingTranscript(null); // Clear the transcript being processed
                await fetchValidatedTranscripts(apiToken); // Refresh the list with token
                setTranscriptData(null); // Clear modal data
                setValidatedTranscript(null); // Clear modal data
                showSaveMessage('Transcript validated and saved successfully!');
            } else {
                throw new Error(response.data.message || 'Validation failed');
            }
        } catch (error) {
            console.error("Failed to validate transcript:", error);
            setError(error.message || 'Failed to validate transcript');
            showSaveMessage('Error validating transcript.', true);
        } finally {
            setIsProcessing(false);
        }
    };

    // --- Manual Transcript Entry ---

    const handleAddCourseToSemester = () => {
        if (!currentCourse.code || !currentCourse.name || !currentCourse.credits) {
            alert('Please fill in all course fields (Code, Name, Credits)');
            return;
        }
        setCurrentSemester(prev => ({
            ...prev,
            courses: [...prev.courses, currentCourse]
        }));
        // Reset course form
        setCurrentCourse({ code: '', name: '', credits: '', grade: 'A', is_science: false });
    };

    const handleRemoveCourseFromSemester = (index) => {
        setCurrentSemester(prev => ({
            ...prev,
            courses: prev.courses.filter((_, i) => i !== index)
        }));
    };

    const handleAddSemesterToTranscript = () => {
        if (!currentSemester.term || !currentSemester.year || currentSemester.courses.length === 0) {
            alert('Please select Term, enter Year, and add at least one course to the semester.');
            return;
        }
        setManualTranscript(prev => ({
            ...prev,
            semesters: [...prev.semesters, currentSemester]
        }));
        // Reset semester form
        setCurrentSemester({ term: '', year: '', calendarType: 'Semester', courses: [] });
    };

     const handleRemoveSemesterFromTranscript = (index) => {
        setManualTranscript(prev => ({
            ...prev,
            semesters: prev.semesters.filter((_, i) => i !== index)
        }));
    };

    const handleSaveManualTranscript = async () => {
        if (!manualTranscript.institution || manualTranscript.semesters.length === 0) {
            alert('Please enter an institution name and add at least one semester.');
            return;
        }
        try {
            setIsManualTranscriptSaving(true);
            setManualTranscriptSaveMessage('');

            // Calculate GPAs using standard method for display consistency, CASPA GPAs calculated separately
            const gpas = calculateStandardGpas(manualTranscript.semesters); // Use standard calc here

            // Normalize institution name
            const normalizedInstitution = manualTranscript.institution.trim();

            // Check if we already have a transcript from this institution (same as in handleValidation)
            const existingNormalizedInstitutions = existingTranscripts.map(t => ({
                id: t.id,
                normalizedName: t.institution?.trim()
            }));
            
            // Try to find a similar institution
            const similarInstitution = existingNormalizedInstitutions.find(t => 
                t.normalizedName && 
                normalizedInstitution && 
                (t.normalizedName.toLowerCase() === normalizedInstitution.toLowerCase() ||
                 t.normalizedName.toLowerCase().includes(normalizedInstitution.toLowerCase()) ||
                 normalizedInstitution.toLowerCase().includes(t.normalizedName.toLowerCase()))
            );
            
            // If we found a similar institution, delete it first
            if (similarInstitution) {
                console.log(`Found similar institution: ${similarInstitution.normalizedName}. Deleting before saving manual transcript.`);
                try {
                    await handleDeleteTranscript(similarInstitution.id, similarInstitution.normalizedName, false);
                    // Wait a moment for the deletion to complete
                    await new Promise(resolve => setTimeout(resolve, 500));
                } catch (error) {
                    console.error("Error deleting similar institution:", error);
                    // Continue anyway - the backend should handle this
                }
            }

            const payload = {
                transcript: {
                    institution: normalizedInstitution, // Use normalized name
                    transcript_data: {
                        semesters: manualTranscript.semesters.map(sem => ({
                            term: sem.term,
                            year: sem.year,
                            calendarType: sem.calendarType || 'Semester', // Ensure calendarType is included
                            courses: sem.courses
                        })),
                        // Include standard calculated GPAs in the summary for backend storage/display
                        academic_summary: {
                            institution: normalizedInstitution, // Use normalized name
                            cumulative_gpa: parseFloat(gpas.calculated_gpa) || 0,
                            science_gpa: parseFloat(gpas.science_gpa) || 0,
                        }
                    }
                }
            };

            console.log("Saving Manual Transcript Payload:", payload);

            const { data: { session }, error: sessionError } = await supabase.auth.getSession();
            if (sessionError || !session?.access_token) {
                throw new Error("User session not found or invalid for saving manual transcript.");
            }
            const apiToken = session.access_token;
            const response = await axios.post(
                'http://127.0.0.1:8000/api/transcript/manual/',
                payload,
                { headers: { Authorization: `Bearer ${apiToken}` } }
            );

            if (response.status === 200 || response.status === 201) {
                setManualTranscriptSaveMessage('Transcript saved successfully!');
                setTimeout(() => setManualTranscriptSaveMessage(''), 3000);
                // Reset form
                setManualTranscript({ institution: '', semesters: [] });
                setShowManualEntry(false);
                await fetchValidatedTranscripts(apiToken); // Pass token to refresh list
            } else {
                 throw new Error(`Failed to save manual transcript. Status: ${response.status}`);
            }
        } catch (error) {
            console.error('Error saving transcript:', error.response ? error.response.data : error);
            setManualTranscriptSaveMessage('Error saving transcript. Please try again.');
        } finally {
            setIsManualTranscriptSaving(false);
        }
    };


    // --- Transcript Display & Deletion ---

    const toggleSchoolTranscript = (schoolName) => {
        setExpandedSchools(prev => ({
            ...prev,
            [schoolName]: !prev[schoolName]
        }));
    };

    const handleDeleteTranscript = async (transcriptId, institutionName, showConfirm = true) => {
         if (showConfirm && !window.confirm(`Are you sure you want to delete the transcript from ${institutionName}? This action cannot be undone.`)) {
            return;
        }
        try {
            console.log(`Attempting to delete transcript with ID: ${transcriptId}`);
            const { data: { session }, error: sessionError } = await supabase.auth.getSession();
            if (sessionError || !session?.access_token) {
                throw new Error("User session not found or invalid for deleting transcript.");
            }
            const apiToken = session.access_token;
            const response = await axios.delete(`http://127.0.0.1:8000/api/transcript/record/${transcriptId}/`, {
                headers: { 'Authorization': `Bearer ${apiToken}` }
            });

            if (response.status === 204) {
                if (showConfirm) {
                    showSaveMessage(`Transcript from ${institutionName} deleted successfully.`);
                }
                // Remove from local state and refresh
                setExistingTranscripts(prev => prev.filter(t => t.id !== transcriptId));
                // Collapse the deleted school if it was expanded
                setExpandedSchools(prev => {
                    const newState = {...prev};
                    delete newState[institutionName];
                    return newState;
                });
                 // Re-fetch all transcripts to ensure consistency if not part of a bulk operation
                 if (showConfirm) {
                     await fetchValidatedTranscripts(apiToken);
                 }
            } else {
                 throw new Error(`Failed to delete transcript. Status: ${response.status}`);
            }
        } catch (error) {
            console.error('Error deleting transcript:', error.response ? error.response.data : error);
            if (showConfirm) {
                setError(`Failed to delete transcript from ${institutionName}.`);
                showSaveMessage(`Error deleting transcript from ${institutionName}.`, true);
            }
        }
    };

    // --- Bulk Save Overrides ---
    const handleSaveAllOverrides = async () => {
        setIsSavingOverrides(true);
        setOverrideSaveMessage('');
        let successCount = 0;
        let failureCount = 0;
        const promises = [];

        // Find all courses that need updating
        const coursesToUpdate = [];
        if (caspaGpas) {
            Object.values(caspaGpas).forEach(levelData => {
                Object.values(levelData).forEach(categoryData => {
                    categoryData.courses.forEach(course => {
                        const courseKey = `${course.institution}-${course.code}-${course.term}-${course.year}`;
                        const manualLevel = manualCourseLevels[courseKey]; // The level currently set in the UI/state
                        const originalBackendLevel = course.manual_academic_level; // The level originally fetched

                        // Check if there's a manual override set AND it's different from the original backend value
                        // OR if an override existed and is now set back to 'Unknown' (needs saving as null)
                        const displayLevel = manualCourseLevels[courseKey]; // Current display value in state
                        const levelToSave = displayLevel === 'Do not count towards CASPA GPA'
                            ? DO_NOT_COUNT_VALUE
                            : displayLevel; // Map display value back to backend value

                        // Check if the level to save is different from the original backend level
                        const needsUpdate = levelToSave !== originalBackendLevel;

                        if (needsUpdate && course.id) {
                            coursesToUpdate.push({
                                id: course.id,
                                key: courseKey,
                                levelToSave: levelToSave // Send the mapped backend value
                            });
                        }
                    });
                });
            });
        }

        console.log("Courses needing override update:", coursesToUpdate);

        // Get token ONCE before the loop
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        if (sessionError || !session?.access_token) {
             console.error("Dashboard: Could not get session token for saving overrides.");
             setOverrideSaveMessage('Error: Could not get user session.');
             setIsSavingOverrides(false);
             return; // Stop if no token
        }
        const apiToken = session.access_token;

        coursesToUpdate.forEach(courseInfo => {
             // Find the original course object to get its unique ID
             let originalCourse = null;
             let transcriptRecordId = null; // We need the transcript record ID for the URL

             // Find the course and its parent transcript ID
             existingTranscripts.find(transcript => {
                 return (transcript.transcript_data?.semesters || []).find(semester => {
                     return semester.courses.find(c => {
                         // Construct the key used in coursesToUpdate
                         const key = `${transcript.institution}-${c.code}-${semester.term}-${semester.year}`;
                         if (key === courseInfo.key) {
                             originalCourse = c; // Found the course object
                             transcriptRecordId = transcript.id; // Found the parent transcript ID
                             return true; // Stop searching
                         }
                         return false;
                     });
                 });
             });


             if (!originalCourse || !originalCourse.id || !transcriptRecordId) {
                 console.error(`Could not find original course data, its unique ID, or transcript ID for key ${courseInfo.key}`);
                 failureCount++;
                 return; // Skip this course if data is missing
             }

             promises.push(
                 axios.patch(
                     // Use the transcriptRecordId in the URL
                     `http://127.0.0.1:8000/api/courses/${transcriptRecordId}/`,
                     {
                         // Send only the new level and the unique course ID
                         manual_academic_level: courseInfo.levelToSave,
                         course_unique_id: originalCourse.id // Send the unique ID from the course object
                     },
                     { headers: { 'Authorization': `Bearer ${apiToken}` } } // Use token fetched outside loop
                 ).then(response => {
                     if (response.status === 200) {
                        successCount++;
                        console.log(`Successfully updated override for course ${courseInfo.key}`);
                        // Optionally update the original course data in state if needed, but re-fetch might be safer
                    } else {
                        failureCount++;
                        console.error(`Failed to save override for course ${courseInfo.key}. Status: ${response.status}`);
                    }
                }).catch(error => {
                    failureCount++;
                    console.error(`Error saving override for course ${courseInfo.key}:`, error);
                })
            );
        });

        await Promise.allSettled(promises);

        let message = '';
        if (failureCount === 0 && successCount > 0) {
            message = `Successfully saved ${successCount} academic level override(s).`;
            setHasUnsavedOverrides(false); // Clear flag only if all succeed
            // Re-fetch transcripts to get the updated 'manual_academic_level' baked into the course objects
            await fetchValidatedTranscripts(apiToken);
        } else if (failureCount > 0) {
            message = `Saved ${successCount} override(s). Failed to save ${failureCount} override(s). Please check console for errors and try again.`;
            // Keep hasUnsavedOverrides true if some failed? Or let user retry? Let's keep it true.
            setHasUnsavedOverrides(true);
        } else {
            message = 'No changes needed saving.'; // No updates were attempted
        }

        setOverrideSaveMessage(message);
        setIsSavingOverrides(false);
        setTimeout(() => setOverrideSaveMessage(''), 5000); // Clear message after 5 seconds
    };

    // --- GPA Calculations ---

    // Standard Grade to Points (A=4, B=3, etc., no +/-) - Used for basic display
     const standardGradeToPoints = (grade) => {
        const gradePoints = {
            'A': 4.0, 'A-': 4.0, // Treat A- as A for simplicity here if needed, or adjust
            'B+': 3.0, 'B': 3.0, 'B-': 3.0,
            'C+': 2.0, 'C': 2.0, 'C-': 2.0,
            'D+': 1.0, 'D': 1.0, 'D-': 1.0,
            'F': 0.0
        };
        return gradePoints.hasOwnProperty(grade) ? gradePoints[grade] : null; // Exclude W, S, U
    };

    // CASPA Grade to Points (Handles +/-, WF)
    const caspaGradeToPoints = (grade) => {
        const gradePoints = {
            'A': 4.0, 'A-': 3.7,
            'B+': 3.3, 'B': 3.0, 'B-': 2.7,
            'C+': 2.3, 'C': 2.0, 'C-': 1.7,
            'D+': 1.3, 'D': 1.0, 'D-': 0.7,
            'F': 0.0, 'WF': 0.0 // Treat WF as F
        };
        // Return null for non-grade values (P, S, U, W etc.) to exclude them
        return gradePoints.hasOwnProperty(grade?.toUpperCase()) ? gradePoints[grade.toUpperCase()] : null;
    };
    
    // Round GPA up to one decimal place (e.g., 3.87 → 3.9)
    const roundUpGPA = (gpa) => {
        if (!gpa) return 'N/A';
        const value = parseFloat(gpa);
        if (isNaN(value)) return gpa;
        // Multiply by 10, ceiling, then divide by 10 to round up to 1 decimal place
        return (Math.ceil(value * 10) / 10).toFixed(1);
    };

    // Refined Subject Categorization using CASPA list
    const getCourseSubjectInfo = (course) => {
        const name = course.name?.toUpperCase().trim() || '';
        const code = course.code?.toUpperCase().trim() || '';
        const defaultResult = { subject: 'Other Non-Science', isScience: false, isBCP: false };

        // Prioritize matching full course name first (more specific)
        if (subjectKeywordMap.has(name)) {
            const info = subjectKeywordMap.get(name);
            return { subject: info.category, isScience: info.isScience, isBCP: info.isBCP };
        }

        // Check keywords within the name
        for (const [keyword, info] of subjectKeywordMap.entries()) {
             // Check if keyword is a whole word in the name to avoid partial matches like 'ART' in 'EARTH'
            const regex = new RegExp(`\\b${keyword.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&')}\\b`); // Escape special chars
            if (regex.test(name)) {
                 return { subject: info.category, isScience: info.isScience, isBCP: info.isBCP };
            }
        }

        // Fallback: Check prefixes in course code (less reliable)
        const codePrefix = code.split(' ')[0]; // Get prefix like 'BIOL' from 'BIOL 101'
        if (codePrefix) {
            if (['BIOL', 'ZOOL', 'ANAT', 'PHYS', 'MICR', 'BOT', 'ECOL', 'GENE'].includes(codePrefix)) return { subject: 'Biology/Zoology', isScience: true, isBCP: true };
            if (codePrefix === 'CHEM') {
                 if (name.includes('ORGANIC')) return { subject: 'Organic Chemistry', isScience: true, isBCP: true };
                 if (name.includes('BIOCHEM')) return { subject: 'Biochemistry', isScience: true, isBCP: true };
                 return { subject: 'Inorganic Chemistry', isScience: true, isBCP: true };
            }
            if (codePrefix === 'PHYS') return { subject: 'Physics', isScience: true, isBCP: true };
            if (['ASTR', 'GEOL', 'ENVS', 'NUTR', 'EPID', 'KINE', 'EXSC'].includes(codePrefix)) return { subject: 'Other Science', isScience: true, isBCP: false }; // Added KINE, EXSC
            if (['MATH', 'STAT'].includes(codePrefix)) return { subject: 'Mathematics', isScience: false, isBCP: false };
            if (['ENGL', 'WRIT', 'LIT'].includes(codePrefix)) return { subject: 'English', isScience: false, isBCP: false };
            if (['PSYC', 'SOCI', 'ANTH', 'ECON', 'POLS', 'HIST', 'GEOG'].includes(codePrefix)) return { subject: 'Social/Behavioral Science', isScience: false, isBCP: false };
        }

        // Final fallback for manually entered 'is_science' flag
        if (course.is_science === true) {
            return { subject: 'Other Science', isScience: true, isBCP: false };
        }

        return defaultResult;
    };


    // Academic Level Determination (Simplified - No Year-Based Levels)
    // Added manualPrimaryInstitution argument
    const getAcademicLevelInfo = (allSemesters, allTranscripts, manualStartYear, manualEndDate, manualPrimaryInstitution) => {
        const levelData = {}; // Will store courses by category
        const institutionData = {}; // Store info per institution

        // Initialize level data structure
        const levels = ['Undergraduate', 'Post-Baccalaureate', 'Pre-Undergraduate'];
        levels.forEach(level => {
            levelData[level] = [];
        });

        // 1. Group semesters by institution and find start/end years
        allTranscripts.forEach(transcript => {
            const institutionName = transcript.institution || 'Unknown Institution';
            if (!institutionData[institutionName]) {
                institutionData[institutionName] = {
                    semesters: [],
                    startYear: null,
                    endYear: null,
                    gradedCredits: 0,
                };
            }
            const semesters = transcript.transcript_data?.semesters || [];
            const semestersWithInst = semesters.map(s => ({ ...s, institution: institutionName }));
            institutionData[institutionName].semesters.push(...semestersWithInst);

            const sortedInstSemesters = semestersWithInst
                .map(s => ({ ...s, yearInt: parseInt(s.year), termOrder: {'Spring': 1, 'Summer': 2, 'Fall': 3, 'Winter': 4}[s.term] || 5 }))
                .filter(s => !isNaN(s.yearInt))
                .sort((a, b) => a.yearInt - b.yearInt || a.termOrder - b.termOrder);

            for (const semester of sortedInstSemesters) {
                const hasGradedCourse = semester.courses.some(course =>
                    caspaGradeToPoints(course.grade) !== null && parseFloat(course.credits) > 0
                );
                if (hasGradedCourse) {
                    if (institutionData[institutionName].startYear === null) {
                        institutionData[institutionName].startYear = semester.yearInt;
                    }
                    institutionData[institutionName].endYear = semester.yearInt;
                    semester.courses.forEach(course => {
                        if (caspaGradeToPoints(course.grade) !== null) {
                            institutionData[institutionName].gradedCredits += parseFloat(course.credits) || 0;
                        }
                    });
                }
            }
        });

        // 2. Identify Primary Institution (heuristic) if manual start not provided
        let primaryInstitution = manualPrimaryInstitution;
        let primaryStartYear = manualStartYear ? parseInt(manualStartYear) : null;
        
        if (!primaryInstitution) {
            let maxCredits = -1;
            let longestDuration = -1;
            Object.entries(institutionData).forEach(([name, data]) => {
                if (data.startYear !== null) {
                    if (data.gradedCredits > maxCredits) {
                        maxCredits = data.gradedCredits;
                        primaryInstitution = name;
                        longestDuration = data.endYear - data.startYear;
                    } else if (data.gradedCredits === maxCredits && data.gradedCredits > 0) {
                        const duration = data.endYear - data.startYear;
                        if (duration > longestDuration) {
                            primaryInstitution = name;
                            longestDuration = duration;
                        }
                    }
                }
            });
            if (!primaryInstitution && Object.keys(institutionData).length > 0) {
                let earliestStart = Infinity;
                Object.entries(institutionData).forEach(([name, data]) => {
                    if (data.startYear !== null && data.startYear < earliestStart) {
                        earliestStart = data.startYear;
                        primaryInstitution = name;
                    }
                });
                console.warn("Could not determine primary institution based on credits/duration. Falling back to earliest start year:", primaryInstitution);
            }
            if (!primaryStartYear && primaryInstitution) {
                primaryStartYear = institutionData[primaryInstitution]?.startYear;
            }
            console.log("Heuristic Identified Primary Institution:", primaryInstitution, "with Start Year:", primaryStartYear);
        } else {
            console.log("Using Manual Primary Institution:", primaryInstitution);
        }

        // 3. Assign Levels - Simplified approach without year-based levels
        allSemesters.forEach(semester => {
            const institutionName = semester.institution; // Should be present now
            const year = parseInt(semester.year);
            if (isNaN(year)) return;

            let level = 'Pre-Undergraduate'; // Default

            // Compute degree conferral year: after Bachelor is conferred
            const primaryEndYear = manualEndDate
                ? parseInt(manualEndDate.split('-')[0])
                : (primaryInstitution ? institutionData[primaryInstitution]?.endYear : null);

            // Simpler approach - just classify as Undergraduate or Post-Baccalaureate
            const isPrimary = primaryInstitution ? institutionName === primaryInstitution : true;
            
            if (isPrimary) {
                if (primaryEndYear && year > primaryEndYear) {
                    level = 'Post-Baccalaureate';
                } else {
                    level = 'Undergraduate';
                }
            } else {
                // Non-primary institutions default to Pre-Undergraduate
                level = 'Pre-Undergraduate';
            }

            // Add courses to the appropriate level
            semester.courses.forEach(course => {
                levelData[level].push({
                    ...course,
                    academicLevel: level,
                    institution: institutionName,
                    calendarType: semester.calendarType || 'Semester',
                    term: semester.term,
                    year: semester.year
                });
            });
        });

        return levelData;
    };

    // Calculate CASPA GPAs (Memoized) - Using simplified levels
    const calculatedCaspaGpas = useMemo(() => {
        console.log("Recalculating CASPA GPAs with Simplified Academic Levels...");
        if (!existingTranscripts || existingTranscripts.length === 0) {
            return null;
        }

        // Initialize structure with simplified levels
        const levelData = {
            'Undergraduate': { science: { qp: 0, credits: 0, courses: [] }, nonScience: { qp: 0, credits: 0, courses: [] }, total: { qp: 0, credits: 0, courses: [] } },
            'Post-Baccalaureate': { science: { qp: 0, credits: 0, courses: [] }, nonScience: { qp: 0, credits: 0, courses: [] }, total: { qp: 0, credits: 0, courses: [] } },
            'Pre-Undergraduate': { science: { qp: 0, credits: 0, courses: [] }, nonScience: { qp: 0, credits: 0, courses: [] }, total: { qp: 0, credits: 0, courses: [] } },
            'Graduate Coursework': { science: { qp: 0, credits: 0, courses: [] }, nonScience: { qp: 0, credits: 0, courses: [] }, total: { qp: 0, credits: 0, courses: [] } }
    };

        // Combine all semesters from all transcripts first
        const allSemestersWithInstitution = existingTranscripts.flatMap(transcript =>
            (transcript.transcript_data?.semesters || []).map(semester => ({
                ...semester,
                institution: transcript.institution || 'Unknown Institution' // Ensure institution name is attached
            }))
        );

        // Determine academic levels using the revised function, passing manual start year, end date, and primary institution
        const levelInfo = getAcademicLevelInfo(
            allSemestersWithInstitution,
            existingTranscripts,
            profileData.undergrad_start_year,     // Pass manual start year from profile
            profileData.undergrad_end_date,       // Pass manual end date from profile
            profileData.primary_undergrad_institution // Pass primary institution from profile
        );

        // Aggregate data based on assigned levels, considering manual overrides
        Object.entries(levelInfo).forEach(([autoLevel, courses]) => {
            courses.forEach(originalCourse => {
                // Make a copy to avoid mutating the original fetched data directly
                const course = { ...originalCourse };
                // Generate a unique key for manual override lookup
                const courseKey = `${course.institution}-${course.code}-${course.term}-${course.year}`;
                // Determine the final level: manual override takes precedence
                const rawLevel = course.academicLevel || 'Pre-Undergraduate'; // Auto-detected level
                const finalLevel = manualCourseLevels[courseKey] || rawLevel; // Use manual override if present

                // --- Skip calculation if level is set to "Do not count" ---
                if (finalLevel === 'Do not count towards CASPA GPA') {
                    // Do not count towards GPA, but still include in the list
                }
                // --- End Skip ---

                // Ensure the finalLevel category exists in levelData
                if (!levelData[finalLevel] && finalLevel !== 'Do not count towards CASPA GPA') {
                    levelData[finalLevel] = { science: { qp: 0, credits: 0, courses: [] }, nonScience: { qp: 0, credits: 0, courses: [] }, total: { qp: 0, credits: 0, courses: [] } };
                }

                let credits = parseFloat(course.credits) || 0;
                const points = caspaGradeToPoints(course.grade);
                const calendarType = course.calendarType || 'Semester';

                if (calendarType === 'Quarter') {
                    credits = credits * 0.667;
                }

                if (points !== null && credits > 0) {
                    const qp = points * credits;
                    const { isScience } = getCourseSubjectInfo(course);
                    // Check if this is an AP or IB course
                    const isAPorIB = isAPorIBCourse(course);
                    
                    // Store the final determined level, original level, term, and year on the course object
                    const courseDetail = {
                        ...course, // Spread existing course properties first
                        qp: qp.toFixed(1),
                        institution: course.institution,
                        determinedLevel: finalLevel, // The level used for calculation
                        autoDetectedLevel: course.academicLevel, // The original level for reference
                        term: course.term, // Explicitly ensure term is present
                        year: course.year,  // Explicitly ensure year is present
                        isAPorIB // Flag to indicate AP/IB course
                    };

                    // Only add to totals if not marked as "Do not count" AND not an AP/IB course
                    if (finalLevel !== 'Do not count towards CASPA GPA' && !isAPorIB) {
                        levelData[finalLevel].total.qp += qp;
                        levelData[finalLevel].total.credits += credits;

                        if (isScience) {
                            levelData[finalLevel].science.qp += qp;
                            levelData[finalLevel].science.credits += credits;
                        } else {
                            levelData[finalLevel].nonScience.qp += qp;
                            levelData[finalLevel].nonScience.credits += credits;
                        }
                    }

                    // Always push the course detail to the list, even for AP/IB courses
                    // This makes them visible and available for prerequisite matching
                    if (finalLevel !== 'Do not count towards CASPA GPA') {
                        levelData[finalLevel].total.courses.push(courseDetail);
                        if (isScience) {
                            levelData[finalLevel].science.courses.push(courseDetail);
                        } else {
                            levelData[finalLevel].nonScience.courses.push(courseDetail);
                        }
                    }
                }
            });
        });

        // Calculate GPAs and Summary Rows
        const calculateGpa = (qp, credits) => {
            if (credits <= 0) return '0.0';
            // Calculate raw GPA
            const rawGpa = qp / credits;
            // Round up to one decimal place
            return (Math.ceil(rawGpa * 10) / 10).toFixed(1);
        };
        const formatTotals = (data) => ({
            credits: data.credits.toFixed(1),
            qp: data.qp.toFixed(1),
            gpa: calculateGpa(data.qp, data.credits),
            courses: data.courses
        });

        const results = {};
        let cumulativeUndergrad = { science: { qp: 0, credits: 0, courses: [] }, nonScience: { qp: 0, credits: 0, courses: [] }, total: { qp: 0, credits: 0, courses: [] } };
        let overallTotal = { science: { qp: 0, credits: 0, courses: [] }, nonScience: { qp: 0, credits: 0, courses: [] }, total: { qp: 0, credits: 0, courses: [] } };

        // Add the main level calculations to results
        Object.entries(levelData).forEach(([level, data]) => {
            // Only add levels with actual courses to the results
            if (data.total.credits > 0) {
                results[level] = {
                    science: formatTotals(data.science),
                    nonScience: formatTotals(data.nonScience),
                    total: formatTotals(data.total)
                };
            }
            
            // Accumulate overall totals
            overallTotal.science.qp += data.science.qp;
            overallTotal.science.credits += data.science.credits;
            overallTotal.science.courses.push(...data.science.courses);
            overallTotal.nonScience.qp += data.nonScience.qp;
            overallTotal.nonScience.credits += data.nonScience.credits;
            overallTotal.nonScience.courses.push(...data.nonScience.courses);
            overallTotal.total.qp += data.total.qp;
            overallTotal.total.credits += data.total.credits;
            overallTotal.total.courses.push(...data.total.courses);

            // Accumulate Cumulative Undergraduate (Undergraduate + Post-Bacc)
            if (level === 'Undergraduate' || level === 'Post-Baccalaureate') {
                cumulativeUndergrad.science.qp += data.science.qp;
                cumulativeUndergrad.science.credits += data.science.credits;
                cumulativeUndergrad.science.courses.push(...data.science.courses);
                cumulativeUndergrad.nonScience.qp += data.nonScience.qp;
                cumulativeUndergrad.nonScience.credits += data.nonScience.credits;
                cumulativeUndergrad.nonScience.courses.push(...data.nonScience.courses);
                cumulativeUndergrad.total.qp += data.total.qp;
                cumulativeUndergrad.total.credits += data.total.credits;
                cumulativeUndergrad.total.courses.push(...data.total.courses);
            }
        });

        // Add the summary rows to the results object
        results['Cumulative Undergraduate'] = {
            science: formatTotals(cumulativeUndergrad.science),
            nonScience: formatTotals(cumulativeUndergrad.nonScience),
            total: formatTotals(cumulativeUndergrad.total)
        };
        
        results['Overall'] = {
            science: formatTotals(overallTotal.science),
            nonScience: formatTotals(overallTotal.nonScience),
            total: formatTotals(overallTotal.total)
        };

        console.log("Final CASPA GPAs (Simplified):", results);
        return results;

    }, [existingTranscripts, profileData.undergrad_start_year, profileData.undergrad_end_date, profileData.primary_undergrad_institution, overrideCategories, manualCourseLevels]);

    // Update state when calculated GPAs change
    useEffect(() => {
        setCaspaGpas(calculatedCaspaGpas);
        setDetailedViewKey(null); // Reset detailed view when transcripts change
        setDetailedViewCourses([]);
    }, [calculatedCaspaGpas]); // Only depends on the calculated result now
    // Auto-open "Overall" detailed view when CASPA tab is selected
    useEffect(() => {
        if (activeTab === 'caspa' && caspaGpas) {
            handleGpaCellClick('Overall', 'total');
        }
    }, [activeTab, caspaGpas]);



    // Calculate Standard GPAs (for basic display in transcript sections)
    const calculateStandardGpas = (semesters) => {
        let totalPoints = 0;
        let totalCredits = 0;
        let sciencePoints = 0;
        let scienceCredits = 0;

        semesters.forEach(semester => {
            semester.courses.forEach(course => {
                const credits = parseFloat(course.credits) || 0;
                const points = standardGradeToPoints(course.grade); // Use standard points

                if (points !== null && credits > 0) {
                    totalPoints += credits * points;
                    totalCredits += credits;
                    // Use basic is_science flag for this calculation
                    if (course.is_science) {
                        sciencePoints += credits * points;
                        scienceCredits += credits;
                    }
                }
            });
        });

        const cumulativeGPA = totalCredits > 0 ? roundUpGPA(totalPoints / totalCredits) : '0.0';
        const scienceGPA = scienceCredits > 0 ? roundUpGPA(sciencePoints / scienceCredits) : '0.0';

        return { calculated_gpa: cumulativeGPA, science_gpa: scienceGPA };
    };

    // Calculate overall Standard GPA across multiple transcripts
    const calculateOverallStandardGpas = (transcripts) => {
        let overallTotalPoints = 0;
        let overallTotalCredits = 0;
        let overallSciencePoints = 0;
        let overallScienceCredits = 0;

        transcripts.forEach(transcript => {
            const semesters = transcript.transcript_data?.semesters || [];
            semesters.forEach(semester => {
                semester.courses.forEach(course => {
                    const credits = parseFloat(course.credits) || 0;
                    const points = standardGradeToPoints(course.grade); // Use standard points

                    if (points !== null && credits > 0) {
                        overallTotalPoints += credits * points;
                        overallTotalCredits += credits;
                        if (course.is_science) {
                            overallSciencePoints += credits * points;
                            overallScienceCredits += credits;
                        }
                    }
                });
            });
        });

        const cumulativeGPA = overallTotalCredits > 0 ? roundUpGPA(overallTotalPoints / overallTotalCredits) : '0.0';
        const scienceGPA = overallScienceCredits > 0 ? roundUpGPA(overallSciencePoints / overallScienceCredits) : '0.0';

        return { cumulativeGPA, scienceGPA };
    };

    // --- Event Handlers ---
    const handleGpaCellClick = (level, category) => {
        const key = `${level}-${category}`;
        if (detailedViewKey === key) {
            // Toggle off if clicking the same key
            setDetailedViewKey(null);
            setDetailedViewCourses([]); // Reset to empty array/object
        } else {
            const courses = caspaGpas?.[level]?.[category]?.courses || [];
            setDetailedViewKey(key);

            // Always group courses by semester for detailed view
            if (!Array.isArray(courses)) {
                console.error(`Courses data for ${level}-${category} is not an array:`, courses);
                setDetailedViewCourses({});
                return;
            }
            // Group courses by semester
            const grouped = courses.reduce((acc, course) => {
                const term = course.term || 'Unknown Term';
                const year = course.year || 'Unknown Year';
                const semKey = `${term} ${year}`;
                if (!acc[semKey]) acc[semKey] = [];
                acc[semKey].push(course);
                return acc;
            }, {});

            // Sort courses alphabetically by code within each semester
            Object.keys(grouped).forEach(semKey => {
                grouped[semKey].sort((a, b) => (a.code || '').localeCompare(b.code || ''));
            });

            // Define semester term order for sorting
            const termOrder = { 'Unknown Term': 0, 'Spring': 1, 'Summer': 2, 'Fall': 3, 'Winter': 4 };

            // Sort semester groups chronologically (year asc, then term asc)
            const sortedEntries = Object.entries(grouped).sort(([aKey], [bKey]) => {
                const aParts = aKey.split(' ');
                const bParts = bKey.split(' ');
                const aYear = parseInt(aParts.pop()) || 0;
                const bYear = parseInt(bParts.pop()) || 0;
                const aTerm = aParts.join(' ');
                const bTerm = bParts.join(' ');
                if (aYear !== bYear) return aYear - bYear;
                return (termOrder[aTerm] || 99) - (termOrder[bTerm] || 99);
            });

            // Rebuild object in sorted order
            const sortedGrouped = sortedEntries.reduce((obj, [sem, list]) => {
                obj[sem] = list;
                return obj;
            }, {});

            setDetailedViewCourses(sortedGrouped);
        }
    };

    const handleTabClick = (tabName) => {
        setActiveTab(tabName);
        // Automatically expand transcript analysis section when transcripts tab is selected
        if (tabName === 'transcripts') {
            setIsTranscriptExpanded(true);
        }
        // Update URL using path parameter
        // Navigate to base /dashboard for the profile tab
        const path = tabName === 'profile' ? '/dashboard' : `/dashboard/${tabName}`;
        navigate(path, { replace: true });
    };

    // --- Render Logic ---

    // Wait for both auth and profile data to load
    if (authLoading || profileLoading) return <div className="loading-indicator">Loading profile...</div>;
    // Don't show general error if a save message is already showing
    const shouldShowGeneralError = error && !saveMessage;

    // Define the order for table rows, matching the simplified structure
    const caspaTableRowOrder = [
        'Undergraduate',
        'Post-Baccalaureate',
        'Graduate Coursework',
        'Pre-Undergraduate',
        'Cumulative Undergraduate',
        'Overall'
    ];

    return (
        <div className="dashboard-container" style={{ 
            maxWidth: '1024px',
            margin: '0 auto',
            fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif',
            padding: '20px',
            background: 'linear-gradient(to bottom, #f9fafb 0%, #f3f4f6 100%)'
        }}>
            {/* Dashboard Header - Spans full width */}
            <div style={{
                backgroundColor: 'white',
                borderRadius: '8px',
                boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                padding: '20px',
                marginBottom: '20px',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                borderLeft: '4px solid #2563eb'
            }}>
                <h1 style={{ fontSize: '26px', margin: '0', color: '#111827' }}>User Dashboard</h1>
                <div style={{ fontSize: '14px', color: '#6b7280' }}>
                    {profileData.firstName && profileData.lastName ? 
                    `Welcome back, ${profileData.firstName} ${profileData.lastName}` : 
                    "Complete your profile to get started"}
                </div>
            </div>
            
            {/* Main Content Area with Sidebar */}
            <div style={{ display: 'flex' }}>
            
                {/* Side Navigation */}
                <div style={{ 
                    width: '260px', 
                    padding: '20px',
                    backgroundColor: 'white',
                    borderRadius: '8px',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.08)',
                    marginRight: '20px',
                    height: 'fit-content'
                }}>
                    {/* Home Button */}
                    <div style={{ 
                        marginBottom: '20px',
                        transition: 'all 0.3s ease'
                    }}>
                        <button
                            style={{ 
                                background: 'linear-gradient(135deg, #2563eb 0%, #1e40af 100%)',
                                border: 'none',
                                fontSize: '16px',
                                fontWeight: '600',
                                color: 'white',
                                cursor: 'pointer',
                                padding: '12px 15px',
                                textAlign: 'left',
                                width: '100%',
                                borderRadius: '6px',
                                display: 'flex',
                                alignItems: 'center',
                                boxShadow: '0 2px 4px rgba(37, 99, 235, 0.2)',
                                transition: 'all 0.2s ease',
                                transform: 'translateY(0)'
                            }}
                            onClick={() => navigate('/home')}
                            onMouseEnter={(e) => {
                                e.target.style.transform = 'translateY(-1px)';
                                e.target.style.boxShadow = '0 4px 8px rgba(37, 99, 235, 0.3)';
                            }}
                            onMouseLeave={(e) => {
                                e.target.style.transform = 'translateY(0)';
                                e.target.style.boxShadow = '0 2px 4px rgba(37, 99, 235, 0.2)';
                            }}
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" style={{ marginRight: '10px' }} width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                                <polyline points="9 22 9 12 15 12 15 22"></polyline>
                            </svg>
                            Back to Home
                        </button>
                    </div>
                    
                    {/* Dashboard Tabs */}
                    <div style={{ 
                        marginBottom: '12px',
                        transition: 'all 0.3s ease'
                    }}>
                        <button
                            style={{ 
                                background: activeTab === 'profile' ? '#EBF5FF' : 'white',
                                border: 'none',
                                fontSize: '16px',
                                fontWeight: activeTab === 'profile' ? 'bold' : 'normal',
                                color: activeTab === 'profile' ? '#2563eb' : '#4b5563',
                                cursor: 'pointer',
                                padding: '10px 15px',
                                textAlign: 'left',
                                width: '100%',
                                borderRadius: '6px',
                                display: 'flex',
                                alignItems: 'center',
                                boxShadow: activeTab === 'profile' ? '0 1px 2px rgba(0,0,0,0.05)' : 'none',
                                transition: 'all 0.2s ease'
                            }}
                            onClick={() => handleTabClick('profile')}
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" style={{ marginRight: '10px' }} width="18" height="18" viewBox="0 0 24 24" fill="none" stroke={activeTab === 'profile' ? '#2563eb' : '#6b7280'} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                <circle cx="12" cy="7" r="4"></circle>
                            </svg>
                            Profile & Tests
                        </button>
                    </div>
                    <div style={{ 
                        marginBottom: '12px',
                        transition: 'all 0.3s ease'
                    }}>
                        <button
                            style={{ 
                                background: activeTab === 'transcripts' ? '#EBF5FF' : 'white',
                                border: 'none',
                                fontSize: '16px',
                                fontWeight: activeTab === 'transcripts' ? 'bold' : 'normal',
                                color: activeTab === 'transcripts' ? '#2563eb' : '#4b5563',
                                cursor: 'pointer',
                                padding: '10px 15px',
                                textAlign: 'left',
                                width: '100%',
                                borderRadius: '6px',
                                display: 'flex',
                                alignItems: 'center',
                                boxShadow: activeTab === 'transcripts' ? '0 1px 2px rgba(0,0,0,0.05)' : 'none',
                                transition: 'all 0.2s ease'
                            }}
                            onClick={() => handleTabClick('transcripts')}
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" style={{ marginRight: '10px' }} width="18" height="18" viewBox="0 0 24 24" fill="none" stroke={activeTab === 'transcripts' ? '#2563eb' : '#6b7280'} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                <polyline points="14 2 14 8 20 8"></polyline>
                                <line x1="16" y1="13" x2="8" y2="13"></line>
                                <line x1="16" y1="17" x2="8" y2="17"></line>
                                <polyline points="10 9 9 9 8 9"></polyline>
                            </svg>
                            Transcripts
                        </button>
                    </div>
                    <div style={{
                        marginBottom: '12px',
                        transition: 'all 0.3s ease'
                    }}>
                        <button
                            style={{ 
                                background: activeTab === 'caspa' ? '#EBF5FF' : 'white',
                                border: 'none',
                                fontSize: '16px',
                                fontWeight: activeTab === 'caspa' ? 'bold' : 'normal',
                                color: activeTab === 'caspa' ? '#2563eb' : '#4b5563',
                                cursor: existingTranscripts.length === 0 ? 'not-allowed' : 'pointer',
                                padding: '10px 15px',
                                textAlign: 'left',
                                width: '100%',
                                borderRadius: '6px',
                                display: 'flex',
                                alignItems: 'center',
                                boxShadow: activeTab === 'caspa' ? '0 1px 2px rgba(0,0,0,0.05)' : 'none',
                                opacity: existingTranscripts.length === 0 ? 0.5 : 1,
                                transition: 'all 0.2s ease'
                            }}
                            onClick={() => handleTabClick('caspa')}
                            disabled={existingTranscripts.length === 0}
                            title={existingTranscripts.length === 0 ? "Upload/verify transcripts first" : ""}
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" style={{ marginRight: '10px' }} width="18" height="18" viewBox="0 0 24 24" fill="none" stroke={activeTab === 'caspa' ? '#2563eb' : '#6b7280'} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <line x1="18" y1="20" x2="18" y2="10"></line>
                                <line x1="12" y1="20" x2="12" y2="4"></line>
                                <line x1="6" y1="20" x2="6" y2="14"></line>
                            </svg>
                            CASPA GPA
                        </button>
                    </div>
                    <div style={{
                        marginBottom: '12px',
                        transition: 'all 0.3s ease'
                    }}>
                        <button
                            style={{ 
                                background: activeTab === 'prerequisites' ? '#EBF5FF' : 'white',
                                border: 'none',
                                fontSize: '16px',
                                fontWeight: activeTab === 'prerequisites' ? 'bold' : 'normal',
                                color: activeTab === 'prerequisites' ? '#2563eb' : '#4b5563',
                                cursor: existingTranscripts.length === 0 ? 'not-allowed' : 'pointer',
                                padding: '10px 15px',
                                textAlign: 'left',
                                width: '100%',
                                borderRadius: '6px',
                                display: 'flex',
                                alignItems: 'center',
                                boxShadow: activeTab === 'prerequisites' ? '0 1px 2px rgba(0,0,0,0.05)' : 'none',
                                opacity: existingTranscripts.length === 0 ? 0.5 : 1,
                                transition: 'all 0.2s ease'
                            }}
                            onClick={() => handleTabClick('prerequisites')}
                            disabled={existingTranscripts.length === 0}
                            title={existingTranscripts.length === 0 ? "Upload/verify transcripts first" : ""}
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" style={{ marginRight: '10px' }} width="18" height="18" viewBox="0 0 24 24" fill="none" stroke={activeTab === 'prerequisites' ? '#2563eb' : '#6b7280'} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <polygon points="12 2 2 7 12 12 22 7 12 2"></polygon>
                                <polyline points="2 17 12 22 22 17"></polyline>
                                <polyline points="2 12 12 17 22 12"></polyline>
                            </svg>
                            Prerequisites
                        </button>
                    </div>

                    {/* Experience Hours */}
                    {activeTab === 'profile' && (
                        <div style={{ marginTop: '30px' }}>
                            <h2 style={{ fontSize: '18px', marginBottom: '15px' }}>Experience Hours</h2>
                            <div style={{ marginBottom: '15px' }}>
                                <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>Direct Patient Care</div>
                                <input 
                                    type="number" 
                                    name="directPatientCareHours" 
                                    value={profileData.directPatientCareHours} 
                                    onChange={handleInputChange}
                                    style={{ 
                                        width: '100%', 
                                        padding: '6px 8px', 
                                        border: '1px solid #cbd5e1', 
                                        borderRadius: '4px',
                                        fontSize: '14px'
                                    }}
                                    min="0"
                                    placeholder="Enter hours"
                                />
                            </div>
                            <div style={{ marginBottom: '10px' }}>
                                <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>Shadowing</div>
                                <input 
                                    type="number" 
                                    name="shadowingHours" 
                                    value={profileData.shadowingHours} 
                                    onChange={handleInputChange}
                                    style={{ 
                                        width: '100%', 
                                        padding: '6px 8px', 
                                        border: '1px solid #cbd5e1', 
                                        borderRadius: '4px',
                                        fontSize: '14px'
                                    }}
                                    min="0"
                                    placeholder="Enter hours"
                                />
                            </div>
                            <div style={{ 
                                fontSize: '12px', 
                                color: '#64748b', 
                                fontStyle: 'italic', 
                                marginTop: '5px',
                                backgroundColor: '#f1f5f9',
                                padding: '8px',
                                borderRadius: '4px'
                            }}>
                                Click "Save Profile Changes" below to update your experience hours.
                            </div>
                        </div>
                    )}

                    {/* Save Profile Button */}
                    {activeTab === 'profile' && (
                        <button 
                            type="button" 
                            onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                console.log("Save button clicked");
                                handleProfileSubmit(e);
                            }}
                            style={{ 
                                backgroundColor: '#1e40af',
                                color: 'white',
                                border: 'none',
                                padding: '10px 15px',
                                borderRadius: '4px',
                                cursor: 'pointer',
                                width: '100%',
                                marginTop: '30px',
                                fontWeight: 'bold'
                            }}
                            disabled={isSaving}
                        >
                            {isSaving ? 'Saving...' : 'Save Profile Changes'}
                        </button>
                    )}
                </div>

                {/* Main Content Area */}
                <div style={{ flex: 1 }}>
                    {/* Error messages */}
                    {shouldShowGeneralError && <div style={{ color: 'red', margin: '10px 0' }}>Error: {error}</div>}
                    {saveMessage && (
                        <div style={{ 
                            padding: '10px', 
                            marginBottom: '10px', 
                            borderRadius: '4px',
                            backgroundColor: saveMessage.includes('Error') ? '#fee2e2' : '#d1fae5',
                            color: saveMessage.includes('Error') ? '#b91c1c' : '#047857'
                        }}>
                            {saveMessage}
                        </div>
                    )}

                    {/* Tab Content */}
                    <div>
                        {/* Profile & Tests Tab */}
                        {activeTab === 'profile' && (
                            <div>
                                {/* Personal Information Section */}
                                <form onSubmit={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    console.log("Form submit prevented");
                                    return false;
                                }}>
                                    <div style={{ 
                                        backgroundColor: 'white',
                                        borderRadius: '8px',
                                        boxShadow: '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)',
                                        padding: '20px',
                                        marginBottom: '20px'
                                    }}>
                                        <h2 style={{ margin: '0 0 20px 0', fontSize: '20px' }}>Personal Information</h2>
                                    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '15px' }}>
                                        <div>
                                            <div style={{ marginBottom: '15px' }}>
                                                <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>First Name</div>
                                                <input 
                                                    type="text" 
                                                    name="firstName" 
                                                    value={profileData.firstName} 
                                                    onChange={handleInputChange} 
                                                    style={{ width: '90%', padding: '8px', border: '1px solid #ddd', borderRadius: '4px' }}
                                                />
                                            </div>
                                        </div>
                                        <div>
                                            <div style={{ marginBottom: '15px' }}>
                                                <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>Last Name</div>
                                                <input 
                                                    type="text" 
                                                    name="lastName" 
                                                    value={profileData.lastName} 
                                                    onChange={handleInputChange} 
                                                    style={{ width: '90%', padding: '8px', border: '1px solid #ddd', borderRadius: '4px' }}
                                                />
                                            </div>
                                        </div>
                                        <div>
                                            <div style={{ marginBottom: '15px' }}>
                                                <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>Email</div>
                                                <input 
                                                    type="email" 
                                                    name="email" 
                                                    value={profileData.email} 
                                                    readOnly 
                                                    style={{ width: '90%', padding: '8px', border: '1px solid #ddd', borderRadius: '4px', backgroundColor: '#f5f5f5' }}
                                                />
                                            </div>
                                        </div>
                                        <div>
                                            <div style={{ marginBottom: '15px' }}>
                                                <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>Date of Birth</div>
                                                <input 
                                                    type="date" 
                                                    name="dateOfBirth" 
                                                    value={profileData.dateOfBirth} 
                                                    onChange={handleInputChange} 
                                                    style={{ width: '90%', padding: '8px', border: '1px solid #ddd', borderRadius: '4px' }}
                                                />
                                            </div>
                                        </div>
                                        <div>
                                            <div style={{ marginBottom: '15px' }}>
                                                <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>Gender</div>
                                                <select 
                                                    name="gender" 
                                                    value={profileData.gender} 
                                                    onChange={handleInputChange} 
                                                    style={{ 
                                                        width: '95%', 
                                                        padding: '8px', 
                                                        border: '1px solid #ddd', 
                                                        borderRadius: '4px',
                                                        backgroundColor: 'white',
                                                        fontSize: '14px'
                                                    }}
                                                >
                                                    <option value="">Select Gender</option>
                                                    <option value="Male">Male</option>
                                                    <option value="Female">Female</option>
                                                    <option value="Prefer not to answer">Prefer not to answer</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                </form>

                                {/* Standardized Tests Section */}
                                <div style={{ 
                                    backgroundColor: 'white',
                                    borderRadius: '8px',
                                    boxShadow: '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)',
                                    padding: '20px',
                                    marginBottom: '20px'
                                }}>
                                    <h2 style={{ margin: '0 0 20px 0', fontSize: '20px' }}>Standardized Tests</h2>
                                    
                                    {/* GRE Scores Section */}
                                    <div style={{ marginBottom: '30px' }}>
                                        <div style={{ 
                                            display: 'flex', 
                                            justifyContent: 'space-between', 
                                            alignItems: 'center', 
                                            marginBottom: '15px',
                                            borderBottom: '1px solid #eee',
                                            paddingBottom: '10px'
                                        }}>
                                            <h3 style={{ margin: 0, fontSize: '18px' }}>GRE Scores</h3>
                                            <button 
                                                onClick={() => setIsEditingGREScores(true)} 
                                                style={{
                                                    background: 'white',
                                                    border: '1px solid #ddd',
                                                    borderRadius: '4px',
                                                    padding: '5px 10px',
                                                    fontSize: '14px',
                                                    cursor: 'pointer'
                                                }}
                                            >
                                                Edit GRE Scores
                                            </button>
                                        </div>
                                        
                                        {isEditingGREScores ? (
                                            <>
                                                <div className="form-grid test-grid">
                                                    <div className="form-group"><label>Verbal Score:</label><input type="number" name="gre_verbal_score" value={profileData.gre_verbal_score} onChange={handleInputChange} /></div>
                                                    <div className="form-group"><label>Verbal %:</label><input type="number" name="gre_verbal_percentile" value={profileData.gre_verbal_percentile} onChange={handleInputChange} /></div>
                                                    <div className="form-group"><label>Quant Score:</label><input type="number" name="gre_quantitative_score" value={profileData.gre_quantitative_score} onChange={handleInputChange} /></div>
                                                    <div className="form-group"><label>Quant %:</label><input type="number" name="gre_quantitative_percentile" value={profileData.gre_quantitative_percentile} onChange={handleInputChange} /></div>
                                                    <div className="form-group"><label>Writing Score:</label><input type="number" step="0.5" name="gre_analytical_writing_score" value={profileData.gre_analytical_writing_score} onChange={handleInputChange} /></div>
                                                    <div className="form-group"><label>Writing %:</label><input type="number" name="gre_analytical_writing_percentile" value={profileData.gre_analytical_writing_percentile} onChange={handleInputChange} /></div>
                                                    <div className="form-group full-width-grid-item"><label>Test Date:</label><input type="date" name="gre_test_date" value={profileData.gre_test_date} onChange={handleInputChange} /></div>
                                                </div>
                                                <button type="button" onClick={handleSaveGREScores} disabled={isSaving} className="save-test-btn">
                                                    {isSaving ? 'Saving...' : 'Save GRE Scores'}
                                                </button>
                                                {(profileData.gre_test_date || profileData.gre_verbal_score) && (
                                                    <button type="button" onClick={() => { setIsEditingGREScores(false); fetchProfileData(); }} className="cancel-test-btn">Cancel</button>
                                                )}
                                            </>
                                        ) : (
                                            <>
                                                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
                                                    <div>
                                                        <p style={{ fontWeight: 'bold', margin: '0 0 5px 0' }}>Verbal</p>
                                                        <div style={{ fontSize: '15px', color: '#333', marginBottom: '5px' }}>
                                                            {profileData.gre_verbal_score ? profileData.gre_verbal_score : 'N/A'}
                                                        </div>
                                                        <div style={{ fontSize: '14px', color: '#666' }}>
                                                            Percentile Rank: {profileData.gre_verbal_percentile ? profileData.gre_verbal_percentile : 'N/A'}
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <p style={{ fontWeight: 'bold', margin: '0 0 5px 0' }}>Quantitative</p>
                                                        <div style={{ fontSize: '15px', color: '#333', marginBottom: '5px' }}>
                                                            {profileData.gre_quantitative_score ? profileData.gre_quantitative_score : 'N/A'}
                                                        </div>
                                                        <div style={{ fontSize: '14px', color: '#666' }}>
                                                            Percentile Rank: {profileData.gre_quantitative_percentile ? profileData.gre_quantitative_percentile : 'N/A'}
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <p style={{ fontWeight: 'bold', margin: '0 0 5px 0' }}>Analytical Writing</p>
                                                        <div style={{ fontSize: '15px', color: '#333', marginBottom: '5px' }}>
                                                            {profileData.gre_analytical_writing_score ? profileData.gre_analytical_writing_score : 'N/A'}
                                                        </div>
                                                        <div style={{ fontSize: '14px', color: '#666' }}>
                                                            Percentile Rank: {profileData.gre_analytical_writing_percentile ? profileData.gre_analytical_writing_percentile : 'N/A'}
                                                        </div>
                                                    </div>
                                                </div>
                                                {profileData.gre_test_date && (
                                                    <div style={{ marginTop: '15px' }}>
                                                        <p style={{ fontWeight: 'bold', margin: '0 0 5px 0' }}>Test Date</p>
                                                        <div style={{ fontSize: '15px' }}>
                                                            {new Date(profileData.gre_test_date).toLocaleDateString()}
                                                        </div>
                                                    </div>
                                                )}
                                            </>
                                        )}
                                    </div>
                                    
                                    {/* PA-CAT Scores Section */}
                                    <div style={{ marginTop: '30px' }}>
                                        <div style={{ 
                                            display: 'flex', 
                                            justifyContent: 'space-between', 
                                            alignItems: 'center', 
                                            marginBottom: '15px',
                                            borderBottom: '1px solid #eee',
                                            paddingBottom: '10px'
                                        }}>
                                            <h3 style={{ margin: 0, fontSize: '18px' }}>PA-CAT Scores</h3>
                                            <button 
                                                onClick={() => setIsEditingPaCatScores(true)} 
                                                style={{
                                                    background: 'white',
                                                    border: '1px solid #ddd',
                                                    borderRadius: '4px',
                                                    padding: '5px 10px',
                                                    fontSize: '14px',
                                                    cursor: 'pointer'
                                                }}
                                            >
                                                Edit PA-CAT Scores
                                            </button>
                                        </div>
                                        
                                        {isEditingPaCatScores ? (
                                            <>
                                                <div className="form-grid test-grid">
                                                    <div className="form-group"><label>Anatomy SS:</label><input type="number" name="pa_cat_anatomy_ss" value={profileData.pa_cat_anatomy_ss} onChange={handleInputChange} /></div>
                                                    <div className="form-group"><label>Anatomy PR:</label><input type="number" name="pa_cat_anatomy_pr" value={profileData.pa_cat_anatomy_pr} onChange={handleInputChange} /></div>
                                                    <div className="form-group"><label>Physiology SS:</label><input type="number" name="pa_cat_physiology_ss" value={profileData.pa_cat_physiology_ss} onChange={handleInputChange} /></div>
                                                    <div className="form-group"><label>Physiology PR:</label><input type="number" name="pa_cat_physiology_pr" value={profileData.pa_cat_physiology_pr} onChange={handleInputChange} /></div>
                                                    <div className="form-group"><label>Biology SS:</label><input type="number" name="pa_cat_biology_ss" value={profileData.pa_cat_biology_ss} onChange={handleInputChange} /></div>
                                                    <div className="form-group"><label>Biology PR:</label><input type="number" name="pa_cat_biology_pr" value={profileData.pa_cat_biology_pr} onChange={handleInputChange} /></div>
                                                    <div className="form-group"><label>Chemistry SS:</label><input type="number" name="pa_cat_chemistry_ss" value={profileData.pa_cat_chemistry_ss} onChange={handleInputChange} /></div>
                                                    <div className="form-group"><label>Chemistry PR:</label><input type="number" name="pa_cat_chemistry_pr" value={profileData.pa_cat_chemistry_pr} onChange={handleInputChange} /></div>
                                                    <div className="form-group"><label>Composite SS:</label><input type="number" name="pa_cat_composite_ss" value={profileData.pa_cat_composite_ss} onChange={handleInputChange} /></div>
                                                    <div className="form-group"><label>Composite PR:</label><input type="number" name="pa_cat_composite_pr" value={profileData.pa_cat_composite_pr} onChange={handleInputChange} /></div>
                                                    <div className="form-group full-width-grid-item"><label>Test Date:</label><input type="date" name="pa_cat_test_date" value={profileData.pa_cat_test_date} onChange={handleInputChange} /></div>
                                                </div>
                                                <button type="button" onClick={handleSavePaCatScores} disabled={isSaving} className="save-test-btn">
                                                    {isSaving ? 'Saving...' : 'Save PA-CAT Scores'}
                                                </button>
                                                {(profileData.pa_cat_test_date || profileData.pa_cat_composite_ss) && (
                                                    <button type="button" onClick={() => { setIsEditingPaCatScores(false); fetchProfileData(); }} className="cancel-test-btn">Cancel</button>
                                                )}
                                            </>
                                        ) : (
                                            <>
                                                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
                                                    <div>
                                                        <p style={{ fontWeight: 'bold', margin: '0 0 5px 0' }}>Anatomy</p>
                                                        <div style={{ fontSize: '15px', color: '#333', marginBottom: '5px' }}>
                                                            {profileData.pa_cat_anatomy_ss ? profileData.pa_cat_anatomy_ss : 'N/A'}
                                                        </div>
                                                        <div style={{ fontSize: '14px', color: '#666' }}>
                                                            Percentile Rank: {profileData.pa_cat_anatomy_pr ? profileData.pa_cat_anatomy_pr : 'N/A'}
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <p style={{ fontWeight: 'bold', margin: '0 0 5px 0' }}>Physiology</p>
                                                        <div style={{ fontSize: '15px', color: '#333', marginBottom: '5px' }}>
                                                            {profileData.pa_cat_physiology_ss ? profileData.pa_cat_physiology_ss : 'N/A'}
                                                        </div>
                                                        <div style={{ fontSize: '14px', color: '#666' }}>
                                                            Percentile Rank: {profileData.pa_cat_physiology_pr ? profileData.pa_cat_physiology_pr : 'N/A'}
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <p style={{ fontWeight: 'bold', margin: '0 0 5px 0' }}>Biology</p>
                                                        <div style={{ fontSize: '15px', color: '#333', marginBottom: '5px' }}>
                                                            {profileData.pa_cat_biology_ss ? profileData.pa_cat_biology_ss : 'N/A'}
                                                        </div>
                                                        <div style={{ fontSize: '14px', color: '#666' }}>
                                                            Percentile Rank: {profileData.pa_cat_biology_pr ? profileData.pa_cat_biology_pr : 'N/A'}
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <p style={{ fontWeight: 'bold', margin: '0 0 5px 0' }}>Composite</p>
                                                        <div style={{ fontSize: '15px', color: '#333', marginBottom: '5px' }}>
                                                            {profileData.pa_cat_composite_ss ? profileData.pa_cat_composite_ss : 'N/A'}
                                                        </div>
                                                        <div style={{ fontSize: '14px', color: '#666' }}>
                                                            Percentile Rank: {profileData.pa_cat_composite_pr ? profileData.pa_cat_composite_pr : 'N/A'}
                                                        </div>
                                                    </div>
                                                </div>
                                                {profileData.pa_cat_test_date && (
                                                    <div style={{ marginTop: '15px' }}>
                                                        <p style={{ fontWeight: 'bold', margin: '0 0 5px 0' }}>Test Date</p>
                                                        <div style={{ fontSize: '15px' }}>
                                                            {new Date(profileData.pa_cat_test_date).toLocaleDateString()}
                                                        </div>
                                                    </div>
                                                )}
                                            </>
                                        )}
                                    </div>
                                    
                                    {/* CASPer Section */}
                                    <div style={{ marginTop: '30px' }}>
                                        <div style={{ 
                                            display: 'flex', 
                                            justifyContent: 'space-between', 
                                            alignItems: 'center', 
                                            marginBottom: '15px',
                                            borderBottom: '1px solid #eee',
                                            paddingBottom: '10px'
                                        }}>
                                            <h3 style={{ margin: 0, fontSize: '18px' }}>CASPer</h3>
                                            <button 
                                                onClick={() => setIsEditingCasperDate(true)} 
                                                style={{
                                                    background: 'white',
                                                    border: '1px solid #ddd',
                                                    borderRadius: '4px',
                                                    padding: '5px 10px',
                                                    fontSize: '14px',
                                                    cursor: 'pointer'
                                                }}
                                            >
                                                Edit CASPer Info
                                            </button>
                                        </div>
                                        
                                        {isEditingCasperDate ? (
                                            <>
                                                <div className="form-grid casper-grid">
                                                    <div className="form-group casper-checkbox">
                                                        <label>
                                                            <input type="checkbox" name="has_taken_casper" checked={profileData.has_taken_casper} onChange={handleCheckboxChange} />
                                                            I have taken/scheduled the CASPer test
                                                        </label>
                                                    </div>
                                                    <div className="form-group casper-date">
                                                        <label>Test Date:</label>
                                                        <input type="date" name="casper_test_date" value={profileData.casper_test_date} onChange={handleInputChange} disabled={!profileData.has_taken_casper} />
                                                    </div>
                                                </div>
                                                <button type="button" onClick={handleSaveCasperDate} disabled={isSaving} className="save-test-btn">
                                                    {isSaving ? 'Saving...' : 'Save CASPer Info'}
                                                </button>
                                                {(profileData.casper_test_date || profileData.has_taken_casper) && (
                                                    <button type="button" onClick={() => { setIsEditingCasperDate(false); fetchProfileData(); }} className="cancel-test-btn">Cancel</button>
                                                )}
                                            </>
                                        ) : (
                                            <div style={{ display: 'flex', gap: '30px' }}>
                                                <div>
                                                    <p style={{ fontWeight: 'bold', margin: '0 0 5px 0' }}>Taken</p>
                                                    <div style={{ fontSize: '15px' }}>
                                                        {profileData.has_taken_casper ? 'Yes' : 'No'}
                                                    </div>
                                                </div>
                                                {profileData.has_taken_casper && profileData.casper_test_date && (
                                                    <div>
                                                        <p style={{ fontWeight: 'bold', margin: '0 0 5px 0' }}>Test Date</p>
                                                        <div style={{ fontSize: '15px' }}>
                                                            {new Date(profileData.casper_test_date).toLocaleDateString()}
                                                        </div>
                                                    </div>
                                                )}
                                            </div>
                                        )}
                                    </div>
                                </div>
                                

                            </div>
                        )}

                        {/* Other tabs - transcripts, caspa, prerequisites */}
                        {activeTab === 'transcripts' && (
                            <div className="profile-section">
                                <h2>Transcript Management</h2>

                                {/* Transcript Upload Component */}
                                <TranscriptUpload
                                    sessionId={sessionId}
                                    transcripts={s3Transcripts} // List of uploaded files
                                    setTranscripts={setS3Transcripts}
                                    onTranscriptUpdate={fetchS3Transcripts} // Callback to refresh S3 list
                                    onProcessingStart={(data) => { // Callback when processing starts
                                        setTranscriptData(data); // Set data for validation modal
                                        setProcessingTranscript(data); // Keep track of which one is processing
                                        setValidationStatus('pending'); // Reset validation status
                                    }}
                                />

                                {/* Verified Transcripts Section */}
                                {existingTranscripts.length > 0 && (
                                    <div className="verified-transcripts-section" style={{
                                        marginTop: '30px',
                                        backgroundColor: 'white',
                                        borderRadius: '8px',
                                        boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
                                        padding: '15px'
                                    }}>
                                        <div style={{ 
                                            display: 'flex', 
                                            justifyContent: 'space-between',
                                            alignItems: 'center', 
                                            marginBottom: '12px'
                                        }}>
                                            <h3 style={{ 
                                                display: 'flex', 
                                                alignItems: 'center', 
                                                margin: '0',
                                                fontSize: '18px',
                                                color: '#1e40af'
                                            }}>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{ marginRight: '8px' }}>
                                                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                                                </svg>
                                                Verified Transcript Analysis
                                            </h3>
                                            <span style={{ 
                                                backgroundColor: '#f0f9ff', 
                                                color: '#0891b2', 
                                                padding: '3px 8px', 
                                                borderRadius: '12px', 
                                                fontSize: '13px',
                                                fontWeight: '500' 
                                            }}>
                                                {existingTranscripts.length} {existingTranscripts.length === 1 ? 'Transcript' : 'Transcripts'}
                                            </span>
                                        </div>
                                        
                                        <div className="transcript-records" style={{ display: 'flex', flexWrap: 'wrap', gap: '10px' }}>
                                            {existingTranscripts.map(transcript => (
                                                <div key={transcript.id} className="institution-section" style={{
                                                    flex: '1 0 300px',
                                                    backgroundColor: '#f8fafc',
                                                    borderRadius: '8px',
                                                    overflow: 'hidden',
                                                    border: '1px solid #e2e8f0'
                                                }}>
                                                    <div className="institution-header" style={{
                                                        display: 'flex',
                                                        justifyContent: 'space-between',
                                                        alignItems: 'center',
                                                        cursor: 'pointer',
                                                        padding: '8px 12px',
                                                        backgroundColor: expandedSchools[transcript.institution] ? '#e0e7ff' : '#f1f5f9',
                                                        borderBottom: '1px solid #e2e8f0',
                                                        transition: 'background-color 0.2s'
                                                    }} onClick={() => toggleSchoolTranscript(transcript.institution)}>
                                                        <h4 style={{ 
                                                            margin: '0', 
                                                            fontSize: '15px',
                                                            display: 'flex', 
                                                            alignItems: 'center',
                                                            color: expandedSchools[transcript.institution] ? '#4338ca' : '#334155'
                                                        }}>
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{ marginRight: '6px' }}>
                                                                <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                                                                <polyline points="9 22 9 12 15 12 15 22"></polyline>
                                                            </svg>
                                                            {transcript.institution}
                                                        </h4>
                                                        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                                                            <div style={{ 
                                                                fontSize: '13px',
                                                                backgroundColor: '#f0fdfa',
                                                                color: '#0d9488',
                                                                fontWeight: '600',
                                                                padding: '2px 6px',
                                                                borderRadius: '4px'
                                                            }}>
                                                                GPA: {roundUpGPA(transcript.transcript_data?.academic_summary?.cumulative_gpa)}
                                                            </div>
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                                                {expandedSchools[transcript.institution] ? 
                                                                    <polyline points="18 15 12 9 6 15"></polyline> : 
                                                                    <polyline points="6 9 12 15 18 9"></polyline>
                                                                }
                                                            </svg>
                                                        </div>
                                                    </div>
                                                    
                                                    {expandedSchools[transcript.institution] && (
                                                        <div style={{ padding: '10px' }}>
                                                            <div style={{ 
                                                                display: 'flex', 
                                                                gap: '8px', 
                                                                marginBottom: '10px',
                                                                flexWrap: 'wrap'
                                                            }}>
                                                                <div style={{ 
                                                                    flex: '1', 
                                                                    backgroundColor: 'white',
                                                                    borderRadius: '6px',
                                                                    padding: '6px 10px',
                                                                    fontSize: '13px',
                                                                    border: '1px solid #e2e8f0',
                                                                    display: 'flex',
                                                                    alignItems: 'center'
                                                                }}>
                                                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="#0d9488" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{ marginRight: '6px' }}>
                                                                        <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20"></path>
                                                                        <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z"></path>
                                                                    </svg>
                                                                    <div>
                                                                        <span style={{ color: '#64748b', marginRight: '5px' }}>Overall:</span> 
                                                                        <strong>{roundUpGPA(transcript.transcript_data?.academic_summary?.cumulative_gpa)}</strong>
                                                                    </div>
                                                                </div>
                                                                <div style={{ 
                                                                    flex: '1', 
                                                                    backgroundColor: 'white',
                                                                    borderRadius: '6px',
                                                                    padding: '6px 10px',
                                                                    fontSize: '13px',
                                                                    border: '1px solid #e2e8f0',
                                                                    display: 'flex',
                                                                    alignItems: 'center'
                                                                }}>
                                                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="#0891b2" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{ marginRight: '6px' }}>
                                                                        <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
                                                                    </svg>
                                                                    <div>
                                                                        <span style={{ color: '#64748b', marginRight: '5px' }}>Science:</span> 
                                                                        <strong>{roundUpGPA(transcript.transcript_data?.academic_summary?.science_gpa)}</strong>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            
                                                            <div style={{ 
                                                                fontSize: '12px',
                                                                color: '#64748b',
                                                                marginBottom: '12px',
                                                                display: 'flex',
                                                                justifyContent: 'space-between',
                                                                alignItems: 'center'
                                                            }}>
                                                                <span>{transcript.entry_method === 'manual' ? 'Manual Entry' : 'PDF Processing'}</span>
                                                                <button 
                                                                    onClick={(e) => {
                                                                        e.stopPropagation();
                                                                        handleDeleteTranscript(transcript.id, transcript.institution);
                                                                    }} 
                                                                    style={{
                                                                        background: 'none',
                                                                        border: 'none',
                                                                        color: '#ef4444',
                                                                        cursor: 'pointer',
                                                                        padding: '3px',
                                                                        borderRadius: '4px',
                                                                        display: 'flex',
                                                                        alignItems: 'center',
                                                                        fontSize: '12px'
                                                                    }}
                                                                >
                                                                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{ marginRight: '4px' }}>
                                                                        <polyline points="3 6 5 6 21 6"></polyline>
                                                                        <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                                                                    </svg>
                                                                    Delete
                                                                </button>
                                                            </div>
                                                            
                                                            {/* Course List - Table Format for compactness */}
                                                            <div style={{ 
                                                                maxHeight: '230px', 
                                                                overflowY: 'auto',
                                                                border: '1px solid #e2e8f0',
                                                                borderRadius: '6px',
                                                                backgroundColor: 'white'
                                                            }}>
                                                                <table style={{ 
                                                                    width: '100%', 
                                                                    borderCollapse: 'collapse',
                                                                    fontSize: '12px'
                                                                }}>
                                                                    <thead>
                                                                        <tr style={{ backgroundColor: '#f8fafc', borderBottom: '1px solid #e2e8f0' }}>
                                                                            <th style={{ padding: '6px', textAlign: 'left', fontSize: '12px', fontWeight: '600', color: '#475569' }}>Course</th>
                                                                            <th style={{ padding: '6px', textAlign: 'center', fontSize: '12px', fontWeight: '600', color: '#475569', width: '50px' }}>Credits</th>
                                                                            <th style={{ padding: '6px', textAlign: 'center', fontSize: '12px', fontWeight: '600', color: '#475569', width: '50px' }}>Grade</th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>
                                                                        {transcript.transcript_data?.semesters?.flatMap((semester, semesterIndex) => [
                                                                            // Semester header row
                                                                            <tr key={`sem-${semesterIndex}`} style={{ backgroundColor: '#fafafa' }}>
                                                                                <td colSpan="3" style={{ 
                                                                                    padding: '4px 6px', 
                                                                                    fontSize: '11px', 
                                                                                    fontWeight: '600',
                                                                                    color: '#64748b',
                                                                                    borderBottom: '1px solid #f1f5f9',
                                                                                    borderTop: semesterIndex > 0 ? '1px solid #f1f5f9' : 'none'
                                                                                }}>
                                                                                    {semester.term} {semester.year}
                                                                                </td>
                                                                            </tr>,
                                                                            // Course rows
                                                                            ...semester.courses.map((course, courseIndex) => (
                                                                                <tr key={`course-${semesterIndex}-${courseIndex}`} 
                                                                                    style={{ 
                                                                                        backgroundColor: courseIndex % 2 === 0 ? 'white' : '#fafafa'
                                                                                    }}
                                                                                >
                                                                                    <td style={{ padding: '3px 6px', borderBottom: '1px solid #f1f5f9' }}>
                                                                                        <div style={{ display: 'flex', alignItems: 'center' }}>
                                                                                            <span style={{ fontWeight: '500' }}>{course.code}</span>
                                                                                            <span style={{ margin: '0 4px', color: '#94a3b8' }}>-</span>
                                                                                            <span style={{ 
                                                                                                whiteSpace: 'nowrap', 
                                                                                                overflow: 'hidden', 
                                                                                                textOverflow: 'ellipsis', 
                                                                                                maxWidth: '140px',
                                                                                                display: 'inline-block'
                                                                                            }}>
                                                                                                {course.name}
                                                                                            </span>
                                                                                            {course.has_lab && <span style={{ 
                                                                                                backgroundColor: '#dbeafe', 
                                                                                                color: '#2563eb',
                                                                                                padding: '0px 4px',
                                                                                                borderRadius: '4px',
                                                                                                fontSize: '10px',
                                                                                                margin: '0 0 0 4px'
                                                                                            }}>Lab</span>}
                                                                                        </div>
                                                                                    </td>
                                                                                    <td style={{ 
                                                                                        padding: '3px 6px', 
                                                                                        textAlign: 'center',
                                                                                        borderBottom: '1px solid #f1f5f9' 
                                                                                    }}>
                                                                                        {course.credits}
                                                                                    </td>
                                                                                    <td style={{ 
                                                                                        padding: '3px 6px', 
                                                                                        textAlign: 'center',
                                                                                        fontWeight: '500',
                                                                                        color: course.grade === 'A' || course.grade === 'A+' || course.grade === 'A-' ? '#15803d' : 
                                                                                               course.grade === 'B+' || course.grade === 'B' || course.grade === 'B-' ? '#0891b2' : 
                                                                                               course.grade === 'C+' || course.grade === 'C' || course.grade === 'C-' ? '#b45309' : 
                                                                                               course.grade === 'D+' || course.grade === 'D' || course.grade === 'D-' || course.grade === 'F' ? '#b91c1c' : 
                                                                                               '#64748b',
                                                                                        borderBottom: '1px solid #f1f5f9'
                                                                                    }}>
                                                                                        {course.grade}
                                                                                    </td>
                                                                                </tr>
                                                                            ))
                                                                        ])}
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </div>
                                                    )}
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                )}

                                {/* Manual Transcript Entry Section */}
                                <div className="manual-transcript-section">
                                    <h3>Manual Transcript Entry</h3>
                                    <button
                                        onClick={() => setShowManualEntry(!showManualEntry)}
                                        className="toggle-manual-entry-btn"
                                    >
                                        {showManualEntry ? (
                                            <>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                                    <line x1="18" y1="6" x2="6" y2="18"></line>
                                                    <line x1="6" y1="6" x2="18" y2="18"></line>
                                                </svg>
                                                Hide Manual Entry
                                            </>
                                        ) : (
                                            <>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                                    <line x1="12" y1="5" x2="12" y2="19"></line>
                                                    <line x1="5" y1="12" x2="19" y2="12"></line>
                                                </svg>
                                                Add Manual Transcript
                                            </>
                                        )}
                                    </button>

                                    {showManualEntry && (
                                        <div className="manual-entry-form">
                                            {/* Institution Input */}
                                            <div className="institution-section">
                                                <label htmlFor="institution-name">Institution Name</label>
                                                <input
                                                    id="institution-name"
                                                    type="text"
                                                    placeholder="Enter your school name (e.g., University of Nevada)"
                                                    value={manualTranscript.institution}
                                                    onChange={(e) => setManualTranscript(prev => ({ ...prev, institution: e.target.value }))}
                                                    className="institution-input"
                                                />
                                            </div>

                                            {/* Semester & Course Entry */}
                                            <div className="semester-section">
                                                <h4>Add Semester</h4>
                                                <div className="semester-header">
                                                    <div className="semester-input-group">
                                                        <label htmlFor="semester-term">Term</label>
                                                        <select 
                                                            id="semester-term"
                                                            value={currentSemester.term} 
                                                            onChange={(e) => setCurrentSemester(prev => ({ ...prev, term: e.target.value }))} 
                                                            className="semester-select"
                                                        >
                                                            <option value="">Select Term</option>
                                                            <option value="Fall">Fall</option>
                                                            <option value="Spring">Spring</option>
                                                            <option value="Summer">Summer</option>
                                                            <option value="Winter">Winter</option>
                                                        </select>
                                                    </div>

                                                    <div className="semester-input-group">
                                                        <label htmlFor="semester-year">Year</label>
                                                        <input 
                                                            id="semester-year"
                                                            type="number" 
                                                            placeholder="YYYY" 
                                                            value={currentSemester.year} 
                                                            onChange={(e) => setCurrentSemester(prev => ({ ...prev, year: e.target.value }))} 
                                                            className="year-input" 
                                                        />
                                                    </div>
                                                    
                                                    <div className="semester-input-group">
                                                        <label htmlFor="calendar-type">Calendar Type</label>
                                                        <select 
                                                            id="calendar-type"
                                                            value={currentSemester.calendarType} 
                                                            onChange={(e) => setCurrentSemester(prev => ({ ...prev, calendarType: e.target.value }))} 
                                                            className="calendar-type-select"
                                                        >
                                                            <option value="Semester">Semester</option>
                                                            <option value="Quarter">Quarter</option>
                                                            <option value="Trimester">Trimester</option>
                                                        </select>
                                                    </div>
                                                </div>

                                                <h5>Add Course to Semester</h5>
                                                <div className="course-entry">
                                                    <div className="course-input-group">
                                                        <label htmlFor="course-code">Course Code</label>
                                                        <input 
                                                            id="course-code"
                                                            type="text" 
                                                            placeholder="e.g., BIOL 101" 
                                                            value={currentCourse.code} 
                                                            onChange={(e) => setCurrentCourse(prev => ({ ...prev, code: e.target.value }))} 
                                                            className="course-input" 
                                                        />
                                                    </div>
                                                    
                                                    <div className="course-input-group">
                                                        <label htmlFor="course-name">Course Name</label>
                                                        <input 
                                                            id="course-name"
                                                            type="text" 
                                                            placeholder="e.g., Introduction to Biology" 
                                                            value={currentCourse.name} 
                                                            onChange={(e) => setCurrentCourse(prev => ({ ...prev, name: e.target.value }))} 
                                                            className="course-input" 
                                                        />
                                                    </div>
                                                    
                                                    <div className="course-input-group">
                                                        <label htmlFor="course-credits">Credits</label>
                                                        <input 
                                                            id="course-credits"
                                                            type="number" 
                                                            placeholder="e.g., 3" 
                                                            value={currentCourse.credits} 
                                                            onChange={(e) => setCurrentCourse(prev => ({ ...prev, credits: e.target.value }))} 
                                                            className="credits-input" 
                                                        />
                                                    </div>
                                                    
                                                    <div className="course-input-group">
                                                        <label htmlFor="course-grade">Grade</label>
                                                        <select 
                                                            id="course-grade"
                                                            value={currentCourse.grade} 
                                                            onChange={(e) => setCurrentCourse(prev => ({ ...prev, grade: e.target.value }))} 
                                                            className="grade-select"
                                                        >
                                                            <option value="">Select</option>
                                                            <option value="A+">A+</option>
                                                            <option value="A">A</option>
                                                            <option value="A-">A-</option>
                                                            <option value="B+">B+</option>
                                                            <option value="B">B</option>
                                                            <option value="B-">B-</option>
                                                            <option value="C+">C+</option>
                                                            <option value="C">C</option>
                                                            <option value="C-">C-</option>
                                                            <option value="D+">D+</option>
                                                            <option value="D">D</option>
                                                            <option value="D-">D-</option>
                                                            <option value="F">F</option>
                                                            <option value="WF">WF</option>
                                                            <option value="P">P (Pass)</option>
                                                            <option value="S">S (Satisfactory)</option>
                                                            <option value="U">U (Unsatisfactory)</option>
                                                            <option value="W">W (Withdraw)</option>
                                                        </select>
                                                    </div>
                                                    
                                                    <div className="science-toggle">
                                                        <label htmlFor="is-science">
                                                            <input 
                                                                id="is-science"
                                                                type="checkbox" 
                                                                checked={currentCourse.is_science} 
                                                                onChange={(e) => setCurrentCourse(prev => ({ ...prev, is_science: e.target.checked }))} 
                                                            /> 
                                                            Science Course
                                                        </label>
                                                    </div>
                                                    
                                                    <button 
                                                        type="button" 
                                                        onClick={handleAddCourseToSemester} 
                                                        className="add-course-btn"
                                                    >
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                                            <path d="M3 3h18v18H3zM12 8v8M8 12h8"/>
                                                        </svg>
                                                        Add Course
                                                    </button>
                                                </div>

                                                {/* Display Courses Added to Current Semester */}
                                                {currentSemester.courses.length > 0 && (
                                                    <div className="courses-list">
                                                        <h6>Courses in Current Semester:</h6>
                                                        {currentSemester.courses.map((course, index) => (
                                                            <div key={index} className={`course-item ${course.is_science ? 'science-course' : ''}`}>
                                                                <span>
                                                                    <strong>{course.code}</strong> - {course.name} ({course.credits} cr) - Grade: {course.grade} 
                                                                    {course.is_science && <span className="science-badge">Science</span>}
                                                                </span>
                                                                <button 
                                                                    onClick={() => handleRemoveCourseFromSemester(index)} 
                                                                    className="remove-course-btn"
                                                                    title="Remove course"
                                                                >
                                                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                                                        <line x1="18" y1="6" x2="6" y2="18"></line>
                                                                        <line x1="6" y1="6" x2="18" y2="18"></line>
                                                                    </svg>
                                                                </button>
                                                            </div>
                                                        ))}
                                                    </div>
                                                )}
                                                
                                                <button 
                                                    type="button" 
                                                    onClick={handleAddSemesterToTranscript} 
                                                    className="add-semester-btn"
                                                    disabled={!currentSemester.term || !currentSemester.year || currentSemester.courses.length === 0}
                                                >
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                                        <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                                                        <line x1="16" y1="2" x2="16" y2="6"></line>
                                                        <line x1="8" y1="2" x2="8" y2="6"></line>
                                                        <line x1="3" y1="10" x2="21" y2="10"></line>
                                                        <line x1="12" y1="14" x2="12" y2="18"></line>
                                                        <line x1="10" y1="16" x2="14" y2="16"></line>
                                                    </svg>
                                                    Add This Semester to Transcript
                                                </button>
                                            </div>

                                            {/* Transcript Preview & Save */}
                                            {manualTranscript.semesters.length > 0 && (
                                                <div className="transcript-preview">
                                                    <h3>
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                                            <polyline points="14 2 14 8 20 8"></polyline>
                                                            <line x1="16" y1="13" x2="8" y2="13"></line>
                                                            <line x1="16" y1="17" x2="8" y2="17"></line>
                                                            <polyline points="10 9 9 9 8 9"></polyline>
                                                        </svg>
                                                        Transcript Preview: {manualTranscript.institution}
                                                    </h3>
                                                    
                                                    {manualTranscript.semesters.map((semester, semesterIndex) => (
                                                        <div key={semesterIndex} className="semester-preview">
                                                            <h4>{semester.term} {semester.year} ({semester.calendarType || 'Semester'})</h4>
                                                            {semester.courses.map((course, courseIndex) => (
                                                                <div 
                                                                    key={courseIndex} 
                                                                    className={`course-preview ${getCourseSubjectInfo(course).isScience ? 'science-course' : ''}`}
                                                                >
                                                                    <span>
                                                                        <strong>{course.code}</strong> - {course.name} ({course.credits} cr) - Grade: {course.grade}
                                                                        {getCourseSubjectInfo(course).isScience && <span className="science-badge">Science</span>}
                                                                    </span>
                                                                </div>
                                                            ))}
                                                            <button 
                                                                onClick={() => handleRemoveSemesterFromTranscript(semesterIndex)} 
                                                                className="remove-semester-btn"
                                                            >
                                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                                                    <polyline points="3 6 5 6 21 6"></polyline>
                                                                    <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                                                                </svg>
                                                                Remove Semester
                                                            </button>
                                                        </div>
                                                    ))}
                                                    <div className="transcript-summary">
                                                        <h4>
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                                                <line x1="18" y1="20" x2="18" y2="10"></line>
                                                                <line x1="12" y1="20" x2="12" y2="4"></line>
                                                                <line x1="6" y1="20" x2="6" y2="14"></line>
                                                            </svg>
                                                            Transcript Summary (Standard Calculation)
                                                        </h4>
                                                        <div className="gpa-summary">
                                                            {(() => {
                                                                const gpas = calculateStandardGpas(manualTranscript.semesters);
                                                                return (
                                                                    <>
                                                                        <p>
                                                                            <span>Calculated GPA:</span>
                                                                            <strong>{gpas.calculated_gpa}</strong>
                                                                        </p>
                                                                        <p>
                                                                            <span>Science GPA:</span>
                                                                            <strong>{gpas.science_gpa}</strong>
                                                                        </p>
                                                                    </>
                                                                );
                                                            })()}
                                                        </div>
                                                    </div>
                                                    <button 
                                                        onClick={handleSaveManualTranscript} 
                                                        className="save-transcript-btn" 
                                                        disabled={isManualTranscriptSaving}
                                                    >
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                                            <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                                                            <polyline points="17 21 17 13 7 13 7 21"></polyline>
                                                            <polyline points="7 3 7 8 15 8"></polyline>
                                                        </svg>
                                                        {isManualTranscriptSaving ? 'Saving...' : 'Save Manual Transcript'}
                                                    </button>
                                                    {manualTranscriptSaveMessage && (
                                                        <span className={`save-message ${manualTranscriptSaveMessage.includes('Error') ? 'error' : 'success'}`}>
                                                            {manualTranscriptSaveMessage}
                                                        </span>
                                                    )}
                                                </div>
                                            )}
                                        </div>
                                    )}
                                </div>
                            </div>
                        )}

                        {/* CASPA GPA Tab */}
                        {activeTab === 'caspa' && (
                            // Wrap the entire section in a card-like container
                            <div className="profile-section caspa-gpa-card"> 
                                {/* Use a more prominent heading */}
                                <h2 className="caspa-card-header">CASPA GPA Calculation (Estimated)</h2>
                                {transcriptsLoading ? (
                                    <p>Loading transcript data...</p>
                                ) : existingTranscripts.length > 0 && caspaGpas ? (
                                    <div className="caspa-gpa-display">
                                        <div style={{ 
                                            backgroundColor: '#f0f9ff', 
                                            padding: '15px', 
                                            borderRadius: '8px', 
                                            marginBottom: '20px',
                                            border: '1px solid #bae6fd'
                                        }}>
                                            <h3 style={{ 
                                                margin: '0 0 10px 0', 
                                                fontSize: '16px', 
                                                color: '#0c4a6e',
                                                display: 'flex',
                                                alignItems: 'center'
                                            }}>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{ marginRight: '8px' }}>
                                                    <circle cx="12" cy="12" r="10"></circle>
                                                    <line x1="12" y1="16" x2="12" y2="12"></line>
                                                    <line x1="12" y1="8" x2="12.01" y2="8"></line>
                                                </svg>
                                                About CASPA GPA Calculation
                                            </h3>
                                            <p style={{ fontSize: '14px', margin: '0 0 8px 0' }}>
                                                CASPA converts all letter grades to numeric values (A=4.0, A-=3.7, etc.), 
                                                multiplies by attempted credits to get Quality Points (QP), then divides total QP 
                                                by attempted credits for your GPA. Quarter hours are converted to semester hours (1.0 quarter = 0.667 semester).
                                            </p>
                                            <p style={{ fontSize: '14px', margin: '0 0 8px 0' }}>
                                                <strong>Non-graded credits</strong> (pass/fail, AP credit, etc.) are not included in GPA calculations. 
                                                A grade of <strong>WF</strong> (withdrawn failing) is factored as an F (0.0).
                                            </p>
                                            <p style={{ fontSize: '14px', margin: '0 0 8px 0' }}>
                                                <strong>AP/IB courses</strong> are excluded from CASPA GPA calculations but are still available for prerequisite matching. Programs vary in their policies for accepting AP/IB credits.
                                            </p>
                                            <p style={{ fontSize: '14px', margin: '0' }}>
                                                <span style={{ 
                                                    display: 'inline-block',
                                                    padding: '2px 5px',
                                                    backgroundColor: '#fef3c7',
                                                    borderRadius: '4px',
                                                    color: '#92400e'
                                                }}>
                                                    Click any GPA value below to see contributing courses and override academic levels if needed.
                                                </span>
                                            </p>
                                        </div>
                                        
                                        {/* Display Manual Start Year if set */}
                                        {profileData.undergrad_start_year && (
                                            <div style={{ 
                                                padding: '8px 12px', 
                                                backgroundColor: '#ecfdf5', 
                                                borderRadius: '6px', 
                                                marginBottom: '15px',
                                                display: 'flex',
                                                alignItems: 'center',
                                                fontSize: '14px',
                                                color: '#065f46'
                                            }}>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{ marginRight: '8px' }}>
                                                    <path d="M22 16.92v3a2 2 0 01-2.18 2 19.79 19.79 0 01-8.63-3.07 19.5 19.5 0 01-6-6 19.79 19.79 0 01-3.07-8.67A2 2 0 014.11 2h3a2 2 0 012 1.72 12.84 12.84 0 00.7 2.81 2 2 0 01-.45 2.11L8.09 9.91a16 16 0 006 6l1.27-1.27a2 2 0 012.11-.45 12.84 12.84 0 002.81.7A2 2 0 0122 16.92z"></path>
                                                </svg>
                                                Using manually set undergraduate start year: <strong>{profileData.undergrad_start_year}</strong>
                                            </div>
                                        )}
                                         
                                        {/* Enhanced CASPA GPA Types Explanation */}
                                        <div style={{ 
                                            display: 'flex', 
                                            marginBottom: '20px', 
                                            gap: '15px',
                                            flexWrap: 'wrap'
                                        }}>
                                            <div style={{ 
                                                flex: '1 0 300px',
                                                padding: '15px',
                                                borderRadius: '8px',
                                                backgroundColor: 'white',
                                                boxShadow: '0 1px 3px rgba(0,0,0,0.05)',
                                                border: '1px solid #e2e8f0'
                                            }}>
                                                <h4 style={{ 
                                                    margin: '0 0 10px 0', 
                                                    fontSize: '15px',
                                                    color: '#1e40af',
                                                    display: 'flex',
                                                    alignItems: 'center'
                                                }}>
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{ marginRight: '8px' }}>
                                                        <polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline>
                                                    </svg>
                                                    Academic Level Categories
                                                </h4>
                                                <ul style={{ 
                                                    padding: '0 0 0 15px', 
                                                    margin: '0',
                                                    fontSize: '13px'
                                                }}>
                                                    <li style={{ marginBottom: '5px' }}><strong>Undergraduate</strong>: Courses taken during a bachelor's degree program</li>
                                                    <li style={{ marginBottom: '5px' }}><strong>Post-Baccalaureate</strong>: Courses taken after completing a bachelor's degree</li>
                                                    <li style={{ marginBottom: '5px' }}><strong>Cumulative Undergraduate</strong>: Combines Undergraduate and Post-Baccalaureate coursework</li>
                                                    <li><strong>Overall</strong>: Combines all coursework across all academic levels</li>
                                                </ul>
                                            </div>
                                            
                                            <div style={{ 
                                                flex: '1 0 300px',
                                                padding: '15px',
                                                borderRadius: '8px',
                                                backgroundColor: 'white',
                                                boxShadow: '0 1px 3px rgba(0,0,0,0.05)',
                                                border: '1px solid #e2e8f0'
                                            }}>
                                                <h4 style={{ 
                                                    margin: '0 0 10px 0', 
                                                    fontSize: '15px',
                                                    color: '#1e40af',
                                                    display: 'flex',
                                                    alignItems: 'center'
                                                }}>
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{ marginRight: '8px' }}>
                                                        <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                                                        <line x1="8" y1="21" x2="16" y2="21"></line>
                                                        <line x1="12" y1="17" x2="12" y2="21"></line>
                                                    </svg>
                                                    Science vs. Non-Science Categories
                                                </h4>
                                                <ul style={{ 
                                                    padding: '0 0 0 15px', 
                                                    margin: '0',
                                                    fontSize: '13px',
                                                    columnCount: 2
                                                }}>
                                                    <li style={{ marginBottom: '5px' }}><strong>Science</strong>: Biology/Zoology</li>
                                                    <li style={{ marginBottom: '5px' }}>Inorganic Chemistry</li>
                                                    <li style={{ marginBottom: '5px' }}>Biochemistry</li>
                                                    <li style={{ marginBottom: '5px' }}>Organic Chemistry</li>
                                                    <li style={{ marginBottom: '5px' }}>Physics</li>
                                                    <li style={{ marginBottom: '5px' }}>Other Science</li>
                                                    <li style={{ marginBottom: '5px' }}><strong>Non-Science</strong>: English</li>
                                                    <li style={{ marginBottom: '5px' }}>Math</li>
                                                    <li style={{ marginBottom: '5px' }}>Behavioral Science</li>
                                                    <li>Other Non-Science</li>
                                                </ul>
                                            </div>
                                        </div>
                                         
                                        {/* Container for enhanced table */}
                                        <div className="caspa-gpa-table-container enhanced-table" style={{
                                            backgroundColor: 'white',
                                            borderRadius: '8px',
                                            boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
                                            padding: '1px',
                                            border: '1px solid #e2e8f0',
                                            overflow: 'hidden'
                                        }}> 
                                            <table className="caspa-gpa-table" style={{ width: '100%', borderCollapse: 'collapse' }}>
                                                {/* Enhanced table header */}
                                                <thead className="enhanced-thead" style={{ backgroundColor: '#f8fafc' }}>
                                                    <tr>
                                                        <th rowSpan="2" className="category-header" style={{ 
                                                            padding: '10px', 
                                                            textAlign: 'left', 
                                                            borderBottom: '1px solid #e2e8f0',
                                                            fontSize: '14px'
                                                        }}>Category</th> 
                                                        <th colSpan="3" className="group-header science-header" style={{ 
                                                            padding: '10px', 
                                                            backgroundColor: '#f0fdfa',
                                                            color: '#0f766e',
                                                            borderBottom: '1px solid #e2e8f0',
                                                            fontSize: '14px',
                                                            fontWeight: '600'
                                                        }}>Science</th> 
                                                        <th colSpan="3" className="group-header non-science-header" style={{ 
                                                            padding: '10px', 
                                                            backgroundColor: '#eff6ff',
                                                            color: '#1e40af',
                                                            borderBottom: '1px solid #e2e8f0',
                                                            fontSize: '14px',
                                                            fontWeight: '600'
                                                        }}>Non-Science</th> 
                                                        <th colSpan="3" className="group-header total-header" style={{ 
                                                            padding: '10px', 
                                                            backgroundColor: '#f5f3ff',
                                                            color: '#5b21b6',
                                                            borderBottom: '1px solid #e2e8f0',
                                                            fontSize: '14px',
                                                            fontWeight: '600'
                                                        }}>Total</th> 
                                                    </tr>
                                                    <tr>
                                                        {/* Add classes to sub-headers */}
                                                        <th className="sub-header" style={{ 
                                                            padding: '8px', 
                                                            fontSize: '13px', 
                                                            color: '#374151',
                                                            fontWeight: '500',
                                                            borderBottom: '1px solid #e2e8f0',
                                                            backgroundColor: '#f0fdfa'
                                                        }}>Credits</th>
                                                        <th className="sub-header" style={{ 
                                                            padding: '8px', 
                                                            fontSize: '13px', 
                                                            color: '#374151',
                                                            fontWeight: '500',
                                                            borderBottom: '1px solid #e2e8f0',
                                                            backgroundColor: '#f0fdfa'
                                                        }}>QP</th>
                                                        <th className="sub-header gpa-header" style={{ 
                                                            padding: '8px', 
                                                            fontSize: '13px', 
                                                            color: '#0f766e',
                                                            fontWeight: '600',
                                                            borderBottom: '1px solid #e2e8f0',
                                                            backgroundColor: '#f0fdfa'
                                                        }}>GPA</th> 
                                                        <th className="sub-header" style={{ 
                                                            padding: '8px', 
                                                            fontSize: '13px', 
                                                            color: '#374151',
                                                            fontWeight: '500',
                                                            borderBottom: '1px solid #e2e8f0',
                                                            backgroundColor: '#eff6ff'
                                                        }}>Credits</th>
                                                        <th className="sub-header" style={{ 
                                                            padding: '8px', 
                                                            fontSize: '13px', 
                                                            color: '#374151',
                                                            fontWeight: '500',
                                                            borderBottom: '1px solid #e2e8f0',
                                                            backgroundColor: '#eff6ff'
                                                        }}>QP</th>
                                                        <th className="sub-header gpa-header" style={{ 
                                                            padding: '8px', 
                                                            fontSize: '13px', 
                                                            color: '#1e40af',
                                                            fontWeight: '600',
                                                            borderBottom: '1px solid #e2e8f0',
                                                            backgroundColor: '#eff6ff'
                                                        }}>GPA</th> 
                                                        <th className="sub-header" style={{ 
                                                            padding: '8px', 
                                                            fontSize: '13px', 
                                                            color: '#374151',
                                                            fontWeight: '500',
                                                            borderBottom: '1px solid #e2e8f0',
                                                            backgroundColor: '#f5f3ff'
                                                        }}>Credits</th>
                                                        <th className="sub-header" style={{ 
                                                            padding: '8px', 
                                                            fontSize: '13px', 
                                                            color: '#374151',
                                                            fontWeight: '500',
                                                            borderBottom: '1px solid #e2e8f0',
                                                            backgroundColor: '#f5f3ff'
                                                        }}>QP</th>
                                                        <th className="sub-header gpa-header" style={{ 
                                                            padding: '8px', 
                                                            fontSize: '13px', 
                                                            color: '#5b21b6',
                                                            fontWeight: '600',
                                                            borderBottom: '1px solid #e2e8f0',
                                                            backgroundColor: '#f5f3ff'
                                                        }}>GPA</th> 
                                                    </tr>
                                                </thead>
                                            <tbody>
                                                {caspaTableRowOrder.map(level => {
                                                    const levelGpaData = caspaGpas[level];

                                                    // Skip rendering if level data doesn't exist or has zero total credits
                                                    // Exception: Always render summary rows even if zero
                                                    const isSummaryRow = ['Cumulative Undergraduate', 'Overall'].includes(level);
                                                    const isUndergradRow = level === 'Undergraduate';
                                                    const isPostBaccRow = level === 'Post-Baccalaureate';
                                                    const isOverallRow = level === 'Overall';

                                                    if (!levelGpaData || (!isSummaryRow && !isUndergradRow && !isPostBaccRow && parseFloat(levelGpaData.total.credits) === 0)) {
                                                        return null; // Don't render empty non-summary rows
                                                    }

                                                    // Apply special styling based on row type
                                                    const rowStyle = {
                                                        backgroundColor: isOverallRow ? '#f5f3ff' : 
                                                                        isSummaryRow ? '#f8fafc' : 
                                                                        isPostBaccRow ? '#fdf2f8' : 
                                                                        'white',
                                                        fontWeight: isSummaryRow ? '600' : '400',
                                                        borderBottom: '1px solid #e2e8f0'
                                                    };

                                                    return (
                                                        <tr key={level} style={rowStyle}>
                                                            <td style={{ 
                                                                padding: '10px', 
                                                                fontSize: '14px',
                                                                color: isOverallRow ? '#5b21b6' : '#374151',
                                                                fontWeight: isOverallRow || isSummaryRow ? '600' : '500'
                                                            }}>{level}</td>
                                                            {/* Science */}
                                                            <td style={{ 
                                                                padding: '10px', 
                                                                textAlign: 'center',
                                                                fontSize: '13px'
                                                            }}>{levelGpaData.science.credits}</td>
                                                            <td style={{ 
                                                                padding: '10px', 
                                                                textAlign: 'center',
                                                                fontSize: '13px'
                                                            }}>{levelGpaData.science.qp}</td>
                                                            <td style={{ 
                                                                padding: '10px', 
                                                                textAlign: 'center',
                                                                fontSize: '14px'
                                                            }}>
                                                                <button 
                                                                    style={{
                                                                        background: 'none',
                                                                        border: 'none',
                                                                        cursor: levelGpaData.science.courses.length === 0 ? 'not-allowed' : 'pointer',
                                                                        fontWeight: '600',
                                                                        color: '#0f766e',
                                                                        padding: '2px 8px',
                                                                        borderRadius: '4px',
                                                                        transition: 'background 0.2s',
                                                                        backgroundColor: levelGpaData.science.courses.length > 0 ? '#f0fdfa' : 'transparent',
                                                                        opacity: levelGpaData.science.courses.length === 0 ? 0.5 : 1
                                                                    }}
                                                                    onClick={() => handleGpaCellClick(level, 'science')} 
                                                                    disabled={levelGpaData.science.courses.length === 0}
                                                                >
                                                                    {levelGpaData.science.gpa}
                                                                </button>
                                                            </td>
                                                            {/* Non-Science */}
                                                            <td style={{ 
                                                                padding: '10px', 
                                                                textAlign: 'center',
                                                                fontSize: '13px'
                                                            }}>{levelGpaData.nonScience.credits}</td>
                                                            <td style={{ 
                                                                padding: '10px', 
                                                                textAlign: 'center',
                                                                fontSize: '13px'
                                                            }}>{levelGpaData.nonScience.qp}</td>
                                                            <td style={{ 
                                                                padding: '10px', 
                                                                textAlign: 'center',
                                                                fontSize: '14px'
                                                            }}>
                                                                <button 
                                                                    style={{
                                                                        background: 'none',
                                                                        border: 'none',
                                                                        cursor: levelGpaData.nonScience.courses.length === 0 ? 'not-allowed' : 'pointer',
                                                                        fontWeight: '600',
                                                                        color: '#1e40af',
                                                                        padding: '2px 8px',
                                                                        borderRadius: '4px',
                                                                        transition: 'background 0.2s',
                                                                        backgroundColor: levelGpaData.nonScience.courses.length > 0 ? '#eff6ff' : 'transparent',
                                                                        opacity: levelGpaData.nonScience.courses.length === 0 ? 0.5 : 1
                                                                    }}
                                                                    onClick={() => handleGpaCellClick(level, 'nonScience')} 
                                                                    disabled={levelGpaData.nonScience.courses.length === 0}
                                                                >
                                                                    {levelGpaData.nonScience.gpa}
                                                                </button>
                                                            </td>
                                                            {/* Total */}
                                                            <td style={{ 
                                                                padding: '10px', 
                                                                textAlign: 'center',
                                                                fontSize: '13px'
                                                            }}>{levelGpaData.total.credits}</td>
                                                            <td style={{ 
                                                                padding: '10px', 
                                                                textAlign: 'center',
                                                                fontSize: '13px'
                                                            }}>{levelGpaData.total.qp}</td>
                                                            <td style={{ 
                                                                padding: '10px', 
                                                                textAlign: 'center',
                                                                fontSize: '14px'
                                                            }}>
                                                                <button 
                                                                    style={{
                                                                        background: 'none',
                                                                        border: 'none',
                                                                        cursor: levelGpaData.total.courses.length === 0 ? 'not-allowed' : 'pointer',
                                                                        fontWeight: '600',
                                                                        color: isOverallRow ? '#5b21b6' : '#6d28d9',
                                                                        padding: '2px 8px',
                                                                        borderRadius: '4px',
                                                                        transition: 'background 0.2s',
                                                                        backgroundColor: levelGpaData.total.courses.length > 0 ? 
                                                                            (isOverallRow ? '#f3e8ff' : '#f5f3ff') : 'transparent',
                                                                        opacity: levelGpaData.total.courses.length === 0 ? 0.5 : 1,
                                                                        boxShadow: isOverallRow ? '0 1px 2px rgba(0,0,0,0.05)' : 'none'
                                                                    }}
                                                                    onClick={() => handleGpaCellClick(level, 'total')} 
                                                                    disabled={levelGpaData.total.courses.length === 0}
                                                                >
                                                                    {levelGpaData.total.gpa}
                                                                </button>
                                                            </td>
                                                        </tr>
                                                    );
                                                })}
                                            </tbody>
                                        </table>
                                        </div> {/* End caspa-gpa-table-container enhanced-table */}
                                                                                {/* Detailed Course View Section */}
                                        {detailedViewKey && (
                                            <div style={{
                                                marginTop: '25px',
                                                backgroundColor: 'white',
                                                borderRadius: '8px',
                                                boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
                                                border: '1px solid #e2e8f0',
                                                padding: '20px',
                                                position: 'relative'
                                            }}> 
                                                {/* Header with GPA breakdown */}
                                                <div style={{
                                                    display: 'flex',
                                                    justifyContent: 'space-between',
                                                    alignItems: 'flex-start',
                                                    marginBottom: '20px',
                                                    borderBottom: '1px solid #e2e8f0',
                                                    paddingBottom: '15px'
                                                }}>
                                                    <div>
                                                        <h3 style={{ 
                                                            margin: '0 0 10px 0', 
                                                            fontSize: '18px',
                                                            color: '#1e40af',
                                                            display: 'flex',
                                                            alignItems: 'center' 
                                                        }}>
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{ marginRight: '8px' }}>
                                                                <circle cx="12" cy="12" r="10"></circle>
                                                                <polyline points="12 6 12 12 16 14"></polyline>
                                                            </svg>
                                                            CASPA GPA Details: {detailedViewKey.replace('-', ' ')}
                                                        </h3>
                                                        
                                                        {/* Display Calculation Breakdown */}
                                                        {(() => {
                                                            const [level, category] = detailedViewKey.split('-');
                                                            const categoryData = caspaGpas?.[level]?.[category];
                                                            if (categoryData) {
                                                                const categoryLabel = category === 'science' ? 'Science' : category === 'nonScience' ? 'Non-Science' : 'Total';
                                                                return (
                                                                    <div style={{ 
                                                                        backgroundColor: '#f0f9ff', 
                                                                        borderRadius: '6px', 
                                                                        padding: '10px 15px',
                                                                        display: 'flex',
                                                                        alignItems: 'center',
                                                                        fontSize: '15px',
                                                                        fontWeight: '500',
                                                                        color: '#0c4a6e'
                                                                    }}>
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{ marginRight: '10px' }}>
                                                                            <path d="M19 5H5a2 2 0 00-2 2v10a2 2 0 002 2h14a2 2 0 002-2V7a2 2 0 00-2-2z"></path>
                                                                            <line x1="12" y1="10" x2="12" y2="16"></line>
                                                                            <line x1="9" y1="13" x2="15" y2="13"></line>
                                                                        </svg>
                                                                        CASPA GPA Calculation: <span style={{ fontWeight: '600' }}>{categoryData.qp}</span> (Quality Points) / <span style={{ fontWeight: '600' }}>{categoryData.credits}</span> (Attempted Credits) = <span style={{ fontWeight: '700', color: '#0369a1' }}>{categoryData.gpa}</span>
                                                                    </div>
                                                                );
                                                            }
                                                            return null;
                                                        })()}
                                                    </div>
                                                    
                                                    <button 
                                                        onClick={() => setDetailedViewKey(null)} 
                                                        style={{
                                                            backgroundColor: '#f1f5f9',
                                                            border: 'none',
                                                            padding: '8px 15px',
                                                            borderRadius: '6px',
                                                            cursor: 'pointer',
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            fontSize: '14px',
                                                            color: '#475569',
                                                            fontWeight: '500',
                                                            transition: 'all 0.2s ease'
                                                        }}
                                                    >
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{ marginRight: '5px' }}>
                                                            <line x1="18" y1="6" x2="6" y2="18"></line>
                                                            <line x1="6" y1="6" x2="18" y2="18"></line>
                                                        </svg>
                                                        Close Details
                                                    </button>
                                                </div>

                                                {/* Academic Level Override Controls */}
                                                {detailedViewKey === 'Overall-total' && (
                                                    <div style={{
                                                        padding: '12px 15px',
                                                        backgroundColor: '#fef9c3',
                                                        borderRadius: '6px',
                                                        marginBottom: '20px',
                                                        display: 'flex',
                                                        alignItems: 'center'
                                                    }}>
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="#92400e" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{ flexShrink: 0, marginRight: '10px' }}>
                                                            <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                                                            <line x1="12" y1="9" x2="12" y2="13"></line>
                                                            <line x1="12" y1="17" x2="12.01" y2="17"></line>
                                                        </svg>
                                                        <div style={{ fontSize: '14px', color: '#92400e' }}>
                                                            <strong>CASPA Note:</strong> You can override the academic level classification to match how your courses should be counted in official CASPA GPAs. Changes to academic levels will affect how courses are grouped in your GPA calculations.
                                                        </div>
                                                    </div>
                                                )}

                                                {/* Course Table */}
                                                <div style={{ overflowX: 'auto' }}>
                                                    <table style={{ 
                                                        width: '100%', 
                                                        borderCollapse: 'collapse',
                                                        fontSize: '14px'
                                                    }}>
                                                        <thead style={{ backgroundColor: '#f8fafc' }}>
                                                            <tr>
                                                                <th style={{ 
                                                                    padding: '10px', 
                                                                    textAlign: 'left', 
                                                                    borderBottom: '1px solid #e2e8f0',
                                                                    fontWeight: '600',
                                                                    color: '#334155'
                                                                }}>Course</th>
                                                                <th style={{ 
                                                                    padding: '10px', 
                                                                    textAlign: 'left', 
                                                                    borderBottom: '1px solid #e2e8f0',
                                                                    fontWeight: '600',
                                                                    color: '#334155',
                                                                    width: '80px'
                                                                }}>Grade</th>
                                                                <th style={{ 
                                                                    padding: '10px', 
                                                                    textAlign: 'center', 
                                                                    borderBottom: '1px solid #e2e8f0',
                                                                    fontWeight: '600',
                                                                    color: '#334155',
                                                                    width: '80px'
                                                                }}>Credits</th>
                                                                <th style={{ 
                                                                    padding: '10px', 
                                                                    textAlign: 'center', 
                                                                    borderBottom: '1px solid #e2e8f0',
                                                                    fontWeight: '600',
                                                                    color: '#334155',
                                                                    width: '80px'
                                                                }}>CASPA Value</th>
                                                                <th style={{ 
                                                                    padding: '10px', 
                                                                    textAlign: 'center', 
                                                                    borderBottom: '1px solid #e2e8f0',
                                                                    fontWeight: '600',
                                                                    color: '#334155',
                                                                    width: '100px'
                                                                }}>Quality Points</th>
                                                                <th style={{ 
                                                                    padding: '10px', 
                                                                    textAlign: 'center', 
                                                                    borderBottom: '1px solid #e2e8f0',
                                                                    fontWeight: '600',
                                                                    color: '#334155',
                                                                    width: '130px'
                                                                }}>Academic Level</th>
                                                            </tr>
                                                        </thead>
                                                        
                                                        <tbody>
                                                            {Object.entries(detailedViewCourses).map(([semesterKey, coursesInSemester]) => (
                                                                // Using React Fragment with key for semester grouping
                                                                <React.Fragment key={semesterKey}>
                                                                    {/* Semester Header Row */}
                                                                    <tr style={{ backgroundColor: '#f1f5f9' }}>
                                                                        <td 
                                                                            colSpan="6" 
                                                                            style={{ 
                                                                                padding: '8px 12px', 
                                                                                fontWeight: '600', 
                                                                                fontSize: '13px',
                                                                                color: '#334155',
                                                                                borderBottom: '1px solid #e2e8f0' 
                                                                            }}
                                                                        >
                                                                            <span style={{ 
                                                                                display: 'flex',
                                                                                alignItems: 'center'
                                                                            }}>
                                                                                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{ marginRight: '6px' }}>
                                                                                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                                                                                    <line x1="16" y1="2" x2="16" y2="6"></line>
                                                                                    <line x1="8" y1="2" x2="8" y2="6"></line>
                                                                                    <line x1="3" y1="10" x2="21" y2="10"></line>
                                                                                </svg>
                                                                                {semesterKey} — {coursesInSemester.length} Course{coursesInSemester.length !== 1 ? 's' : ''}
                                                                            </span>
                                                                        </td>
                                                                    </tr>
                                                                    
                                                                    {/* Course Rows */}
                                                                    {coursesInSemester.map((course, index) => {
                                                                        const courseKey = `${course.institution}-${course.code}-${course.term}-${course.year}`;
                                                                        const currentLevel = manualCourseLevels[courseKey] || course.determinedLevel || course.autoDetectedLevel;
                                                                        // Get CASPA numeric value for the grade
                                                                        const caspaValue = caspaGradeToPoints(course.grade) || 'N/A';
                                                                        
                                                                        return (
                                                                            <tr 
                                                                                key={`${detailedViewKey}-course-${semesterKey}-${index}`} 
                                                                                style={{ 
                                                                                    backgroundColor: index % 2 === 0 ? 'white' : '#fafafa',
                                                                                    borderBottom: '1px solid #f1f5f9'
                                                                                }}
                                                                            >
                                                                                {/* Course Info */}
                                                                                <td style={{ padding: '8px 12px' }}>
                                                                                    <div>
                                                                                        <div style={{ 
                                                                                            display: 'flex', 
                                                                                            alignItems: 'center' 
                                                                                        }}>
                                                                                            <span style={{ 
                                                                                                fontWeight: '600', 
                                                                                                marginRight: '6px',
                                                                                                color: '#334155'
                                                                                            }}>
                                                                                                {course.code}
                                                                                            </span>
                                                                                            {course.has_lab && (
                                                                                                <span style={{ 
                                                                                                    backgroundColor: '#dbeafe', 
                                                                                                    color: '#1e40af',
                                                                                                    fontSize: '11px',
                                                                                                    fontWeight: '600',
                                                                                                    padding: '1px 5px',
                                                                                                    borderRadius: '4px'
                                                                                                }}>
                                                                                                    LAB
                                                                                                </span>
                                                                                            )}
                                                                                            {course.isAPorIB && (
                                                                                                <span style={{ 
                                                                                                    backgroundColor: '#fef3c7', 
                                                                                                    color: '#92400e',
                                                                                                    fontSize: '11px',
                                                                                                    fontWeight: '600',
                                                                                                    padding: '1px 5px',
                                                                                                    borderRadius: '4px',
                                                                                                    marginLeft: '4px'
                                                                                                }}>
                                                                                                    AP/IB
                                                                                                </span>
                                                                                            )}
                                                                                        </div>
                                                                                        <div style={{ 
                                                                                            fontSize: '13px', 
                                                                                            color: '#4b5563',
                                                                                            marginTop: '2px'
                                                                                        }}>
                                                                                            {course.name}
                                                                                        </div>
                                                                                        <div style={{ 
                                                                                            fontSize: '12px', 
                                                                                            color: '#64748b',
                                                                                            marginTop: '3px'
                                                                                        }}>
                                                                                            {course.institution}
                                                                                        </div>
                                                                                    </div>
                                                                                </td>
                                                                                
                                                                                {/* Grade */}
                                                                                <td style={{ 
                                                                                    padding: '8px 12px',
                                                                                    fontWeight: '600',
                                                                                    fontSize: '15px',
                                                                                    color: caspaValue >= 3.5 ? '#166534' : 
                                                                                           caspaValue >= 3.0 ? '#0284c7' : 
                                                                                           caspaValue >= 2.0 ? '#ca8a04' : 
                                                                                           caspaValue === 'N/A' ? '#64748b' : '#b91c1c'
                                                                                }}>
                                                                                    {course.grade}
                                                                                </td>
                                                                                
                                                                                {/* Credits */}
                                                                                <td style={{ 
                                                                                    padding: '8px 12px',
                                                                                    textAlign: 'center'
                                                                                }}>
                                                                                    {course.credits}
                                                                                </td>
                                                                                
                                                                                {/* CASPA Grade Value */}
                                                                                <td style={{ 
                                                                                    padding: '8px 12px',
                                                                                    textAlign: 'center',
                                                                                    fontWeight: '500'
                                                                                }}>
                                                                                    {caspaValue !== 'N/A' ? caspaValue.toFixed(1) : 'N/A'}
                                                                                </td>
                                                                                
                                                                                {/* Quality Points */}
                                                                                <td style={{ 
                                                                                    padding: '8px 12px',
                                                                                    textAlign: 'center',
                                                                                    fontWeight: '500'
                                                                                }}>
                                                                                    {course.qp}
                                                                                </td>
                                                                                
                                                                                {/* Academic Level - with dropdown in Overall view */}
                                                                                <td style={{ 
                                                                                    padding: '8px 12px',
                                                                                    textAlign: 'center'
                                                                                }}>
                                                                                    {detailedViewKey === 'Overall-total' ? (
                                                                                        <select
                                                                                            value={currentLevel}
                                                                                            onChange={(e) => handleManualCourseLevelChange(courseKey, e.target.value)}
                                                                                            style={{
                                                                                                width: '100%',
                                                                                                padding: '5px 10px',
                                                                                                borderRadius: '4px',
                                                                                                border: '1px solid #cbd5e1',
                                                                                                fontSize: '13px'
                                                                                            }}
                                                                                        >
                                                                                            {academicLevelOptions.map(levelOption => (
                                                                                                <option key={levelOption} value={levelOption}>
                                                                                                    {levelOption}
                                                                                                </option>
                                                                                            ))}
                                                                                        </select>
                                                                                    ) : (
                                                                                        <div style={{ 
                                                                                            padding: '5px', 
                                                                                            fontSize: '13px', 
                                                                                            fontWeight: '500',
                                                                                            color: currentLevel === 'Undergraduate' ? '#0891b2' : 
                                                                                                   currentLevel === 'Post-Baccalaureate' ? '#be185d' :
                                                                                                   currentLevel === 'Graduate Coursework' ? '#7e22ce' : '#64748b'
                                                                                        }}>
                                                                                            {currentLevel}
                                                                                        </div>
                                                                                    )}
                                                                                </td>
                                                                            </tr>
                                                                        );
                                                                    })}
                                                                </React.Fragment>
                                                            ))}
                                                        </tbody>
                                                    </table>
                                                </div>

                                                {/* Save Button Area */}
                                                {detailedViewKey === 'Overall-total' && (
                                                    <div style={{ 
                                                        marginTop: '20px',
                                                        display: 'flex',
                                                        justifyContent: 'flex-end',
                                                        alignItems: 'center',
                                                        gap: '10px'
                                                    }}> 
                                                        {hasUnsavedOverrides && (
                                                            <button
                                                                onClick={handleSaveAllOverrides}
                                                                disabled={isSavingOverrides}
                                                                style={{
                                                                    backgroundColor: '#1e40af',
                                                                    color: 'white',
                                                                    border: 'none',
                                                                    padding: '8px 15px',
                                                                    borderRadius: '6px',
                                                                    cursor: isSavingOverrides ? 'not-allowed' : 'pointer',
                                                                    fontWeight: '500',
                                                                    fontSize: '14px',
                                                                    display: 'flex',
                                                                    alignItems: 'center',
                                                                    opacity: isSavingOverrides ? 0.7 : 1
                                                                }}
                                                            >
                                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{ marginRight: '6px' }}>
                                                                    <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                                                                    <polyline points="17 21 17 13 7 13 7 21"></polyline>
                                                                    <polyline points="7 3 7 8 15 8"></polyline>
                                                                </svg>
                                                                {isSavingOverrides ? 'Saving...' : 'Save Academic Level Changes'}
                                                            </button>
                                                        )}
                                                        
                                                        {overrideSaveMessage && (
                                                            <div style={{ 
                                                                padding: '8px 15px', 
                                                                borderRadius: '6px',
                                                                fontSize: '14px',
                                                                backgroundColor: overrideSaveMessage.includes('Failed') || overrideSaveMessage.includes('Error') 
                                                                    ? '#fee2e2' 
                                                                    : '#ecfdf5',
                                                                color: overrideSaveMessage.includes('Failed') || overrideSaveMessage.includes('Error')
                                                                    ? '#b91c1c'
                                                                    : '#065f46'
                                                            }}>
                                                                {overrideSaveMessage}
                                                            </div>
                                                        )}
                                                    </div>
                                                )}
                                            </div>
                                        )}

                                </div>
                            ) : (
                                <p>No verified transcripts available to calculate CASPA GPAs. Please add transcripts via the 'Transcripts' tab.</p>
                            )}
                        </div>
                    )}


                    {/* Prerequisites Tab */}
                    {activeTab === 'prerequisites' && (
                         existingTranscripts.length > 0 ? (
                            <PrerequisiteClasses existingTranscripts={existingTranscripts} />
                        ) : (
                             <div className="profile-section">
                                <h2>Prerequisites</h2>
                                <p>Please upload, verify, or manually enter transcript data under the "Transcripts" tab before mapping prerequisites.</p>
                            </div>
                        )
                    )}
                </div> {/* End tab-content */}


                </div>
            </div>
        </div>
    );
};

export default UserProfileDashboard;
