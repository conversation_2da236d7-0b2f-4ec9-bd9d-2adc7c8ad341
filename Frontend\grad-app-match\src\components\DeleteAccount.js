import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import styled from '@emotion/styled';

const Container = styled.div`
  max-width: 500px;
  margin: 2rem auto;
  padding: 2rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
`;

const WarningBox = styled.div`
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 1rem;
  border-radius: 6px;
  margin-bottom: 1.5rem;
`;

const Button = styled.button`
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.375rem;
  font-size: 1rem;
  cursor: pointer;
  margin: 0.5rem;
  transition: background-color 0.2s;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const DangerButton = styled(Button)`
  background-color: #dc2626;
  color: white;

  &:hover:not(:disabled) {
    background-color: #b91c1c;
  }
`;

const SecondaryButton = styled(Button)`
  background-color: #6b7280;
  color: white;

  &:hover:not(:disabled) {
    background-color: #4b5563;
  }
`;

const Input = styled.input`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  margin-bottom: 1rem;
`;

const DeleteAccount = ({ onCancel }) => {
  const [confirmText, setConfirmText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  
  const { currentUser, supabase, signOut } = useAuth();
  const navigate = useNavigate();

  const handleDeleteAccount = async () => {
    if (confirmText !== 'DELETE') {
      setError('Please type "DELETE" to confirm account deletion.');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // First, delete user data from your backend
      const { data: { session } } = await supabase.auth.getSession();
      
      if (session?.access_token) {
        // Call your backend to delete user data
        const response = await fetch('/api/delete-account/', {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${session.access_token}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error('Failed to delete account data');
        }
      }

      // Then delete the Supabase auth user
      const { error: deleteError } = await supabase.auth.admin.deleteUser(
        currentUser.id
      );

      if (deleteError) {
        throw deleteError;
      }

      // Sign out and redirect
      await signOut();
      navigate('/');
      
    } catch (err) {
      console.error('Account deletion error:', err);
      setError('Failed to delete account. Please contact support.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Container>
      <h2>Delete Account</h2>
      
      <WarningBox>
        <h3>⚠️ This action cannot be undone!</h3>
        <p>Deleting your account will permanently remove:</p>
        <ul>
          <li>Your profile and personal information</li>
          <li>All uploaded transcripts and documents</li>
          <li>Your program matches and saved data</li>
          <li>Your account access and login credentials</li>
        </ul>
      </WarningBox>

      {error && (
        <div style={{ color: '#dc2626', marginBottom: '1rem' }}>
          {error}
        </div>
      )}

      <p>To confirm deletion, please type <strong>DELETE</strong> in the box below:</p>
      
      <Input
        type="text"
        value={confirmText}
        onChange={(e) => setConfirmText(e.target.value)}
        placeholder="Type DELETE to confirm"
        disabled={isLoading}
      />

      <div>
        <DangerButton 
          onClick={handleDeleteAccount}
          disabled={isLoading || confirmText !== 'DELETE'}
        >
          {isLoading ? 'Deleting Account...' : 'Delete My Account'}
        </DangerButton>
        
        <SecondaryButton onClick={onCancel} disabled={isLoading}>
          Cancel
        </SecondaryButton>
      </div>
    </Container>
  );
};

export default DeleteAccount;