/**
 * Normalize profile data to handle both camelCase and snake_case field names
 * This ensures compatibility between frontend and backend field naming conventions
 */
export const normalizeProfileData = (profileData) => {
  if (!profileData) return {};
  
  // Create a normalized copy with both camelCase and snake_case fields
  const normalized = { ...profileData };
  
  // Field mapping for experience hours
  const experienceFields = [
    { snake: 'direct_patient_care_hours', camel: 'directPatientCareHours' },
    { snake: 'shadowing_hours', camel: 'shadowingHours' }
  ];
  
  // Field mapping for GRE scores
  const greFields = [
    { snake: 'gre_verbal_score', camel: 'greVerbalScore' },
    { snake: 'gre_quantitative_score', camel: 'greQuantitativeScore' },
    { snake: 'gre_analytical_writing_score', camel: 'greAnalyticalWritingScore' },
    { snake: 'gre_verbal_percentile', camel: 'greVerbalPercentile' },
    { snake: 'gre_quantitative_percentile', camel: 'greQuantitativePercentile' },
    { snake: 'gre_analytical_writing_percentile', camel: 'greAnalyticalWritingPercentile' }
  ];
  
  // Field mapping for PA-CAT scores
  const pacatFields = [
    { snake: 'pa_cat_composite_ss', camel: 'paCatCompositeSs' },
    { snake: 'pa_cat_anatomy_ss', camel: 'paCatAnatomySs' },
    { snake: 'pa_cat_physiology_ss', camel: 'paCatPhysiologySs' },
    { snake: 'pa_cat_general_biology_ss', camel: 'paCatGeneralBiologySs' },
    { snake: 'pa_cat_organic_chemistry_ss', camel: 'paCatOrganicChemistrySs' },
    { snake: 'pa_cat_biochemistry_ss', camel: 'paCatBiochemistrySs' }
  ];
  
  // Field mapping for boolean fields
  const booleanFields = [
    { snake: 'has_taken_casper', camel: 'hasTakenCasper' },
    { snake: 'has_taken_pa_cat', camel: 'hasTakenPaCat' }
  ];
  
  // Normalize all field types
  const allFields = [...experienceFields, ...greFields, ...pacatFields, ...booleanFields];
  
  allFields.forEach(({ snake, camel }) => {
    // Add camelCase version if snake_case exists
    if (profileData[snake] !== undefined) {
      if (experienceFields.some(f => f.snake === snake)) {
        // Parse as integer for experience hours
        normalized[camel] = parseInt(profileData[snake]) || 0;
      } else if (greFields.some(f => f.snake === snake) || pacatFields.some(f => f.snake === snake)) {
        // Parse as float for test scores
        normalized[camel] = parseFloat(profileData[snake]) || null;
      } else {
        // Keep as-is for boolean fields
        normalized[camel] = profileData[snake];
      }
    }
    
    // Add snake_case version if camelCase exists
    if (profileData[camel] !== undefined) {
      if (experienceFields.some(f => f.camel === camel)) {
        // Parse as integer for experience hours
        normalized[snake] = parseInt(profileData[camel]) || 0;
      } else if (greFields.some(f => f.camel === camel) || pacatFields.some(f => f.camel === camel)) {
        // Parse as float for test scores
        normalized[snake] = parseFloat(profileData[camel]) || null;
      } else {
        // Keep as-is for boolean fields
        normalized[snake] = profileData[camel];
      }
    }
  });
  
  return normalized;
};