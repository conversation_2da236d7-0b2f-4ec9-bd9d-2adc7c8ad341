"""
Test JSON parsing and transformation logic for program data migration.

This module tests the core functionality of task 3:
- JSON data parsing from ScrapperPAEAReal format
- Data transformation to normalized internal format
- Field validation and data type conversions
"""

import json
import tempfile
import unittest
from pathlib import Path
from unittest.mock import patch

from django.test import TestCase

from ..services.program_data_migration import (
    ProgramDataMigrationProcessor,
    JSONDataParser,
    DataValidator,
    DataValidationError,
    MigrationError
)


class TestJSONDataParser(TestCase):
    """Test the JSONDataParser utility class"""
    
    def setUp(self):
        self.parser = JSONDataParser()
    
    def test_extract_nested_value(self):
        """Test extracting values from nested dictionaries"""
        data = {
            'level1': {
                'level2': {
                    'target': 'found_value'
                }
            }
        }
        
        result = self.parser.extract_nested_value(data, 'level1.level2.target')
        self.assertEqual(result, 'found_value')
        
        # Test with default value
        result = self.parser.extract_nested_value(data, 'nonexistent.path', 'default')
        self.assertEqual(result, 'default')
    
    def test_find_field_variants(self):
        """Test finding first available field from variants"""
        data = {
            'field2': 'value2',
            'field3': 'value3'
        }
        
        variants = ['field1', 'field2', 'field3']
        result = self.parser.find_field_variants(data, variants)
        self.assertEqual(result, 'value2')
        
        # Test with no matches
        variants = ['nonexistent1', 'nonexistent2']
        result = self.parser.find_field_variants(data, variants)
        self.assertIsNone(result)
    
    def test_normalize_list_or_dict(self):
        """Test normalizing data to list of dictionaries"""
        # Test with dictionary
        data = {'key': 'value'}
        result = self.parser.normalize_list_or_dict(data)
        self.assertEqual(result, [{'key': 'value'}])
        
        # Test with list of dictionaries
        data = [{'key1': 'value1'}, {'key2': 'value2'}]
        result = self.parser.normalize_list_or_dict(data)
        self.assertEqual(result, data)
        
        # Test with mixed list (should filter out non-dicts)
        data = [{'key': 'value'}, 'string', 123]
        result = self.parser.normalize_list_or_dict(data)
        self.assertEqual(result, [{'key': 'value'}])


class TestDataValidator(TestCase):
    """Test the DataValidator utility class"""
    
    def setUp(self):
        self.validator = DataValidator()
    
    def test_clean_null_values(self):
        """Test cleaning null-like values"""
        # Test null-like strings
        null_values = [
            'no information provided',
            'not applicable',
            'n/a',
            'NA',
            'null',
            'none',
            '',
            'unknown',
            'not specified',
            'not available'
        ]
        
        for value in null_values:
            result = self.validator.clean_null_values(value)
            self.assertIsNone(result, f"Failed to clean: {value}")
        
        # Test valid values
        valid_values = ['valid string', '123', 'Yes']
        for value in valid_values:
            result = self.validator.clean_null_values(value)
            self.assertEqual(result, value)
    
    def test_convert_to_boolean(self):
        """Test converting various values to boolean"""
        # Test true values
        true_values = ['yes', 'YES', 'true', 'TRUE', '1', 'required', 'accepted']
        for value in true_values:
            result = self.validator.convert_to_boolean(value)
            self.assertTrue(result, f"Failed to convert {value} to True")
        
        # Test false values
        false_values = ['no', 'NO', 'false', 'FALSE', '0', 'not required', 'not accepted']
        for value in false_values:
            result = self.validator.convert_to_boolean(value)
            self.assertFalse(result, f"Failed to convert {value} to False")
        
        # Test None and invalid values
        self.assertIsNone(self.validator.convert_to_boolean(None))
        self.assertIsNone(self.validator.convert_to_boolean('maybe'))
    
    def test_extract_numeric_value(self):
        """Test extracting numeric values from strings"""
        # Test integer extraction
        self.assertEqual(self.validator.extract_numeric_value('123'), 123)
        self.assertEqual(self.validator.extract_numeric_value('$123'), 123)
        
        # Test float extraction
        self.assertEqual(self.validator.extract_numeric_value('3.5'), 3.5)
        self.assertEqual(self.validator.extract_numeric_value('GPA: 3.5'), 3.5)
        
        # Test invalid values
        self.assertIsNone(self.validator.extract_numeric_value('no number'))
        self.assertIsNone(self.validator.extract_numeric_value(None))
    
    def test_validate_gpa(self):
        """Test GPA validation"""
        # Test valid GPAs
        self.assertEqual(float(self.validator.validate_gpa('3.5')), 3.5)
        self.assertEqual(float(self.validator.validate_gpa(3.0)), 3.0)
        self.assertEqual(float(self.validator.validate_gpa('2.75 minimum')), 2.75)
        
        # Test invalid GPAs
        self.assertIsNone(self.validator.validate_gpa('5.0'))  # Out of range
        self.assertIsNone(self.validator.validate_gpa('-1.0'))  # Out of range
        self.assertIsNone(self.validator.validate_gpa('invalid'))
        self.assertIsNone(self.validator.validate_gpa(None))
    
    def test_extract_percentage_value(self):
        """Test extracting percentage values"""
        self.assertEqual(self.validator.extract_percentage_value('95%'), 95.0)
        self.assertEqual(self.validator.extract_percentage_value('85.5%'), 85.5)
        self.assertEqual(self.validator.extract_percentage_value(90), 90.0)
        
        # Test invalid values
        self.assertIsNone(self.validator.extract_percentage_value('no percentage'))
        self.assertIsNone(self.validator.extract_percentage_value(None))


class TestJSONTransformation(TestCase):
    """Test JSON transformation from ScrapperPAEAReal format"""
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.processor = ProgramDataMigrationProcessor(self.temp_dir)
        
        # Sample ScrapperPAEAReal format data
        self.sample_program_data = {
            "program_name": "Test University PA Program",
            "program_url": "https://example.com/program",
            "program_website": "https://example.com",
            "phone_number": "************",
            "address": {
                "street": "123 Main St",
                "city": "Test City",
                "state": "TS",
                "zip_code": "12345"
            },
            "mission_statement": "To educate compassionate healthcare providers",
            "unique_program_features": "Small class sizes, hands-on learning",
            "curriculum_focus": ["Case-Based Learning", "Problem-Based Learning"],
            "credentials_offered": ["Master's degree"],
            "program_length_months": "24 Months",
            "start_month": "August 2025",
            "estimated_incoming_class_size": "30",
            "part_time_option": "No",
            "distance_learning": "No",
            "on_campus_housing": "Yes",
            "admission_type": "Rolling",
            "minimum_overall_gpa_required": "3.0",
            "minimum_prereq_gpa_required": "3.2",
            "healthcare_experience": "Required",
            "hours_needed": "1000",
            "human_anatomy_credits": "3 Credit(s)",
            "microbiology_credits": "3 Credit(s)",
            "statistics_credits": "3 Credit(s)",
            "program_matriculants_by_gender": {
                "female_matriculants": "20",
                "male_matriculants": "10"
            },
            "ethnicity_of_program_matriculants": {
                "white_matriculants": "15",
                "asian_matriculants": "10",
                "hispanic_latino_or_spanish_matriculants": "5"
            }
        }
    
    def test_validate_json_structure_scrapper_format(self):
        """Test JSON structure validation for ScrapperPAEAReal format"""
        # Test valid single program format
        result = self.processor._validate_json_structure(self.sample_program_data, 'test.json')
        self.assertTrue(result)
        
        # Test valid array format
        array_data = [self.sample_program_data]
        result = self.processor._validate_json_structure(array_data, 'test.json')
        self.assertTrue(result)
        
        # Test invalid format (missing program_name)
        invalid_data = {"invalid": "data"}
        result = self.processor._validate_json_structure(invalid_data, 'test.json')
        self.assertFalse(result)
    
    def test_transform_json_to_normalized_format(self):
        """Test transformation from ScrapperPAEAReal to normalized format"""
        normalized = self.processor._transform_json_to_normalized_format(self.sample_program_data)
        
        # Test basic fields
        self.assertEqual(normalized['program_name'], 'Test University PA Program')
        self.assertEqual(normalized['program_website'], 'https://example.com')
        self.assertEqual(normalized['phone_number'], '************')
        self.assertEqual(normalized['mission_statement'], 'To educate compassionate healthcare providers')
        
        # Test address transformation
        expected_address = '123 Main St, Test City, TS, 12345'
        self.assertEqual(normalized['address'], expected_address)
        
        # Test list field transformation
        expected_curriculum = 'Case-Based Learning, Problem-Based Learning'
        self.assertEqual(normalized['curriculum_focus'], expected_curriculum)
        
        # Test GPA requirements
        self.assertEqual(normalized['minimum_overall_gpa_required'], '3.0')
        self.assertEqual(normalized['minimum_prereq_gpa_required'], '3.2')
        
        # Test prerequisite courses transformation
        prereq_courses = normalized['prerequisite_courses']
        self.assertIsInstance(prereq_courses, list)
        self.assertTrue(len(prereq_courses) > 0)
        
        # Check specific prerequisite course
        anatomy_course = next((course for course in prereq_courses if course['course_name'] == 'Human Anatomy'), None)
        self.assertIsNotNone(anatomy_course)
        self.assertEqual(anatomy_course['credits'], '3 Credit(s)')
    
    def test_transform_address_data(self):
        """Test address data transformation"""
        address_data = {
            "street": "123 Main St",
            "city": "Test City",
            "state": "TS",
            "zip_code": "12345"
        }
        
        result = self.processor._transform_address_data(address_data)
        expected = "123 Main St, Test City, TS, 12345"
        self.assertEqual(result, expected)
        
        # Test with missing fields
        partial_address = {"city": "Test City", "state": "TS"}
        result = self.processor._transform_address_data(partial_address)
        expected = "Test City, TS"
        self.assertEqual(result, expected)
        
        # Test with null values
        null_address = {"street": "No information provided", "city": "Test City"}
        result = self.processor._transform_address_data(null_address)
        expected = "Test City"
        self.assertEqual(result, expected)
    
    def test_transform_list_field(self):
        """Test list field transformation"""
        # Test with list
        list_data = ["Item 1", "Item 2", "Item 3"]
        result = self.processor._transform_list_field(list_data)
        self.assertEqual(result, "Item 1, Item 2, Item 3")
        
        # Test with string
        string_data = "Single Item"
        result = self.processor._transform_list_field(string_data)
        self.assertEqual(result, "Single Item")
        
        # Test with null values in list
        list_with_nulls = ["Item 1", "No information provided", "Item 3"]
        result = self.processor._transform_list_field(list_with_nulls)
        self.assertEqual(result, "Item 1, Item 3")
    
    def test_transform_prerequisite_courses(self):
        """Test prerequisite courses transformation"""
        raw_data = {
            "human_anatomy_credits": "3 Credit(s)",
            "microbiology_credits": "4 Credit(s)",
            "statistics_credits": "3 Credit(s)",
            "biology_credits": "No information provided",  # Should be filtered out
            "prereq_time_limit": "5 years"
        }
        
        result = self.processor._transform_prerequisite_courses(raw_data)
        
        # Should have 3 courses (biology filtered out due to null value)
        self.assertEqual(len(result), 3)
        
        # Check specific course
        anatomy_course = next((course for course in result if course['course_name'] == 'Human Anatomy'), None)
        self.assertIsNotNone(anatomy_course)
        self.assertEqual(anatomy_course['credits'], '3 Credit(s)')
        self.assertEqual(anatomy_course['time_limit'], '5 years')
        self.assertFalse(anatomy_course['lab_required'])  # Default value
    
    def test_extract_class_size_from_normalized(self):
        """Test class size extraction from normalized data"""
        normalized_data = {
            'estimated_incoming_class_size': '30',
            'incoming_class_profile': {
                'class_size': '25'
            }
        }
        
        # Should prefer estimated_incoming_class_size
        result = self.processor._extract_class_size_from_normalized(normalized_data)
        self.assertEqual(result, 30)
        
        # Test fallback to class profile
        normalized_data_fallback = {
            'incoming_class_profile': {
                'class_size': '25'
            }
        }
        result = self.processor._extract_class_size_from_normalized(normalized_data_fallback)
        self.assertEqual(result, 25)
        
        # Test with no valid data
        empty_data = {}
        result = self.processor._extract_class_size_from_normalized(empty_data)
        self.assertEqual(result, 0)


class TestJSONFileProcessing(TestCase):
    """Test processing of actual JSON files"""
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.processor = ProgramDataMigrationProcessor(self.temp_dir)
    
    def test_process_single_program_file(self):
        """Test processing a single JSON program file"""
        # Create a test JSON file
        test_data = {
            "program_name": "Test University PA Program",
            "program_website": "https://example.com",
            "mission_statement": "Test mission",
            "minimum_overall_gpa_required": "3.0",
            "human_anatomy_credits": "3 Credit(s)"
        }
        
        test_file = Path(self.temp_dir) / "test_program.json"
        with open(test_file, 'w') as f:
            json.dump(test_data, f)
        
        # Mock the database operations since we're testing parsing logic
        with patch.object(self.processor, 'update_or_create_program') as mock_update:
            mock_update.return_value = type('MockProgram', (), {'id': 1, 'name': 'Test Program'})()
            
            result = self.processor.process_single_program(test_file)
            self.assertTrue(result)
            
            # Verify the method was called with transformed data
            mock_update.assert_called_once()
            call_args = mock_update.call_args[0][0]
            self.assertEqual(call_args['program_name'], 'Test University PA Program')
    
    def test_invalid_json_file(self):
        """Test handling of invalid JSON files"""
        # Create an invalid JSON file
        test_file = Path(self.temp_dir) / "invalid.json"
        with open(test_file, 'w') as f:
            f.write("{ invalid json content")
        
        result = self.processor.process_single_program(test_file)
        self.assertFalse(result)
        
        # Check that error was recorded
        self.assertTrue(len(self.processor.errors) > 0)
        self.assertEqual(self.processor.errors[0]['type'], 'json_error')
    
    def tearDown(self):
        """Clean up temporary files"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)


if __name__ == '__main__':
    unittest.main()