@api_view(['GET'])
def get_program_list(request):
    """Get the first 20 programs with their prerequisites and requirements"""
    try:
        programs = Program.objects.select_related('school').prefetch_related(
            'shadowing_requirements',
            'gre_requirements'
        ).all()[:20]
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    serializer = ProgramSerializer(programs, many=True)
    return Response(serializer.data) 