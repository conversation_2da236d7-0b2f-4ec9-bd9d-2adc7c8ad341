"""
Data Transformation and Mapping Logic for PA Program Migration

This module provides mapping functions to transform JSON structure to database models,
handles nested JSON data (prerequisites, requirements, etc.), and implements duplicate
detection and resolution for existing programs.
"""

import logging
import re
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, date
from decimal import Decimal, InvalidOperation
from django.db import transaction
from django.core.exceptions import ValidationError

from ..models import (
    School, Program, PrerequisiteCourse, HealthcareExperience, PatientCareExperience,
    ShadowingRequirement, GRERequirement, GPARequirement, InterviewRequirement,
    ApplicationRequirement, CASPARequirement, TuitionInformation, OtherRequirement,
    RecommendationRequirement, ClassProfile, PANCEPassRate, AttritionData,
    EnhancedClassProfile, MatriculantDemographics, ProgramCurriculum
)


class DataTransformationError(Exception):
    """Custom exception for data transformation errors"""
    pass


class ProgramDataTransformer:
    """
    Handles transformation of JSON program data to Django model instances.
    Implements mapping logic for nested JSON data and duplicate detection.
    """
    
    def __init__(self):
        """Initialize the data transformer with logging"""
        self.logger = self._setup_logger()
        self.transformation_stats = {
            'programs_processed': 0,
            'programs_created': 0,
            'programs_updated': 0,
            'programs_skipped': 0,
            'errors': []
        }
    
    def _setup_logger(self) -> logging.Logger:
        """Set up logging for the transformer"""
        logger = logging.getLogger('program_data_transformer')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    def _safe_get_from_data(self, data: Any, key: str, default: Any = None) -> Any:
        """
        Safely get a value from data that might be a dict or list.
        
        Args:
            data: Data structure (dict, list, or other)
            key: Key to look for
            default: Default value if key not found or data is not a dict
            
        Returns:
            Value from data or default
        """
        if isinstance(data, dict):
            return data.get(key, default)
        elif isinstance(data, list) and data:
            # If it's a list, try to find a dict with the key
            for item in data:
                if isinstance(item, dict) and key in item:
                    return item[key]
        return default
    
    def _safe_convert_to_decimal(self, value: Any, default: Optional[Decimal] = None) -> Optional[Decimal]:
        """
        Safely convert a value to Decimal, handling various input formats.
        
        Args:
            value: Value to convert
            default: Default value if conversion fails
            
        Returns:
            Decimal value or default
        """
        if value is None or value == "" or value == "No information provided":
            return default
            
        try:
            # Handle string values that might contain currency symbols or commas
            if isinstance(value, str):
                # Remove common currency symbols and formatting
                cleaned_value = value.replace('$', '').replace(',', '').strip()
                if not cleaned_value:
                    return default
                return Decimal(cleaned_value)
            
            return Decimal(str(value))
        except (InvalidOperation, ValueError, TypeError):
            self.logger.warning(f"Failed to convert '{value}' to Decimal, using default: {default}")
            return default
    
    def _safe_convert_to_int(self, value: Any, default: Optional[int] = None) -> Optional[int]:
        """
        Safely convert a value to integer.
        
        Args:
            value: Value to convert
            default: Default value if conversion fails
            
        Returns:
            Integer value or default
        """
        if value is None or value == "" or value == "No information provided":
            return default
            
        try:
            if isinstance(value, str):
                cleaned_value = value.replace(',', '').strip()
                if not cleaned_value:
                    return default
                return int(float(cleaned_value))  # Handle decimal strings
            
            return int(value)
        except (ValueError, TypeError):
            self.logger.warning(f"Failed to convert '{value}' to int, using default: {default}")
            return default
    
    def _safe_convert_to_date(self, value: Any) -> Optional[date]:
        """
        Safely convert a value to date, handling various formats.
        
        Args:
            value: Value to convert
            
        Returns:
            Date object or None
        """
        if value is None or value == "" or value == "No information provided":
            return None
            
        try:
            if isinstance(value, str):
                value = value.strip()
                
                # Try common date formats first
                for fmt in ['%m/%d/%Y', '%Y-%m-%d', '%m-%d-%Y', '%d/%m/%Y', '%m/%d/%y', '%Y-%m-%d']:
                    try:
                        return datetime.strptime(value, fmt).date()
                    except ValueError:
                        continue
                
                # Try month-year formats like "January 2026", "June 2026"
                month_year_formats = [
                    '%B %Y',  # "January 2026"
                    '%b %Y',  # "Jan 2026"
                    '%B, %Y', # "January, 2026"
                    '%b, %Y'  # "Jan, 2026"
                ]
                
                for fmt in month_year_formats:
                    try:
                        # Parse the month-year and default to the 1st day of the month
                        parsed_date = datetime.strptime(value, fmt)
                        return parsed_date.replace(day=1).date()
                    except ValueError:
                        continue
                
                # Try to handle "Month Year" format with manual parsing
                import re
                month_year_match = re.match(r'^([A-Za-z]+)\s+(\d{4})$', value)
                if month_year_match:
                    month_name, year_str = month_year_match.groups()
                    month_mapping = {
                        'january': 1, 'jan': 1,
                        'february': 2, 'feb': 2,
                        'march': 3, 'mar': 3,
                        'april': 4, 'apr': 4,
                        'may': 5,
                        'june': 6, 'jun': 6,
                        'july': 7, 'jul': 7,
                        'august': 8, 'aug': 8,
                        'september': 9, 'sep': 9, 'sept': 9,
                        'october': 10, 'oct': 10,
                        'november': 11, 'nov': 11,
                        'december': 12, 'dec': 12
                    }
                    
                    month_num = month_mapping.get(month_name.lower())
                    if month_num:
                        try:
                            year = int(year_str)
                            return date(year, month_num, 1)
                        except (ValueError, TypeError):
                            pass
                
                # If no format matches, log warning
                self.logger.warning(f"Failed to parse date: {value}")
                return None
                
        except Exception as e:
            self.logger.warning(f"Error converting date '{value}': {str(e)}")
            return None
    
    def _normalize_boolean(self, value: Any) -> bool:
        """
        Normalize various boolean representations to Python boolean.
        
        Args:
            value: Value to normalize
            
        Returns:
            Boolean value
        """
        if isinstance(value, bool):
            return value
        
        if isinstance(value, str):
            value_lower = value.lower().strip()
            if value_lower in ['yes', 'true', '1', 'y']:
                return True
            elif value_lower in ['no', 'false', '0', 'n']:
                return False
        
        # Default to False for unclear values
        return False
    
    def _safe_truncate_field(self, value: Any, max_length: int, field_name: str = "") -> Optional[str]:
        """
        Safely truncate a text field to fit database constraints.
        
        Args:
            value: Value to truncate
            max_length: Maximum allowed length
            field_name: Name of field for logging (optional)
            
        Returns:
            Truncated string or None
        """
        if value is None:
            return None
            
        str_value = str(value).strip()
        if not str_value or str_value == "No information provided":
            return None
            
        if len(str_value) > max_length:
            truncated = str_value[:max_length-3] + "..."
            if field_name:
                self.logger.warning(f"Truncated {field_name} from {len(str_value)} to {max_length} characters")
            return truncated
            
        return str_value
    
    def _clean_text_field(self, value: Any, max_length: int = None, field_name: str = "") -> Optional[str]:
        """
        Clean and normalize text field values with optional length constraint.
        
        Args:
            value: Value to clean
            max_length: Optional maximum length for truncation
            field_name: Name of field for logging (optional)
            
        Returns:
            Cleaned string or None
        """
        if value is None or value == "" or value == "No information provided":
            return None
            
        if isinstance(value, str):
            cleaned = value.strip()
            if not cleaned:
                return None
                
            # Apply length constraint if specified
            if max_length and len(cleaned) > max_length:
                return self._safe_truncate_field(cleaned, max_length, field_name)
                
            return cleaned
            
        str_value = str(value) if value else None
        if max_length and str_value and len(str_value) > max_length:
            return self._safe_truncate_field(str_value, max_length, field_name)
            
        return str_value
    
    def _extract_school_data(self, json_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract school information from JSON data.
        
        Args:
            json_data: Raw JSON program data
            
        Returns:
            Dictionary with school data
        """
        # Extract school name from program name (assuming format: "School Name (Campus)")
        program_name = json_data.get('program_name', '')
        
        # Try to extract school name from program name
        if '(' in program_name:
            school_name = program_name.split('(')[0].strip()
        else:
            school_name = program_name
        
        # Extract location information
        address_data = json_data.get('address', {})
        city = self._clean_text_field(address_data.get('city'))
        state = self._clean_text_field(address_data.get('state'))
        
        location = None
        if city and state:
            location = f"{city}, {state}"
        elif city:
            location = city
        elif state:
            location = state
        
        return {
            'name': school_name,
            'location': location or 'Unknown',
            'mission_statement': self._clean_text_field(json_data.get('mission_statement')) or '',
            'website': self._clean_text_field(json_data.get('program_website')) or ''
        }
    
    def _find_or_create_school(self, school_data: Dict[str, Any]) -> School:
        """
        Find existing school or create new one.
        
        Args:
            school_data: School data dictionary
            
        Returns:
            School instance
        """
        try:
            # Try to find existing school by name
            school = School.objects.filter(name=school_data['name']).first()
            
            if school:
                # Update school information if needed
                updated = False
                if school.location != school_data['location']:
                    school.location = school_data['location']
                    updated = True
                if school.mission_statement != school_data['mission_statement']:
                    school.mission_statement = school_data['mission_statement']
                    updated = True
                if school.website != school_data['website']:
                    school.website = school_data['website']
                    updated = True
                
                if updated:
                    school.save()
                    self.logger.info(f"Updated existing school: {school.name}")
                
                return school
            else:
                # Create new school
                school = School.objects.create(**school_data)
                self.logger.info(f"Created new school: {school.name}")
                return school
                
        except Exception as e:
            self.logger.error(f"Error finding/creating school '{school_data['name']}': {str(e)}")
            raise DataTransformationError(f"Failed to process school data: {str(e)}")
    
    def _extract_program_data(self, json_data: Dict[str, Any], school: School) -> Dict[str, Any]:
        """
        Extract main program information from JSON data.
        
        Args:
            json_data: Raw JSON program data
            school: School instance
            
        Returns:
            Dictionary with program data
        """
        address_data = json_data.get('address', {})
        
        return {
            'school': school,
            'name': self._clean_text_field(json_data.get('program_name')) or 'Unknown Program',
            'description': self._clean_text_field(json_data.get('unique_program_features')) or '',
            'url': self._clean_text_field(json_data.get('program_url')) or '',
            'application_deadline': self._safe_convert_to_date(json_data.get('application_deadline')),
            'program_start_date': self._safe_convert_to_date(json_data.get('start_month')),
            'average_gpa': self._safe_convert_to_decimal(json_data.get('minimum_overall_gpa_required')),
            'class_size': self._safe_convert_to_int(json_data.get('estimated_incoming_class_size'), 0),
            
            # Enhanced fields from comprehensive JSON data - with safe field lengths
            'phone': self._clean_text_field(json_data.get('phone_number'), max_length=50, field_name='phone'),
            'address': self._format_address(address_data),
            'mission_statement': self._clean_text_field(json_data.get('mission_statement')),
            'unique_features': self._clean_text_field(json_data.get('unique_program_features')),
            'curriculum_focus': ', '.join(json_data.get('curriculum_focus', [])) if json_data.get('curriculum_focus') else None,
            'credentials_offered': self._clean_text_field(', '.join(json_data.get('credentials_offered', [])) if json_data.get('credentials_offered') else None, max_length=100, field_name='credentials_offered'),
            'bridge_dual_degree': self._clean_text_field(json_data.get('bridge_dual_degree'), max_length=50, field_name='bridge_dual_degree'),
            'doctorate_offered': self._clean_text_field(json_data.get('doctorate_degree_offered'), max_length=20, field_name='doctorate_offered'),
            'masters_degree_type': self._clean_text_field(', '.join(json_data.get('type_of_masters_degree', [])) if json_data.get('type_of_masters_degree') else None, max_length=100, field_name='masters_degree_type'),
            'program_length': self._clean_text_field(json_data.get('program_length_months'), max_length=50, field_name='program_length'),
            'start_month': self._clean_text_field(json_data.get('start_month'), max_length=50, field_name='start_month'),
            'estimated_class_size': self._clean_text_field(json_data.get('estimated_incoming_class_size'), max_length=20, field_name='estimated_class_size'),
            'part_time_option': self._clean_text_field(json_data.get('part_time_option'), max_length=20, field_name='part_time_option'),
            'distance_learning': self._clean_text_field(json_data.get('distance_learning'), max_length=20, field_name='distance_learning'),
            'on_campus_housing': self._clean_text_field(json_data.get('on_campus_housing'), max_length=20, field_name='on_campus_housing'),
            'admission_type': self._clean_text_field(json_data.get('admission_type'), max_length=100, field_name='admission_type'),
            
            # New fields from enhanced JSON structure - with safe field lengths
            'city_state': self._format_city_state(address_data),
            'caspa_member': self._normalize_boolean(json_data.get('caspa_member')),
            'upcoming_caspa_cycle': self._normalize_boolean(json_data.get('upcoming_caspa_cycle')),
            'caspa_deadline': self._safe_convert_to_date(json_data.get('application_deadline')),
            'caspa_deadline_requirement': self._clean_text_field(json_data.get('deadline_requirement')),
            'program_website': self._clean_text_field(json_data.get('program_website')),
            'program_social': self._clean_text_field(json_data.get('program_social'), max_length=200, field_name='program_social'),
            'email': self._clean_text_field(json_data.get('email')),
            'last_updated': self._clean_text_field(json_data.get('last_updated'), max_length=50, field_name='last_updated'),
            'international_application_link': self._clean_text_field(json_data.get('international_application_link')),
            'curriculum_focus_json': json_data.get('curriculum_focus'),
            'credentials_offered_json': json_data.get('credentials_offered'),
            'type_of_masters_degree': json_data.get('type_of_masters_degree'),
            'program_length_months': self._clean_text_field(json_data.get('program_length_months'), max_length=50, field_name='program_length_months'),
            'required_onsite_interview': self._clean_text_field(json_data.get('required_onsite_interview'), max_length=20, field_name='required_onsite_interview'),
            'types_of_interviews': json_data.get('types_of_interviews'),
            
            # Store complete enhanced JSON data for flexibility
            'enhanced_data': json_data  # Store the entire original JSON
        }
    
    def _format_address(self, address_data: Dict[str, Any]) -> Optional[str]:
        """
        Format address from address components.
        
        Args:
            address_data: Address dictionary
            
        Returns:
            Formatted address string or None
        """
        if not address_data:
            return None
            
        components = []
        
        street = self._clean_text_field(address_data.get('street'))
        city = self._clean_text_field(address_data.get('city'))
        state = self._clean_text_field(address_data.get('state'))
        zip_code = self._clean_text_field(address_data.get('zip_code'))
        
        if street:
            components.append(street)
        if city:
            components.append(city)
        if state:
            components.append(state)
        if zip_code:
            components.append(zip_code)
            
        return ', '.join(components) if components else None
    
    def _format_city_state(self, address_data: Dict[str, Any]) -> Optional[str]:
        """
        Format city and state from address components.
        
        Args:
            address_data: Address dictionary
            
        Returns:
            City, State string or None
        """
        if not address_data:
            return None
            
        city = self._clean_text_field(address_data.get('city'))
        state = self._clean_text_field(address_data.get('state'))
        
        if city and state:
            return f"{city}, {state}"
        elif city:
            return city
        elif state:
            return state
        else:
            return None
    
    def _detect_duplicate_program(self, program_data: Dict[str, Any]) -> Optional[Program]:
        """
        Detect if a program already exists in the database.
        
        Args:
            program_data: Program data dictionary
            
        Returns:
            Existing Program instance or None
        """
        try:
            # Try to find by exact name and school
            program = Program.objects.filter(
                name=program_data['name'],
                school=program_data['school']
            ).first()
            
            if program:
                return program
            
            # Try to find by similar name (case-insensitive)
            program = Program.objects.filter(
                name__iexact=program_data['name'],
                school=program_data['school']
            ).first()
            
            if program:
                return program
            
            # Try to find by URL if available
            if program_data.get('url'):
                program = Program.objects.filter(
                    url=program_data['url']
                ).first()
                
                if program:
                    return program
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error detecting duplicate program: {str(e)}")
            return None
    
    def _create_or_update_program(self, program_data: Dict[str, Any]) -> Tuple[Program, bool]:
        """
        Create new program or update existing one.
        
        Args:
            program_data: Program data dictionary
            
        Returns:
            Tuple of (Program instance, created_flag)
        """
        existing_program = self._detect_duplicate_program(program_data)
        
        if existing_program:
            # Update existing program
            for field, value in program_data.items():
                if field != 'school':  # Don't update school reference
                    setattr(existing_program, field, value)
            
            existing_program.save()
            self.logger.info(f"Updated existing program: {existing_program.name}")
            return existing_program, False
        else:
            # Create new program
            program = Program.objects.create(**program_data)
            self.logger.info(f"Created new program: {program.name}")
            return program, True
    
    def _create_caspa_requirements(self, program: Program, json_data: Dict[str, Any]) -> None:
        """
        Create CASPA requirements for the program.
        
        Args:
            program: Program instance
            json_data: Raw JSON program data
        """
        try:
            # Remove existing CASPA requirements
            CASPARequirement.objects.filter(program=program).delete()
            
            caspa_data = {
                'program': program,
                'caspa_member': self._normalize_boolean(json_data.get('caspa_member')),
                'upcoming_caspa_cycle': self._normalize_boolean(json_data.get('upcoming_caspa_cycle')),
                'application_deadline': self._safe_convert_to_date(json_data.get('application_deadline')),
                'deadline_requirement': self._clean_text_field(json_data.get('deadline_requirement')),
                'supplemental_application': self._normalize_boolean(json_data.get('supplemental_application')),
                'supplemental_deadline': self._safe_convert_to_date(json_data.get('supplemental_deadline')),
                'supplemental_application_fee': self._clean_text_field(json_data.get('supplemental_application_fee')),
                'supplemental_application_fee_waiver': self._normalize_boolean(json_data.get('supplemental_application_fee_waiver')),
                'view_supplemental_application': self._normalize_boolean(json_data.get('view_supplemental_application'))
            }
            
            CASPARequirement.objects.create(**caspa_data)
            self.logger.debug(f"Created CASPA requirements for {program.name}")
            
        except Exception as e:
            self.logger.error(f"Error creating CASPA requirements for {program.name}: {str(e)}")
    
    def _create_tuition_information(self, program: Program, json_data: Dict[str, Any]) -> None:
        """
        Create tuition information for the program.
        
        Args:
            program: Program instance
            json_data: Raw JSON program data
        """
        try:
            # Remove existing tuition information
            TuitionInformation.objects.filter(program=program).delete()
            
            tuition_data = {
                'program': program,
                'separate_tuition_rates': self._normalize_boolean(json_data.get('separate_tuition_rates')),
                'tuition': self._clean_text_field(json_data.get('tuition')),
                'resident_tuition': self._clean_text_field(json_data.get('resident_tuition')),
                'non_resident_tuition': self._clean_text_field(json_data.get('non_resident_tuition')),
                'seat_deposit': self._normalize_boolean(json_data.get('seat_deposit')),
                'seat_deposit_cost': self._clean_text_field(json_data.get('seat_deposit_cost')),
                'refundable_seat_deposit': self._normalize_boolean(json_data.get('refundable_seat_deposit'))
            }
            
            TuitionInformation.objects.create(**tuition_data)
            self.logger.debug(f"Created tuition information for {program.name}")
            
        except Exception as e:
            self.logger.error(f"Error creating tuition information for {program.name}: {str(e)}")
    
    def _create_gpa_requirements(self, program: Program, json_data: Dict[str, Any]) -> None:
        """
        Create GPA requirements for the program.
        
        Args:
            program: Program instance
            json_data: Raw JSON program data
        """
        try:
            # Remove existing GPA requirements
            GPARequirement.objects.filter(program=program).delete()
            
            gpa_data = {
                'program': program,
                'minimum_overall': self._clean_text_field(json_data.get('minimum_overall_gpa_required')) or 'Not specified',
                'minimum_prereq': self._clean_text_field(json_data.get('minimum_prereq_gpa_required')) or 'Not specified',
                'minimum_science': self._clean_text_field(json_data.get('minimum_science_gpa_required')) or 'Not specified'
            }
            
            GPARequirement.objects.create(**gpa_data)
            self.logger.debug(f"Created GPA requirements for {program.name}")
            
        except Exception as e:
            self.logger.error(f"Error creating GPA requirements for {program.name}: {str(e)}")
    
    def _create_gre_requirements(self, program: Program, json_data: Dict[str, Any]) -> None:
        """
        Create GRE requirements for the program.
        
        Args:
            program: Program instance
            json_data: Raw JSON program data
        """
        try:
            # Remove existing GRE requirements
            GRERequirement.objects.filter(program=program).delete()
            
            # Extract GRE scores and percentiles
            gre_scores = json_data.get('gre_average_scores', {})
            gre_percentiles = json_data.get('gre_average_percentiles', {})
            
            gre_data = {
                'program': program,
                'required': None,  # This would need to be inferred from other data
                'verbal_score': self._clean_text_field(gre_scores.get('average_verbal_reasoning_gre_score')) or 'Not specified',
                'quantitative_score': self._clean_text_field(gre_scores.get('average_quantitative_reasoning_gre_score')) or 'Not specified',
                'analytical_score': self._clean_text_field(gre_scores.get('average_analytical_writing_gre_score')) or 'Not specified',
                'verbal_percentile': self._clean_text_field(gre_percentiles.get('average_verbal_reasoning_gre_percentile')) or 'Not specified',
                'quantitative_percentile': self._clean_text_field(gre_percentiles.get('average_quantitative_reasoning_gre_percentile')) or 'Not specified',
                'analytical_percentile': self._clean_text_field(gre_percentiles.get('average_analytical_writing_gre_percentile')) or 'Not specified'
            }
            
            GRERequirement.objects.create(**gre_data)
            self.logger.debug(f"Created GRE requirements for {program.name}")
            
        except Exception as e:
            self.logger.error(f"Error creating GRE requirements for {program.name}: {str(e)}")
    
    def _create_recommendation_requirements(self, program: Program, json_data: Dict[str, Any]) -> None:
        """
        Create recommendation requirements for the program.
        
        Args:
            program: Program instance
            json_data: Raw JSON program data
        """
        try:
            # Remove existing recommendation requirements
            RecommendationRequirement.objects.filter(program=program).delete()
            
            rec_data = {
                'program': program,
                'number_required': self._clean_text_field(json_data.get('minimum_number_of_reference_letters_required')) or 'Not specified',
                'types_required': json_data.get('types_of_references_required', []),
                'specific_requirements': 'See program requirements for specific details'
            }
            
            RecommendationRequirement.objects.create(**rec_data)
            self.logger.debug(f"Created recommendation requirements for {program.name}")
            
        except Exception as e:
            self.logger.error(f"Error creating recommendation requirements for {program.name}: {str(e)}")
    
    def _create_other_requirements(self, program: Program, json_data: Dict[str, Any]) -> None:
        """
        Create other requirements for the program.
        
        Args:
            program: Program instance
            json_data: Raw JSON program data
        """
        try:
            # Remove existing other requirements
            OtherRequirement.objects.filter(program=program).delete()
            
            other_data = {
                'program': program,
                'daca_accepted': self._normalize_boolean(json_data.get('daca_status_applicants_considered')),
                'veteran_support': self._clean_text_field(json_data.get('support_for_veterans')) or 'Not specified',
                'transfer_accepted': self._normalize_boolean(json_data.get('transfer_students_accepted')),
                'out_of_state_accepted': self._normalize_boolean(json_data.get('out_of_state_students_accepted')),
                'international_accepted': self._normalize_boolean(json_data.get('international_applicants_accepted')),
                'international_requirements': self._clean_text_field(json_data.get('international_application_link')) or 'Not specified'
            }
            
            OtherRequirement.objects.create(**other_data)
            self.logger.debug(f"Created other requirements for {program.name}")
            
        except Exception as e:
            self.logger.error(f"Error creating other requirements for {program.name}: {str(e)}")
    
    def _create_interview_requirements(self, program: Program, json_data: Dict[str, Any]) -> None:
        """
        Create interview requirements for the program.
        
        Args:
            program: Program instance
            json_data: Raw JSON program data
        """
        try:
            # Remove existing interview requirements
            InterviewRequirement.objects.filter(program=program).delete()
            
            interview_types = json_data.get('types_of_interviews', [])
            
            interview_data = {
                'program': program,
                'required': True,  # Assume interviews are required unless specified otherwise
                'onsite_required': self._normalize_boolean(json_data.get('required_onsite_interview')),
                'mmi_used': 'Multiple Mini Interview (MMI)' in interview_types,
                'interview_types': interview_types
            }
            
            InterviewRequirement.objects.create(**interview_data)
            self.logger.debug(f"Created interview requirements for {program.name}")
            
        except Exception as e:
            self.logger.error(f"Error creating interview requirements for {program.name}: {str(e)}")
    
    def _create_matriculant_demographics(self, program: Program, json_data: Dict[str, Any]) -> None:
        """
        Create matriculant demographics for the program.
        
        Args:
            program: Program instance
            json_data: Raw JSON program data
        """
        try:
            # Remove existing demographics
            MatriculantDemographics.objects.filter(program=program).delete()
            
            gender_data = json_data.get('program_matriculants_by_gender', {})
            ethnicity_data = json_data.get('ethnicity_of_program_matriculants', {})
            
            demographics_data = {
                'program': program,
                'year': None,  # Year not specified in this data format
                'female_count': self._clean_text_field(gender_data.get('female_matriculants')),
                'male_count': self._clean_text_field(gender_data.get('male_matriculants')),
                'non_binary_count': self._clean_text_field(gender_data.get('non_binary_matriculants')),
                'gender_unknown_count': self._clean_text_field(gender_data.get('gender_unknown_matriculants')),
                'american_indian_count': self._clean_text_field(ethnicity_data.get('american_indian_or_alaskan_native_matriculants')),
                'asian_count': self._clean_text_field(ethnicity_data.get('asian_matriculants')),
                'black_count': self._clean_text_field(ethnicity_data.get('black_or_african_american_matriculants')),
                'hispanic_count': self._clean_text_field(ethnicity_data.get('hispanic_latino_or_spanish_matriculants')),
                'hawaiian_pacific_count': self._clean_text_field(ethnicity_data.get('native_hawaiian_or_pacific_islander_matriculants')),
                'white_count': self._clean_text_field(ethnicity_data.get('white_matriculants')),
                'other_count': self._clean_text_field(ethnicity_data.get('other_matriculants'))
            }
            
            MatriculantDemographics.objects.create(**demographics_data)
            self.logger.debug(f"Created matriculant demographics for {program.name}")
            
        except Exception as e:
            self.logger.error(f"Error creating matriculant demographics for {program.name}: {str(e)}")
    
    def _create_program_curriculum(self, program: Program, json_data: Dict[str, Any]) -> None:
        """
        Create program curriculum information.
        
        Args:
            program: Program instance
            json_data: Raw JSON program data
        """
        try:
            # Remove existing curriculum
            ProgramCurriculum.objects.filter(program=program).delete()
            
            curriculum_data = {
                'program': program,
                'curriculum_focus': json_data.get('curriculum_focus'),
                'didactic_phase_length': None,  # Not available in current JSON structure
                'clinical_phase_length': None,  # Not available in current JSON structure
                'total_program_length': self._clean_text_field(json_data.get('program_length_months')),
                'didactic_courses': None,  # Not available in current JSON structure
                'clinical_rotations': None,  # Not available in current JSON structure
                'elective_rotations': None  # Not available in current JSON structure
            }
            
            ProgramCurriculum.objects.create(**curriculum_data)
            self.logger.debug(f"Created program curriculum for {program.name}")
            
        except Exception as e:
            self.logger.error(f"Error creating program curriculum for {program.name}: {str(e)}")
    
    def transform_program_data(self, json_data: Dict[str, Any]) -> Optional[Program]:
        """
        Transform a single program's JSON data to database models.
        
        Args:
            json_data: Raw JSON program data
            
        Returns:
            Program instance or None if transformation failed
        """
        try:
            self.transformation_stats['programs_processed'] += 1
            
            with transaction.atomic():
                # Extract and create/find school
                school_data = self._extract_school_data(json_data)
                school = self._find_or_create_school(school_data)
                
                # Extract and create/update program
                program_data = self._extract_program_data(json_data, school)
                program, created = self._create_or_update_program(program_data)
                
                if created:
                    self.transformation_stats['programs_created'] += 1
                else:
                    self.transformation_stats['programs_updated'] += 1
                
                # Create related models
                self._create_caspa_requirements(program, json_data)
                self._create_tuition_information(program, json_data)
                self._create_gpa_requirements(program, json_data)
                self._create_gre_requirements(program, json_data)
                self._create_recommendation_requirements(program, json_data)
                self._create_other_requirements(program, json_data)
                self._create_interview_requirements(program, json_data)
                self._create_matriculant_demographics(program, json_data)
                self._create_program_curriculum(program, json_data)
                
                self.logger.info(f"Successfully transformed program: {program.name}")
                return program
                
        except Exception as e:
            self.transformation_stats['programs_skipped'] += 1
            error_msg = f"Failed to transform program '{json_data.get('program_name', 'Unknown')}': {str(e)}"
            self.transformation_stats['errors'].append(error_msg)
            self.logger.error(error_msg)
            return None
    
    def transform_batch(self, json_data_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Transform a batch of program JSON data.
        
        Args:
            json_data_list: List of raw JSON program data
            
        Returns:
            Transformation summary
        """
        self.logger.info(f"Starting batch transformation of {len(json_data_list)} programs")
        
        # Reset stats
        self.transformation_stats = {
            'programs_processed': 0,
            'programs_created': 0,
            'programs_updated': 0,
            'programs_skipped': 0,
            'errors': []
        }
        
        successful_programs = []
        
        for json_data in json_data_list:
            program = self.transform_program_data(json_data)
            if program:
                successful_programs.append(program)
        
        # Generate summary
        summary = {
            'total_programs': len(json_data_list),
            'successful_transformations': len(successful_programs),
            'programs_created': self.transformation_stats['programs_created'],
            'programs_updated': self.transformation_stats['programs_updated'],
            'programs_skipped': self.transformation_stats['programs_skipped'],
            'success_rate': len(successful_programs) / len(json_data_list) * 100 if json_data_list else 0,
            'errors': self.transformation_stats['errors']
        }
        
        self.logger.info(f"Batch transformation complete: {summary['successful_transformations']}/{summary['total_programs']} successful")
        
        return summary
    
    def get_transformation_stats(self) -> Dict[str, Any]:
        """
        Get current transformation statistics.
        
        Returns:
            Dictionary with transformation statistics
        """
        return self.transformation_stats.copy()

    def transform_comprehensive_program_data(self, json_data: Dict[str, Any]) -> Optional[Program]:
        """
        Transform comprehensive program JSON data to database models.
        Handles the new structure with program_data containing nested sections.
        
        Args:
            json_data: Raw JSON program data with comprehensive structure
            
        Returns:
            Program instance or None if transformation failed
        """
        try:
            self.transformation_stats['programs_processed'] += 1
            
            with transaction.atomic():
                # Extract program_data section
                program_data = json_data.get('program_data', {})
                general_info = program_data.get('general_information', {})
                
                # Extract and create/find school from program name (simplified approach)
                program_name = json_data.get('program_name', general_info.get('program_name', 'Unknown Program'))
                
                # Create a basic school entry (can be enhanced later)
                school_name = program_name
                if 'University' in program_name:
                    school_name = program_name.replace(' PA Program', '').replace(' Physician Assistant Program', '')
                
                school_data = {
                    'name': school_name,
                    'location': general_info.get('address', ''),
                    'mission_statement': general_info.get('mission_statement', ''),
                    'website': json_data.get('url', '')
                }
                school = self._find_or_create_school(school_data)
                
                # JSONB-First Approach: Only populate essential fields + enhanced_data
                program_fields = {
                    'school': school,
                    'name': program_name,
                    'description': general_info.get('unique_program_features', ''),
                    'url': json_data.get('url', ''),
                    'application_deadline': self._safe_convert_to_date(program_data.get('caspa_deadlines', {}).get('application_deadline')),
                    'program_start_date': self._safe_convert_to_date(general_info.get('start_month')),
                    'average_gpa': self._safe_convert_to_decimal(program_data.get('requirements', {}).get('gpa_requirements', {}).get('minimum_overall_gpa')),
                    'class_size': self._safe_convert_to_int(general_info.get('estimated_incoming_class_size'), 0),
                    
                    # Store complete JSON data (no character limits!)
                    'enhanced_data': json_data
                }
                

                
                # Add normalized search fields
                normalized_fields = self._extract_normalized_search_fields(json_data)
                program_fields.update(normalized_fields)
                
                program, created = self._create_or_update_program(program_fields)
                

                
                if created:
                    self.transformation_stats['programs_created'] += 1
                else:
                    self.transformation_stats['programs_updated'] += 1
                
                # Re-enable structured data creation alongside JSONB storage
                # Note: Related models will store structured data for searchability
                # while enhanced_data JSONB field preserves everything
                try:
                    self._create_comprehensive_caspa_requirements(program, program_data.get('caspa_deadlines', {}))
                    self._create_comprehensive_tuition_information(program, program_data.get('tuition_deposits', {}))
                    self._create_comprehensive_gpa_requirements(program, program_data.get('requirements', {}).get('gpa_requirements', {}))
                    # Enable all enhanced data creation - always create placeholder records
                    self._create_comprehensive_healthcare_experience(program, program_data.get('requirements', {}).get('healthcare_experience', {}))
                    self._create_comprehensive_shadowing_requirements(program, program_data.get('requirements', {}).get('shadowing', {}))
                    self._create_comprehensive_other_requirements(program, program_data.get('matriculants', {}))
                    self._create_comprehensive_matriculant_demographics(program, program_data.get('matriculants', {}))
                    self._create_comprehensive_enhanced_class_profile(program, program_data.get('class_profile', {}))
                    self._create_comprehensive_attrition_data(program, program_data.get('attrition', {}))
                    self._create_comprehensive_pance_pass_rates(program, program_data.get('pance_pass_rates', []))
                    self._create_comprehensive_program_curriculum(program, program_data.get('program_curriculum', {}))
                    self._create_comprehensive_prerequisites(program, program_data.get('prerequisites', {}))
                    self._create_comprehensive_recommendation_requirements(program, program_data.get('requirements', {}).get('references', {}))
                except Exception as e:
                    self.logger.warning(f"Some related data creation failed (data preserved in JSONB): {e}")
                
                self.logger.info(f"Successfully transformed comprehensive program: {program.name}")
                return program
                
        except Exception as e:
            self.logger.error(f"Error transforming comprehensive program data: {str(e)}")
            self.transformation_stats['failed_programs'] = self.transformation_stats.get('failed_programs', 0) + 1
            return None

    def _extract_normalized_search_fields(self, json_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract and normalize critical fields for efficient searching.
        Converts string values to proper data types.
        """
        normalized = {}
        
        # Get the general information section
        program_data = json_data.get('program_data', {})
        general_info = program_data.get('general_information', {})
        requirements = program_data.get('requirements', {})
        gpa_reqs = requirements.get('gpa_requirements', {})
        experience_reqs = requirements.get('healthcare_experience', {})
        shadowing_reqs = requirements.get('shadowing', {})
        tuition_data = program_data.get('tuition_deposits', {})
        
        # Parse class size (handle ranges like "28-30" or single values like "50")
        class_size_str = general_info.get('estimated_incoming_class_size', '')
        if class_size_str:
            if '-' in class_size_str:
                parts = class_size_str.split('-')
                try:
                    normalized['class_size_min'] = int(parts[0].strip())
                    normalized['class_size_max'] = int(parts[1].strip())
                except (ValueError, IndexError):
                    pass
            else:
                try:
                    size = int(class_size_str.strip())
                    normalized['class_size_min'] = size
                    normalized['class_size_max'] = size
                except ValueError:
                    pass
        
        # Parse program length (e.g., "24 Months", "27-30 Months")
        program_length_str = general_info.get('program_length', '')
        if program_length_str:
            # Extract numbers from string
            import re
            numbers = re.findall(r'\d+', program_length_str)
            if numbers:
                if len(numbers) == 1:
                    try:
                        months = int(numbers[0])
                        normalized['program_length_months_min'] = months
                        normalized['program_length_months_max'] = months
                    except ValueError:
                        pass
                elif len(numbers) >= 2:
                    try:
                        normalized['program_length_months_min'] = int(numbers[0])
                        normalized['program_length_months_max'] = int(numbers[1])
                    except ValueError:
                        pass
        
        # Parse boolean fields
        part_time = general_info.get('part_time_option', '').lower()
        normalized['has_part_time_option'] = part_time == 'yes' if part_time else None
        
        distance = general_info.get('distance_learning', '').lower()
        normalized['has_distance_learning'] = distance == 'yes' if distance else None
        
        housing = general_info.get('on_campus_housing', '').lower()
        normalized['has_on_campus_housing'] = housing == 'yes' if housing else None
        
        interview = general_info.get('required_onsite_interview', '').lower()
        normalized['requires_onsite_interview'] = interview == 'yes' if interview else None
        
        doctorate = general_info.get('doctorate_degree_offered', '').lower()
        normalized['offers_doctorate'] = doctorate == 'yes' if doctorate else None
        
        # Parse tuition (remove $ and commas)
        def parse_tuition(value):
            if not value or value == 'No information provided':
                return None
            # Remove $, commas, and any text after numbers
            clean_value = re.sub(r'[^\d.]', '', str(value).split()[0])
            try:
                return Decimal(clean_value)
            except:
                return None
        
        # Check for separate tuition rates
        if tuition_data.get('separate_tuition_rates') == 'Yes':
            normalized['tuition_in_state'] = parse_tuition(tuition_data.get('resident_tuition'))
            normalized['tuition_out_of_state'] = parse_tuition(tuition_data.get('non_resident_tuition'))
        else:
            # Use single tuition for both
            tuition = parse_tuition(tuition_data.get('tuition'))
            if tuition:
                normalized['tuition_in_state'] = tuition
                normalized['tuition_out_of_state'] = tuition
        
        # Parse GPA requirements
        def parse_gpa(value):
            if not value or value == 'No GPA minimum required':
                return None
            try:
                # Handle values like "3.00" or "3.0"
                return Decimal(str(value).strip())
            except:
                return None
        
        normalized['min_gpa_overall'] = parse_gpa(gpa_reqs.get('minimum_overall_gpa'))
        normalized['min_gpa_science'] = parse_gpa(gpa_reqs.get('minimum_science_gpa'))
        normalized['min_gpa_prereq'] = parse_gpa(gpa_reqs.get('minimum_prereq_gpa'))
        
        # Parse experience hours
        def parse_hours(value):
            if not value:
                return None
            try:
                # Extract first number from string
                match = re.search(r'\d+', str(value))
                return int(match.group()) if match else None
            except:
                return None
        
        # Healthcare experience hours
        if experience_reqs.get('required_or_recommended') in ['Yes, required', 'Required']:
            normalized['min_patient_care_hours'] = parse_hours(experience_reqs.get('hours_needed'))
        
        # Shadowing hours
        shadowing_hours = shadowing_reqs.get('Shadowing_hours') or shadowing_reqs.get('shadowing_hours')
        if shadowing_hours and shadowing_reqs.get('shadowing_pa') != 'Not Required':
            normalized['min_shadowing_hours'] = parse_hours(shadowing_hours)
        
        # Parse location - try multiple sources
        address = general_info.get('address', '')

        # First try to get from address field
        if address:
            # Try to extract state code (last 2 uppercase letters before zip)
            state_match = re.search(r'\b([A-Z]{2})\s+\d{5}', address)
            if state_match:
                normalized['state'] = state_match.group(1)

            # Extract city (usually before state)
            city_match = re.search(r'([^,\n]+),\s*[A-Z]{2}', address)
            if city_match:
                normalized['city'] = city_match.group(1).strip()

        # If no address, try to infer from program name (basic mapping for known universities)
        if not normalized.get('state'):
            program_name = general_info.get('program_name', '').lower()
            state_mappings = {
                'rosalind franklin': 'IL',
                'midwestern university': 'IL',  # Has multiple campuses, but main is IL
                'university of chicago': 'IL',
                'northwestern': 'IL',
                'loyola': 'IL',
                'rush university': 'IL',
                'southern illinois': 'IL',
                # Add more mappings as needed
            }

            for name_part, state_code in state_mappings.items():
                if name_part in program_name:
                    normalized['state'] = state_code
                    break
        
        return normalized
    
    def _create_comprehensive_caspa_requirements(self, program: Program, caspa_data: Dict[str, Any]):
        """Create CASPA requirements from comprehensive data"""
        if not caspa_data:
            return
            
        # Delete existing requirements
        program.caspa_requirements.all().delete()
        
        CASPARequirement.objects.create(
            program=program,
            caspa_member=caspa_data.get('caspa_member', 'No') == 'Yes',
            upcoming_caspa_cycle=caspa_data.get('upcoming_caspa_cycle', 'No') == 'Yes',
            application_deadline=self._safe_convert_to_date(caspa_data.get('application_deadline')),
            deadline_requirement=caspa_data.get('deadline_requirement'),
            supplemental_application=caspa_data.get('supplemental_application', 'No') == 'Yes',
            supplemental_deadline=self._safe_convert_to_date(caspa_data.get('supplemental_deadline')),
            supplemental_application_fee=caspa_data.get('supplemental_application_fee'),
            supplemental_application_fee_waiver=caspa_data.get('supplemental_application_fee_waiver', 'No') == 'Yes'
        )

    def _create_comprehensive_tuition_information(self, program: Program, tuition_data: Dict[str, Any]):
        """Create tuition information from comprehensive data"""
        if not tuition_data:
            return
            
        # Delete existing tuition info
        program.tuition_information.all().delete()
        
        TuitionInformation.objects.create(
            program=program,
            separate_tuition_rates=tuition_data.get('separate_tuition_rates', 'No') == 'Yes',
            tuition=self._clean_text_field(tuition_data.get('tuition'), max_length=50, field_name='tuition'),
            resident_tuition=self._clean_text_field(tuition_data.get('resident_tuition'), max_length=50, field_name='resident_tuition'),
            non_resident_tuition=self._clean_text_field(tuition_data.get('non_resident_tuition'), max_length=50, field_name='non_resident_tuition'),
            seat_deposit=tuition_data.get('seat_deposit', 'No') == 'Yes',
            seat_deposit_cost=self._clean_text_field(tuition_data.get('seat_deposit_cost'), max_length=50, field_name='seat_deposit_cost'),
            refundable_seat_deposit=tuition_data.get('refundable_seat_deposit', 'No') == 'Yes'
        )

    def _create_comprehensive_gpa_requirements(self, program: Program, gpa_data: Dict[str, Any]):
        """Create GPA requirements from comprehensive data"""
        if not gpa_data:
            return
            
        # Delete existing GPA requirements
        program.gpa_requirements.all().delete()
        
        GPARequirement.objects.create(
            program=program,
            minimum_overall=gpa_data.get('minimum_overall_gpa', 'N/A'),
            minimum_prereq=gpa_data.get('minimum_prereq_gpa', 'N/A'),
            minimum_science=gpa_data.get('minimum_science_gpa', 'N/A')
        )

    def _create_comprehensive_healthcare_experience(self, program: Program, healthcare_data: Dict[str, Any]):
        """Create healthcare experience requirements from comprehensive data - always create placeholder record"""
        # Delete existing requirements
        program.healthcare_experience.all().delete()
        program.patient_care_experience.all().delete()
        
        # General healthcare experience
        HealthcareExperience.objects.create(
            program=program,
            required=healthcare_data.get('required_or_recommended', '').lower() == 'required',
            hours_needed=self._safe_convert_to_int(healthcare_data.get('hours_needed')),
            time_limit=self._clean_text_field(healthcare_data.get('time_limit')) or 'No Requirement',  # Ensure non-null
            paid_accepted=True,  # Default
            volunteer_accepted=True,  # Default
            virtual_accepted=healthcare_data.get('accepted_experience_types', {}).get('virtual_health_care_accepted', 'No') == 'Yes',
            status='Required' if healthcare_data.get('required_or_recommended', '').lower() == 'required' else 'Recommended'
        )
        
        # Patient care experience (if specified)
        experience_types = healthcare_data.get('accepted_experience_types', {})
        if experience_types.get('direct_patient_care_hours_needed'):
            PatientCareExperience.objects.create(
                program=program,
                required=True,  # If hours specified, assume required
                hours_needed=self._safe_convert_to_int(experience_types.get('direct_patient_care_hours_needed')),
                time_limit=experience_types.get('direct_patient_care_time_limit', 'No Requirement'),
                paid_accepted=experience_types.get('paid_direct_patient_care', 'Not Accepted') != 'Not Accepted',
                volunteer_accepted=experience_types.get('volunteer_direct_patient_care', 'Not Accepted') != 'Not Accepted',
                paid_status='Accepted' if experience_types.get('paid_direct_patient_care') == 'Accepted' else 'Not Specified',
                volunteer_status='Accepted' if experience_types.get('volunteer_direct_patient_care') == 'Accepted' else 'Not Specified'
            )

    def _create_comprehensive_shadowing_requirements(self, program: Program, shadowing_data: Dict[str, Any]):
        """Create shadowing requirements from comprehensive data - always create placeholder record"""
        # Delete existing requirements
        program.shadowing_requirements.all().delete()
        
        ShadowingRequirement.objects.create(
            program=program,
            pa_shadowing=shadowing_data.get('shadowing_pa', 'Not Required'),
            physician_shadowing=shadowing_data.get('shadowing_physician', 'Not Required'),
            other_shadowing=shadowing_data.get('shadowing_other_health_care_provider', 'Not Required'),
            virtual_accepted=shadowing_data.get('virtual_shadowing_accepted', 'No') == 'Yes'
        )

    def _create_comprehensive_other_requirements(self, program: Program, matriculants_data: Dict[str, Any]):
        """Create other requirements from matriculants data - always create placeholder record"""
        # Delete existing requirements
        program.other_requirements.all().delete()
        
        OtherRequirement.objects.create(
            program=program,
            daca_accepted=matriculants_data.get('daca_status_applicants_considered', 'No') == 'Yes',
            veteran_support=matriculants_data.get('support_for_veterans', ''),
            transfer_accepted=matriculants_data.get('transfer_students_accepted', 'No') == 'Yes',
            out_of_state_accepted=matriculants_data.get('out_of_state_students_accepted', 'Yes') == 'Yes',
            international_accepted=matriculants_data.get('international_applicants_accepted', 'No').startswith('Yes'),
            international_requirements=matriculants_data.get('international_applicants_accepted', '')
        )

    def _create_comprehensive_matriculant_demographics(self, program: Program, matriculants_data: Dict[str, Any]):
        """Create matriculant demographics from comprehensive data - always create placeholder record"""
        # Delete existing demographics
        program.matriculant_demographics.all().delete()

        gender_data = matriculants_data.get('gender', {})
        ethnicity_data = matriculants_data.get('ethnicity', {})

        # Always create demographics record, even with "No information provided" values
        MatriculantDemographics.objects.create(
            program=program,
            year='Current',  # Can be made dynamic
            female_count=gender_data.get('female'),
            male_count=gender_data.get('male'),
            non_binary_count=gender_data.get('non_binary'),
            gender_unknown_count=gender_data.get('gender_unknown'),
            american_indian_count=ethnicity_data.get('american_indian_or_alaskan_native'),
            asian_count=ethnicity_data.get('asian'),
            black_count=ethnicity_data.get('black_or_african_american'),
            hispanic_count=ethnicity_data.get('hispanic_latino_or_spanish'),
            hawaiian_pacific_count=ethnicity_data.get('native_hawaiian_or_pacific_islander'),
            white_count=ethnicity_data.get('white'),
            other_count=ethnicity_data.get('other')
        )

    def _create_comprehensive_enhanced_class_profile(self, program: Program, class_profile_data: Dict[str, Any]):
        """Create enhanced class profile from comprehensive data - always create placeholder record"""
        # Delete existing profiles
        program.enhanced_class_profiles.all().delete()

        # Always create class profile record, even with "No information provided" values
        gre_percentiles = class_profile_data.get('mean_gre_percentiles', {})

        EnhancedClassProfile.objects.create(
            program=program,
            year=class_profile_data.get('year', 'Current'),
            total_applications=class_profile_data.get('total_applications'),
            total_interviewed=class_profile_data.get('total_interviewed'),
            total_matriculants=class_profile_data.get('total_matriculants'),
            average_overall_gpa=class_profile_data.get('average_overall_gpa'),
            average_science_gpa=class_profile_data.get('average_science_gpa'),
            average_healthcare_hours=class_profile_data.get('average_healthcare_hours'),
            gre_verbal_percentile=gre_percentiles.get('verbal'),
            gre_quantitative_percentile=gre_percentiles.get('quantitative'),
            gre_analytical_percentile=gre_percentiles.get('analytical')
        )

    def _create_comprehensive_attrition_data(self, program: Program, attrition_data: Dict[str, Any]):
        """Create attrition data from comprehensive data - always create placeholder record"""
        # Delete existing attrition data
        program.attrition_data.all().delete()

        # Always create attrition record, even with "No information provided" values
        AttritionData.objects.create(
            program=program,
            class_year=attrition_data.get('class_year', 'Current'),
            max_entering_class_size=self._safe_convert_to_int(attrition_data.get('maximum_entering_class_size')),
            entering_class_size=self._safe_convert_to_int(attrition_data.get('entering_class_size')),
            graduates=self._safe_convert_to_int(attrition_data.get('graduates')),
            attrition_rate=attrition_data.get('attrition_rate'),
            graduation_rate=attrition_data.get('graduation_rate')
        )

    def _create_comprehensive_pance_pass_rates(self, program: Program, pance_data):
        """Create PANCE pass rates from comprehensive data - always create placeholder record"""
        # Delete existing PANCE data
        program.pance_pass_rates.all().delete()

        # Always create PANCE record, even with "No information provided" values
        # Handle both string and list formats
        if isinstance(pance_data, str):
            # If it's a string like "No information provided", preserve the original string for text fields
            PANCEPassRate.objects.create(
                program=program,
                year='Current',
                group='All Takers',
                program_pass_rate=pance_data,  # CharField - keep original string
                national_pass_rate="No information provided",  # CharField - consistent placeholder
                candidates_took_pance=None  # IntegerField - must be None for missing data
            )
        elif isinstance(pance_data, list) and pance_data:
            # If it's a list of PANCE records, create each one
            for pance_record in pance_data:
                PANCEPassRate.objects.create(
                    program=program,
                    year=pance_record.get('year', 'Current'),
                    group=pance_record.get('group', 'All Takers'),
                    program_pass_rate=pance_record.get('program_pass_rate') or pance_record.get('pass_rate'),
                    national_pass_rate=pance_record.get('national_pass_rate'),
                    candidates_took_pance=self._safe_convert_to_int(pance_record.get('candidates_took_pance') or pance_record.get('total_test_takers'))
                )
        else:
            # Create placeholder record
            PANCEPassRate.objects.create(
                program=program,
                year='Current',
                group='All Takers',
                program_pass_rate=None,
                national_pass_rate=None,
                candidates_took_pance=None
            )

    def _create_comprehensive_program_curriculum(self, program: Program, curriculum_data: Dict[str, Any]):
        """Create program curriculum from comprehensive data"""
        if not curriculum_data:
            return
            
        # Delete existing curriculum
        program.program_curriculum.all().delete()
        
        didactic_phase = curriculum_data.get('didactic_phase', {})
        clinical_phase = curriculum_data.get('clinical_phase', {})
        
        # Only create if we have meaningful data
        if didactic_phase or clinical_phase:
            ProgramCurriculum.objects.create(
                program=program,
                didactic_courses=didactic_phase.get('terms'),
                clinical_rotations=clinical_phase.get('required_rotations'),
                didactic_phase_length=didactic_phase.get('term_type')  # Use correct field name
            )

    def _create_comprehensive_prerequisites(self, program: Program, prerequisites_data: Dict[str, Any]):
        """Create prerequisites from comprehensive data"""
        if not prerequisites_data or not prerequisites_data.get('courses'):
            return
            
        # Delete existing prerequisites
        program.prerequisite_courses.all().delete()
        
        prereq_time_limit = prerequisites_data.get('prereq_time_limit', 'No Requirement')
        
        for course_data in prerequisites_data.get('courses', []):
            PrerequisiteCourse.objects.create(
                program=program,
                course_name=course_data.get('name', ''),
                credits=course_data.get('credits', ''),
                lab_required=False,  # Default, can be enhanced
                time_limit=prereq_time_limit
            )

    def _create_comprehensive_recommendation_requirements(self, program: Program, references_data: Dict[str, Any]):
        """Create recommendation requirements from comprehensive data"""
        if not references_data:
            return
            
        # Delete existing requirements
        program.recommendation_requirements.all().delete()
        
        # Convert single type to list if needed
        types_required = references_data.get('types_of_references_required', [])
        if isinstance(types_required, str):
            types_required = [types_required]
        
        RecommendationRequirement.objects.create(
            program=program,
            number_required=references_data.get('minimum_number_required', 'N/A'),
            types_required=types_required,
            specific_requirements=''
        )


class DuplicateDetector:
    """
    Utility class for detecting and resolving duplicate programs.
    """
    
    def __init__(self):
        """Initialize the duplicate detector"""
        self.logger = logging.getLogger('duplicate_detector')
    
    def find_potential_duplicates(self) -> List[Dict[str, Any]]:
        """
        Find potential duplicate programs in the database.
        
        Returns:
            List of potential duplicate groups
        """
        duplicates = []
        
        try:
            # Find programs with similar names
            programs = Program.objects.all().order_by('name')
            
            for i, program in enumerate(programs):
                similar_programs = []
                
                # Check remaining programs for similarities
                for other_program in programs[i+1:]:
                    if self._are_similar_programs(program, other_program):
                        similar_programs.append(other_program)
                
                if similar_programs:
                    duplicates.append({
                        'primary_program': program,
                        'similar_programs': similar_programs,
                        'similarity_reasons': self._get_similarity_reasons(program, similar_programs)
                    })
        
        except Exception as e:
            self.logger.error(f"Error finding duplicates: {str(e)}")
        
        return duplicates
    
    def _are_similar_programs(self, program1: Program, program2: Program) -> bool:
        """
        Check if two programs are similar (potential duplicates).
        
        Args:
            program1: First program
            program2: Second program
            
        Returns:
            True if programs are similar
        """
        # Check if same school and similar names
        if program1.school == program2.school:
            name1 = program1.name.lower().strip()
            name2 = program2.name.lower().strip()
            
            # Exact match
            if name1 == name2:
                return True
            
            # Similar names (simple similarity check)
            if self._calculate_similarity(name1, name2) > 0.8:
                return True
        
        # Check if same URL
        if program1.url and program2.url and program1.url == program2.url:
            return True
        
        return False
    
    def _calculate_similarity(self, str1: str, str2: str) -> float:
        """
        Calculate similarity between two strings.
        
        Args:
            str1: First string
            str2: Second string
            
        Returns:
            Similarity score (0.0 to 1.0)
        """
        # Simple similarity calculation (can be improved with more sophisticated algorithms)
        if not str1 or not str2:
            return 0.0
        
        # Calculate Jaccard similarity based on words
        words1 = set(str1.split())
        words2 = set(str2.split())
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union) if union else 0.0
    
    def _get_similarity_reasons(self, primary: Program, similar_programs: List[Program]) -> List[str]:
        """
        Get reasons why programs are considered similar.
        
        Args:
            primary: Primary program
            similar_programs: List of similar programs
            
        Returns:
            List of similarity reasons
        """
        reasons = []
        
        for program in similar_programs:
            if primary.school == program.school:
                reasons.append(f"Same school: {primary.school.name}")
            
            if primary.url and program.url and primary.url == program.url:
                reasons.append(f"Same URL: {primary.url}")
            
            name_similarity = self._calculate_similarity(
                primary.name.lower(), program.name.lower()
            )
            if name_similarity > 0.8:
                reasons.append(f"Similar names (similarity: {name_similarity:.2f})")
        
        return reasons