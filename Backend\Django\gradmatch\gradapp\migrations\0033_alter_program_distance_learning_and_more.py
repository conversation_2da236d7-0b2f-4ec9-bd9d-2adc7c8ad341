# Generated by Django 5.0.1 on 2025-07-28 03:52

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('gradapp', '0032_add_enhanced_data_jsonb'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='program',
            name='distance_learning',
            field=models.Char<PERSON>ield(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='program',
            name='doctorate_offered',
            field=models.Char<PERSON>ield(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='program',
            name='estimated_class_size',
            field=models.Char<PERSON>ield(blank=True, max_length=100, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='program',
            name='on_campus_housing',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='program',
            name='part_time_option',
            field=models.Char<PERSON><PERSON>(blank=True, max_length=100, null=True),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name='program',
            name='required_onsite_interview',
            field=models.Char<PERSON>ield(blank=True, max_length=100, null=True),
        ),
    ]
