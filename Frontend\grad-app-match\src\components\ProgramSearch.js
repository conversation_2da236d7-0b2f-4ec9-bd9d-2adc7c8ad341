import React from 'react';
import axios from 'axios';

const ProgramSearch = ({ onResults }) => {
    const handleShowAllSchools = async () => {
        try {
            // Updated to include the full URL pointing to your Django backend
            const response = await axios.get(`${process.env.REACT_APP_API_URL || 'http://127.0.0.1:8000'}/api/schools/all`);
            onResults(response.data); // Pass the fetched data to the parent component
        } catch (error) {
            console.error('Failed to fetch school names:', error);
        }
    };

    return (
        <div className="program-search-form" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
            <button onClick={handleShowAllSchools}>Display List of Schools</button>
        </div>
    );
};

export default ProgramSearch;
