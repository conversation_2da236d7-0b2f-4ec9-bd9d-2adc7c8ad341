# Generated by Django 5.0.1 on 2025-05-01 00:50

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('gradapp', '0021_alter_healthcareexperience_required_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ProgramMatchResult',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('match_status', models.CharField(choices=[('eligible', 'Eligible'), ('needs_review', 'Needs Review'), ('ineligible', 'Ineligible'), ('pending', 'Pending Calculation')], default='pending', max_length=20)),
                ('match_details', models.JSONField(blank=True, help_text='Detailed breakdown of requirement matching (e.g., fulfilled/missing prerequisites, GPA status)', null=True)),
                ('last_calculated', models.DateTimeField(blank=True, help_text='Timestamp of the last calculation', null=True)),
                ('is_stale', models.BooleanField(default=True, help_text='True if user/program data changed since last calculation')),
                ('program', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='match_results', to='gradapp.program')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='match_results', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['user', 'program'],
                'indexes': [models.Index(fields=['user', 'is_stale'], name='gradapp_pro_user_id_36b216_idx'), models.Index(fields=['user', 'program', 'last_calculated'], name='gradapp_pro_user_id_0ef175_idx')],
                'unique_together': {('user', 'program')},
            },
        ),
    ]
