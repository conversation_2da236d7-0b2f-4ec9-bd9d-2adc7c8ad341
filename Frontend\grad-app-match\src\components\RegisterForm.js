import React, { useState } from 'react';
import { useNavigate, <PERSON> } from 'react-router-dom'; // Import Link
import { useAuth } from '../contexts/AuthContext';
import styled from '@emotion/styled';

// Styled components (keep as they are)
const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
`;

const Input = styled.input`
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  font-size: 1rem;
  width: 100%;

  &:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 1px #3498db;
  }
`;

const Button = styled.button`
  padding: 0.75rem;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: #2980b9;
  }

  &:disabled {
    background-color: #bdc3c7;
    cursor: not-allowed;
  }
`;

const Message = styled.div`
  font-size: 0.875rem;
  margin-top: 0.5rem;
  padding: 0.75rem;
  border-radius: 0.375rem;
  text-align: center;
`;

const ErrorMessage = styled(Message)`
  color: #e74c3c;
  background-color: #fdecea;
  border: 1px solid #e74c3c;
`;

const SuccessMessage = styled(Message)`
  color: #27ae60;
  background-color: #eafaf1;
  border: 1px solid #27ae60;
`;


function RegisterForm() {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
  });
  const [error, setError] = useState('');
  const [message, setMessage] = useState(''); // For success messages
  const [isLoading, setIsLoading] = useState(false);
  const { signUp } = useAuth(); // Only need signUp from context here
  const navigate = useNavigate();

  const validatePassword = (password) => {
    if (password.length < 6) {
      return 'Password must be at least 6 characters long.';
    }
    if (!/\d/.test(password)) { // Check for at least one digit
      return 'Password must include at least one number.';
    }
    // Check for at least one special character (adjust regex as needed)
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      return 'Password must include at least one special character (e.g., !@#$%).';
    }
    return null; // Password is valid
  };


  const handleChange = (event) => {
    const { name, value } = event.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
    setError(''); // Clear messages on change
    setMessage('');
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    setError('');
    setMessage('');

    // --- Enhanced Validation ---
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    const passwordError = validatePassword(formData.password);
    if (passwordError) {
        setError(passwordError);
        return;
    }
    // --- End Enhanced Validation ---


    setIsLoading(true);

    try {
      // Prepare options for Supabase signUp, including metadata
      const options = {
        data: {
          first_name: formData.firstName,
          last_name: formData.lastName,
        }
      };

      console.log('Attempting Supabase registration...');
      const { data, error: signUpError } = await signUp(formData.email, formData.password, options);

      if (signUpError) {
        // Throw the specific Supabase error
        throw signUpError;
      }

      console.log('Supabase registration successful:', data);

      // --- Improved Success Handling for Email Verification ---
      if (data) {
          // Check if user was created but needs email confirmation
          if (data.user && !data.session) {
              setMessage('Registration successful! Please check your email to verify your account. Click the verification link in your email to complete registration.');
              // Don't auto-redirect - let user read the message
          }
          // Check if user is immediately signed in (email confirmation disabled)
          else if (data.session && data.user) {
              setMessage('Registration successful! Redirecting to dashboard...');
              // AuthContext listener will handle navigation
              setTimeout(() => navigate('/home'), 2000);
          }
          else {
              // Fallback case
              setMessage('Registration processed. Please check your email for verification instructions.');
          }
      } else {
          setMessage('Registration processed. Please check your email for verification instructions.');
          console.warn('Supabase signUp resolved successfully but returned no data.');
      }
      // --- End Improved Success Handling ---


    } catch (error) {
      console.error('Supabase Registration Error:', error);
      // --- Improved Error Handling ---
      let specificError = 'Failed to register. Please try again.'; // Default generic error
      if (error.message) {
          if (error.message.includes('User already registered')) {
              specificError = 'An account with this email already exists. Please try logging in.';
          } else if (error.message.includes('Password should be stronger')) {
              // This message might come from Supabase password policies if configured
              specificError = 'Password is too weak. Please ensure it meets the requirements.';
          } else if (error.message.includes('rate limit exceeded')) {
               specificError = 'Registration rate limit exceeded. Please try again later.';
          } else if (error.message.includes('Unable to validate email address')) {
               specificError = 'Invalid email address format.';
          }
          // You can add more specific checks based on errors you encounter
          else {
              // Use the Supabase message if it seems informative, otherwise keep generic
              specificError = `Registration failed: ${error.message}. Please check your details and try again.`;
          }
      }
      setError(specificError);
      // --- End Improved Error Handling ---
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Form onSubmit={handleSubmit}>
      {error && <ErrorMessage>{error}</ErrorMessage>}
      {message && <SuccessMessage>{message}</SuccessMessage>}
      <Input
        type="text"
        name="firstName"
        value={formData.firstName}
        onChange={handleChange}
        placeholder="First Name"
        required
        disabled={isLoading}
      />
      <Input
        type="text"
        name="lastName"
        value={formData.lastName}
        onChange={handleChange}
        placeholder="Last Name"
        required
        disabled={isLoading}
      />
      <Input
        type="email"
        name="email"
        value={formData.email}
        onChange={handleChange}
        placeholder="Email"
        required
        disabled={isLoading}
        autoComplete="email"
      />
      <Input
        type="password"
        name="password"
        value={formData.password}
        onChange={handleChange}
        // Updated placeholder
        placeholder="Password (min 6 chars, 1 number, 1 special char)"
        required
        disabled={isLoading}
        autoComplete="new-password"
      />
      <Input
        type="password"
        name="confirmPassword"
        value={formData.confirmPassword}
        onChange={handleChange}
        placeholder="Confirm Password"
        required
        disabled={isLoading}
        autoComplete="new-password"
      />
      <Button type="submit" disabled={isLoading}>
        {isLoading ? 'Creating Account...' : 'Sign Up'}
      </Button>
       {/* Link back to Login */}
       <div style={{ textAlign: 'center', marginTop: '1rem' }}>
            Already have an account? <Link to="/login">Login here</Link>
        </div>
    </Form>
  );
}

export default RegisterForm;
