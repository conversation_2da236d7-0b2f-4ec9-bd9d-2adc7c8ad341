"""
Tests for the Data Transformation and Mapping Logic

This module tests the ProgramDataTransformer class and its ability to transform
JSON program data to database models, handle nested data, and detect duplicates.
"""

import json
from decimal import Decimal
from datetime import date
from django.test import TestCase
from django.db import transaction
from unittest.mock import patch, MagicMock

from ..models import (
    School, Program, CASPARequirement, TuitionInformation, GPARequirement,
    GRERequirement, RecommendationRequirement, OtherRequirement,
    InterviewRequirement, MatriculantDemographics, ProgramCurriculum
)
from ..utils.data_transformer import ProgramDataTransformer, DuplicateDetector, DataTransformationError


class ProgramDataTransformerTest(TestCase):
    """Test cases for ProgramDataTransformer"""
    
    def setUp(self):
        """Set up test data"""
        self.transformer = ProgramDataTransformer()
        
        # Sample JSON data based on the actual structure
        self.sample_json_data = {
            "program_name": "Test University PA Program (Main Campus)",
            "program_url": "https://example.com/pa-program",
            "program_website": "https://example.com",
            "program_social": "Facebook: @testpa",
            "phone_number": "************",
            "address": {
                "street": "123 Main St",
                "city": "Test City",
                "state": "CA",
                "zip_code": "12345"
            },
            "email": "<EMAIL>",
            "last_updated": "2024-01-15",
            "international_application_link": "https://example.com/international",
            "mission_statement": "To educate competent physician assistants",
            "unique_program_features": "Strong clinical focus with community partnerships",
            "curriculum_focus": [
                "Problem-Based Learning",
                "Case-Based Learning"
            ],
            "credentials_offered": [
                "Master's degree (not Master's of Public Health)"
            ],
            "doctorate_degree_offered": "No",
            "type_of_masters_degree": [
                "MS: Master of Science"
            ],
            "program_length_months": "24 Months",
            "start_month": "September 2026",
            "estimated_incoming_class_size": "50",
            "part_time_option": "No",
            "distance_learning": "No",
            "on_campus_housing": "Yes",
            "required_onsite_interview": "Yes",
            "types_of_interviews": [
                "Multiple Mini Interview (MMI)",
                "Traditional Interview"
            ],
            "admission_type": "Rolling",
            "caspa_member": "Yes",
            "upcoming_caspa_cycle": "Yes",
            "application_deadline": "10/01/2025",
            "deadline_requirement": "Green-Verified by deadline",
            "supplemental_application": "Yes",
            "supplemental_deadline": "10/31/2025",
            "supplemental_application_fee": "$75",
            "supplemental_application_fee_waiver": "Yes",
            "view_supplemental_application": True,
            "separate_tuition_rates": "No",
            "tuition": "$50,000",
            "resident_tuition": "No information provided",
            "non_resident_tuition": "No information provided",
            "seat_deposit": "Yes",
            "seat_deposit_cost": "$500",
            "refundable_seat_deposit": "No",
            "out_of_state_students_accepted": "Yes",
            "transfer_students_accepted": "No",
            "daca_status_applicants_considered": "Yes",
            "international_applicants_accepted": "No",
            "support_for_veterans": "Full support available",
            "program_matriculants_by_gender": {
                "female_matriculants": "30",
                "male_matriculants": "20",
                "non_binary_matriculants": "0",
                "gender_unknown_matriculants": "0"
            },
            "ethnicity_of_program_matriculants": {
                "american_indian_or_alaskan_native_matriculants": "1",
                "asian_matriculants": "10",
                "black_or_african_american_matriculants": "5",
                "hispanic_latino_or_spanish_matriculants": "8",
                "native_hawaiian_or_pacific_islander_matriculants": "0",
                "white_matriculants": "25",
                "other_matriculants": "1"
            },
            "gre_average_scores": {
                "average_verbal_reasoning_gre_score": "155",
                "average_analytical_writing_gre_score": "4.0",
                "average_quantitative_reasoning_gre_score": "150"
            },
            "gre_average_percentiles": {
                "average_verbal_reasoning_gre_percentile": "70",
                "average_analytical_writing_gre_percentile": "60",
                "average_quantitative_reasoning_gre_percentile": "65"
            },
            "minimum_number_of_reference_letters_required": "3",
            "types_of_references_required": [
                "PA",
                "Physician",
                "Academic Reference"
            ],
            "minimum_overall_gpa_required": "3.0",
            "minimum_prereq_gpa_required": "3.2",
            "minimum_science_gpa_required": "3.0"
        }
    
    def test_safe_convert_to_decimal(self):
        """Test decimal conversion utility"""
        # Valid conversions
        self.assertEqual(self.transformer._safe_convert_to_decimal("3.5"), Decimal("3.5"))
        self.assertEqual(self.transformer._safe_convert_to_decimal("$50,000"), Decimal("50000"))
        self.assertEqual(self.transformer._safe_convert_to_decimal(3.5), Decimal("3.5"))
        
        # Invalid conversions
        self.assertIsNone(self.transformer._safe_convert_to_decimal("invalid"))
        self.assertIsNone(self.transformer._safe_convert_to_decimal(""))
        self.assertIsNone(self.transformer._safe_convert_to_decimal("No information provided"))
        
        # Default value
        self.assertEqual(self.transformer._safe_convert_to_decimal("invalid", Decimal("0")), Decimal("0"))
    
    def test_safe_convert_to_int(self):
        """Test integer conversion utility"""
        # Valid conversions
        self.assertEqual(self.transformer._safe_convert_to_int("50"), 50)
        self.assertEqual(self.transformer._safe_convert_to_int("1,000"), 1000)
        self.assertEqual(self.transformer._safe_convert_to_int(50), 50)
        
        # Invalid conversions
        self.assertIsNone(self.transformer._safe_convert_to_int("invalid"))
        self.assertIsNone(self.transformer._safe_convert_to_int(""))
        self.assertIsNone(self.transformer._safe_convert_to_int("No information provided"))
        
        # Default value
        self.assertEqual(self.transformer._safe_convert_to_int("invalid", 0), 0)
    
    def test_safe_convert_to_date(self):
        """Test date conversion utility"""
        # Valid conversions
        self.assertEqual(self.transformer._safe_convert_to_date("10/01/2025"), date(2025, 10, 1))
        self.assertEqual(self.transformer._safe_convert_to_date("2025-10-01"), date(2025, 10, 1))
        
        # Invalid conversions
        self.assertIsNone(self.transformer._safe_convert_to_date("invalid"))
        self.assertIsNone(self.transformer._safe_convert_to_date(""))
        self.assertIsNone(self.transformer._safe_convert_to_date("No information provided"))
    
    def test_normalize_boolean(self):
        """Test boolean normalization utility"""
        # True values
        self.assertTrue(self.transformer._normalize_boolean(True))
        self.assertTrue(self.transformer._normalize_boolean("Yes"))
        self.assertTrue(self.transformer._normalize_boolean("yes"))
        self.assertTrue(self.transformer._normalize_boolean("TRUE"))
        self.assertTrue(self.transformer._normalize_boolean("1"))
        
        # False values
        self.assertFalse(self.transformer._normalize_boolean(False))
        self.assertFalse(self.transformer._normalize_boolean("No"))
        self.assertFalse(self.transformer._normalize_boolean("no"))
        self.assertFalse(self.transformer._normalize_boolean("FALSE"))
        self.assertFalse(self.transformer._normalize_boolean("0"))
        
        # Default to False for unclear values
        self.assertFalse(self.transformer._normalize_boolean("maybe"))
        self.assertFalse(self.transformer._normalize_boolean(""))
    
    def test_clean_text_field(self):
        """Test text field cleaning utility"""
        # Valid text
        self.assertEqual(self.transformer._clean_text_field("  Test Text  "), "Test Text")
        self.assertEqual(self.transformer._clean_text_field("Normal Text"), "Normal Text")
        
        # Empty/null values
        self.assertIsNone(self.transformer._clean_text_field(""))
        self.assertIsNone(self.transformer._clean_text_field("   "))
        self.assertIsNone(self.transformer._clean_text_field(None))
        self.assertIsNone(self.transformer._clean_text_field("No information provided"))
    
    def test_extract_school_data(self):
        """Test school data extraction"""
        school_data = self.transformer._extract_school_data(self.sample_json_data)
        
        self.assertEqual(school_data['name'], "Test University PA Program")
        self.assertEqual(school_data['location'], "Test City, CA")
        self.assertEqual(school_data['mission_statement'], "To educate competent physician assistants")
        self.assertEqual(school_data['website'], "https://example.com")
    
    def test_find_or_create_school(self):
        """Test school creation and finding"""
        school_data = {
            'name': 'Test University',
            'location': 'Test City, CA',
            'mission_statement': 'Test mission',
            'website': 'https://test.edu'
        }
        
        # Create new school
        school1 = self.transformer._find_or_create_school(school_data)
        self.assertEqual(school1.name, 'Test University')
        self.assertEqual(School.objects.count(), 1)
        
        # Find existing school
        school2 = self.transformer._find_or_create_school(school_data)
        self.assertEqual(school1.id, school2.id)
        self.assertEqual(School.objects.count(), 1)
    
    def test_extract_program_data(self):
        """Test program data extraction"""
        school = School.objects.create(
            name='Test University',
            location='Test City, CA',
            mission_statement='Test mission',
            website='https://test.edu'
        )
        
        program_data = self.transformer._extract_program_data(self.sample_json_data, school)
        
        self.assertEqual(program_data['school'], school)
        self.assertEqual(program_data['name'], "Test University PA Program (Main Campus)")
        self.assertEqual(program_data['url'], "https://example.com/pa-program")
        self.assertEqual(program_data['class_size'], 50)
        self.assertEqual(program_data['caspa_member'], True)
        self.assertEqual(program_data['upcoming_caspa_cycle'], True)
        self.assertEqual(program_data['city_state'], "Test City, CA")
    
    def test_format_address(self):
        """Test address formatting"""
        address_data = {
            'street': '123 Main St',
            'city': 'Test City',
            'state': 'CA',
            'zip_code': '12345'
        }
        
        formatted = self.transformer._format_address(address_data)
        self.assertEqual(formatted, "123 Main St, Test City, CA, 12345")
        
        # Test with missing components
        partial_address = {'city': 'Test City', 'state': 'CA'}
        formatted_partial = self.transformer._format_address(partial_address)
        self.assertEqual(formatted_partial, "Test City, CA")
        
        # Test with empty address
        self.assertIsNone(self.transformer._format_address({}))
        self.assertIsNone(self.transformer._format_address(None))
    
    def test_format_city_state(self):
        """Test city/state formatting"""
        address_data = {
            'city': 'Test City',
            'state': 'CA'
        }
        
        formatted = self.transformer._format_city_state(address_data)
        self.assertEqual(formatted, "Test City, CA")
        
        # Test with only city
        city_only = {'city': 'Test City'}
        self.assertEqual(self.transformer._format_city_state(city_only), "Test City")
        
        # Test with only state
        state_only = {'state': 'CA'}
        self.assertEqual(self.transformer._format_city_state(state_only), "CA")
        
        # Test with empty data
        self.assertIsNone(self.transformer._format_city_state({}))
    
    def test_detect_duplicate_program(self):
        """Test duplicate program detection"""
        # Create a school and program
        school = School.objects.create(
            name='Test University',
            location='Test City, CA',
            mission_statement='Test mission',
            website='https://test.edu'
        )
        
        existing_program = Program.objects.create(
            school=school,
            name='Test PA Program',
            description='Test description',
            url='https://example.com/program',
            class_size=50
        )
        
        # Test exact match detection
        program_data = {
            'name': 'Test PA Program',
            'school': school,
            'url': 'https://example.com/program'
        }
        
        duplicate = self.transformer._detect_duplicate_program(program_data)
        self.assertEqual(duplicate, existing_program)
        
        # Test case-insensitive match
        program_data_case = {
            'name': 'test pa program',
            'school': school,
            'url': 'https://different.com'
        }
        
        duplicate_case = self.transformer._detect_duplicate_program(program_data_case)
        self.assertEqual(duplicate_case, existing_program)
        
        # Test URL match
        program_data_url = {
            'name': 'Different Name',
            'school': school,
            'url': 'https://example.com/program'
        }
        
        duplicate_url = self.transformer._detect_duplicate_program(program_data_url)
        self.assertEqual(duplicate_url, existing_program)
        
        # Test no match
        program_data_no_match = {
            'name': 'Completely Different Program',
            'school': school,
            'url': 'https://different.com/program'
        }
        
        no_duplicate = self.transformer._detect_duplicate_program(program_data_no_match)
        self.assertIsNone(no_duplicate)
    
    def test_transform_program_data_success(self):
        """Test successful program data transformation"""
        program = self.transformer.transform_program_data(self.sample_json_data)
        
        # Verify program was created
        self.assertIsNotNone(program)
        self.assertEqual(program.name, "Test University PA Program (Main Campus)")
        self.assertEqual(program.class_size, 50)
        
        # Verify school was created
        self.assertEqual(School.objects.count(), 1)
        school = School.objects.first()
        self.assertEqual(school.name, "Test University PA Program")
        
        # Verify related models were created
        self.assertEqual(CASPARequirement.objects.filter(program=program).count(), 1)
        self.assertEqual(TuitionInformation.objects.filter(program=program).count(), 1)
        self.assertEqual(GPARequirement.objects.filter(program=program).count(), 1)
        self.assertEqual(GRERequirement.objects.filter(program=program).count(), 1)
        self.assertEqual(RecommendationRequirement.objects.filter(program=program).count(), 1)
        self.assertEqual(OtherRequirement.objects.filter(program=program).count(), 1)
        self.assertEqual(InterviewRequirement.objects.filter(program=program).count(), 1)
        self.assertEqual(MatriculantDemographics.objects.filter(program=program).count(), 1)
        self.assertEqual(ProgramCurriculum.objects.filter(program=program).count(), 1)
        
        # Verify specific related model data
        caspa_req = CASPARequirement.objects.get(program=program)
        self.assertTrue(caspa_req.caspa_member)
        self.assertTrue(caspa_req.upcoming_caspa_cycle)
        self.assertEqual(caspa_req.application_deadline, date(2025, 10, 1))
        
        tuition_info = TuitionInformation.objects.get(program=program)
        self.assertEqual(tuition_info.tuition, "$50,000")
        self.assertTrue(tuition_info.seat_deposit)
        self.assertEqual(tuition_info.seat_deposit_cost, "$500")
        
        gpa_req = GPARequirement.objects.get(program=program)
        self.assertEqual(gpa_req.minimum_overall, "3.0")
        self.assertEqual(gpa_req.minimum_prereq, "3.2")
        
        # Verify statistics were updated
        stats = self.transformer.get_transformation_stats()
        self.assertEqual(stats['programs_processed'], 1)
        self.assertEqual(stats['programs_created'], 1)
        self.assertEqual(stats['programs_updated'], 0)
        self.assertEqual(stats['programs_skipped'], 0)
    
    def test_transform_program_data_update_existing(self):
        """Test updating existing program"""
        # Create existing program first
        school = School.objects.create(
            name='Test University PA Program',
            location='Test City, CA',
            mission_statement='Old mission',
            website='https://old.edu'
        )
        
        existing_program = Program.objects.create(
            school=school,
            name='Test University PA Program (Main Campus)',
            description='Old description',
            url='https://example.com/pa-program',
            class_size=40
        )
        
        # Transform with updated data
        program = self.transformer.transform_program_data(self.sample_json_data)
        
        # Verify program was updated, not created
        self.assertEqual(program.id, existing_program.id)
        self.assertEqual(Program.objects.count(), 1)
        
        # Verify data was updated
        program.refresh_from_db()
        self.assertEqual(program.class_size, 50)  # Updated from 40 to 50
        
        # Verify statistics
        stats = self.transformer.get_transformation_stats()
        self.assertEqual(stats['programs_updated'], 1)
        self.assertEqual(stats['programs_created'], 0)
    
    def test_transform_batch(self):
        """Test batch transformation"""
        # Create multiple program data entries
        json_data_list = [
            self.sample_json_data,
            {
                **self.sample_json_data,
                'program_name': 'Another University PA Program',
                'program_url': 'https://another.edu/pa-program',
                'estimated_incoming_class_size': '75'
            }
        ]
        
        summary = self.transformer.transform_batch(json_data_list)
        
        # Verify summary
        self.assertEqual(summary['total_programs'], 2)
        self.assertEqual(summary['successful_transformations'], 2)
        self.assertEqual(summary['programs_created'], 2)
        self.assertEqual(summary['programs_updated'], 0)
        self.assertEqual(summary['programs_skipped'], 0)
        self.assertEqual(summary['success_rate'], 100.0)
        
        # Verify programs were created
        self.assertEqual(Program.objects.count(), 2)
        self.assertEqual(School.objects.count(), 2)
    
    def test_transform_program_data_error_handling(self):
        """Test error handling during transformation"""
        # Create invalid JSON data (missing required fields)
        invalid_json_data = {
            'program_name': None,  # Invalid name
            'estimated_incoming_class_size': 'invalid_number'
        }
        
        program = self.transformer.transform_program_data(invalid_json_data)
        
        # Should return None due to error
        self.assertIsNone(program)
        
        # Verify statistics
        stats = self.transformer.get_transformation_stats()
        self.assertEqual(stats['programs_skipped'], 1)
        self.assertTrue(len(stats['errors']) > 0)


class DuplicateDetectorTest(TestCase):
    """Test cases for DuplicateDetector"""
    
    def setUp(self):
        """Set up test data"""
        self.detector = DuplicateDetector()
        
        # Create test school
        self.school = School.objects.create(
            name='Test University',
            location='Test City, CA',
            mission_statement='Test mission',
            website='https://test.edu'
        )
    
    def test_calculate_similarity(self):
        """Test string similarity calculation"""
        # Identical strings
        self.assertEqual(self.detector._calculate_similarity("test program", "test program"), 1.0)
        
        # Completely different strings
        self.assertEqual(self.detector._calculate_similarity("test program", "different words"), 0.0)
        
        # Partially similar strings
        similarity = self.detector._calculate_similarity("test university program", "test program university")
        self.assertGreater(similarity, 0.5)
        
        # Empty strings
        self.assertEqual(self.detector._calculate_similarity("", "test"), 0.0)
        self.assertEqual(self.detector._calculate_similarity("", ""), 0.0)
    
    def test_are_similar_programs(self):
        """Test program similarity detection"""
        # Create programs
        program1 = Program.objects.create(
            school=self.school,
            name='Test PA Program',
            url='https://example.com/program',
            class_size=50
        )
        
        program2 = Program.objects.create(
            school=self.school,
            name='Test PA Program',  # Same name
            url='https://different.com/program',
            class_size=60
        )
        
        program3 = Program.objects.create(
            school=self.school,
            name='Completely Different Program',
            url='https://example.com/program',  # Same URL
            class_size=40
        )
        
        program4 = Program.objects.create(
            school=self.school,
            name='Unrelated Program',
            url='https://unrelated.com/program',
            class_size=30
        )
        
        # Test similar programs
        self.assertTrue(self.detector._are_similar_programs(program1, program2))  # Same name
        self.assertTrue(self.detector._are_similar_programs(program1, program3))  # Same URL
        self.assertFalse(self.detector._are_similar_programs(program1, program4))  # Different
    
    def test_find_potential_duplicates(self):
        """Test finding potential duplicates"""
        # Create similar programs
        program1 = Program.objects.create(
            school=self.school,
            name='Test PA Program',
            url='https://example.com/program1',
            class_size=50
        )
        
        program2 = Program.objects.create(
            school=self.school,
            name='Test PA Program',  # Duplicate name
            url='https://example.com/program2',
            class_size=60
        )
        
        program3 = Program.objects.create(
            school=self.school,
            name='Different Program',
            url='https://example.com/program1',  # Duplicate URL
            class_size=40
        )
        
        program4 = Program.objects.create(
            school=self.school,
            name='Unique Program',
            url='https://unique.com/program',
            class_size=30
        )
        
        duplicates = self.detector.find_potential_duplicates()
        
        # Should find duplicate groups
        self.assertGreater(len(duplicates), 0)
        
        # Verify duplicate detection
        found_programs = set()
        for duplicate_group in duplicates:
            found_programs.add(duplicate_group['primary_program'].id)
            for similar_program in duplicate_group['similar_programs']:
                found_programs.add(similar_program.id)
        
        # Should include programs with duplicates
        self.assertIn(program1.id, found_programs)
        self.assertIn(program2.id, found_programs)
        self.assertIn(program3.id, found_programs)
        
        # Should not include unique program
        # (Note: program4 might still be included if it's similar to others)
    
    def test_get_similarity_reasons(self):
        """Test getting similarity reasons"""
        program1 = Program.objects.create(
            school=self.school,
            name='Test PA Program',
            url='https://example.com/program',
            class_size=50
        )
        
        program2 = Program.objects.create(
            school=self.school,
            name='Test PA Program',  # Same name and school
            url='https://different.com/program',
            class_size=60
        )
        
        program3 = Program.objects.create(
            school=self.school,
            name='Different Program',
            url='https://example.com/program',  # Same URL
            class_size=40
        )
        
        reasons = self.detector._get_similarity_reasons(program1, [program2, program3])
        
        # Should include reasons for similarity
        self.assertGreater(len(reasons), 0)
        
        # Check for expected reason types
        reason_text = ' '.join(reasons)
        self.assertIn('Same school', reason_text)
        self.assertIn('Same URL', reason_text)