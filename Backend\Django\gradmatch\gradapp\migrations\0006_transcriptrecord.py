# Generated by Django 5.0.1 on 2025-01-15 23:12

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('gradapp', '0005_admissionrequirement_classprofile_curriculum_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='TranscriptRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('institution', models.CharField(max_length=255)),
                ('student_name', models.CharField(max_length=255)),
                ('calculated_gpa', models.DecimalField(decimal_places=2, max_digits=4)),
                ('validation_status', models.Char<PERSON>ield(default='pending', max_length=20)),
                ('transcript_data', models.J<PERSON><PERSON>ield()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-updated_at'],
            },
        ),
    ]
