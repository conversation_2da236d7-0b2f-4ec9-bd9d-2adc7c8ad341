# Design Document

## Overview

This design outlines the migration from the current PA program database to an enhanced schema that accommodates the comprehensive JSON data structure from the "D:\ScrapperPAEAReal" directory. The new data format contains significantly more detailed information including nested program requirements, class profiles, PANCE pass rates, attrition data, and curriculum details.

## Architecture

### Migration Strategy
- **Backup-First Approach**: Create complete backup of existing database before any modifications
- **Schema Evolution**: Extend existing models rather than complete replacement to maintain compatibility
- **Data Transformation**: Parse JSON files and map to enhanced database models
- **Validation Pipeline**: Implement robust validation for each program's data before insertion

### Processing Flow
1. **File Discovery**: Scan "D:\ScrapperPAEAReal" directory for JSON files
2. **Data Parsing**: Extract and validate JSON structure for each program
3. **Schema Mapping**: Transform JSON data to match enhanced database models
4. **Batch Processing**: Process programs in batches with error handling
5. **Verification**: Validate migration success and data integrity

## Components and Interfaces

### Core Components

#### 1. Migration Manager
- **Purpose**: Orchestrate the entire migration process
- **Responsibilities**: 
  - Coordinate backup, schema updates, and data import
  - Handle error recovery and rollback scenarios
  - Provide progress tracking and logging

#### 2. JSON Data Parser
- **Purpose**: Parse and validate individual program JSON files
- **Responsibilities**:
  - Read JSON files from local directory
  - Validate data structure and required fields
  - Transform nested JSON to flat database records

#### 3. Schema Updater
- **Purpose**: Apply database schema changes
- **Responsibilities**:
  - Create Django migrations for new fields
  - Update existing models with enhanced data structure
  - Maintain backward compatibility where possible

#### 4. Data Importer
- **Purpose**: Import parsed data into database
- **Responsibilities**:
  - Map JSON data to database models
  - Handle duplicate detection and resolution
  - Batch insert operations for performance

## Data Models

### Enhanced Schema Changes

Based on the JSON structure, the following model enhancements are needed:

#### Program Model Extensions
```python
# Additional fields to accommodate comprehensive JSON data
city_state = models.CharField(max_length=100, null=True, blank=True)
caspa_member = models.BooleanField(default=True)
upcoming_caspa_cycle = models.BooleanField(default=True)
caspa_deadline = models.DateField(null=True, blank=True)
caspa_deadline_requirement = models.TextField(null=True, blank=True)
```

#### New Models for Comprehensive Data

**CASPARequirement Model**
- Stores CASPA application deadlines and requirements
- Links to Program with ForeignKey relationship

**TuitionInformation Model**  
- Stores detailed tuition and deposit information
- Replaces basic tuition fields in ApplicationRequirement

**ProgramCurriculum Model**
- Stores detailed curriculum structure
- Supports both didactic and clinical phase information

### Data Mapping Strategy

#### JSON to Database Mapping
- **general_information** → Program model fields
- **caspa_deadlines** → CASPARequirement model
- **tuition_deposits** → TuitionInformation model
- **requirements** → Multiple requirement models (GPA, GRE, Healthcare, etc.)
- **prerequisites** → PrerequisiteCourse model
- **pance_pass_rates** → PANCEPassRate model (already exists)
- **attrition** → AttritionData model (already exists)
- **matriculants** → MatriculantDemographics model (already exists)

## Error Handling

### Validation Strategy
- **File-Level Validation**: Ensure JSON files are readable and well-formed
- **Data-Level Validation**: Validate required fields and data types
- **Business Logic Validation**: Ensure data makes sense (e.g., dates, numeric ranges)

### Error Recovery
- **Skip Invalid Programs**: Log errors but continue processing other programs
- **Partial Data Handling**: Import valid portions of partially corrupt data
- **Rollback Capability**: Ability to restore from backup if migration fails

### Logging and Monitoring
- **Detailed Logging**: Track each program's processing status
- **Error Reporting**: Comprehensive error messages with context
- **Progress Tracking**: Real-time status of migration progress

## Testing Strategy

### Unit Testing
- **JSON Parser Tests**: Validate parsing of various JSON structures
- **Data Transformation Tests**: Ensure correct mapping from JSON to models
- **Validation Logic Tests**: Test error detection and handling

### Integration Testing
- **End-to-End Migration**: Test complete migration process with sample data
- **Database Integrity**: Verify foreign key relationships and constraints
- **Matching System Compatibility**: Ensure existing matching logic still works

### Data Validation Testing
- **Sample Data Verification**: Manually verify a subset of migrated programs
- **Comparison Testing**: Compare old vs new data for overlapping fields
- **Performance Testing**: Ensure migration completes within reasonable time

## Implementation Approach

### Phase 1: Schema Preparation
1. Create Django migrations for new/modified models
2. Apply schema changes to development database
3. Test schema changes with sample data

### Phase 2: Data Processing Pipeline
1. Implement JSON file reader and parser
2. Create data transformation logic
3. Build validation and error handling

### Phase 3: Migration Execution
1. Create database backup
2. Execute migration with progress monitoring
3. Validate results and update matching system

### Phase 4: Cleanup and Optimization
1. Remove old unused data if needed
2. Optimize database indexes for new schema
3. Update API endpoints to leverage new data

## Performance Considerations

### Batch Processing
- Process programs in batches of 50-100 to manage memory usage
- Use database transactions for atomic operations
- Implement progress checkpoints for resumable migration

### Database Optimization
- Create appropriate indexes for new fields
- Use bulk_create for efficient batch inserts
- Optimize foreign key relationships

### Memory Management
- Stream JSON file processing to avoid loading all data in memory
- Clear processed data from memory after each batch
- Monitor memory usage during migration