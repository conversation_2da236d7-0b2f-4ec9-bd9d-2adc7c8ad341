# Generated by Django 5.0.1 on 2025-01-24 22:15

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('gradapp', '0011_convert_to_textfield'),
    ]

    operations = [
        migrations.AlterField(
            model_name='applicationrequirement',
            name='admission_type',
            field=models.TextField(),
        ),
        migrations.AlterField(
            model_name='applicationrequirement',
            name='supplemental_fee',
            field=models.TextField(),
        ),
        migrations.AlterField(
            model_name='curriculum',
            name='year',
            field=models.TextField(),
        ),
        migrations.AlterField(
            model_name='gparequirement',
            name='minimum_overall',
            field=models.TextField(),
        ),
        migrations.AlterField(
            model_name='gparequirement',
            name='minimum_prereq',
            field=models.TextField(),
        ),
        migrations.AlterField(
            model_name='gparequirement',
            name='minimum_science',
            field=models.TextField(),
        ),
        migrations.AlterField(
            model_name='grerequirement',
            name='analytical_percentile',
            field=models.TextField(),
        ),
        migrations.AlterField(
            model_name='grerequirement',
            name='analytical_score',
            field=models.TextField(),
        ),
        migrations.AlterField(
            model_name='grerequirement',
            name='quantitative_percentile',
            field=models.TextField(),
        ),
        migrations.AlterField(
            model_name='grerequirement',
            name='quantitative_score',
            field=models.TextField(),
        ),
        migrations.AlterField(
            model_name='grerequirement',
            name='required',
            field=models.TextField(),
        ),
        migrations.AlterField(
            model_name='grerequirement',
            name='verbal_percentile',
            field=models.TextField(),
        ),
        migrations.AlterField(
            model_name='grerequirement',
            name='verbal_score',
            field=models.TextField(),
        ),
        migrations.AlterField(
            model_name='healthcareexperience',
            name='status',
            field=models.TextField(),
        ),
        migrations.AlterField(
            model_name='healthcareexperience',
            name='time_limit',
            field=models.TextField(),
        ),
        migrations.AlterField(
            model_name='otherrequirement',
            name='veteran_support',
            field=models.TextField(),
        ),
        migrations.AlterField(
            model_name='patientcareexperience',
            name='paid_status',
            field=models.TextField(),
        ),
        migrations.AlterField(
            model_name='patientcareexperience',
            name='time_limit',
            field=models.TextField(),
        ),
        migrations.AlterField(
            model_name='patientcareexperience',
            name='volunteer_status',
            field=models.TextField(),
        ),
        migrations.AlterField(
            model_name='recommendationrequirement',
            name='number_required',
            field=models.TextField(),
        ),
        migrations.AlterField(
            model_name='shadowingrequirement',
            name='other_shadowing',
            field=models.TextField(),
        ),
        migrations.AlterField(
            model_name='shadowingrequirement',
            name='pa_shadowing',
            field=models.TextField(),
        ),
        migrations.AlterField(
            model_name='shadowingrequirement',
            name='physician_shadowing',
            field=models.TextField(),
        ),
    ]
