/* Color palette */
:root {
   --primary-color: #4a4a4a;
   --secondary-color: #ffffff;
   --accent-color: #ff5a5f;
   --text-color: #333333;
   --background-color: #ffffff;
   --success-color: #4CAF50;
   --error-color: #f44336;
   --border-color: #ddd;
}

/* Global styles */
body {
   font-family: 'Noto Sans KR', sans-serif;
   background-color: var(--background-color);
   color: var(--text-color);
   line-height: 1.6;
}

/* App container */
.App {
   max-width: 1200px;
   margin: 0 auto;
   padding: 20px;
}

/* Header */
.App-header {
   text-align: center;
   margin-bottom: 40px;
}

.App-header h1 {
   font-size: 2.5rem;
   font-weight: 700;
   color: var(--primary-color);
}

/* Main content layout */
main {
   display: grid;
   grid-template-columns: repeat(2, 1fr);
   grid-template-rows: auto auto auto 1fr; /* Added an extra row for GPA */
   gap: 30px;
   position: relative;
}

main::before {
   content: "";
   position: absolute;
   top: 0;
   left: 0;
   right: 0;
   bottom: 0;
   background-image: url("C:\\Users\\<USER>\\MasterGradMatch\\NatureJapan.png");
   background-size: cover;
   background-position: center;
   background-repeat: no-repeat;
   opacity: 0.8;
   z-index: -1;
}

/* Program search form */
.program-search-form {
   display: flex;
   justify-content: center;
   margin-bottom: 30px;
}

.program-search-form button {
   padding: 10px 20px;
   font-size: 1rem;
   background-color: var(--accent-color);
   color: #ffffff;
   border: none;
   border-radius: 4px;
   cursor: pointer;
   transition: background-color 0.3s ease;
}

.program-search-form button:hover {
   background-color: #ff7b7f;
}

/* Schools list */
.schools-list {
   background-color: var(--secondary-color);
   padding: 20px;
   border-radius: 8px;
   box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
   grid-column: 1 / -1;
   margin: 20px 0;
   border: 1px solid var(--border-color);
}

.school {
   margin-bottom: 20px;
   padding: 15px;
   background-color: #f5f5f5;
   border-radius: 4px;
   box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
   border: 1px solid var(--border-color);
}

.school p {
   margin: 0;
   font-size: 1.1rem;
   color: var(--primary-color);
}

/* User profile */
.user-profile {
   background-color: var(--secondary-color);
   padding: 20px;
   border-radius: 8px;
   box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
   grid-column: 1 / -1;
   margin: 20px 0;
   border: 1px solid var(--border-color);
}

/* GPA Sections */
.gpa-section {
   margin: 0;
   padding: 0;
   background: none;
   border: none;
   box-shadow: none;
   text-align: left;
}

.overall-gpa {
   text-align: left;
   padding: 0;
   background: none;
   border: none;
   box-shadow: none;
   margin: 0;
}

.overall-gpa h3 {
   font-size: inherit;
   margin: 8px 0;
   font-weight: normal;
}

.overall-gpa .gpa-value {
   font-size: inherit;
   font-weight: normal;
   color: inherit;
   margin: 8px 0;
}

/* Course Details */
.course-details {
   margin-top: 30px;
}

.course-details h3 {
   margin-bottom: 20px;
   color: var(--primary-color);
}

/* Chat section */
.chat-section {
   grid-column: 1 / -1;
   margin: 20px 0;
}

.input-container {
   display: flex;
   gap: 10px;
   margin-bottom: 10px;
}

.input-box {
   flex-grow: 1;
   padding: 10px;
   border: 1px solid var(--border-color);
   border-radius: 4px;
}

.output-box {
   margin: 20px;
   padding: 20px;
   border: 1px solid #ccc;
   border-radius: 5px;
   background-color: #f9f9f9;
   max-height: 500px;
   overflow-y: auto;
   font-family: monospace;
   white-space: pre-wrap;
   text-align: left;
   width: 100%;
   box-sizing: border-box;
   box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
   grid-column: 1 / -1; /* Ensure it spans full width */
}

/* Process button */
.process-button {
   padding: 10px 20px;
   font-size: 16px;
   background-color: var(--success-color);
   color: white;
   border: none;
   border-radius: 5px;
   cursor: pointer;
   margin: 20px;
}

.process-button:disabled {
   background-color: #cccccc;
   cursor: not-allowed;
}

.process-button:hover:not(:disabled) {
   background-color: #45a049;
}

/* Transcript Analysis Section */
.transcript-analysis-section {
   margin: 20px;
   padding: 20px;
   border: 1px solid #ccc;
   border-radius: 5px;
   grid-column: 1 / -1;
}

.transcript-content {
   max-height: 500px;
   overflow-y: auto;
   white-space: pre-wrap;
   text-align: left;
   background: #f5f5f5;
   padding: 15px;
   border-radius: 5px;
}

/* Transcript Validation - Base Styles */
.transcript-validation {
   max-width: 100%;
   width: 95%;
   margin: 0 auto;
   padding: 20px;
   background-color: var(--secondary-color);
   border-radius: 8px;
   box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
   grid-column: 1 / -1;
}

/* Transcript Table - Common Styles */
.transcript-table {
   width: 100%;
   table-layout: fixed;
   margin: 15px 0;
   background-color: #ffffff;
}

.transcript-table th,
.transcript-table td {
   padding: 12px;
   text-align: left;
   border: 1px solid var(--border-color);
}

.transcript-table th {
   background-color: #f5f5f5;
   font-weight: bold;
   color: var(--text-color);
}

/* Editable State Specific Styles */
.transcript-validation:not(.validated) .transcript-table input,
.transcript-validation:not(.validated) .transcript-table select {
   width: 100%;
   padding: 8px;
   border: 1px solid var(--border-color);
   border-radius: 4px;
   font-size: 14px;
}

.transcript-validation:not(.validated) .validation-actions {
   margin-top: 30px;
   padding: 20px;
   border-top: 1px solid var(--border-color);
   background-color: #f8f9fa;
}

/* Read-only State Specific Styles */
.transcript-validation.validated .transcript-table td {
   background-color: #ffffff;
   padding: 12px;
}

.transcript-validation.validated .semester-section {
   border: 1px solid #e0e0e0;
   margin-bottom: 20px;
}

.semester-section {
   margin: 30px 0;
   padding: 20px;
   background-color: #fff;
   border-radius: 8px;
   box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
   width: 100%;
}

.semester-summary {
   margin-top: 15px;
   padding-top: 15px;
   border-top: 1px solid var(--border-color);
   color: #666;
}

.academic-summary {
   background-color: #f8f9fa;
   padding: 20px;
   border-radius: 8px;
   margin-bottom: 30px;
   box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.validation-actions {
   margin-top: 30px;
   padding: 20px;
   text-align: center;
}

.approve-btn {
   background-color: var(--success-color);
   color: white;
   padding: 12px 24px;
   border: none;
   border-radius: 4px;
   cursor: pointer;
   font-weight: 500;
   font-size: 16px;
   transition: all 0.3s ease;
}

.approve-btn:hover {
   background-color: #45a049;
}

.approve-btn:disabled {
   background-color: #cccccc;
   cursor: not-allowed;
   opacity: 0.7;
}

/* Error Messages */
.error-message {
   color: var(--error-color);
   padding: 10px;
   margin: 10px 0;
   border: 1px solid var(--error-color);
   border-radius: 4px;
   background-color: #ffebee;
}

/* Media Queries */
@media screen and (max-width: 768px) {
   .App {
       padding: 10px;
   }

   .App-header h1 {
       font-size: 2rem;
   }

   main {
       grid-template-columns: 1fr;
   }

   .output-box {
       grid-column: 1;
   }

   .transcript-validation {
       padding: 15px;
   }

   .transcript-table {
       font-size: 14px;
   }

   .transcript-table th,
   .transcript-table td {
       padding: 8px;
   }

   .validation-actions button {
       width: 100%;
       margin-bottom: 10px;
   }
}

.dashboard-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.dashboard-nav {
  background-color: #2c3e50;
  color: white;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dashboard-nav h1 {
  margin: 0;
  font-size: 1.5rem;
}

.dashboard-main {
  flex: 1;
  padding: 2rem;
  background-color: #f5f7fa;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .dashboard-main {
    padding: 1rem;
  }
}

.logout-button {
  padding: 0.5rem 1rem;
  background-color: transparent;
  color: white;
  border: 1px solid white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.logout-button:hover {
  background-color: white;
  color: #2c3e50;
}

.logout-button:active {
  transform: translateY(1px);
}

.dashboard {
    min-height: 100vh;
    background-color: #f5f7fa;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background-color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dashboard-header h1 {
    margin: 0;
    color: #2c3e50;
}

.dashboard-header button {
    padding: 0.5rem 1rem;
    background-color: #e74c3c;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.dashboard-header button:hover {
    background-color: #c0392b;
}

.dashboard-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.profile-section,
.transcript-section {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 2rem;
}

.profile-section h2,
.transcript-section h2 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-size: 1.8rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
}

.form-section {
    background-color: #ebf5fb;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid #bde0f3;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.form-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(52, 152, 219, 0.1);
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.form-section h3 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-size: 1.4rem;
}

.process-transcript-btn {
    padding: 1rem 2rem;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.2s;
    width: 100%;
    max-width: 300px;
    margin: 0 auto;
    display: block;
}

.process-transcript-btn:hover {
    background-color: #2980b9;
}

.process-transcript-btn:disabled {
    background-color: #bdc3c7;
    cursor: not-allowed;
}

.transcript-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.transcript-table th,
.transcript-table td {
    padding: 0.75rem;
    border: 1px solid #e2e8f0;
    text-align: left;
}

.transcript-table th {
    background-color: #f8fafc;
    font-weight: 600;
}

.semester-section {
    margin-bottom: 2rem;
}

.semester-summary {
    margin-top: 1rem;
    padding: 1rem;
    background-color: #f8fafc;
    border-radius: 4px;
}

.error-message {
    color: #e74c3c;
    padding: 1rem;
    background-color: #fdf2f0;
    border-radius: 4px;
    margin-bottom: 1rem;
}

/* Program Matching Button */
.matching-action {
    margin-top: 2rem;
    text-align: center;
}

.match-programs-btn {
    padding: 1rem 2rem;
    font-size: 1.2rem;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.match-programs-btn:hover {
    background-color: #45a049;
}

/* Program Matching Page */
.program-matching {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 2rem;
}

.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1rem 2rem;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content {
    flex: 1;
}

.chat-header h1 {
    margin-bottom: 0.5rem;
}

.back-to-profile-btn {
    padding: 0.75rem 1.5rem;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.2s;
    margin-left: 2rem;
}

.back-to-profile-btn:hover {
    background-color: #2980b9;
}

.chat-header p {
    color: #7f8c8d;
    font-size: 1.1rem;
}

.chat-container {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 1rem;
    height: 70vh;
    display: flex;
    flex-direction: column;
}

.chat-messages {
    flex-grow: 1;
    overflow-y: auto;
    padding: 1rem;
    margin-bottom: 1rem;
}

.message {
    margin-bottom: 1rem;
    max-width: 80%;
}

.message.user {
    margin-left: auto;
}

.message.assistant {
    margin-right: auto;
}

.message-content {
    max-width: 100%;
    overflow-x: auto;
    white-space: pre-wrap;
    font-family: inherit;
    line-height: 1.6;
    padding: 1.5rem;
}

.message.user .message-content {
    background-color: #007bff;
    color: white;
    border-radius: 8px;
    text-align: right;
}

.message.assistant .message-content {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    width: 100%;
}

.message.error .message-content {
    background-color: #dc3545;
    color: white;
    border-radius: 8px;
}

.chat-input-form {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    border-top: 1px solid #dee2e6;
}

.chat-input-form input {
    flex-grow: 1;
    padding: 0.75rem;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 1rem;
}

.chat-input-form button {
    padding: 0.75rem 1.5rem;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.chat-input-form button:hover {
    background-color: #0056b3;
}

.chat-input-form button:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
}

/* Transcript Warning */
.transcript-warning {
    background-color: #fff3cd;
    border: 1px solid #ffeeba;
    color: #856404;
    padding: 1rem;
    margin: 1rem;
    border-radius: 4px;
    text-align: center;
}

.transcript-warning p {
    margin-bottom: 1rem;
}

.validate-transcript-btn {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.validate-transcript-btn:hover {
    background-color: #0056b3;
}

/* Chat Message Improvements */
.markdown-content {
    white-space: pre-wrap;
    line-height: 1.6;
}

.markdown-content p {
    margin-bottom: 0.8rem;
}

.markdown-content h2, 
.markdown-content h3, 
.markdown-content h4 {
    margin-top: 1.5rem;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.markdown-content table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
}

.markdown-content th,
.markdown-content td {
    border: 1px solid #dee2e6;
    padding: 0.75rem;
    text-align: left;
}

.markdown-content th {
    background-color: #f8f9fa;
}

/* Program Information Display */
.sources-container {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    margin-top: 2rem;
}

.sources-container h3 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

.programs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.program-source {
    background-color: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.program-source h4 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.25rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
}

.info-section {
    display: grid;
    gap: 1.5rem;
}

.location-info,
.accreditation-info,
.class-profile,
.admissions {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 1rem;
}

.location-info h5,
.accreditation-info h5,
.class-profile h5,
.admissions h5 {
    color: #2c3e50;
    margin-bottom: 0.75rem;
    font-size: 1rem;
}

.info-grid {
    display: grid;
    gap: 0.75rem;
}

.info-item {
    display: flex;
    gap: 0.5rem;
    align-items: baseline;
}

.info-item strong {
    color: #2c3e50;
    text-transform: capitalize;
    min-width: 120px;
}

/* Message Content Improvements */
.message-content {
    max-width: 100%;
    overflow-x: auto;
}

.message.assistant .message-content {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
}

.message.user .message-content {
    background-color: #007bff;
    color: white;
    border-radius: 8px;
    padding: 1rem 1.5rem;
}

/* Chat Container Improvements */
.chat-container {
    max-width: 1200px;
    margin: 0 auto;
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 2rem;
}

.chat-messages {
    max-height: 600px;
    overflow-y: auto;
    padding: 1rem;
    margin-bottom: 2rem;
    border: 1px solid #e9ecef;
    border-radius: 8px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .programs-grid {
        grid-template-columns: 1fr;
    }
    
    .chat-container {
        padding: 1rem;
    }
    
    .info-item {
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .info-item strong {
        min-width: auto;
    }
}

.message-content {
    max-width: 100%;
    overflow-x: auto;
    white-space: pre-wrap;
    font-family: inherit;
    line-height: 1.6;
    padding: 1.5rem;
}

.message.assistant .message-content {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    width: 100%;
}

.message.user .message-content {
    background-color: #007bff;
    color: white;
    border-radius: 8px;
    text-align: right;
}

.message.error .message-content {
    background-color: #dc3545;
    color: white;
    border-radius: 8px;
}

.chat-messages {
    flex-grow: 1;
    overflow-y: auto;
    padding: 1rem;
    margin-bottom: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.message {
    max-width: 100%;
    display: flex;
}

.message.user {
    justify-content: flex-end;
}

.message.assistant {
    justify-content: flex-start;
}

/* Table Styles */
.message-content table {
    border-collapse: collapse;
    width: 100%;
    margin: 1rem 0;
}

.message-content th,
.message-content td {
    border: 1px solid #dee2e6;
    padding: 0.75rem;
    text-align: left;
}

.message-content th {
    background-color: #f8f9fa;
    font-weight: bold;
}

/* Headers */
.message-content h1,
.message-content h2,
.message-content h3,
.message-content h4,
.message-content h5,
.message-content h6 {
    margin-top: 1.5rem;
    margin-bottom: 1rem;
    font-weight: bold;
}

/* Lists */
.message-content ul,
.message-content ol {
    margin: 1rem 0;
    padding-left: 2rem;
}

.message-content li {
    margin-bottom: 0.5rem;
}

/* Horizontal Rule */
.message-content hr {
    margin: 2rem 0;
    border: 0;
    border-top: 1px solid #dee2e6;
}

/* Bold and Italic */
.message-content strong {
    font-weight: bold;
}

.message-content em {
    font-style: italic;
}

/* Welcome Section */
.welcome-section {
    text-align: center;
    margin-bottom: 3rem;
    padding: 2rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.welcome-section h2 {
    color: #2c3e50;
    font-size: 2rem;
    margin-bottom: 1rem;
}

.welcome-section p {
    color: #6c757d;
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
}

/* Transcript Section Enhancements */
.transcript-section {
    margin-top: 2rem;
    background-color: white;
    padding: 3rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.transcript-upload-container {
    max-width: 600px;
    margin: 0 auto;
    padding: 2rem;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 300px;
}

.transcript-upload-container h3 {
    color: #2c3e50;
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
    text-align: center;
}

.transcript-upload-container p {
    color: #6c757d;
    margin-bottom: 2.5rem;
    text-align: center;
    max-width: 400px;
    line-height: 1.6;
}

.process-transcript-btn {
    padding: 1.2rem 2.5rem;
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    max-width: 300px;
    margin: 1rem auto;
    display: block;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-align: center;
}

.process-transcript-btn:hover {
    background: linear-gradient(135deg, #2980b9 0%, #2471a3 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(41, 128, 185, 0.3);
}

.process-transcript-btn:disabled {
    background: linear-gradient(135deg, #bdc3c7 0%, #95a5a6 100%);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.processing-indicator {
    color: #3498db;
    font-size: 1.1rem;
    margin: 2rem 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.processing-indicator::after {
    content: '';
    width: 20px;
    height: 20px;
    border: 3px solid #3498db;
    border-top: 3px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* PAMatch Styles */
.pamatch-container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 2rem;
}

.pamatch-header {
    text-align: center;
    margin-bottom: 2rem;
}

.pamatch-header h1 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.pamatch-header p {
    color: #7f8c8d;
    font-size: 1.1rem;
}

.results-container {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    min-height: 400px;
}

.loading-message {
    text-align: center;
    color: #7f8c8d;
    font-size: 1.2rem;
    padding: 2rem;
}

.navigation-buttons {
    margin-top: 2rem;
    text-align: center;
}

.back-button {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    background-color: #34495e;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.back-button:hover {
    background-color: #2c3e50;
}

/* Program Card Styles */
.program-card {
    background-color: white;
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.program-card h3 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #3498db;
}

.program-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.info-section,
.requirements-section,
.class-profile-section,
.dates-section {
    background-color: #f8f9fa;
    padding: 1.5rem;
    border-radius: 6px;
}

.info-section h4,
.requirements-section h4,
.class-profile-section h4,
.dates-section h4 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.requirement-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
}

.requirement-status {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: 500;
}

.requirement-status.met {
    background-color: #d4edda;
    color: #155724;
}

.requirement-status.not-met {
    background-color: #f8d7da;
    color: #721c24;
}

.prerequisites-list {
    margin-top: 1rem;
}

.prerequisites-list .requirement-item:last-child {
    border-bottom: none;
}

/* Loading and Error States */
.loading-message {
    text-align: center;
    padding: 2rem;
    font-size: 1.2rem;
    color: #6c757d;
}

.error-message {
    background-color: #f8d7da;
    color: #721c24;
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
}

/* Navigation */
.navigation-buttons {
    margin-top: 2rem;
    text-align: center;
}

.back-button {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    background-color: #34495e;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.back-button:hover {
    background-color: #2c3e50;
}

/* Programs Comparison Styles */
.programs-comparison {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.program-column {
    background-color: white;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.program-column h2 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #3498db;
    font-size: 1.5rem;
}

.requirements-section h3 {
    color: #2c3e50;
    margin: 1.5rem 0 1rem;
    font-size: 1.2rem;
}

.requirement-group {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 6px;
    margin-bottom: 1.5rem;
}

.requirement-item {
    display: grid;
    grid-template-columns: 2fr 1fr;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e9ecef;
}

.requirement-item:last-child {
    border-bottom: none;
}

.requirement-item span {
    color: #2c3e50;
}

.requirement-value {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: auto;
}

.requirement-status {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    font-weight: bold;
    margin-left: 0.5rem;
}

.requirement-value.met .requirement-status {
    background-color: #d4edda;
    color: #155724;
}

.requirement-value.not-met .requirement-status {
    background-color: #f8d7da;
    color: #721c24;
}

.timeframe {
    grid-column: 1 / -1;
    color: #6c757d;
    font-size: 0.9rem;
    margin-top: 0.25rem;
}

@media (max-width: 768px) {
    .programs-comparison {
        grid-template-columns: 1fr;
    }
}

.program-section {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: 6px;
}

.program-section h2 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #3498db;
    text-align: center;
}

.requirements-section {
    background-color: #f8f9fa;
    padding: 1.5rem;
    border-radius: 6px;
    margin-bottom: 1.5rem;
}

.requirements-section h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.requirement-group {
    background-color: white;
    padding: 1rem;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.programs-comparison {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
}

.course-detail {
    padding: 8px 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #edf2f7;
    position: relative;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.course-detail.used-in-gpa {
    background-color: rgba(52, 152, 219, 0.05);
    border-left: 3px solid #3498db;
}

.course-info {
    flex: 1;
    padding-right: 1rem;
}

.course-name {
    color: #2c3e50;
    font-size: 0.95rem;
    display: flex;
    align-items: center;
}

.course-stats {
    min-width: 80px;
    text-align: right;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.course-grade {
    color: #4a5568;
    font-weight: 500;
}

.prerequisite-courses {
    margin-top: 8px;
    padding: 8px 16px;
    background-color: #f8fafc;
    border-left: 3px solid #e2e8f0;
    font-size: 0.9em;
}

.gpa-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.gpa-label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.gpa-details {
    background-color: #ebf5fb;
    padding: 1rem;
    border-radius: 4px;
    margin-top: 1rem;
    text-align: center;
}

.gpa-details p {
    margin: 0.5rem 0;
    font-weight: 500;
}

.dashboard-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.profile-btn {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
}

.profile-btn:hover {
    background-color: #2980b9;
}

.logout-btn {
    background-color: #e74c3c;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
}

.logout-btn:hover {
    background-color: #c0392b;
}

.program-matching-section {
    margin-top: 2rem;
    padding: 2rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.program-matching-container {
    text-align: center;
}

.program-matching-container h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.program-matching-container p {
    color: #666;
    margin-bottom: 1.5rem;
}

.program-matching-btn {
    background-color: #3498db;
    color: white;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.program-matching-btn:hover {
    background-color: #2980b9;
}

/* Profile Section Enhancements */
.profile-section {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    padding: 2rem;
    margin-bottom: 2rem;
}

.profile-section h2 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-size: 1.8rem;
    font-weight: 600;
    border-bottom: none;
    padding-bottom: 0;
    position: relative;
}

.profile-section h2::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(to right, #3498db, #2980b9);
    border-radius: 2px;
}

.form-section {
    background-color: #ebf5fb;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid #bde0f3;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.form-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(52, 152, 219, 0.1);
}

.form-section h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.2rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.form-group label {
    color: #4a5568;
    font-size: 0.9rem;
    font-weight: 500;
}

.form-group input,
.form-group select {
    padding: 0.6rem;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 0.95rem;
    transition: all 0.2s ease;
    background-color: white;
}

.form-group small {
    color: #718096;
    font-size: 0.8rem;
    margin-top: 0.2rem;
}

.form-actions {
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid #bde0f3;
    display: flex;
    justify-content: flex-end;
}

.save-profile-btn {
    padding: 0.75rem 2rem;
    background: linear-gradient(to right, #3498db, #2980b9);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.save-profile-btn:hover {
    background: linear-gradient(to right, #2980b9, #2471a3);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.2);
}

.save-profile-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(52, 152, 219, 0.2);
}

/* Error Message Enhancement */
.error-message {
    background-color: #fff5f5;
    color: #c53030;
    padding: 1rem;
    border-radius: 6px;
    border-left: 4px solid #fc8181;
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.experience-header {
    display: grid;
    grid-template-columns: 1fr auto auto;
    align-items: flex-start;
    width: 100%;
    padding: 0.25rem 0;
    gap: 1rem;
}

.experience-title {
    font-weight: 500;
    color: #2c3e50;
    text-align: left;
}

.experience-details {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
    color: #4a5568;
    line-height: 1.2;
}

.requirement-value {
    margin-left: 1rem;
}

.requirement-group {
    background-color: white;
    padding: 0.75rem;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.requirement-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
}

.requirement-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.requirement-item:first-child {
    padding-top: 0;
}

.requirement-status {
    align-self: flex-end;
    margin-top: 0.5rem;
}

.requirement-group .requirement-item {
    background-color: #ebf5fb;
    border: 1px solid #bde0f3;
    border-radius: 8px;
    margin-bottom: 1rem;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.requirement-group .requirement-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(52, 152, 219, 0.1);
}

.requirement-status {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    font-size: 1.2rem;
    background-color: white;
}

.met .requirement-status {
    color: #48bb78;
    border: 2px solid #48bb78;
}

.not-met .requirement-status {
    color: #e53e3e;
    border: 2px solid #e53e3e;
}

.existing-transcript {
    background-color: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.transcript-summary {
    margin-bottom: 2rem;
}

.transcript-summary h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.4rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
}

.academic-summary {
    background-color: #ebf5fb;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border: 1px solid #bde0f3;
}

.academic-summary p {
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.academic-summary strong {
    color: #2c3e50;
    font-weight: 600;
}

.semester-section {
    background-color: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.semester-section h4 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.transcript-actions {
    text-align: center;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e2e8f0;
}

.transcript-actions p {
    color: #4a5568;
    margin-bottom: 1rem;
}

.home-btn {
    background: #3498db;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    cursor: pointer;
    transition: background 0.2s ease;
    font-size: 1rem;
    margin-right: 1rem;
}

.home-btn:hover {
    background: #2980b9;
}

.header-left {
    display: flex;
    align-items: center;
}

.header-center {
    flex: 1;
    text-align: center;
}

.header-content {
    flex: 1;
    text-align: center;
}

/* Update existing header styles */
.dashboard-header,
.chat-header,
.pamatch-header {
    display: flex;
    align-items: center;
    padding: 1rem;
    margin-bottom: 2rem;
    background: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Science course highlighting */
.science-course {
    background-color: rgba(52, 152, 219, 0.05); /* Light blue background */
}

.science-course td {
    position: relative;
}

.science-course td::after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 3px;
    background-color: #3498db;
    opacity: 0.5;
}

/* Science Course Checkbox Styles */
.transcript-table input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
    position: relative;
    margin: 0 auto;
    display: block;
}

.transcript-table input[type="checkbox"]:disabled {
    cursor: not-allowed;
    opacity: 0.7;
}

/* Update Science Course Row Styles */
.science-course {
    background-color: rgba(52, 152, 219, 0.05);
    transition: background-color 0.3s ease;
}

.science-course:hover {
    background-color: rgba(52, 152, 219, 0.1);
}

.science-course td {
    position: relative;
}

.science-course td:first-child::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 3px;
    background-color: #3498db;
    opacity: 0.7;
}

/* Position styling for checkbox column */
.transcript-table td:last-child {
    position: relative;
}

.program-title-link {
    color: #3498db;
    text-decoration: none;
    transition: color 0.2s ease;
}

.program-title-link:hover {
    color: #2980b9;
    text-decoration: underline;
}

.eligible-programs,
.ineligible-programs {
    margin-bottom: 2rem;
    padding: 1.5rem;
    border-radius: 8px;
    width: 100%;
    height: fit-content;
    display: flex;
    flex-direction: column;
}

.eligible-programs {
    background-color: rgba(46, 204, 113, 0.05);
    border: 1px solid rgba(46, 204, 113, 0.2);
}

.eligible-programs h2 {
    color: #27ae60;
    margin-bottom: 1.5rem;
}

.ineligible-programs {
    background-color: rgba(231, 76, 60, 0.05);
    border: 1px solid rgba(231, 76, 60, 0.2);
}

.ineligible-programs h2 {
    color: #c0392b;
    margin-bottom: 1.5rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    padding: 0.75rem;
    border-radius: 6px;
    transition: background-color 0.2s ease;
    margin-bottom: 0.5rem;
}

.section-header:hover {
    background-color: rgba(0, 0, 0, 0.03);
}

.section-header h2 {
    margin: 0;
}

.expand-icon {
    font-size: 1.2rem;
    margin-left: 1rem;
    transition: transform 0.2s ease;
}

.section-content {
    padding: 0.5rem;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive layout for larger screens */
@media (min-width: 1024px) {
    .programs-comparison {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
}

.used-in-gpa-indicator {
    display: none;
}

.remove-course-btn {
    background: none;
    border: none;
    color: #e74c3c;
    cursor: pointer;
    font-size: 1.2rem;
    padding: 0.2rem 0.5rem;
    transition: color 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.remove-course-btn:hover {
    color: #c0392b;
}

.incomplete-programs {
    margin-bottom: 2rem;
    padding: 1.5rem;
    border-radius: 8px;
    width: 100%;
    height: fit-content;
    display: flex;
    flex-direction: column;
    background-color: rgba(241, 196, 15, 0.05);
    border: 1px solid rgba(241, 196, 15, 0.2);
}

.incomplete-programs h2 {
    color: #d35400;
    margin-bottom: 1.5rem;
}

/* Skeleton Loading Styles */
@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.skeleton {
    background: linear-gradient(90deg, 
        #f0f0f0 25%, 
        #e0e0e0 50%, 
        #f0f0f0 75%
    );
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 4px;
}

.program-skeleton {
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.skeleton-title {
    height: 24px;
    width: 60%;
    margin-bottom: 1.5rem;
}

.skeleton-section {
    height: 16px;
    width: 100%;
    margin-bottom: 1rem;
}

.skeleton-item {
    height: 20px;
    width: 90%;
    margin-bottom: 0.75rem;
}

.skeleton-group {
    padding: 1rem;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 6px;
    margin-bottom: 1.5rem;
}

.transcript-section .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    padding: 0.75rem;
    border-radius: 6px;
    transition: background-color 0.2s ease;
    margin-bottom: 0.5rem;
    background-color: #f8f9fa;
}

.transcript-section .section-header:hover {
    background-color: #e9ecef;
}

.transcript-section .section-header h3 {
    margin: 0;
    color: #2c3e50;
}

.transcript-section .expand-icon {
    font-size: 1.2rem;
    margin-left: 1rem;
    transition: transform 0.2s ease;
    color: #3498db;
}

.transcript-section .transcript-summary {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.transcript-schools {
    margin-top: 1rem;
}

.school-transcript {
    margin-bottom: 1rem;
    background-color: white;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.school-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    cursor: pointer;
    border-radius: 6px;
    transition: background-color 0.2s ease;
    background-color: #f8f9fa;
}

.school-header:hover {
    background-color: #e9ecef;
}

.school-header h4 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.1rem;
    font-weight: 600;
}

.school-transcript .transcript-summary {
    padding: 1rem;
    animation: fadeIn 0.3s ease-in-out;
}

.school-transcript .academic-summary {
    margin-top: 0;
    border-top: none;
}

.school-transcript .expand-icon {
    color: #3498db;
    font-size: 1.1rem;
}

.program-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    cursor: pointer;
    border-radius: 6px;
    transition: background-color 0.2s ease;
    margin-bottom: 0.5rem;
    background-color: white;
    border: 1px solid #e2e8f0;
}

.program-header:hover {
    background-color: #f8f9fa;
}

.program-header h3 {
    margin: 0;
    font-size: 1.1rem;
    color: #2c3e50;
}

.program-details {
    padding: 1rem;
    background-color: white;
    border-radius: 6px;
    margin-top: -0.5rem;
    margin-bottom: 1rem;
    border: 1px solid #e2e8f0;
    animation: fadeIn 0.3s ease-in-out;
}

.program-title-link {
    color: #3498db;
    text-decoration: none;
    transition: color 0.2s ease;
}

.program-title-link:hover {
    color: #2980b9;
    text-decoration: underline;
}