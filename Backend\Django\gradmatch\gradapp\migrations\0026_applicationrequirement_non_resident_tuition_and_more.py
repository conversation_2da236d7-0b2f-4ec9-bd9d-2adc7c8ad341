# Generated by Django 5.0.1 on 2025-07-20 21:06

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('gradapp', '0025_alter_prerequisiteselection_options_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='applicationrequirement',
            name='non_resident_tuition',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='applicationrequirement',
            name='resident_tuition',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='applicationrequirement',
            name='seat_deposit_amount',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='applicationrequirement',
            name='seat_deposit_refundable',
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='applicationrequirement',
            name='seat_deposit_required',
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='applicationrequirement',
            name='tuition_amount',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='applicationrequirement',
            name='tuition_separate_rates',
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='curriculum',
            name='clinical_rotations',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='curriculum',
            name='didactic_term_type',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='curriculum',
            name='didactic_terms',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='address',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='admission_type',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='bridge_dual_degree',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='credentials_offered',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='curriculum_focus',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='distance_learning',
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='doctorate_offered',
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='estimated_class_size',
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='masters_degree_type',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='mission_statement',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='on_campus_housing',
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='part_time_option',
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='phone',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='program_length',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='start_month',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='unique_features',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.CreateModel(
            name='AttritionData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('class_year', models.CharField(max_length=4)),
                ('max_entering_class_size', models.IntegerField(blank=True, null=True)),
                ('entering_class_size', models.IntegerField(blank=True, null=True)),
                ('graduates', models.IntegerField(blank=True, null=True)),
                ('attrition_rate', models.CharField(blank=True, max_length=10, null=True)),
                ('graduation_rate', models.CharField(blank=True, max_length=10, null=True)),
                ('program', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attrition_data', to='gradapp.program')),
            ],
            options={
                'ordering': ['-class_year'],
                'unique_together': {('program', 'class_year')},
            },
        ),
        migrations.CreateModel(
            name='EnhancedClassProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.CharField(max_length=50)),
                ('total_applications', models.CharField(blank=True, max_length=50, null=True)),
                ('total_interviewed', models.CharField(blank=True, max_length=50, null=True)),
                ('total_matriculants', models.CharField(blank=True, max_length=50, null=True)),
                ('average_overall_gpa', models.CharField(blank=True, max_length=20, null=True)),
                ('average_science_gpa', models.CharField(blank=True, max_length=20, null=True)),
                ('average_healthcare_hours', models.CharField(blank=True, max_length=50, null=True)),
                ('gre_verbal_score', models.CharField(blank=True, max_length=20, null=True)),
                ('gre_quantitative_score', models.CharField(blank=True, max_length=20, null=True)),
                ('gre_analytical_score', models.CharField(blank=True, max_length=20, null=True)),
                ('gre_verbal_percentile', models.CharField(blank=True, max_length=20, null=True)),
                ('gre_quantitative_percentile', models.CharField(blank=True, max_length=20, null=True)),
                ('gre_analytical_percentile', models.CharField(blank=True, max_length=20, null=True)),
                ('program', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='enhanced_class_profiles', to='gradapp.program')),
            ],
            options={
                'ordering': ['-year'],
                'unique_together': {('program', 'year')},
            },
        ),
        migrations.CreateModel(
            name='MatriculantDemographics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.CharField(blank=True, max_length=50, null=True)),
                ('female_count', models.CharField(blank=True, max_length=10, null=True)),
                ('male_count', models.CharField(blank=True, max_length=10, null=True)),
                ('non_binary_count', models.CharField(blank=True, max_length=10, null=True)),
                ('gender_unknown_count', models.CharField(blank=True, max_length=10, null=True)),
                ('american_indian_count', models.CharField(blank=True, max_length=10, null=True)),
                ('asian_count', models.CharField(blank=True, max_length=10, null=True)),
                ('black_count', models.CharField(blank=True, max_length=10, null=True)),
                ('hispanic_count', models.CharField(blank=True, max_length=10, null=True)),
                ('hawaiian_pacific_count', models.CharField(blank=True, max_length=10, null=True)),
                ('white_count', models.CharField(blank=True, max_length=10, null=True)),
                ('other_count', models.CharField(blank=True, max_length=10, null=True)),
                ('program', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='matriculant_demographics', to='gradapp.program')),
            ],
            options={
                'ordering': ['-year'],
                'unique_together': {('program', 'year')},
            },
        ),
        migrations.CreateModel(
            name='PANCEPassRate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.CharField(max_length=4)),
                ('group', models.CharField(max_length=50)),
                ('candidates_took_pance', models.IntegerField(blank=True, null=True)),
                ('exam_attempts', models.IntegerField(blank=True, null=True)),
                ('exams_passed', models.IntegerField(blank=True, null=True)),
                ('program_pass_rate', models.CharField(blank=True, max_length=10, null=True)),
                ('national_pass_rate', models.CharField(blank=True, max_length=10, null=True)),
                ('ultimate_pass_rate', models.CharField(blank=True, max_length=10, null=True)),
                ('program', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='pance_pass_rates', to='gradapp.program')),
            ],
            options={
                'ordering': ['-year', 'group'],
                'unique_together': {('program', 'year', 'group')},
            },
        ),
    ]
