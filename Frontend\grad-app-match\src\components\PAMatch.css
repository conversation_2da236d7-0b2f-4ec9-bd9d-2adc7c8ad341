/* PAMatch Component - Figma Mockup Design */
.pamatch-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: #f8fafc;
    min-height: 100vh;
}

/* Header Section */
.pamatch-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;
}

.header-left {
    display: flex;
    align-items: center;
}

.header-title {
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-icon {
    font-size: 24px;
}

.pamatch-header h1 {
    font-size: 24px;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
}

.back-home-btn {
    background: #3b82f6;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.back-home-btn:hover {
    background: #2563eb;
    transform: translateY(-1px);
}

/* Academic Summary Section */
.academic-summary {
    margin-bottom: 32px;
}

.summary-section-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
}

.section-icon {
    font-size: 20px;
}

.summary-section-header h2 {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
}

.gpa-cards-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.gpa-card {
    background: white;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    position: relative;
    overflow: hidden;
}

.gpa-card.cumulative-gpa {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
}

.gpa-card.science-gpa {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    color: white;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.card-label {
    font-size: 14px;
    font-weight: 500;
    opacity: 0.9;
}

.card-icon {
    font-size: 20px;
    opacity: 0.8;
}

.gpa-value {
    font-size: 36px;
    font-weight: 700;
    margin: 0;
}

.gpa-card.loading .gpa-value {
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    animation: pulse 1.5s ease-in-out infinite;
}

/* Search and Controls Section */
.search-and-controls {
    display: flex;
    gap: 16px;
    align-items: center;
    margin-bottom: 24px;
    flex-wrap: wrap;
}

.search-bar {
    flex: 1;
    position: relative;
    min-width: 300px;
}

.search-icon {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: #64748b;
    font-size: 16px;
}

.search-input {
    width: 100%;
    padding: 12px 16px 12px 48px;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    font-size: 14px;
    background: white;
    transition: all 0.2s ease;
    box-sizing: border-box;
}

.search-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filter-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.filter-dropdown,
.region-dropdown,
.state-dropdown {
    position: relative;
    display: flex;
    align-items: center;
}

.filter-icon,
.location-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 14px;
    color: #64748b;
    z-index: 1;
}

.filter-select,
.region-select,
.state-select,
.sort-select {
    padding: 12px 16px 12px 36px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    background: white;
    cursor: pointer;
    min-width: 140px;
    transition: all 0.2s ease;
}

.sort-select {
    padding: 12px 16px;
}

.filter-select:hover,
.region-select:hover,
.state-select:hover,
.sort-select:hover {
    border-color: #3b82f6;
}

.filter-select:focus,
.region-select:focus,
.state-select:focus,
.sort-select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.view-toggle {
    display: flex;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    overflow: hidden;
}

.view-btn {
    padding: 12px 16px;
    border: none;
    background: transparent;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #64748b;
}

.view-btn.active {
    background: #3b82f6;
    color: white;
}

.view-btn:hover:not(.active) {
    background: #f1f5f9;
}

/* Program Summary Cards */
.program-summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.summary-card {
    background: white;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    cursor: pointer;
    transition: all 0.2s ease;
    user-select: none;
}

.summary-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.summary-card.active {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    border-color: #3b82f6;
}

.summary-card.active .card-title {
    color: #3b82f6;
    font-weight: 600;
}

.summary-card.active .card-number {
    color: #1e40af;
}

.summary-card.eligible {
    background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
    border-color: #16a34a;
}

.summary-card.eligible.active {
    background: linear-gradient(135deg, #bbf7d0 0%, #86efac 100%);
    border-color: #15803d;
    box-shadow: 0 4px 12px rgba(22, 163, 74, 0.3);
}

.summary-card.need-prerequisites {
    background: linear-gradient(135deg, #fed7aa 0%, #fdba74 100%);
    border-color: #ea580c;
}

.summary-card.need-prerequisites.active {
    background: linear-gradient(135deg, #fdba74 0%, #fb923c 100%);
    border-color: #c2410c;
    box-shadow: 0 4px 12px rgba(234, 88, 12, 0.3);
}

.summary-card.incomplete {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    border-color: #64748b;
}

.summary-card.incomplete.active {
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
    border-color: #475569;
    box-shadow: 0 4px 12px rgba(100, 116, 139, 0.3);
}

.card-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-title {
    font-size: 14px;
    font-weight: 500;
    color: #64748b;
    margin-bottom: 4px;
}

.card-number {
    font-size: 28px;
    font-weight: 700;
    color: #1e293b;
}

.card-icon {
    font-size: 24px;
    opacity: 0.6;
}

.summary-card.eligible .card-icon {
    color: #16a34a;
}

.summary-card.need-prerequisites .card-icon {
    color: #ea580c;
}

.summary-card.incomplete .card-icon {
    color: #64748b;
}

/* Results Info */
.results-info {
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 12px;
}

.results-count {
    font-size: 14px;
    color: #64748b;
}

.filter-indicator {
    color: #3b82f6;
    font-weight: 600;
}

.clear-filter-btn {
    background: #f1f5f9;
    color: #64748b;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.clear-filter-btn:hover {
    background: #e2e8f0;
    color: #1e293b;
}

/* Programs List */
.programs-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* Program List Item */
.program-list-item {
    background: white;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    padding: 20px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.program-list-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #cbd5e1;
}

.program-item-header {
    display: flex;
    align-items: center;
    gap: 20px;
}

.program-main-info {
    flex: 1;
}

.program-name {
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    margin: 0 0 4px 0;
}

.program-name a {
    color: #3b82f6;
    text-decoration: none;
    transition: color 0.2s ease;
}

.program-name a:hover {
    color: #1d4ed8;
}

.program-location {
    font-size: 14px;
    color: #64748b;
}

.program-stats {
    display: flex;
    align-items: center;
    gap: 32px;
}

.stat-group {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.stat-label {
    color: #64748b;
    font-weight: 500;
}

.stat-value {
    color: #1e293b;
    font-weight: 600;
}

.stat-group.completion {
    gap: 12px;
}

.completion-bar {
    width: 60px;
    height: 6px;
    background: #e2e8f0;
    border-radius: 3px;
    overflow: hidden;
}

.completion-fill {
    height: 100%;
    background: #3b82f6;
    border-radius: 3px;
    transition: width 0.3s ease;
}

.program-status {
    display: flex;
    align-items: center;
    gap: 12px;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    color: white;
}

.status-badge.eligible {
    background: #16a34a;
}

.status-badge.ineligible {
    background: #ea580c;
}

.status-badge.incomplete {
    background: #64748b;
}

.expand-arrow {
    color: #64748b;
    font-size: 16px;
    transition: transform 0.2s ease;
}

.program-list-item:hover .expand-arrow {
    transform: translateX(4px);
}

/* Program Item Details (Expanded) */
.program-item-details {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e2e8f0;
}

.requirements-section {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.requirement-group h4 {
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    margin: 0 0 12px 0;
}

.gpa-requirements,
.prerequisites-list,
.experience-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.gpa-item,
.prerequisite-item,
.experience-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f8fafc;
    border-radius: 8px;
    font-size: 14px;
}

/* Detailed Prerequisite Items */
.prerequisite-item-detailed {
    background: #f8fafc;
    border-radius: 12px;
    padding: 16px;
    border: 1px solid #e2e8f0;
    margin-bottom: 12px;
}

.prerequisite-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.prerequisite-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.course-name {
    font-weight: 600;
    color: #1e293b;
    font-size: 14px;
}

.lab-required {
    background: #ef4444;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.matched-courses {
    margin-top: 12px;
}

/* Used vs Unused Course Sections */
.used-courses-section {
    margin-bottom: 16px;
}

.unused-courses-section {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #e2e8f0;
}

.courses-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    font-size: 13px;
    color: #64748b;
    font-weight: 500;
}

.courses-header.used {
    color: #166534;
    background: #f0fdf4;
    padding: 8px 12px;
    border-radius: 8px;
    border: 1px solid #bbf7d0;
}

.courses-header.unused {
    color: #64748b;
    background: #f8fafc;
    padding: 8px 12px;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.credits-summary {
    color: #1e293b;
    font-weight: 600;
}

.courses-count {
    color: #64748b;
    font-weight: 500;
}

.course-cards {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.course-card {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.2s ease;
}

.course-card:hover {
    border-color: #3b82f6;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

/* Used course cards - highlighted */
.course-card.used {
    border-color: #16a34a;
    background: #f0fdf4;
    position: relative;
}

.course-card.used::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: #16a34a;
    border-radius: 4px 0 0 4px;
}

.course-card.used:hover {
    border-color: #15803d;
    box-shadow: 0 2px 8px rgba(22, 163, 74, 0.15);
}

/* Unused course cards - muted */
.course-card.unused {
    opacity: 0.7;
    background: #f9fafb;
    border-color: #d1d5db;
}

.course-card.unused:hover {
    opacity: 1;
    border-color: #9ca3af;
    box-shadow: 0 2px 8px rgba(156, 163, 175, 0.1);
}

.course-details {
    flex: 1;
}

.course-code-name {
    display: block;
    font-weight: 600;
    color: #1e293b;
    font-size: 14px;
    margin-bottom: 4px;
}

.course-meta {
    display: block;
    font-size: 12px;
    color: #64748b;
}

.course-stats {
    display: flex;
    align-items: center;
    gap: 12px;
}

.course-credits {
    font-size: 12px;
    color: #64748b;
    font-weight: 500;
}

.course-grade {
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 700;
    min-width: 32px;
    text-align: center;
}

/* Grade Color System */
.course-grade.grade-A {
    background: #dcfce7;
    color: #166534;
}

.course-grade.grade-B {
    background: #dbeafe;
    color: #1e40af;
}

.course-grade.grade-C {
    background: #fef3c7;
    color: #92400e;
}

.course-grade.grade-D {
    background: #fed7aa;
    color: #ea580c;
}

.course-grade.grade-F {
    background: #fee2e2;
    color: #dc2626;
}

.no-courses {
    margin-top: 8px;
    padding: 12px;
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 8px;
}

.no-courses-text {
    color: #dc2626;
    font-size: 13px;
    font-style: italic;
}

.status.met {
    color: #16a34a;
    font-weight: 600;
}

.status.not-met {
    color: #ef4444;
    font-weight: 600;
}

/* Program Info Section Styles */
.program-info-section {
    padding: 0;
    border-radius: 8px;
    background: #f8fafc;
}

.info-tabs {
    display: flex;
    border-bottom: 1px solid #e2e8f0;
    background: white;
    border-radius: 8px 8px 0 0;
}

.tab {
    padding: 12px 20px;
    font-weight: 500;
    color: #64748b;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
}

.tab.active {
    color: #3b82f6;
    border-bottom-color: #3b82f6;
    background: #f8fafc;
}

.tab:hover {
    color: #3b82f6;
    background: #f1f5f9;
}

/* Tab Content */
.tab-content {
    background: white;
    border-radius: 0 0 8px 8px;
    min-height: 200px;
}

.requirements-tab {
    padding: 20px;
}

.match-tab {
    padding: 20px;
}

.match-analysis {
    text-align: center;
    color: #64748b;
    padding: 40px 20px;
}

.match-analysis h4 {
    margin-bottom: 12px;
    color: #1e293b;
}

/* Quick Stats Grid */
.quick-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
    padding: 20px;
}

.stat-card {
    display: flex;
    align-items: center;
    padding: 16px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
}

.stat-card:hover {
    border-color: #cbd5e1;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.stat-icon {
    font-size: 24px;
    margin-right: 12px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f1f5f9;
    border-radius: 50%;
}

.stat-content {
    flex: 1;
}

.stat-label {
    font-size: 12px;
    color: #64748b;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-value {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin-top: 2px;
}

/* Enhanced Statistics Section */
.enhanced-stats-section {
    margin-bottom: 24px;
    padding: 0 20px;
}

.enhanced-stats-section h4 {
    margin: 0 0 16px 0;
    color: #1e293b;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.enhanced-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 12px;
    margin-bottom: 16px;
}

.enhanced-stat-card {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
    min-height: 70px;
}

.enhanced-stat-card:hover {
    border-color: #cbd5e1;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.enhanced-stat-card.pance-stat {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border-color: #10b981;
}

.enhanced-stat-card.pance-stat .stat-icon {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.enhanced-stat-card.pance-stat .stat-label,
.enhanced-stat-card.pance-stat .stat-value,
.enhanced-stat-card.pance-stat .stat-subtext {
    color: white;
}

.enhanced-stat-card.tuition-stat {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    border-color: #f59e0b;
}

.enhanced-stat-card.tuition-stat .stat-icon {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.enhanced-stat-card.tuition-stat .stat-label,
.enhanced-stat-card.tuition-stat .stat-value {
    color: white;
}

.enhanced-stat-card .stat-icon {
    font-size: 20px;
    margin-right: 10px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f1f5f9;
    border-radius: 50%;
    flex-shrink: 0;
}

.enhanced-stat-card .stat-content {
    flex: 1;
    min-width: 0;
}

.enhanced-stat-card .stat-label {
    font-size: 11px;
    color: #64748b;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 2px;
}

.enhanced-stat-card .stat-value {
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    line-height: 1.2;
    word-break: break-word;
}

.enhanced-stat-card .stat-subtext {
    font-size: 10px;
    color: #64748b;
    font-weight: 400;
    margin-top: 2px;
    opacity: 0.8;
}

/* Program Details Grid */
.program-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
    padding: 0 20px 20px;
}

.detail-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.detail-section h4 {
    margin: 0 0 16px 0;
    color: #1e293b;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.detail-items {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f1f5f9;
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 500;
    color: #64748b;
    min-width: 120px;
}

.detail-value {
    color: #1e293b;
    font-weight: 500;
    text-align: right;
}

.detail-link {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

.detail-link:hover {
    color: #2563eb;
    text-decoration: underline;
}

/* Eligibility Overview */
.eligibility-overview {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.eligibility-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: #f8fafc;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.eligibility-label {
    font-weight: 500;
    color: #64748b;
}

.eligibility-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
}

.eligibility-status .status {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

/* Prerequisites Section */
.prerequisites-section {
    background: white;
    margin: 20px;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.prerequisites-section h4 {
    margin: 0 0 20px 0;
    color: #1e293b;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .pamatch-container {
        padding: 16px;
    }
    
    .pamatch-header {
        flex-direction: column;
        gap: 16px;
        text-align: center;
    }
    
    .gpa-cards-container {
        grid-template-columns: 1fr;
    }
    
    .search-and-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-bar {
        min-width: unset;
    }
    
    .filter-controls {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .filter-dropdown,
    .region-dropdown,
    .state-dropdown,
    .sort-dropdown {
        min-width: 140px;
    }
    
    .program-summary-cards {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .program-item-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }
    
    .program-stats {
        justify-content: space-between;
        gap: 16px;
    }
    
    .program-status {
        justify-content: center;
    }

    .quick-stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
        padding: 16px;
    }

    .enhanced-stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }

    .enhanced-stat-card {
        padding: 10px 12px;
        min-height: 60px;
    }

    .enhanced-stat-card .stat-icon {
        font-size: 18px;
        width: 28px;
        height: 28px;
        margin-right: 8px;
    }

    .enhanced-stat-card .stat-label {
        font-size: 10px;
    }

    .enhanced-stat-card .stat-value {
        font-size: 14px;
    }

    .enhanced-stat-card .stat-subtext {
        font-size: 9px;
    }

    .program-details-grid {
        grid-template-columns: 1fr;
        gap: 16px;
        padding: 0 16px 16px;
    }
    
    .stat-card {
        padding: 12px;
    }
    
    .stat-icon {
        font-size: 20px;
        width: 32px;
        height: 32px;
    }
    
    .stat-value {
        font-size: 16px;
    }
    
    .detail-section {
        padding: 16px;
    }
    
    .detail-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
    
    .detail-value {
        text-align: left;
    }
}

@media (max-width: 480px) {
    .program-summary-cards {
        grid-template-columns: 1fr;
    }
    
    .results-info {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }
    
    .program-stats {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }
    
    .stat-group {
        justify-content: space-between;
        padding: 8px 0;
        border-bottom: 1px solid #e2e8f0;
    }
    
    .stat-group:last-child {
        border-bottom: none;
    }

    .enhanced-stats-grid {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .enhanced-stat-card {
        padding: 8px 10px;
        min-height: 50px;
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Saved Results Banner */
.saved-results-banner {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    border: 1px solid #93c5fd;
    border-radius: 12px;
    padding: 16px 20px;
    margin-bottom: 24px;
    font-size: 14px;
}

.saved-results-banner span {
    color: #1e40af;
    font-weight: 500;
}

.recalculate-btn {
    background: #3b82f6;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 8px 16px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.recalculate-btn:hover {
    background: #2563eb;
}

.recalculate-btn:disabled {
    background: #94a3b8;
    cursor: not-allowed;
}