// Optimized Program Matching Utilities
// This file contains performance-optimized functions tching

/*
 * Web Worker for heavy program matching calculations
 * Prevents UI blocking during computation
 */
export const createMatchingWorker = () => {
    const workerCode = `
        self.onmessage = function(e) {
   
    
            const checkProgramEligibility = (progr
                // Same logic as main thread but runs in background
   {
  gible';
                }
                
                const hasPrereqs = program.requirements.prerequisites && 
   
  
                if (!hasPrereqs) {
                    return 'incomplete';
         }

                cons
  };ormalized;
   return n  
  }
      || 0;
s)wingHourta.shadot(profileDaarseIn pours =g_howinhadalized.s    normned) {
    undefiHours !== shadowingofileData. if (pr
    
    0;
    }Hours) ||retCaectPatienData.dirileeInt(profhours = parst_care__patienzed.directliorma       n) {
  undefinedeHours !==tCarenctPatidireta.leDa(profi if ts
   ismelCase exsions if cacase verke_/ Add sna /
   
       }|| 0;
 ing_hours) Data.shadowt(profile = parseIningHourszed.shadow  normali      defined) {
!== unurs ing_hoshadowprofileData.if (    
       }
| 0;
 ) |t_care_hoursen_patiirecta.dfileDatInt(proours = parsetientCareHectPaalized.dir    norm{
    ndefined) ours !== uient_care_hct_pateData.direprofilts
    if (xisnake_case e if se versionsCaselamdd c    // Aa };
    
leDatprofi= { ...alized  norm  consts
  e_case field snaklCase andoth camepy with bed cormaliz noCreate a//   
    
  ) return {};fileData!pro (   if) => {
 atafileData = (proeProfileDliz const norma
 */
exportamesld ne_case fiee and snakth camelCaso handle bofile data tmalize pro*
 * Nor
/*  };
};

  tartIndexdIndex - sbleItems: enisi   vex,
          endInd   fer
r buf // -1 fo1),rtIndex - (0, stax: Math.maxIndert sta   
    n {tur  
    re  r
+2 for buffe; // talItems)t + 2, toeCounisiblrtIndex + vtath.min(sndex = Maconst endI   ight);
 ht / itemHeontainerHeigil(c = Math.ceCountible const viseight);
   Top / itemHfloor(scrollMath.= tartIndex onst s
    c { =>tems)totalIght, einerHt, contaiHeighemollTop, ittems = (screIeVisibl calculatexport const*/
ts
 program lisrge or lahelper fl scrolling rtuaVi

/**
 * 
};y);
    };s), dela..arg(.> callback =tTimeout(()outId = se      time;
  meoutId)t(tiarTimeou  cle> {
      .args) =(..
    return utId;
    t timeo{
    le 300) => k, delay =callbac = (charedSeeDebounconst creat
export ctering
 */rogram filfor pch nced sear
 * Debou*/*
};


    };
        })0)h) * 100, 10ngtms.le/ prograndex urrentI((cMath.minrcentage:        pe    length,
 rograms.: ptotal        ngth),
    rograms.lerentIndex, p(cur.minoaded: Math      l> ({
      () =ess: gr     getPro },
        
  Index = 0;  current         => {
 ()      reset:    },
   h;
     turn batc  re          e;
izatchStIndex += bcurren  
          e);+ batchSizrentIndex curndex, currentIe(rams.slicbatch = progonst      c
       > {Next: () =  loadgth,
      programs.lenrentIndex < > cure: () =sMor        ha {

    return  dex = 0;
  urrentIn    let c
> {) == 20Size tchrograms, baLoader = (pssiveeateProgrenst cr/
export colists
 *ram large proging for oad lgressive**
 * Pro
/
    }
}
heKey);em(this.cace.removeItocalStorag        l = {};
  this.cache   {
       clear() 
 }
   he();
    is.saveCac     th
   
        };.now()stamp: Date       timeults,
        res
         = {cache[key] s.     thiData);
   profileipt, TranscrcheKey(useretCathis.gkey = onst        cs) {
 lt, resuleDataprofit, anscripuserTr 
    set(   }
   [key];
 heis.cac theturn       ra);
  profileDatipt,(userTranscracheKeyhis.getC tt key =  cons
      ta) {Da profilet,anscripTrser   get(u  }
    
 ();
  ing hash.toStr  return  }
      eger
      intto 32-bit // Convert ash;  & hsh = hash        ha  ;
  h) + char - has((hash << 5) =  hash         
  At(i);deCoharaString.c datr =haconst c           i++) {
  ing.length;Str< datat i = 0; i  (le     for   sh = 0;
      let haction
  funh  hasmple// Si        
    });
     }
                _cat
   _taken_paata?.hasofileD pr     paCat:           n_casper,
s_takehaofileData?.  casper: pr           ts: {
       tes},
                  
  ng_scoreytical_writialgre_anta?. profileDawriting:              e,
  ve_scorantitatiquata?.gre_eDofilnt: pr        qua   
     core,rbal_s?.gre_veatarofileD: p      verbal           gre: {
          ,
 ingHours.shadowrofileData?|| pwing_hours doData?.sharofilehadowing: p        sours,
    entCareHtita?.directPa|| profileDacare_hours ent__patiectir.dprofileData?e: tientCar   pa     
    isites,rerequ?.prTranscriptites: useuis   prereq        pa,
 ience_gnscript?.scTraeGpa: user      scienc,
      paculated_gpt?.calcrirTrans   gpa: use
         .stringify({g = JSONStrint data        conses
t chang to detecuser data hash of // Create a       leData) {
 rofiipt, prTranscry(useacheKeetC   
    g
   }}
    );
      :', errorng cachehimatcve program ailed to saole.warn('Fcons            ) {
catch (error    }    
 cache));fy(this.gitrinKey, JSON.sis.cachethm(tItesetorage.lS    loca {
               try) {
  saveCache(
    
      }     }
 ;
    {}rn     retu      catch {
         } ed) : {};
rse(cach JSON.pahed ?n cac       retur;
     heKey)his.cacem(t.getItgeStoraaled = loccach const          {
          try ache() {
loadC
    
    }
    dCache(); = this.loa  this.cache      gCache';
tchingramMaey = 'procheK.cahis        t() {
ortructons {
    cacheramMatchingCProgort class 
 */
expeenc persistlStoragecath lowiing am matchogrprd  * Cache
;
};

/**URL(blob))eObject(URL.creatkerurn new Wor);
    retript' }on/javasccatiappli], { type: 'workerCodeb([b = new Bloblot   
    cons    `;
  ;

        }   });     ounts
           c     sults,
        re           omplete',
 e: 'c        typ    {
    Message(elf.post s            
                  });
}
                 ;
       })                
 ...counts }ts: {         coun         
       rams.length,: prog   total                    ndex + 1,
 sed: ies     proc         
          ,progress'type: '                     age({
   elf.postMess        s       ) {
     % 10 === 0  if (index             dates
  s upnd progres       // Se       
               us]++;
   ts[stat        coun       
 s;e] = statuam.namrogrlts[p   resu            ram);
 (proggibilitykProgramElius = chec  const stat       
        index) => {ram,h((progams.forEacrogr          p 
           0 };
   lete:comp ine: 0,igibl: 0, inellegibunts = { elionst co    c
        s = {}; resultonst c          cking
 t bloevens to prhetcograms in ba press    // Proc  
              
     };           e';
gibln 'ineliur ret                 }

              ligible';
   return 'e             sts) {
    rdizedTetandang && meetsSShadowiets&& meCare tienttsPameePrereqs &&  meets &&ienceGPA&& meetsScA if (meetsGP           

           }            }
                 alse;
 = fdizedTests darsStan   meet                  ) {
   _pa_catenas_taka?.hrofileDat(!p      if            )) {
   'pacat'.includes(iredTests requ ||t')udes('pa-cainclests.redTif (requi                  }

          }
                     alse;
   dTests = fdardizean meetsSt                  er) {
     aken_caspa?.has_teDatrofil      if (!p          
    )) {es('casper'sts.includdTeuire   if (req        
      }
      }
                          
   s = false;ardizedTestmeetsStand                        
re) {ting_sco_wriallyticanagre_ofileData?.score && !pritative_e_quantleData?.grfiro&& !pbal_score ta?.gre_ver!profileDa        if (           'gre')) {
 s.includes(stedTerequir if (           );

    => ter(t    .filt                                      
     im())=> t.tr(t     .map                                       \\)/)
   t(/,|\\(|e().splitoLowerCasdString.stsRequirets = teequiredTes ronst  c               || "";
requiredsts?.tests__terdizeds.standaementquirram.reprogng = equiredStrionst testsR c             true;
   edTests =sStandardizet  let me         ;

     rs"] || 0) Hou"Shadowingience[.experequirementsm.r     (progra               >= 
) urs || 0HoadowingData?.shle profig_hours ||winshadota?.profileDahadowing = (t meetsS    cons          || 0);
  ] Hours"e tient Carience["Paents.experam.requiremogr(pr                  >= 
   rs || 0)eHouCarrectPatientata?.di|| profileDe_hours ent_carirect_patia?.dfileDatre = (protientCaetsPa    const me                  
          });
             
       ditsNum;re= requiredCCredits >alurn tot       ret                          }
              th > 0;
 es.lengqName].coursereqs[prereogramProurses && prqName].ceqs[prereer programPreturn      r                     sNum)) {
 reditredCaN(requiisNif (                        ts);

dit(req.creoam = parseFleditsNudCr require    const              ;
      | 0dits |me].totalCres[prereqNarereq= programPalCredits onst tot       c                 
false;eturn e]) rprereqNams[eqrer (!programP          if              req]) => {
me, [prereqNa  .every((             ites)
     requisements.preogram.requirentries(prect. = ObjsPrereqsetnst me     co                   

        .science;rements.gparequiam.) >= progr_gpapt.scienceranscrioat(userTrseFlpa = ienceGPASc meetsnst      co        ll;
  a.overairements.gpprogram.requ=  >lculated_gpascript.ca = userTranetsGPA    const me           }

       
          mplete';eturn 'inco   r                ) {
  0).length ===reqsprogramPres(keys || Object.eqer(!programPr   if            