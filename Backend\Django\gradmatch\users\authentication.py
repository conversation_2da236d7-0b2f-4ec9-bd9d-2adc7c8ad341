import jwt
from django.conf import settings # Import Django settings
from django.contrib.auth import get_user_model
from rest_framework import authentication
from rest_framework import exceptions
import logging
from django.core.exceptions import ImproperlyConfigured # For config checks

logger = logging.getLogger(__name__)
User = get_user_model()

# --- Get Configuration from Django Settings ---
try:
    SUPABASE_JWT_SECRET = settings.SUPABASE_JWT_SECRET
    SUPABASE_URL = settings.SUPABASE_URL
    SUPABASE_AUDIENCE = getattr(settings, 'SUPABASE_AUDIENCE', 'authenticated') # Use getattr for optional setting with default
    SUPABASE_ISSUER = getattr(settings, 'SUPABASE_ISSUER', SUPABASE_URL) # Default issuer to URL if not set
except AttributeError as e:
     logger.error(f"Missing required Supabase setting: {e}. Please define SUPABASE_URL and SUPABASE_JWT_SECRET in your Django settings.")
     raise ImproperlyConfigured(f"Missing required Supabase setting: {e}")

# Basic check if the secret is configured
if not SUPABASE_JWT_SECRET:
     logger.error("SUPABASE_JWT_SECRET is not configured in Django settings.")
     raise ImproperlyConfigured("SUPABASE_JWT_SECRET is not configured.")
# --- End Configuration ---


class SupabaseAuthentication(authentication.BaseAuthentication):
    """
    Custom authentication backend for validating Supabase JWTs using HS256,
    configured via Django settings.
    """
    def authenticate(self, request):
        auth_header = authentication.get_authorization_header(request).split()

        if not auth_header or auth_header[0].lower() != b'bearer':
            logger.debug("No Bearer token found in Authorization header.")
            return None  # No token provided

        if len(auth_header) == 1:
            raise exceptions.AuthenticationFailed('Invalid token header. No credentials provided.')
        elif len(auth_header) > 2:
            raise exceptions.AuthenticationFailed('Invalid token header. Token string should not contain spaces.')

        try:
            token = auth_header[1].decode('utf-8')
        except UnicodeError:
            raise exceptions.AuthenticationFailed('Invalid token header. Token string should not contain invalid characters.')

        try:
            # Decode using the shared secret and HS256 algorithm from settings
            payload = jwt.decode(
                token,
                SUPABASE_JWT_SECRET,
                algorithms=["HS256"], # Use HS256 for shared secret
                audience=SUPABASE_AUDIENCE,
                issuer=SUPABASE_ISSUER
            )

            supabase_user_id = payload.get('sub')
            user_email = payload.get('email') # Get email from token

            if not supabase_user_id:
                logger.warning("Token payload missing 'sub' (Supabase User ID).")
                raise exceptions.AuthenticationFailed('Invalid token: Missing user ID.')
            if not user_email:
                 logger.warning("Token payload missing 'email'.")
                 # Decide if email is strictly required. If so, raise error.
                 # raise exceptions.AuthenticationFailed('Invalid token: Missing email.')


            # --- Find or Link Django User ---
            user = None
            # 1. Try finding by Supabase ID (preferred)
            #    Assumes you add 'supabase_user_id' field to UserProfile or Custom User
            try:
                # Adjust the lookup based on where you add the field
                # Example using UserProfile:
                profile = UserProfile.objects.select_related('user').get(supabase_user_id=supabase_user_id)
                user = profile.user
                logger.info(f"Authenticated user {user.email} via supabase_user_id.")

            except UserProfile.DoesNotExist: # Adjust exception if using User model directly
                 logger.info(f"No user found with supabase_user_id {supabase_user_id}. Trying email lookup.")
                 # 2. If not found by ID, try finding by email (for first login post-migration)
                 try:
                     logger.debug(f"Attempting to find Django user by email: {user_email}")
                     user = User.objects.get(email__iexact=user_email) # Use iexact for case-insensitive match
                     logger.info(f"Found Django user {user.id} ({user.email}) via email. Attempting to link profile.")
                     # Link the Supabase ID to the profile for future logins
                     try:
                         logger.debug(f"Attempting to find UserProfile for user {user.id}")
                         profile = UserProfile.objects.get(user=user)
                         logger.debug(f"Found UserProfile {profile.id}. Checking for supabase_user_id field.")
                         # Ensure the field exists before trying to save it
                         if hasattr(profile, 'supabase_user_id'):
                             profile.supabase_user_id = supabase_user_id
                             profile.save(update_fields=['supabase_user_id'])
                             logger.info(f"Successfully linked supabase_user_id {supabase_user_id} to user {user.email}.")
                         else:
                              logger.warning(f"UserProfile model for {user.email} does not have 'supabase_user_id' field. Cannot link ID.")

                     except UserProfile.DoesNotExist:
                          logger.error(f"CRITICAL: UserProfile.DoesNotExist for user {user.id} ({user.email}) during supabase_id linking. Profile needs to be created.")
                          # Consider raising AuthenticationFailed here if a profile is essential
                          # raise exceptions.AuthenticationFailed('User profile missing.')
                     except Exception as e:
                          logger.error(f"CRITICAL: Error saving supabase_user_id for user {user.id} ({user.email}): {e}", exc_info=True)
                          # Consider raising AuthenticationFailed here as linking failed
                          # raise exceptions.AuthenticationFailed('Failed to link user account.')

                 except User.DoesNotExist:
                     logger.warning(f"No Django user found for email '{user_email}' from token.")
                     raise exceptions.AuthenticationFailed(f"No Django user found matching email '{user_email}'.")
                 except Exception as e: # Catch potential errors during email lookup/linking
                      logger.error(f"Error during email lookup/linking for {user_email}: {e}")
                      raise exceptions.AuthenticationFailed('Error linking user account.')

            except Exception as e: # Catch potential errors during supabase_id lookup
                 logger.error(f"Error looking up user by supabase_user_id {supabase_user_id}: {e}")
                 raise exceptions.AuthenticationFailed('Error retrieving user.')


            if not user.is_active:
                logger.warning(f"Attempt to authenticate inactive user: {user.email}")
                raise exceptions.AuthenticationFailed('User inactive or deleted.')

            return (user, token) # Successfully authenticated

        except jwt.ExpiredSignatureError:
            logger.info("Token has expired.")
            raise exceptions.AuthenticationFailed('Token has expired.')
        except jwt.InvalidAudienceError:
            logger.warning(f"Invalid audience in token. Expected: {SUPABASE_AUDIENCE}")
            raise exceptions.AuthenticationFailed('Invalid token audience.')
        except jwt.InvalidIssuerError:
             logger.warning(f"Invalid issuer in token. Expected: {SUPABASE_ISSUER}")
             raise exceptions.AuthenticationFailed('Invalid token issuer.')
        except jwt.InvalidTokenError as e: # Catch other JWT errors (includes signature errors)
            logger.warning(f"Invalid token: {e}")
            raise exceptions.AuthenticationFailed(f'Invalid token: {e}')
        except exceptions.AuthenticationFailed as e: # Re-raise specific auth failures
             raise e
        except Exception as e: # Catch unexpected errors during validation/lookup
            logger.error(f"Unexpected authentication error: {e}", exc_info=True)
            raise exceptions.AuthenticationFailed('Authentication failed due to an unexpected error.')


# Helper model import (assuming UserProfile is in users.models)
# This needs to be at the end or handled carefully to avoid circular imports
# if UserProfile itself imports things that depend on settings.
try:
    from users.models import UserProfile
except ImportError:
    logger.error("Could not import UserProfile from users.models. Make sure the model exists and the path is correct.")
    # Define a dummy UserProfile if needed for the code to load, but log error.
    class UserProfile: # Dummy class
         objects = None # type: ignore
         DoesNotExist = type('DoesNotExist', (Exception,), {})
         # Add select_related if needed by the dummy
         @staticmethod
         def select_related(*args):
             return UserProfile() # Return dummy instance
         def get(self, *args, **kwargs):
             raise self.DoesNotExist("Dummy UserProfile cannot be fetched")