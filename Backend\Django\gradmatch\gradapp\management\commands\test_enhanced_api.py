from django.core.management.base import BaseCommand
from django.http import JsonResponse
from gradapp.models import Program
import json

class Command(BaseCommand):
    help = 'Test the enhanced API data structure for a sample program'

    def add_arguments(self, parser):
        parser.add_argument('--program-name', type=str, help='Name of program to test (partial match)')

    def handle(self, *args, **options):
        program_name = options.get('program_name') or '<PERSON><PERSON>'

        # Find a program that matches the name
        program = Program.objects.select_related('school').prefetch_related(
            'pance_pass_rates',
            'attrition_data', 
            'enhanced_class_profiles',
            'matriculant_demographics',
            'tuition_information',
            'program_curriculum'
        ).filter(name__icontains=program_name).first()
        
        if not program:
            self.stdout.write(self.style.ERROR(f'No program found matching "{program_name}"'))
            return
            
        self.stdout.write(self.style.SUCCESS(f'Testing enhanced data for: {program.name}'))
        
        # Test PANCE data
        pance_data = program.pance_pass_rates.first()
        if pance_data:
            self.stdout.write(f'\n=== PANCE DATA ===')
            self.stdout.write(f'Program Pass Rate: {pance_data.program_pass_rate}')
            self.stdout.write(f'National Pass Rate: {pance_data.national_pass_rate}')
            self.stdout.write(f'Candidates: {pance_data.candidates_took_pance}')
        else:
            self.stdout.write(self.style.WARNING('No PANCE data found'))
            
        # Test Class Profile data
        class_profile = program.enhanced_class_profiles.first()
        if class_profile:
            self.stdout.write(f'\n=== CLASS PROFILE DATA ===')
            self.stdout.write(f'Total Applications: {class_profile.total_applications}')
            self.stdout.write(f'Total Matriculants: {class_profile.total_matriculants}')
            self.stdout.write(f'Average Overall GPA: {class_profile.average_overall_gpa}')
            self.stdout.write(f'Average Science GPA: {class_profile.average_science_gpa}')
        else:
            self.stdout.write(self.style.WARNING('No class profile data found'))
            
        # Test Attrition data
        attrition = program.attrition_data.first()
        if attrition:
            self.stdout.write(f'\n=== ATTRITION DATA ===')
            self.stdout.write(f'Graduation Rate: {attrition.graduation_rate}')
            self.stdout.write(f'Entering Class Size: {attrition.entering_class_size}')
        else:
            self.stdout.write(self.style.WARNING('No attrition data found'))
            
        # Test Demographics data
        demographics = program.matriculant_demographics.first()
        if demographics:
            self.stdout.write(f'\n=== DEMOGRAPHICS DATA ===')
            self.stdout.write(f'Female: {demographics.female_count}')
            self.stdout.write(f'Male: {demographics.male_count}')
        else:
            self.stdout.write(self.style.WARNING('No demographics data found'))
            
        # Test Tuition data
        tuition = program.tuition_information.first()
        if tuition:
            self.stdout.write(f'\n=== TUITION DATA ===')
            self.stdout.write(f'Tuition: {tuition.tuition}')
        else:
            self.stdout.write(self.style.WARNING('No tuition data found'))
            
        self.stdout.write(self.style.SUCCESS('\nTest completed!'))
