import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './PrerequisiteClasses.css';
import { useAuth } from '../contexts/AuthContext';

const PrerequisiteClasses = ({ existingTranscripts }) => {
    const { supabase } = useAuth();
    const [prerequisites, setPrerequisites] = useState({
        'Human Anatomy and Physiology': { courses: [], manuallySelected: null },
        'Biochemistry': { courses: [], manuallySelected: null },
        'Microbiology': { courses: [], manuallySelected: null },
        'Medical Terminology': { courses: [], manuallySelected: null },
        'Statistics & Other Math': { courses: [], manuallySelected: null }, // Renamed from Statistics
        'Psychology': { courses: [], manuallySelected: null },
        'English': { courses: [], manuallySelected: null },
        'Physics': { courses: [], manuallySelected: null },
        'Organic Chemistry': { courses: [], manuallySelected: null },
        'Genetics': { courses: [], manuallySelected: null },
        'Humanities': { courses: [], manuallySelected: null },
        'Social Science': { courses: [], manuallySelected: null },
        'Chemistry (general)': { courses: [], manuallySelected: null }, // Renamed from Inorganic Chemistry
        'Biology (general)': { courses: [], manuallySelected: null }, // Added Biology (general)
        'Math': { courses: [], manuallySelected: null }, // Added Math
        'Sociology': { courses: [], manuallySelected: null }, // Added Sociology
        'CPR': { courses: [], manuallySelected: null },
        'Foreign Language': { courses: [], manuallySelected: null }, // New category
        'American History/Government': { courses: [], manuallySelected: null }, // New category
        'Oral Communication': { courses: [], manuallySelected: null } // New category
    });
    const [allCourses, setAllCourses] = useState([]);
    const [showAddCourse, setShowAddCourse] = useState({});
    const [isSaving, setIsSaving] = useState(false);
    const [saveMessage, setSaveMessage] = useState('');
    const [expandedItems, setExpandedItems] = useState({});

    // Function to toggle expand/collapse state of a prerequisite card
    const toggleExpand = (prereqName) => {
        setExpandedItems(prev => ({
            ...prev,
            [prereqName]: !prev[prereqName]
        }));
    };

    // Helper function to get grade badge class
    const getGradeBadgeClass = (grade) => {
        if (!grade) return '';
        const firstChar = grade.charAt(0).toUpperCase();
        if (['A'].includes(firstChar)) return 'grade-a';
        if (['B'].includes(firstChar)) return 'grade-b';
        if (['C'].includes(firstChar)) return 'grade-c';
        if (['D'].includes(firstChar)) return 'grade-d';
        if (['F', 'W'].includes(firstChar)) return 'grade-f';
        return '';
    };

    // Course matching logic from PAMatch
    const courseMatchesPrereq = (courseName, courseCode, prereqName) => {
        courseName = courseName.toLowerCase();
        courseCode = courseCode.toLowerCase();
        prereqName = prereqName.toLowerCase();

        // Common course prefix patterns
        const chemistryPrefixes = ['chem', 'chm', 'chemy'];
        const biologyPrefixes = ['bio', 'biol', 'bsc'];
        const psychologyPrefixes = ['psy', 'psyc', 'psych'];
        const englishPrefixes = ['eng', 'engl', 'eh'];
        const statisticsPrefixes = ['stat', 'stats', 'sta', 'mth'];
        const sociologyPrefixes = ['soc', 'socy'];
        const anthropologyPrefixes = ['anth', 'ant'];
        const foreignLangPrefixes = [
            // Romance languages
            'span', 'spn', 'spanish',
            'fren', 'fr', 'french',
            'ital', 'it', 'italian',
            'port', 'pt', 'portuguese',
            'rom', 'romanian',
            'cata', 'catalan',
            // Germanic languages
            'germ', 'ger', 'german',
            'dutch', 'neth',
            'swed', 'swedish',
            'nor', 'norwegian',
            'dan', 'danish',
            // Slavic languages
            'russ', 'rus', 'russian',
            'pol', 'polish',
            'czec', 'cz', 'czech',
            'serb', 'serbian',
            'ukr', 'ukrainian',
            // Asian languages
            'japn', 'jpn', 'japanese',
            'chin', 'chn', 'chinese', 'mand', 'mandr', 'mandarin', 'cant', 'cantonese',
            'kor', 'korean',
            'thai',
            'viet', 'vietnamese',
            'hind', 'hindi', 'urdu',
            'pers', 'persian', 'farsi',
            // Middle Eastern languages
            'arab', 'arabic',
            'hebr', 'heb', 'hebrew',
            'turk', 'turkish',
            // Other languages
            'lang', 'for', 'foreign', 'fl',
            'grek', 'greek',
            'latn', 'lat', 'latin',
            'asl', 'sign', 'ameslan', 'aslan'
        ];
        const historyPrefixes = ['hist', 'pols', 'govt', 'amst', 'const'];
        const commPrefixes = ['comm', 'spch', 'public', 'speak', 'present', 'com'];

        switch(true) {
            // --- Combined Chemistry Sequence ---
            case prereqName.includes('chemistry sequence (gen/org/biochem)'): // Match the new name
                const isGenChem = chemistryPrefixes.some(prefix => courseCode.startsWith(prefix)) &&
                       (courseName.includes('general') || courseName.includes('inorganic') ||
                        courseCode.match(/\d+/)?.some(num => parseInt(num) < 200));
                const isOrgChem = chemistryPrefixes.some(prefix => courseCode.startsWith(prefix)) &&
                       (courseName.includes('organic') ||
                        courseCode.match(/\d+/)?.some(num => parseInt(num) >= 200 && parseInt(num) < 400));
                const isBiochem = courseCode.startsWith('bch') || courseCode.startsWith('bioc') ||
                       courseName.includes('biochem');
                return isGenChem || isOrgChem || isBiochem; // Match if any chemistry type fits

            // --- Standard Categories ---
            case prereqName.includes('anatomy and physiology'):
                return (courseName.includes('anatomy') && courseName.includes('physiology')) ||
                       courseName.includes('a&p') ||
                       courseName.includes('anat & phys') ||
                       courseName.includes('anatomical and physiological') ||
                       courseCode.includes('a&p');

            case prereqName.includes('biochemistry'):
                return courseCode.startsWith('bch') || courseCode.startsWith('bioc') ||
                       courseName.includes('biochem');

            case prereqName.includes('microbiology'):
                return (biologyPrefixes.some(prefix => courseCode.startsWith(prefix)) &&
                       courseName.includes('micro')) ||
                       courseName.includes('microbiology');

            case prereqName.includes('medical terminology'):
                return courseName.includes('medical terminology') ||
                       (courseCode.startsWith('nurs') && courseName.includes('terminology'));

            case prereqName.includes('statistics & other math'): // Updated name
                return statisticsPrefixes.some(prefix => courseCode.startsWith(prefix)) ||
                       courseName.includes('statistics') ||
                       courseName.includes('statistical') ||
                       courseName.includes('algebra') ||
                       courseName.includes('calculus') ||
                       courseName.includes('precalculus') ||
                       courseName.includes('math');

            case prereqName.includes('psychology'):
                return psychologyPrefixes.some(prefix => courseCode.startsWith(prefix)) ||
                       courseName.includes('psychology');

            case prereqName.includes('english'):
                return englishPrefixes.some(prefix => courseCode.startsWith(prefix)) ||
                       courseName.includes('english') ||
                       courseName.includes('composition') ||
                       courseName.includes('writing');

            case prereqName.includes('physics'):
                return courseCode.startsWith('phys') ||
                       courseName.includes('physics');

            case prereqName.includes('organic chemistry'):
                return chemistryPrefixes.some(prefix => courseCode.startsWith(prefix)) &&
                       (courseName.includes('organic') || 
                        courseCode.match(/\d+/)?.some(num => parseInt(num) >= 200 && parseInt(num) < 400));

            case prereqName.includes('genetics'):
                return biologyPrefixes.some(prefix => courseCode.startsWith(prefix)) &&
                       courseName.includes('genetic');

            case prereqName.includes('humanities'):
                return courseName.includes('humanities') ||
                       courseName.includes('philosophy') ||
                       courseName.includes('literature') ||
                       courseName.includes('art') ||
                       courseName.includes('music') ||
                       courseName.includes('theater') ||
                       courseName.includes('religion');

            case prereqName.includes('social science'):
                return sociologyPrefixes.some(prefix => courseCode.startsWith(prefix)) ||
                       psychologyPrefixes.some(prefix => courseCode.startsWith(prefix)) ||
                       anthropologyPrefixes.some(prefix => courseCode.startsWith(prefix)) ||
                       courseName.includes('sociology') ||
                       courseName.includes('psychology') ||
                       courseName.includes('anthropology') ||
                       courseName.includes('economics') ||
                       courseName.includes('political science');

           case prereqName.includes('chemistry (general)'): // Updated category name
               // This logic correctly identifies general/inorganic chemistry
               return chemistryPrefixes.some(prefix => courseCode.startsWith(prefix)) &&
                      (courseName.includes('general') || courseName.includes('inorganic') ||
                       courseCode.match(/\d+/)?.some(num => parseInt(num) < 200));

           case prereqName.includes('biology (general)'): // Added logic for Biology (general)
               // Must have bio prefix but NOT contain keywords for more specific categories
               const isGeneralBio = biologyPrefixes.some(prefix => courseCode.startsWith(prefix)) &&
                      !courseName.includes('anatomy') &&
                      !courseName.includes('physiology') &&
                      !courseName.includes('micro') && // Exclude microbiology
                      !courseName.includes('genetic'); // Exclude genetics
               return isGeneralBio; // Correct return for Biology (general)

           case prereqName.includes('math'): // Added logic for Math
               // Exclude statistics as it's a separate category
               const isMath = (courseCode.startsWith('math') || courseCode.startsWith('mth')) &&
                              !courseName.includes('stat'); // Exclude statistics
               // Could add more specific checks for 'algebra', 'calculus', 'pre-calculus' if needed
               return isMath;

           case prereqName.includes('sociology'): // Broadened logic for Sociology
                const isSociologyRelated = ( // Use a different variable name just in case
                   sociologyPrefixes.some(prefix => courseCode.startsWith(prefix)) || // SOC code
                   anthropologyPrefixes.some(prefix => courseCode.startsWith(prefix)) || // ANTH code
                   courseName.includes('sociology') ||
                   courseName.includes('social welfare') ||
                   courseName.includes('social work') ||
                   courseName.includes('criminology') ||
                   courseName.includes('criminal justice') ||
                   courseName.includes('marriage and family') ||
                   courseName.includes('family studies') ||
                   courseName.includes('social ecology') ||
                   courseName.includes('anthropology') || // Added Anthropology
                   courseName.includes('gender studies') || // Added Gender Studies
                   courseName.includes('ethnic studies') // Added Ethnic Studies
               );
               return isSociologyRelated;

           case prereqName.includes('cpr'):
               return courseName.includes('cpr') ||
                      courseName.includes('cardiopulmonary resuscitation') ||
                      courseName.includes('basic life support');
                      
           case prereqName.includes('foreign language'):
                return foreignLangPrefixes.some(prefix => courseCode.startsWith(prefix)) ||
                       courseName.includes('spanish') ||
                       courseName.includes('french') ||
                       courseName.includes('german') ||
                       courseName.includes('italian') ||
                       courseName.includes('japanese') ||
                       courseName.includes('chinese') ||
                       courseName.includes('arabic') ||
                       courseName.includes('language');

            case prereqName.includes('american history/government'):
                return historyPrefixes.some(prefix => courseCode.startsWith(prefix)) ||
                       courseName.includes('history') ||
                       courseName.includes('american') ||
                       courseName.includes('political') ||
                       courseName.includes('government') ||
                       courseName.includes('constitution');

            case prereqName.includes('oral communication'):
                return commPrefixes.some(prefix => courseCode.startsWith(prefix)) ||
                       courseName.includes('speech') ||
                       courseName.includes('public speaking') ||
                       courseName.includes('oral') ||
                       courseName.includes('presentation') ||
                       courseName.includes('communication');

            default:
                return false;
        }
    };

    // Extract all courses from transcripts
    useEffect(() => {
        if (!existingTranscripts) return;
        const courses = [];
        console.log("Full transcript data:", JSON.stringify(existingTranscripts, null, 2));
        
        existingTranscripts.forEach(transcript => {
            console.log(`Processing transcript from ${transcript.institution}:`, transcript);
            const semesters = transcript.transcript_data?.semesters || [];
            semesters.forEach(semester => {
                semester.courses.forEach(course => {
                    // IMPORTANT: Prioritize the existing has_lab value from transcript data
                    // This is the value saved in the database from transcript verification
                    
                    console.log(`Raw course data for ${course.code}:`, course);
                    
                    // Access has_lab directly from the course object
                    const hasLab = course.has_lab === true;
                    
                    console.log(`Course ${course.code} - ${course.name} lab status:`, {
                        directHasLabValue: course.has_lab,
                        typeOfHasLab: typeof course.has_lab,
                        finalValue: hasLab
                    });
                    
                    // Add each course with full details including semester and unique identifier
                    courses.push({
                        ...course,
                        institution: transcript.institution,
                        term: semester.term,
                        year: semester.year,
                        has_lab: hasLab, // Use the value directly from transcript data
                        // Create a display string for the course
                        displayString: `${course.code} - ${course.name} (${course.credits} credits, Grade: ${course.grade}, ${transcript.institution} ${semester.term} ${semester.year}${hasLab ? ', Has Lab' : ', No Lab'})`
                    });
                });
            });
        });
        // Sort courses by institution, code, year, and term
        courses.sort((a, b) => {
            if (a.institution !== b.institution) return a.institution.localeCompare(b.institution);
            if (a.code !== b.code) return a.code.localeCompare(b.code);
            if (a.year !== b.year) return b.year.localeCompare(a.year); // Most recent first
            return b.term.localeCompare(a.term); // Most recent term first
        });
        setAllCourses(courses);
    }, [existingTranscripts]);

    // Combined effect to load saved prerequisites and process transcripts
    useEffect(() => {
        const loadDataAndProcess = async () => {
            try {
                // 1. Initialize with base structure
                let suggestedPrerequisites = {};
                Object.keys(prerequisites).forEach(key => {
                    suggestedPrerequisites[key] = { courses: [], manuallySelected: null };
                });

                // 2. Populate suggestions from all transcripts
                if (existingTranscripts) {
                    existingTranscripts.forEach(transcript => {
                        const semesters = transcript.transcript_data?.semesters || [];
                        semesters.forEach(semester => {
                            semester.courses.forEach(course => {
                                Object.keys(suggestedPrerequisites).forEach(prereqName => {
                                    if (courseMatchesPrereq(course.name, course.code, prereqName)) {
                                        // Avoid duplicates within suggestions for the same course code/institution
                                        const courseExists = suggestedPrerequisites[prereqName].courses.some(c =>
                                            c.code === course.code && c.institution === transcript.institution
                                        );
                                        if (!courseExists) {
                                            // IMPORTANT: Prioritize the existing has_lab value from transcript data
                                            // This is the value saved in the database from transcript verification
                                            const hasLab = course.has_lab === true;
                                            
                                            console.log(`Course ${course.code} - ${course.name} lab status:`, {
                                                directHasLabValue: course.has_lab,
                                                typeOfHasLab: typeof course.has_lab,
                                                finalValue: hasLab
                                            });
                                            
                                            suggestedPrerequisites[prereqName].courses.push({
                                                ...course,
                                                institution: transcript.institution,
                                                term: semester.term,
                                                year: semester.year,
                                                has_lab: hasLab // Use the value directly from transcript data
                                            });
                                        }
                                    }
                                });
                            });
                        });
                    });
                }

                // 3. Load saved prerequisites
                let savedPrereqsData = {};
                try {
                    // Get the current session token from Supabase
                    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
                    if (sessionError || !session?.access_token) {
                        throw new Error("User session not found or invalid.");
                    }
                    const apiToken = session.access_token;
                    
                    const response = await axios.get(
                        'http://127.0.0.1:8000/api/prerequisites/', 
                        {
                            headers: {
                                'Authorization': `Bearer ${apiToken}`
                            }
                        }
                    );
                    // Assuming response.data is an array like [{ prerequisite_name: '...', courses: [...] }, ...]
                    response.data.forEach(savedPrereq => {
                        savedPrereqsData[savedPrereq.prerequisite_name] = savedPrereq.courses;
                    });
                } catch (savedError) {
                     console.warn('Could not load saved prerequisites:', savedError);
                     // Continue without saved data if fetch fails
                }

                // 4. Merge: Start with suggestions, overwrite with saved data where available
                const finalPrerequisites = { ...suggestedPrerequisites };
                Object.keys(savedPrereqsData).forEach(prereqName => {
                    if (finalPrerequisites[prereqName]) { // Check if category exists in our initial state
                         // Overwrite suggested courses with explicitly saved courses
                        finalPrerequisites[prereqName].courses = savedPrereqsData[prereqName];
                    }
                });

                // 5. Update state
                setPrerequisites(finalPrerequisites);

            } catch (error) {
                console.error('Error processing prerequisite data:', error);
            }
        };

        loadDataAndProcess();
    }, [existingTranscripts, supabase]);

    const handleManualSelection = (prereqName, course) => {
        setPrerequisites(prev => ({
            ...prev,
            [prereqName]: {
                ...prev[prereqName],
                manuallySelected: course
            }
        }));
        setShowAddCourse(prev => ({
            ...prev,
            [prereqName]: false
        }));
    };

    const handleAddCourseToggle = (prereqName) => {
        setShowAddCourse(prev => ({
            ...prev,
            [prereqName]: !prev[prereqName]
        }));
        // Also expand the card when adding a course
        if (!expandedItems[prereqName]) {
            toggleExpand(prereqName);
        }
    };

    const handleManualAdd = (prereqName, course) => {
        setPrerequisites(prev => ({
            ...prev,
            [prereqName]: {
                courses: [...prev[prereqName].courses.filter(c => 
                    !(c.code === course.code && c.institution === course.institution)
                ), course],
                manuallySelected: course
            }
        }));
        setShowAddCourse(prev => ({
            ...prev,
            [prereqName]: false
        }));
    };

    const handleDeleteCourse = (prereqName, courseToDelete) => {
        setPrerequisites(prev => ({
            ...prev,
            [prereqName]: {
                ...prev[prereqName],
                courses: prev[prereqName].courses.filter(course => 
                    !(course.code === courseToDelete.code && course.institution === courseToDelete.institution)
                ),
                // If the deleted course was selected, clear the selection
                manuallySelected: prev[prereqName].manuallySelected?.code === courseToDelete.code ? null : prev[prereqName].manuallySelected
            }
        }));
    };

    const handleSavePrerequisites = async () => {
        setIsSaving(true);
        setSaveMessage('');
        
        try {
            // Get the current session token from Supabase
            const { data: { session }, error: sessionError } = await supabase.auth.getSession();
            if (sessionError || !session?.access_token) {
                throw new Error("User session not found or invalid.");
            }
            const apiToken = session.access_token;
            
            // Format the data for saving - include all courses, not just selected ones
            const prerequisiteData = Object.entries(prerequisites).map(([name, data]) => ({
                prerequisite_name: name,
                courses: data.courses.map(course => ({
                    code: course.code,
                    name: course.name,
                    credits: course.credits,
                    grade: course.grade,
                    institution: course.institution,
                    term: course.term,
                    year: course.year,
                    has_lab: course.has_lab || false // Make sure has_lab is included
                }))
            })).filter(item => item.courses.length > 0);

            await axios.post(
                'http://127.0.0.1:8000/api/prerequisites/',
                { prerequisites: prerequisiteData },
                {
                    headers: {
                        'Authorization': `Bearer ${apiToken}`,
                        'Content-Type': 'application/json'
                    }
                }
            );

            setSaveMessage('Prerequisites saved successfully!');
            setTimeout(() => setSaveMessage(''), 3000); // Clear message after 3 seconds
        } catch (error) {
            console.error('Error saving prerequisites:', error);
            setSaveMessage('Error saving prerequisites. Please try again.');
        } finally {
            setIsSaving(false);
        }
    };

    const handleLabToggle = (prereqName, course) => {
        // Allow users to manually toggle lab status
        // By default, lab status is determined by whether "lab" appears in the course name or code
        setPrerequisites(prev => ({
            ...prev,
            [prereqName]: {
                ...prev[prereqName],
                courses: prev[prereqName].courses.map(c =>
                    c.code === course.code && c.institution === course.institution ? { ...c, has_lab: !c.has_lab } : c
                ),
                manuallySelected: course
            }
        }));
    };

    return (
        <div className="profile-section">
            <div className="prerequisites-header">
                <h2>Prerequisite Classes</h2>
                <div className="save-prerequisites">
                    <button 
                        onClick={handleSavePrerequisites}
                        className="save-prerequisites-btn"
                        disabled={isSaving}
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                            <polyline points="17 21 17 13 7 13 7 21"></polyline>
                            <polyline points="7 3 7 8 15 8"></polyline>
                        </svg>
                        {isSaving ? 'Saving...' : 'Save Prerequisite Classes'}
                    </button>
                    <button 
                        onClick={() => {
                            // Clear existing prerequisites and re-populate from current transcripts
                            // Initialize with base structure
                            let suggestedPrerequisites = {};
                            Object.keys(prerequisites).forEach(key => {
                                suggestedPrerequisites[key] = { courses: [], manuallySelected: null };
                            });
                            
                            console.log("Refreshing prerequisites from transcripts:", existingTranscripts);

                            // Populate from current transcripts only
                            if (existingTranscripts) {
                                existingTranscripts.forEach(transcript => {
                                    const semesters = transcript.transcript_data?.semesters || [];
                                    semesters.forEach(semester => {
                                        semester.courses.forEach(course => {
                                            Object.keys(suggestedPrerequisites).forEach(prereqName => {
                                                if (courseMatchesPrereq(course.name, course.code, prereqName)) {
                                                    // Avoid duplicates
                                                    const courseExists = suggestedPrerequisites[prereqName].courses.some(c =>
                                                        c.code === course.code && c.institution === transcript.institution
                                                    );
                                                    if (!courseExists) {
                                                        // IMPORTANT: Prioritize the existing has_lab value from transcript data
                                                        // This is the value saved in the database from transcript verification
                                                        const hasLab = course.has_lab === true;
                                                        
                                                        console.log(`Course ${course.code} - ${course.name} lab status:`, {
                                                            directHasLabValue: course.has_lab,
                                                            typeOfHasLab: typeof course.has_lab,
                                                            finalValue: hasLab
                                                        });
                                                        
                                                        suggestedPrerequisites[prereqName].courses.push({
                                                            ...course,
                                                            institution: transcript.institution,
                                                            term: semester.term,
                                                            year: semester.year,
                                                            has_lab: hasLab // Use the value directly from transcript data
                                                        });
                                                    }
                                                }
                                            });
                                        });
                                    });
                                });
                            }
                            
                            // Update state with new data
                            setPrerequisites(suggestedPrerequisites);
                            setSaveMessage('Prerequisites refreshed from current transcripts. Remember to save changes.');
                            setTimeout(() => setSaveMessage(''), 3000);
                        }}
                        className="refresh-prereqs-btn"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M21.5 2v6h-6M2.5 22v-6h6M2 11.5a10 10 0 0 1 18.8-4.3M22 12.5a10 10 0 0 1-18.8 4.2"/>
                        </svg>
                        Refresh from Transcripts
                    </button>
                    {saveMessage && (
                        <span className={`save-message ${saveMessage.includes('Error') ? 'error' : 'success'}`}>
                            {saveMessage}
                        </span>
                    )}
                </div>
            </div>
            <div className="prerequisites-container">
                {Object.entries(prerequisites).map(([prereqName, prereqData]) => (
                    <div key={prereqName} className={`prerequisite-item ${expandedItems[prereqName] ? 'expanded' : ''}`}>
                        <div className="prerequisite-header" onClick={() => toggleExpand(prereqName)}>
                            <h3>
                                {prereqName}
                                {prereqData.courses.length > 0 && (
                                    <span className="course-count">{prereqData.courses.length}</span>
                                )}
                            </h3>
                            <svg xmlns="http://www.w3.org/2000/svg" className="expand-icon" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <polyline points="6 9 12 15 18 9"></polyline>
                            </svg>
                        </div>
                        
                        <div className="prerequisite-content">
                            <div className="prerequisite-actions">
                                <button 
                                    onClick={() => handleAddCourseToggle(prereqName)}
                                    className="add-course-btn"
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                        <line x1="12" y1="5" x2="12" y2="19"></line>
                                        <line x1="5" y1="12" x2="19" y2="12"></line>
                                    </svg>
                                    {showAddCourse[prereqName] ? 'Cancel' : 'Add Course'}
                                </button>
                            </div>
                            
                            {showAddCourse[prereqName] && (
                                <div className="course-dropdown">
                                    <select 
                                        onChange={(e) => {
                                            const selectedCourse = allCourses[parseInt(e.target.value)];
                                            if (selectedCourse) {
                                                handleManualAdd(prereqName, selectedCourse);
                                            }
                                        }}
                                        value=""
                                        className="course-select"
                                    >
                                        <option value="">Select a course...</option>
                                        {allCourses.map((course, index) => (
                                            <option key={index} value={index}>
                                                {course.code} - {course.name} ({course.credits} cr, {course.institution})
                                            </option>
                                        ))}
                                    </select>
                                </div>
                            )}

                            {prereqData.courses.length > 0 ? (
                                <div className="matching-courses">
                                    <h4>Matching Courses:</h4>
                                    {prereqData.courses.map((course, index) => (
                                        <div 
                                            key={index} 
                                            className={`prerequisite-course-item ${course.has_lab ? 'has-lab' : ''}`}
                                        >
                                            <div className="course-info">
                                                <div className="course-title">
                                                    {course.code} - {course.name}
                                                    <span className={`grade-badge ${getGradeBadgeClass(course.grade)}`}>
                                                        {course.grade}
                                                    </span>
                                                    {course.has_lab ? (
                                                        <span className="lab-badge has-lab">Lab</span>
                                                    ) : (
                                                        <span className="lab-badge no-lab">No Lab</span>
                                                    )}
                                                </div>
                                                <span className="course-details">{course.credits} credits</span>
                                                <span className="course-institution">{course.institution} - {course.term} {course.year}</span>
                                                <div className="form-check">
                                                    <input 
                                                        className="form-check-input" 
                                                        type="checkbox" 
                                                        id={`lab-${prereqName}-${index}`}
                                                        checked={course.has_lab || false}
                                                        onChange={() => handleLabToggle(prereqName, course)}
                                                    />
                                                    <label className="form-check-label" htmlFor={`lab-${prereqName}-${index}`}>
                                                        Has lab component
                                                    </label>
                                                </div>
                                            </div>
                                            <button
                                                onClick={() => handleDeleteCourse(prereqName, course)}
                                                className="delete-course-btn"
                                                title="Remove this course"
                                            >
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M6 6L18 18M6 18L18 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                                                </svg>
                                            </button>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <p className="no-matches">No matching courses found</p>
                            )}
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default PrerequisiteClasses; 