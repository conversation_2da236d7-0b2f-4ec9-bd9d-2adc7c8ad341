"""
Django management command for migrating PA program data from JSON files
"""

import logging
import os
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings

from gradapp.services.program_data_migration import ProgramDataMigrationProcessor, MigrationError


class Command(BaseCommand):
    help = 'Migrate PA program data from JSON files to database'

    def add_arguments(self, parser):
        parser.add_argument(
            'json_directory',
            type=str,
            help='Path to directory containing JSON program files'
        )
        
        parser.add_argument(
            '--batch-size',
            type=int,
            default=50,
            help='Number of programs to process in each batch (default: 50)'
        )
        
        parser.add_argument(
            '--log-level',
            choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
            default='INFO',
            help='Set the logging level (default: INFO)'
        )
        
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Perform a dry run without making database changes'
        )
        
        parser.add_argument(
            '--continue-on-error',
            action='store_true',
            help='Continue processing even if individual programs fail'
        )
        
        parser.add_argument(
            '--report-file',
            type=str,
            help='Path to save detailed migration report (JSON format)'
        )

    def handle(self, *args, **options):
        """
        Main command handler
        """
        # Set up logging level
        log_level = getattr(logging, options['log_level'])
        
        # Validate directory path
        json_directory = options['json_directory']
        if not os.path.exists(json_directory):
            raise CommandError(f"Directory does not exist: {json_directory}")
        
        if not os.path.isdir(json_directory):
            raise CommandError(f"Path is not a directory: {json_directory}")
        
        # Initialize processor
        try:
            processor = ProgramDataMigrationProcessor(
                json_directory_path=json_directory,
                log_level=log_level
            )
        except MigrationError as e:
            raise CommandError(f"Failed to initialize processor: {str(e)}")
        
        # Display configuration
        self.stdout.write(
            self.style.SUCCESS(f"Starting program data migration...")
        )
        self.stdout.write(f"Source directory: {json_directory}")
        self.stdout.write(f"Batch size: {options['batch_size']}")
        self.stdout.write(f"Log level: {options['log_level']}")
        
        if options['dry_run']:
            self.stdout.write(
                self.style.WARNING("DRY RUN MODE - No database changes will be made")
            )
        
        # Process programs
        try:
            if options['dry_run']:
                # For dry run, we would implement validation-only logic
                self.stdout.write(
                    self.style.WARNING("Dry run functionality not yet implemented")
                )
                return
            
            # Run the actual migration
            report = processor.process_all_programs(
                batch_size=options['batch_size']
            )
            
            # Display results
            self._display_results(report)
            
            # Save report if requested
            if options['report_file']:
                self._save_report(report, options['report_file'])
            
            # Check for errors
            if report['errors'] and not options['continue_on_error']:
                raise CommandError(
                    f"Migration completed with {len(report['errors'])} errors. "
                    "Use --continue-on-error to ignore errors."
                )
            
        except Exception as e:
            if options['continue_on_error']:
                self.stdout.write(
                    self.style.ERROR(f"Migration failed but continuing: {str(e)}")
                )
            else:
                raise CommandError(f"Migration failed: {str(e)}")
        
        self.stdout.write(
            self.style.SUCCESS("Program data migration completed successfully!")
        )

    def _display_results(self, report):
        """
        Display migration results in a formatted way
        
        Args:
            report: Migration report dictionary
        """
        stats = report['statistics']
        
        self.stdout.write("\n" + "="*50)
        self.stdout.write(self.style.SUCCESS("MIGRATION RESULTS"))
        self.stdout.write("="*50)
        
        # Statistics
        self.stdout.write(f"Total files processed: {stats['total_files']}")
        self.stdout.write(f"Successfully processed: {stats['processed_successfully']}")
        self.stdout.write(f"Failed processing: {stats['failed_processing']}")
        self.stdout.write(f"Programs created: {stats['programs_created']}")
        self.stdout.write(f"Programs updated: {stats['programs_updated']}")
        self.stdout.write(f"Schools created: {stats['schools_created']}")
        self.stdout.write(f"Schools updated: {stats['schools_updated']}")
        
        # Success rate
        success_rate = report['success_rate']
        if success_rate >= 90:
            style = self.style.SUCCESS
        elif success_rate >= 70:
            style = self.style.WARNING
        else:
            style = self.style.ERROR
            
        self.stdout.write(style(f"Success rate: {success_rate:.2f}%"))
        
        # Errors summary
        if report['errors']:
            self.stdout.write(f"\n{self.style.ERROR('ERRORS ENCOUNTERED:')}")
            
            # Group errors by type
            error_types = {}
            for error in report['errors']:
                error_type = error.get('type', 'unknown')
                if error_type not in error_types:
                    error_types[error_type] = []
                error_types[error_type].append(error)
            
            for error_type, errors in error_types.items():
                self.stdout.write(f"\n{error_type.upper()}: {len(errors)} errors")
                
                # Show first few errors of each type
                for error in errors[:3]:
                    file_name = error.get('file', 'Unknown')
                    error_msg = error.get('error', 'Unknown error')
                    self.stdout.write(f"  - {file_name}: {error_msg}")
                
                if len(errors) > 3:
                    self.stdout.write(f"  ... and {len(errors) - 3} more")
        
        # Warnings summary
        if report['warnings']:
            self.stdout.write(f"\n{self.style.WARNING('WARNINGS:')}")
            for warning in report['warnings'][:5]:
                self.stdout.write(f"  - {warning}")
            
            if len(report['warnings']) > 5:
                self.stdout.write(f"  ... and {len(report['warnings']) - 5} more warnings")

    def _save_report(self, report, report_file):
        """
        Save detailed report to JSON file
        
        Args:
            report: Migration report dictionary
            report_file: Path to save the report
        """
        import json
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            self.stdout.write(
                self.style.SUCCESS(f"Detailed report saved to: {report_file}")
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Failed to save report: {str(e)}")
            )