# Add this to your existing users/views.py file

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.db import transaction
from django.contrib.auth import get_user_model
from .models import UserProfile
from gradapp.models import (
    TranscriptRecord, UserTranscript, PrerequisiteSelection,
    ProgramMatchingResult
)
import boto3
import os
import logging

logger = logging.getLogger(__name__)
User = get_user_model()

@api_view(['GET', 'PATCH'])
@permission_classes([IsAuthenticated])
def profile_view(request):
    """
    GET: Retrieve user profile information
    PATCH: Update user profile information
    """
    user = request.user
    
    if request.method == 'GET':
        # Return user profile data
        try:
            # Get or create the user profile
            profile, created = UserProfile.objects.get_or_create(user=user)
            
            # Return user profile data
            profile_data = {
                'id': user.id,
                'email': user.email,
                'username': user.username,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'date_joined': user.date_joined,
                'last_login': user.last_login,
                
                # Add UserProfile fields
                'date_of_birth': profile.date_of_birth,
                'gender': profile.gender,
                'direct_patient_care_hours': profile.direct_patient_care_hours,
                'shadowing_hours': profile.shadowing_hours,
                'gre_verbal_score': profile.gre_verbal_score,
                'gre_quantitative_score': profile.gre_quantitative_score,
                'gre_analytical_writing_score': profile.gre_analytical_writing_score,
                'gre_test_date': profile.gre_test_date,
                'pa_cat_composite_ss': profile.pa_cat_composite_ss,
                'pa_cat_test_date': profile.pa_cat_test_date,
                'has_taken_casper': profile.has_taken_casper,
                'casper_test_date': profile.casper_test_date,
                'has_taken_pa_cat': profile.has_taken_pa_cat,
            }
            
            return Response(profile_data, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error retrieving profile for user {user.id}: {str(e)}")
            return Response(
                {'error': 'Failed to retrieve profile data'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    elif request.method == 'PATCH':
        # Update user profile data
        try:
            data = request.data
            
            # Update User model fields
            if 'first_name' in data:
                user.first_name = data['first_name']
            if 'last_name' in data:
                user.last_name = data['last_name']
                
            user.save()
            
            # Update UserProfile fields if the model exists
            profile_updated = False
            # Get or create the user profile
            profile, created = UserProfile.objects.get_or_create(user=user)
            
            # Map of field names to their types for validation
            profile_fields = {
                'date_of_birth': str,  # Date as string
                'gender': str,
                'direct_patient_care_hours': float,
                'shadowing_hours': float,
                'gre_verbal_score': int,
                'gre_quantitative_score': int,
                'gre_analytical_writing_score': float,
                'gre_test_date': str,  # Date as string
                'pa_cat_composite_ss': int,
                'pa_cat_test_date': str,  # Date as string
                'has_taken_casper': bool,
                'casper_test_date': str,  # Date as string
                'has_taken_pa_cat': bool,
            }
            
            profile_updated = False
            for field, field_type in profile_fields.items():
                if field in data:
                    # Handle None values
                    if data[field] is None:
                        setattr(profile, field, None)
                        profile_updated = True
                    else:
                        try:
                            # Convert to appropriate type if needed
                            if field_type == bool and isinstance(data[field], str):
                                value = data[field].lower() == 'true'
                            else:
                                value = field_type(data[field])
                            
                            setattr(profile, field, value)
                            profile_updated = True
                        except (ValueError, TypeError):
                            return Response(
                                {'error': f'Invalid value for {field}'},
                                status=status.HTTP_400_BAD_REQUEST
                            )
            
            if profile_updated or created:
                profile.save()
            
            return Response({'message': 'Profile updated successfully'}, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error updating profile for user {user.id}: {str(e)}")
            return Response(
                {'error': 'Failed to update profile data'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def delete_account(request):
    """
    Permanently delete user account and all associated data
    """
    try:
        user = request.user
        user_id = user.id
        
        with transaction.atomic():
            # Delete S3 files first (before database records)
            try:
                # Get all transcript files for this user
                user_transcripts = UserTranscript.objects.filter(user=user)
                
                if user_transcripts.exists():
                    s3_client = boto3.client(
                        's3',
                        aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
                        aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY'),
                        region_name=os.getenv('AWS_REGION', 'us-east-2')
                    )
                    
                    bucket_name = 'your-s3-bucket-name'  # Replace with your actual bucket name
                    
                    for transcript in user_transcripts:
                        if transcript.s3_key:
                            try:
                                s3_client.delete_object(
                                    Bucket=bucket_name,
                                    Key=transcript.s3_key
                                )
                                logger.info(f"Deleted S3 file: {transcript.s3_key}")
                            except Exception as s3_error:
                                logger.error(f"Failed to delete S3 file {transcript.s3_key}: {s3_error}")
                                # Continue with deletion even if S3 fails
                
            except Exception as s3_error:
                logger.error(f"S3 cleanup error for user {user_id}: {s3_error}")
                # Continue with database deletion even if S3 cleanup fails
            
            # Delete database records in correct order (respecting foreign keys)
            
            # 1. Delete program match results
            ProgramMatchingResult.objects.filter(user=user).delete()
            
            # 2. Delete prerequisite selections
            PrerequisiteSelection.objects.filter(user=user).delete()
            
            # 3. Delete transcript records
            TranscriptRecord.objects.filter(user=user).delete()
            
            # 4. Delete user transcript files
            UserTranscript.objects.filter(user=user).delete()
            
            # 5. Delete user profile (if you have a separate UserProfile model)
            if hasattr(user, 'userprofile'):
                user.userprofile.delete()
            
            # 6. Finally delete the user account
            user_email = user.email  # Store for logging
            user.delete()
            
            logger.info(f"Successfully deleted account for user: {user_email} (ID: {user_id})")
            
        return Response({
            'message': 'Account successfully deleted',
            'deleted_user_id': user_id
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Account deletion failed for user {request.user.id}: {str(e)}")
        return Response({
            'error': 'Failed to delete account',
            'details': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def account_info(request):
    """
    Get account information and statistics
    """
    try:
        user = request.user
        
        # Count user data
        transcript_count = TranscriptRecord.objects.filter(user=user).count()
        file_count = UserTranscript.objects.filter(user=user).count()
        prerequisite_count = PrerequisiteSelection.objects.filter(user=user).count()
        
        return Response({
            'user_id': user.id,
            'email': user.email,
            'date_joined': user.date_joined,
            'last_login': user.last_login,
            'data_summary': {
                'transcript_records': transcript_count,
                'uploaded_files': file_count,
                'prerequisite_mappings': prerequisite_count,
            }
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Failed to get account info for user {request.user.id}: {str(e)}")
        return Response({
            'error': 'Failed to retrieve account information'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)