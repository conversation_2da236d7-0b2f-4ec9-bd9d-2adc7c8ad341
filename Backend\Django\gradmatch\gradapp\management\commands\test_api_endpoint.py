from django.core.management.base import BaseCommand
from django.test import RequestFactory
from django.contrib.auth.models import User
from gradapp.views import get_program_list
import traceback

class Command(BaseCommand):
    help = 'Test the get_program_list API endpoint directly'

    def handle(self, *args, **options):
        try:
            # Create a test request
            factory = RequestFactory()
            request = factory.get('/api/programs/')
            
            # Create a test user and authenticate the request
            user = User.objects.first()
            if not user:
                self.stdout.write(self.style.ERROR('No users found in database'))
                return
                
            request.user = user
            
            self.stdout.write(f'Testing API endpoint with user: {user.username}')
            
            # Call the view function directly
            response = get_program_list(request)
            
            self.stdout.write(f'Response status: {response.status_code}')
            
            if response.status_code == 200:
                self.stdout.write(self.style.SUCCESS('API endpoint working correctly!'))
                # Parse the JSON response to check structure
                import json
                data = json.loads(response.content)
                if 'programs' in data:
                    program_count = len(data['programs'])
                    self.stdout.write(f'Returned {program_count} programs')
                    
                    if program_count > 0:
                        # Check first program structure
                        first_program = data['programs'][0]
                        self.stdout.write(f'First program: {first_program.get("name", "Unknown")}')
                        
                        # Check if enhanced data is present
                        if 'program_data' in first_program:
                            program_data = first_program['program_data']
                            self.stdout.write('Enhanced data sections found:')
                            for section in program_data.keys():
                                self.stdout.write(f'  - {section}')
                        else:
                            self.stdout.write(self.style.WARNING('No program_data found in response'))
                else:
                    self.stdout.write(self.style.WARNING('No programs key found in response'))
            else:
                self.stdout.write(self.style.ERROR(f'API returned error status: {response.status_code}'))
                self.stdout.write(f'Response content: {response.content.decode()}')
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error testing API endpoint: {str(e)}'))
            self.stdout.write(f'Traceback: {traceback.format_exc()}')
