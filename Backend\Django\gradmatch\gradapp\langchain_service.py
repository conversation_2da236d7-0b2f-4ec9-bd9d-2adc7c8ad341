##THIS IS LANGCHAIN_SERVICE.PY. This is the file that uses does the JSON processing and langchain initalization. This will pull the transcript data from the ProcessTranscript.py file.

import os
import logging
from voyageai import Client as VoyageClient
from langchain_text_splitters import RecursiveCharacterTextSplitter
from pinecone import Pinecone, ServerlessSpec
from langchain_pinecone import PineconeVectorStore
import json
from langchain_community.chat_message_histories import ChatMessageHistory
from langchain_core.runnables import RunnablePassthrough
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI, OpenAIEmbeddings
from langsmith import Client
from langsmith import traceable
from langchain_core.documents import Document
from dotenv import load_dotenv
from PIL import Image
import io
import time
from typing import Union, List, Dict, Any, Optional, Tuple
import base64
import boto3
from botocore.exceptions import ClientError
from langchain_community.document_loaders import JSONLoader
import re
from typing import TypedDict
from langchain_pinecone import PineconeVectorStore
import tempfile
from typing import Optional
from langchain_core.embeddings import Embeddings
from datetime import datetime
from typing import Union, List, Dict, Any, Optional, Tuple
from langchain_core.messages import HumanMessage


# Load environment variables
load_dotenv()

# Validate required API keys
required_keys = ["VOYAGE_API_KEY", "PINECONE_API_KEY", "OPENAI_API_KEY", "AWS_ACCESS_KEY_ID", "AWS_SECRET_ACCESS_KEY"]
missing_keys = [key for key in required_keys if not os.getenv(key)]
if missing_keys:
    raise EnvironmentError(f"Missing required API keys: {', '.join(missing_keys)}")

# Message history persistence
class MessageStore:
    _store = {}
    
    @staticmethod
    def get_history(session_id: str) -> ChatMessageHistory:
        if session_id not in MessageStore._store:
            MessageStore._store[session_id] = ChatMessageHistory()
        return MessageStore._store[session_id]
    
    @staticmethod
    def add_message(session_id: str, message: str, is_human: bool = True):
        history = MessageStore.get_history(session_id)
        if is_human:
            history.add_user_message(message)
        else:
            history.add_ai_message(message)
    
    @staticmethod
    def get_messages(session_id: str) -> List[Dict[str, str]]:
        history = MessageStore.get_history(session_id)
        return history.messages

# AWS S3 Configuration
AWS_REGION = "us-east-2"
S3_BUCKET = "gradmatch"
UNR_JSON_KEY = "UNR.JSON"
RFUMS_JSON_KEY = "RFUMS.JSON"
PDF_KEY = "DemoTranscript.pdf"
INDEX_NAME = "grad1"

# LangSmith setup
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_ENDPOINT"] = "https://api.smith.langchain.com"
os.environ["LANGCHAIN_PROJECT"] = "GradMatch"

# Initialize LangSmith client
client = Client()

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Initialize VoyageAI client
voyage_client = VoyageClient(
    api_key=os.getenv("VOYAGE_API_KEY"),
    max_retries=3
)

# Define VoyageEmbeddings class
class VoyageEmbeddings(Embeddings):
    def __init__(self, voyage_client):
        self.client = voyage_client
        self.model = "voyage-multimodal-3"
        self.dimension = 1024

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """Embed a list of documents"""
        formatted_inputs = [{"content": [{"type": "text", "text": text}]} for text in texts]
        result = self.client.multimodal_embed(
            inputs=formatted_inputs,
            model=self.model,
            input_type="document",
            truncation=True
        )
        return result.embeddings

    def embed_query(self, text: str) -> List[float]:
        """Embed a query"""
        return self.embed_documents([text])[0]

# Initialize embeddings
voyage_embeddings = VoyageEmbeddings(voyage_client)

# Initialize Pinecone client
pc = Pinecone(api_key=os.environ["PINECONE_API_KEY"])

# Get list of existing indexes
existing_indexes = [index_info["name"] for index_info in pc.list_indexes()]

# Update index creation if needed
if INDEX_NAME not in existing_indexes:
    pc.create_index(
        name=INDEX_NAME,
        dimension=1024,  # Match VoyageEmbeddings dimension
        metric='cosine',
        spec=ServerlessSpec(cloud='aws', region='us-west-2')
    )

index = pc.Index(INDEX_NAME)

# Initialize the vector store with VoyageAI embeddings
vectorstore = PineconeVectorStore(
    index=index,
    embedding=voyage_embeddings,
    text_key="content"
)

# Initialize LLM
llm = ChatOpenAI(
    model_name="o1-mini-2024-09-12",
    api_key=os.getenv("OPENAI_API_KEY")
)

# Custom exceptions
class VoyageAPIError(Exception):
    """Base exception for Voyage API errors"""
    pass

class VoyageRateLimitError(VoyageAPIError):
    """Raised when hitting API rate limits"""
    pass

class VoyageTokenLimitError(VoyageAPIError):
    """Raised when exceeding token limits"""
    pass

class VoyageImageError(VoyageAPIError):
    """Raised when there are issues with image processing"""
    pass




def store_conversation(session_id: str, messages: List[Dict]):
    """Store conversation history in vector store"""
    try:
        doc = Document(
            page_content=str(messages),
            metadata={
                "type": "conversation_history",
                "session_id": session_id
            }
        )
        
        vectorstore.add_documents(
            documents=[doc],
            namespace="conversations"
        )
        return True
    except Exception as e:
        logger.error(f"Error storing conversation: {str(e)}")
        return False        
    
def validate_vector_dimensions(vector):
    """Validate vector dimensions match expected size (1024 for voyage-multimodal-3)"""
    if len(vector) != 1024:
        raise ValueError(f"Vector dimension mismatch. Expected 1024, got {len(vector)}")
    return True

def truncate_text(text: str, chunk_size: int = 75, chunk_overlap: int = 25) -> List[str]:
    """Split text into chunks using RecursiveCharacterTextSplitter"""
    splitter = RecursiveCharacterTextSplitter(
        chunk_size=chunk_size,
        chunk_overlap=chunk_overlap,
        separators=[
            "\n\n\n",    # Major section breaks
            "\n\n",      # Paragraph breaks
            "\nTerm",    # Term sections
            "\n",        # Line breaks
            ". ",        # Sentences
            ", ",        # Clauses
            " ",         # Words
            ""          # Characters
        ],
        keep_separator=True
    )
    return splitter.split_text(text)

def get_message_history(session_id: str) -> ChatMessageHistory:
    session_store = {}
    if session_id not in session_store:
        session_store[session_id] = ChatMessageHistory()
    return session_store[session_id]


def extract_program_profile(json_data: Dict[str, Any]) -> str:
    """Extract class profile information from program data"""
    try:
        program_data = json_data.get('program_comparison', {})
        profile_info = []
        
        for school, school_data in program_data.items():
            # Look specifically for class profile information
            class_profile = school_data.get('Class Profile', {})
            if class_profile:
                profile_info.append(f"School: {school}")
                for key, value in class_profile.items():
                    profile_info.append(f"{key}: {value}")
                    
        return "\n".join(profile_info) if profile_info else "No class profile information available."
        
    except Exception as e:
        logger.error(f"Error extracting program profile: {str(e)}")
        return "Error processing program information"

def create_vector_with_metadata(content, source_identifier):
    """Create a vector with metadata from text/image content"""
    try:
        # Format content for multimodal embedding
        if isinstance(content, str):
            # Format text-only content
            formatted_input = [{
                "content": [{"type": "text", "text": content}]
            }]
        elif isinstance(content, list):
            # Format multimodal content
            formatted_content = []
            for item in content:
                if isinstance(item, str):
                    formatted_content.append({"type": "text", "text": item})
                elif isinstance(item, Image.Image):
                    # Convert PIL Image to base64
                    buffered = io.BytesIO()
                    item.save(buffered, format="PNG")
                    img_str = base64.b64encode(buffered.getvalue()).decode()
                    formatted_content.append({
                        "type": "image_base64",
                        "image_base64": f"data:image/png;base64,{img_str}"
                    })
            formatted_input = [{"content": formatted_content}]
        else:
            raise TypeError("Content must be either a string or a list of strings/images.")

        vector_result = voyage_client.multimodal_embed(
            inputs=formatted_input,
            model="voyage-multimodal-3",
            input_type="document",
            truncation=True
        )
        vector_embedding = vector_result.embeddings[0]
        validate_vector_dimensions(vector_embedding)

        # Store original content in metadata
        content_for_metadata = str(content[0]) if isinstance(content, list) else str(content)

        return {
            "id": f"{source_identifier}_{hash(content_for_metadata)}",
            "values": vector_embedding,
            "metadata": {
                "source": source_identifier,
                "content": content_for_metadata
            }
        }
    except Exception as e:
        logger.error(f"Error creating vector: {str(e)}")
        raise     
    


def query_and_rerank(query: str, namespace: str, top_k: int = 5):
    """Query vectors and rerank results"""
    try:
        # Get results for each school separately
        unr_results = vectorstore.similarity_search(
            query + " UNR Nevada",  # Add school-specific terms
            k=10,
            namespace=namespace,
            filter={"source": "UNR"}  # Filter for UNR specifically
        )
        
        rfums_results = vectorstore.similarity_search(
            query + " RFUMS Rosalind Franklin",  # Add school-specific terms
            k=10,
            namespace=namespace,
            filter={"source": "RFUMS"}  # Filter for RFUMS specifically
        )
        
        # Combine results
        results = []
        if unr_results:
            results.extend(unr_results[:top_k])
        if rfums_results:
            results.extend(rfums_results[:top_k])
            
        logger.debug(f"Query results - UNR: {len(unr_results)}, RFUMS: {len(rfums_results)}")
        
        return results, []
        
    except Exception as e:
        logger.error(f"Error in similarity search: {str(e)}")
        return [], []


    
def get_stored_analysis(session_id: str, user_id: int) -> Optional[str]:
    """Retrieve stored transcript analysis from PostgreSQL"""
    try:
        logger.debug(f"Attempting to retrieve transcript for user {user_id}")
        
        from .models import TranscriptRecord
        
        # Get the most recent approved transcript for the user
        transcript = TranscriptRecord.objects.filter(
            user_id=user_id,
            validation_status='approved'
        ).order_by('-updated_at').first()
        
        if transcript:
            logger.info("Found approved transcript")
            # Convert transcript data to JSON string
            return json.dumps(transcript.transcript_data)
            
        # If no approved transcript exists, inform the user
        logger.warning(f"No approved transcript found for user {user_id}")
        return None
            
    except Exception as e:
        logger.error(f"Error retrieving transcript: {str(e)}", exc_info=True)
        return None



def create_success_response(response: str, transcript_chunks: List[Dict], 
                          program_content: Dict[str, Any]) -> Dict[str, Any]:
    """Create standardized success response"""
    return {
        "answer": response,
        "sources": {
            "transcript": [chunk["content"] if isinstance(chunk, dict) else str(chunk) 
                         for chunk in transcript_chunks],
            "programs": {
                "unr": program_content.get("unr", []),
                "rfums": program_content.get("rfums", [])
            }
        }
    }

def create_combined_content(stored_analysis: Optional[str], program_content: Dict[str, Any], 
                          transcript_text: str, transcript_chunks: List[Dict]) -> str:
    """Create combined content for LLM processing"""
    content_parts = []
    
    # Add stored transcript analysis
    if stored_analysis:
        content_parts.append("### Transcript Analysis\n" + stored_analysis)
    
    # Add program information
    if program_content:
        for school, results in program_content.items():
            for info in results:
                # Get institution name from the nested content structure
                institution_name = info.get('content', {}).get('institution_info', {}).get('name', school.upper())
                content_parts.append(f"\n### {institution_name} Information")
                
                # Add the full content
                if 'content' in info:
                    for section, data in info['content'].items():
                        if data and isinstance(data, dict):
                            content_parts.append(f"\n{section.replace('_', ' ').title()}:")
                            for key, value in data.items():
                                content_parts.append(f"- {key}: {value}")
    
    # Combine everything
    combined_content = "\n".join(content_parts)
    logger.debug(f"Created combined content with length: {len(combined_content)}")
    logger.debug(f"Combined content preview: {combined_content[:500]}...")
    
    return combined_content

# Move S3Manager class definition here since it's used in both files
class S3Manager:
    def __init__(self):
        self.s3_client = boto3.client(
            's3',
            region_name=os.getenv('AWS_REGION', 'us-east-2'),
            aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
            aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY')
        )
        self.bucket = os.getenv('S3_BUCKET', 'gradmatch')

def load_and_embed_json(s3_manager: S3Manager):
    """Load and embed JSON data"""
    try:
        # Load JSON files
        unr_response = s3_manager.s3_client.get_object(Bucket=S3_BUCKET, Key=UNR_JSON_KEY)
        rfums_response = s3_manager.s3_client.get_object(Bucket=S3_BUCKET, Key=RFUMS_JSON_KEY)
        
        # Parse JSON content
        unr_data = json.loads(unr_response['Body'].read().decode('utf-8'))
        rfums_data = json.loads(rfums_response['Body'].read().decode('utf-8'))

        # Create documents with better structured content
        documents = []
        for data, source, school in [(unr_data, "UNR", "University of Nevada, Reno"), 
                                   (rfums_data, "RFUMS", "Rosalind Franklin University")]:
            formatted_content = _format_json_for_embedding(data)
            documents.append(Document(
                page_content=formatted_content,
                metadata={
                    "source": source,
                    "school": school,
                    "type": "pa_program",
                    "id": f"program_{hash(formatted_content)}",
                    "raw_content": json.dumps(data)
                }
            ))

        # Upload to vectorstore
        vectorstore.add_documents(
            documents=documents,
            namespace="json"
        )
        
        logger.info("Successfully loaded and embedded JSON files")
        return True
        
    except Exception as e:
        logger.error(f"Error loading JSON files: {str(e)}", exc_info=True)
        return False


@traceable
def process_input_with_langchain(user_input: str, user_profile: Dict[str, Any], session_id: str) -> Dict[str, Any]:
    """Main processing function for handling user queries"""
    try:
        logger.debug("Starting LangSmith traced function")
        logger.debug(f"Processing input: '{user_input}' for session {session_id}")
        
        # Get stored analysis from previous transcript processing
        stored_analysis = get_stored_analysis(session_id, user_profile['user_id'])
        logger.debug(f"Retrieved stored analysis: {stored_analysis is not None}")
        
        if not stored_analysis:
            logger.warning(f"No stored analysis found for session {session_id}")
            response = "Please complete the transcript validation process to get the final analysis before asking questions."
            MessageStore.add_message(session_id, user_input, is_human=True)
            MessageStore.add_message(session_id, response, is_human=False)
            return {
                "answer": response,
                "sources": {"transcript": [], "programs": {"unr": [], "rfums": []}}
            }
        
        try:
            # Parse stored analysis if it's a JSON string
            if isinstance(stored_analysis, str):
                stored_analysis_data = json.loads(stored_analysis)
                stored_analysis = json.dumps(stored_analysis_data, indent=2)  # Pretty print for LLM
        except json.JSONDecodeError:
            logger.debug("Stored analysis is not in JSON format, using as is")
        
        logger.debug(f"Retrieved stored analysis length: {len(stored_analysis)}")
        
        # Initialize S3 manager and load JSON data
        s3_manager = S3Manager()
        json_loaded = load_and_embed_json(s3_manager)
        logger.debug(f"JSON loading and embedding status: {json_loaded}")
        
        # Perform vector search for relevant program information
        logger.debug("Performing vector search for program information")
        results, _ = query_and_rerank(
            user_input,
            "json",
            top_k=6
        )
        
        logger.debug(f"Retrieved {len(results)} program results from vector store")
        
        # Process program information
        program_content = process_program_results(results)
        logger.debug(f"Processed program results: UNR ({len(program_content['unr'])}), RFUMS ({len(program_content['rfums'])})")
        
        # Create combined content
        combined_content = create_combined_content(
            stored_analysis,
            program_content,
            "",  # No need for transcript_text
            []   # No need for transcript_chunks
        )
        
        logger.debug(f"Created combined content of length: {len(combined_content)}")
        logger.debug("Sample of combined content:")
        logger.debug(combined_content[:500] + "...")
        
        # Process with LLM using conversation history
        logger.debug("Processing with LLM")
        response = process_with_llm(user_input, combined_content, user_input, session_id)
        logger.info("Received LLM response")
        
        # Return formatted response
        return create_success_response(
            response,
            [],  # No need for transcript_chunks
            program_content
        )
            
    except Exception as e:
        logger.error(f"Error in main process: {str(e)}", exc_info=True)
        error_message = f"Error processing request: {str(e)}"
        # Store error in conversation history
        MessageStore.add_message(session_id, user_input, is_human=True)
        MessageStore.add_message(session_id, error_message, is_human=False)
        return {
            "answer": error_message,
            "sources": {
                "transcript": [],
                "programs": {"unr": [], "rfums": []}
            }
        }


def process_program_results(results) -> Dict[str, Any]:
    """Process program information from query results"""
    logger.debug(f"Processing search results for LLM analysis")
    logger.debug(f"Number of results to process: {len(results)}")
    if results:
        logger.debug(f"First result metadata: {json.dumps(results[0].metadata, indent=2)}")
        logger.debug(f"First result content sample: {results[0].page_content[:200]}...")
    
    unr_results = []
    rfums_results = []
    
    for r in results:
        try:
            metadata = r.metadata
            source = metadata.get('source', '').upper()
            
            # Use the raw content directly
            content = json.loads(metadata.get('raw_content', '{}'))
            
            # Sort into appropriate school list
            if 'UNR' in source:
                unr_results.append(content)
            elif 'RFUMS' in source:
                rfums_results.append(content)
                
            logger.debug(f"Processed {source} data: {json.dumps(content, indent=2)}")
            
        except Exception as e:
            logger.error(f"Error processing result: {str(e)}")
            continue
    
    logger.debug(f"Final results - UNR: {len(unr_results)}, RFUMS: {len(rfums_results)}")
    return {
        "unr": unr_results,
        "rfums": rfums_results
    }


def _format_json_for_embedding(json_data: Dict[str, Any]) -> str:
    """Format JSON data for embedding with preserved structure"""
    def _format_value(value: Any, prefix: str = "", depth: int = 0) -> List[str]:
        indent = "  " * depth
        if isinstance(value, dict):
            parts = []
            for k, v in value.items():
                # Handle nested dicts
                if isinstance(v, dict):
                    parts.append(f"{indent}{prefix}{k}:")
                    parts.extend(_format_value(v, "", depth + 1))
                else:
                    parts.append(f"{indent}{prefix}{k}: {v}")
            return parts
        elif isinstance(value, list):
            parts = []
            for item in value:
                if isinstance(item, dict):
                    parts.extend(_format_value(item, "", depth + 1))
                else:
                    parts.append(f"{indent}{prefix}- {item}")
            return parts
        else:
            return [f"{indent}{prefix}{value}"] if value is not None and value != "" else []

    formatted_parts = []
    for key, value in json_data.items():
        if isinstance(value, dict):
            formatted_parts.append(f"{key}:")
            formatted_parts.extend(_format_value(value, "", 1))
        else:
            formatted_parts.extend(_format_value(value, f"{key}: "))
    
    return "\n".join(formatted_parts)      

def process_with_llm(user_input: str, context: str, original_query: str, session_id: str) -> str:
    """Process input with LLM using context and conversation history"""
    try:
        # Get conversation history
        message_history = MessageStore.get_messages(session_id)
        
        # Format conversation history
        conversation_context = "\n".join([
            f"{'User' if isinstance(msg, HumanMessage) else 'Assistant'}: {msg.content}"
            for msg in message_history[-5:]  # Get last 5 messages for context window
        ])
        
        prompt_template = ChatPromptTemplate.from_template(
            """Based on the following context, conversation history, and user question, provide a detailed response:
            
            Context:
            {context}
            
            Previous Conversation:
            {conversation}
            
            User Question: {question}
            
            Provide a clear, detailed answer focusing on the specific information requested."""
        )
        
        chain = (
            {
                "context": lambda x: context, 
                "conversation": lambda x: conversation_context,
                "question": lambda x: user_input
            }
            | prompt_template
            | llm
            | StrOutputParser()
        )
        
        response = chain.invoke({})
        
        # Store the conversation
        MessageStore.add_message(session_id, user_input, is_human=True)
        MessageStore.add_message(session_id, response, is_human=False)
        
        return response
        
    except Exception as e:
        logger.error(f"Error in LLM processing: {str(e)}")
        raise

if __name__ == "__main__":
    try:
        user_input = "What is the class profile for the incoming physician assistant program?"
        user_profile = {"bio": "Name", "location": "New York"}
        session_id = "session123"
        
        result = process_input_with_langchain(
            user_input, 
            user_profile,
            session_id
        )
        print(result)
        
        logger.info("Check your LangSmith dashboard for traces")
        
    except Exception as e:
        logger.error(f"Error in main: {str(e)}", exc_info=True)