import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import styled from '@emotion/styled';

const Container = styled.div`
  max-width: 400px;
  margin: 0 auto;
  padding: 1.5rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const Input = styled.input`
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 1rem;

  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 1px #3b82f6;
  }
`;

const Button = styled.button`
  padding: 0.75rem;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover:not(:disabled) {
    background-color: #2563eb;
  }

  &:disabled {
    background-color: #9ca3af;
    cursor: not-allowed;
  }
`;

const Message = styled.div`
  padding: 0.75rem;
  border-radius: 0.375rem;
  text-align: center;
  margin-bottom: 1rem;
`;

const ErrorMessage = styled(Message)`
  color: #dc2626;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
`;

const SuccessMessage = styled(Message)`
  color: #059669;
  background-color: #ecfdf5;
  border: 1px solid #a7f3d0;
`;

const InfoMessage = styled(Message)`
  color: #1d4ed8;
  background-color: #eff6ff;
  border: 1px solid #bfdbfe;
`;

const ChangeEmail = ({ onCancel }) => {
  const [newEmail, setNewEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  
  const { currentUser, supabase } = useAuth();

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setMessage('');

    if (!newEmail || !password) {
      setError('Please fill in all fields.');
      return;
    }

    if (newEmail === currentUser?.email) {
      setError('New email must be different from current email.');
      return;
    }

    setIsLoading(true);

    try {
      // First verify the current password
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: currentUser.email,
        password: password
      });

      if (signInError) {
        throw new Error('Current password is incorrect.');
      }

      // Update the email
      const { data, error: updateError } = await supabase.auth.updateUser({
        email: newEmail
      });

      if (updateError) {
        throw updateError;
      }

      setMessage('Email change initiated! Please check both your old and new email addresses for confirmation links. You must confirm the change from both emails.');
      
      // Clear form
      setNewEmail('');
      setPassword('');

    } catch (err) {
      console.error('Email change error:', err);
      
      if (err.message?.includes('rate_limit')) {
        setError('Too many requests. Please wait before trying again.');
      } else if (err.message?.includes('same_email')) {
        setError('New email must be different from current email.');
      } else {
        setError(err.message || 'Failed to change email. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Container>
      <h3>Change Email Address</h3>
      
      <InfoMessage>
        Current email: <strong>{currentUser?.email}</strong>
      </InfoMessage>

      {error && <ErrorMessage>{error}</ErrorMessage>}
      {message && <SuccessMessage>{message}</SuccessMessage>}

      <Form onSubmit={handleSubmit}>
        <Input
          type="email"
          value={newEmail}
          onChange={(e) => setNewEmail(e.target.value)}
          placeholder="New email address"
          required
          disabled={isLoading}
        />
        
        <Input
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          placeholder="Current password (for verification)"
          required
          disabled={isLoading}
          autoComplete="current-password"
        />

        <Button type="submit" disabled={isLoading}>
          {isLoading ? 'Changing Email...' : 'Change Email'}
        </Button>
        
        {onCancel && (
          <Button 
            type="button" 
            onClick={onCancel}
            style={{ backgroundColor: '#6b7280' }}
            disabled={isLoading}
          >
            Cancel
          </Button>
        )}
      </Form>

      <InfoMessage style={{ marginTop: '1rem', fontSize: '0.875rem' }}>
        <strong>Note:</strong> You'll need to confirm the email change from both your current and new email addresses. Your account will remain accessible with your current email until the change is confirmed.
      </InfoMessage>
    </Container>
  );
};

export default ChangeEmail;