# SECURITY NOTICE: Never commit actual .env files to version control
# Copy this file to .env and fill in your actual values

# Django Configuration
DJANGO_SECRET_KEY=your_secure_django_secret_key_here_minimum_50_characters
DJANGO_DEBUG=False

# Database Configuration
DB_NAME=gradmatch_db
DB_USER=postgres
DB_PASSWORD=your_secure_database_password
DB_HOST=your_database_host
DB_PORT=5432

# Supabase Authentication
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_JWT_SECRET=your_supabase_jwt_secret
SUPABASE_AUDIENCE=authenticated
SUPABASE_ISSUER=https://your-project.supabase.co/auth/v1

# AWS Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-2

# API Keys (Rotate these immediately if compromised)
OPENAI_API_KEY=your_openai_api_key
LANGCHAIN_API_KEY=your_langchain_api_key
PINECONE_API_KEY=your_pinecone_api_key
PINECONE_ENVIRONMENT=gcp-starter
PINECONE_INDEX_NAME=gradmatch
VOYAGE_API_KEY=your_voyage_api_key