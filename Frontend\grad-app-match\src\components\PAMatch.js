import React, { useState, useEffect, useCallback } from 'react';
import axios from 'axios';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import ProgramSkeleton from './ProgramSkeleton';
import { normalizeProfileData } from '../utils/profileUtils';
import './PAMatch.css';

// US Census Bureau Regions and Divisions
const US_REGIONS = {
    // Census Regions
    'Northeast': ['CT', 'ME', 'MA', 'NH', 'RI', 'VT', 'NJ', 'NY', 'PA'],
    'Midwest': ['IL', 'IN', 'MI', 'OH', 'WI', 'IA', 'KS', 'MN', 'MO', 'NE', 'ND', 'SD'],
    'South': ['DE', 'DC', 'FL', 'GA', 'MD', 'NC', 'SC', 'VA', 'WV', 'AL', 'KY', 'MS', 'TN', 'AR', 'LA', 'OK', 'TX'],
    'West': ['AZ', 'CO', 'ID', 'MT', 'NV', 'NM', 'UT', 'WY', 'AK', 'CA', 'HI', 'OR', 'WA'],
    
    // Census Divisions
    'New England': ['CT', 'ME', 'MA', 'NH', 'RI', 'VT'],
    'Mid-Atlantic': ['NJ', 'NY', 'PA'],
    'East North Central': ['IL', 'IN', 'MI', 'OH', 'WI'],
    'West North Central': ['IA', 'KS', 'MN', 'MO', 'NE', 'ND', 'SD'],
    'South Atlantic': ['DE', 'DC', 'FL', 'GA', 'MD', 'NC', 'SC', 'VA', 'WV'],
    'East South Central': ['AL', 'KY', 'MS', 'TN'],
    'West South Central': ['AR', 'LA', 'OK', 'TX'],
    'Mountain': ['AZ', 'CO', 'ID', 'MT', 'NV', 'NM', 'UT', 'WY'],
    'Pacific': ['AK', 'CA', 'HI', 'OR', 'WA']
};

// Helper function to get region for a state
const getStateRegion = (state) => {
    if (!state) return null;
    const stateCode = state.toUpperCase();
    for (const [region, states] of Object.entries(US_REGIONS)) {
        if (states.includes(stateCode)) {
            return region;
        }
    }
    return 'Other';
};

const PAMatch = () => {
    console.log('PAMatch: Component Mounting...'); // <-- Add log
    const { token } = useAuth();
    const navigate = useNavigate();
    const [userTranscript, setUserTranscript] = useState(null);
    const [profileData, setProfileData] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    const [programs, setPrograms] = useState([]);
    const [selectedPrereq, setSelectedPrereq] = useState(null);
    const [showCourseModal, setShowCourseModal] = useState(false);
    const [availableCourses, setAvailableCourses] = useState([]);
    const [expandedSections, setExpandedSections] = useState({
        eligible: false,
        ineligible: false,
        incomplete: false
    });
    const [expandedPrograms, setExpandedPrograms] = useState({});
    const [activeTabs, setActiveTabs] = useState({}); // Track active tab for each program
    // New state variables for saved results functionality
    const [usingSavedResults, setUsingSavedResults] = useState(false);
    const [savedResultsTimestamp, setSavedResultsTimestamp] = useState(null);
    const [savingResults, setSavingResults] = useState(false);
    // Add new state for manually approved programs
    const [manuallyApprovedPrograms, setManuallyApprovedPrograms] = useState([]);
    
    // New state for view options
    const [viewMode, setViewMode] = useState('accordion'); // 'accordion' or 'grid'
    const [searchTerm, setSearchTerm] = useState('');
    const [sortBy, setSortBy] = useState('name'); // 'name', 'gpa', 'eligibility'
    const [filterBy, setFilterBy] = useState('all'); // 'all', 'eligible', 'ineligible', 'incomplete'
    const [stateFilter, setStateFilter] = useState('all'); // 'all', specific state, or region
    const [regionFilter, setRegionFilter] = useState('all'); // 'all', 'northeast', 'southeast', etc.

    // Check for token on mount and when token changes
    useEffect(() => {
        console.log('PAMatch: Token Check Effect - Token:', token); // <-- Add log
        if (!token) {
            console.log('PAMatch: No token found, navigating to /login'); // <-- Add log
            navigate('/login');
            return;
        }
    }, [token, navigate]);

    useEffect(() => {
        console.log('PAMatch: Data Fetch Effect - Running with token:', token); // <-- Add log
        
        // Try to get saved results first, then fallback to fetching and calculating
        const loadData = async () => {
            setIsLoading(true);
            setError(null);
            
            try {
                // Check for saved results first
                await checkForSavedResults();
            } catch (err) {
                console.error('PAMatch: Error checking for saved results:', err);
                // If error in getting saved results, fall back to normal data fetch
                await fetchAndCalculateResults();
            } finally {
                setIsLoading(false);
            }
        };
        
        if (token) {
            loadData();
        }
    }, [token, navigate]);

    // New function to check for saved matching results
    const checkForSavedResults = async () => {
        console.log('PAMatch: Checking for saved matching results...');
        
        try {
            const headers = {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
            };
            
            const response = await axios.get(`${process.env.REACT_APP_API_URL || 'http://127.0.0.1:8000'}/api/matching-results/`, { headers });
            console.log('PAMatch: Saved results response:', response.data);
            
            if (response.data.status === 'success') {
                // We have valid saved results, use them
                console.log('PAMatch: Using saved matching results from', response.data.timestamp);
                
                const savedData = response.data.matching_results;
                
                // Update state with saved data
                setPrograms(savedData.programs || []);
                setUserTranscript(savedData.userTranscript || null);
                setProfileData(savedData.profileData || null);
                
                // Load manually approved programs if they exist
                if (savedData.manuallyApprovedPrograms) {
                    setManuallyApprovedPrograms(savedData.manuallyApprovedPrograms);
                }
                
                // Set metadata about saved results
                setSavedResultsTimestamp(response.data.timestamp);
                setUsingSavedResults(true);
                return true;
            } else if (response.data.status === 'stale') {
                // Data has changed, need fresh calculation
                console.log('PAMatch: Saved results are stale, calculating fresh results');
                await fetchAndCalculateResults();
                return false;
            } else {
                // No saved results found
                console.log('PAMatch: No saved results found, calculating fresh results');
                await fetchAndCalculateResults();
                return false;
            }
        } catch (err) {
            console.error('PAMatch: Error retrieving saved results:', err);
            throw err;
        }
    };

    // Refactored original data fetch into a separate function
    const fetchAndCalculateResults = async () => {
        console.log('PAMatch: Starting data fetch...'); // <-- Add log
        setUsingSavedResults(false); // Mark that we're using fresh data
        
        try {
            if (!token) {
                 console.log('PAMatch: Token became null/undefined before API calls'); // <-- Add log
                throw new Error('No authentication token available');
            }

            const headers = {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
            };

            // Fetch programs
            console.log('PAMatch: Fetching programs...'); // <-- Add log
            const programsResponse = await axios.get(`${process.env.REACT_APP_API_URL || 'http://127.0.0.1:8000'}/api/programs/`, { headers });
            const fetchedPrograms = programsResponse.data.programs;
            setPrograms(fetchedPrograms);
            console.log('PAMatch: Fetched programs successfully.'); // <-- Add log

            // Fetch saved prerequisites
             console.log('PAMatch: Fetching saved prerequisites...'); // <-- Add log
            const savedPrereqsResponse = await axios.get(`${process.env.REACT_APP_API_URL || 'http://127.0.0.1:8000'}/api/prerequisites/saved/`, { headers });
             console.log('PAMatch: Fetched saved prerequisites successfully.'); // <-- Add log

            // Fetch profile data
            console.log('PAMatch: Fetching profile data...'); // <-- Add log
            const profileResponse = await axios.get(`${process.env.REACT_APP_API_URL || 'http://127.0.0.1:8000'}/api/profile/`, { headers });
            console.log('PAMatch: Profile data received:', profileResponse.data); // Debug log to see what fields are available
            
            // Normalize profile data to ensure both camelCase and snake_case fields are available
            const normalizedProfileData = normalizeProfileData(profileResponse.data);
            console.log('PAMatch: Normalized profile data:', normalizedProfileData);
            
            setProfileData(normalizedProfileData);
            console.log('PAMatch: Fetched profile data successfully.'); // <-- Add log

            // Fetch transcript data to get accurate GPAs
            console.log('PAMatch: Fetching transcript data...'); // <-- Add log
            const transcriptResponse = await axios.get(`${process.env.REACT_APP_API_URL || 'http://127.0.0.1:8000'}/api/transcript/get/`, { headers });
            const transcripts = transcriptResponse.data.transcripts || [];
            console.log('PAMatch: Fetched transcript data successfully.'); // <-- Add log
            
            // Use the CASPA GPA values provided by the user
            // These values come from the CASPA GPA calculator on the Dashboard page
            console.log('PAMatch: Using user-provided CASPA GPAs'); 
            
            // CASPA GPA values from the CASPA tab
            const caspaCalculatedGPA = 3.41; // Overall GPA from CASPA calculation (from Overall row, Total column)
            const caspaScienceGPA = 3.20;   // Science GPA from CASPA calculation (from Overall row, Science column)
            
            console.log('PAMatch: Using CASPA GPAs - Overall:', caspaCalculatedGPA, 'Science:', caspaScienceGPA);

            // Calculate basic GPA for prerequisites only
            let totalPoints = 0;
            let totalCredits = 0;
            let sciencePoints = 0;
            let scienceCredits = 0;

            transcripts.forEach(transcript => {
                const semesters = transcript.transcript_data?.semesters || [];
                semesters.forEach(semester => {
                    semester.courses.forEach(course => {
                        if (!['F', 'W', 'U'].includes(course.grade)) {
                            const credits = parseFloat(course.credits) || 0;
                            const gradePoints = gradeToPoints(course.grade);
                            
                            if (!isNaN(credits) && !isNaN(gradePoints)) {
                                totalPoints += credits * gradePoints;
                                totalCredits += credits;

                                if (course.is_science) {
                                    sciencePoints += credits * gradePoints;
                                    scienceCredits += credits;
                                }
                            }
                        }
                    });
                });
            });

            // These are just used for prerequisite mapping, not for program matching
            const calculatedGPA = totalCredits > 0 ? totalPoints / totalCredits : 0;
            const scienceGPA = scienceCredits > 0 ? sciencePoints / scienceCredits : 0;

            // --- Prepare All Transcript Courses (with de-duplication) ---
            let allTranscriptCourses = []; // Use let
            const seenCourses = new Set(); // Use a Set to track unique courses

            transcripts.forEach(transcript => {
                const institution = transcript.institution; // Get institution name
                const semesters = transcript.transcript_data?.semesters || [];
                semesters.forEach(semester => {
                    const term = semester.term;
                    const year = semester.year;
                    semester.courses.forEach(course => {
                        // We only want to add *unique* courses based on code and name primarily,
                        // preferring the best grade if taken multiple times.
                        // Let's build a temporary map first.
                        // Key: `${normalizedInstitution}-${course.code}`
                        // Value: { bestCourseObject }
                        if (!['F', 'W', 'U'].includes(course.grade)) {
                            const normalizedInstitution = institution.toLowerCase().replace(/[,.]/g, '').trim();
                            const courseMapKey = `${normalizedInstitution}-${course.code}`;
                            const currentCourseData = {
                                ...course,
                                institution: institution,
                                term: term,
                                year: year,
                                gradePoints: gradeToPoints(course.grade) // Add grade points for comparison
                            };

                            if (!seenCourses[courseMapKey] || currentCourseData.gradePoints > seenCourses[courseMapKey].gradePoints) {
                                 seenCourses[courseMapKey] = currentCourseData; // Store/replace with the better grade
                            }
                        }
                    });
                });
            });
            // Now, convert the map values back into the final array
            allTranscriptCourses = Object.values(seenCourses); // Assign to existing variable (no const)
            console.log("De-duplicated (best grade) allTranscriptCourses:", allTranscriptCourses);
            // --- End Prepare All Transcript Courses ---


            // Use only the saved prerequisites for program matching
            const savedPrereqs = savedPrereqsResponse.data.prerequisites; // Assuming this is the correct structure from API
            console.log('Saved prerequisites:', savedPrereqs);

            // Create a case-insensitive lookup for SAVED prerequisites
            // Assuming savedPrereqs is like: { "Human Anatomy": { courses: [...] }, ... }
            const savedPrereqsLookup = Object.entries(savedPrereqs || {}).reduce((acc, [key, value]) => {
                // Ensure value has a courses array
                acc[key.toLowerCase()] = { courses: Array.isArray(value?.courses) ? value.courses : [] };
                return acc;
            }, {});
            
            // Map prerequisites for each program using SAVED and SUGGESTED courses
            const programPrereqs = {};
            fetchedPrograms.forEach(program => {
                console.log(`Processing prerequisites for program: ${program.name}`);
                if (program.requirements.prerequisites) {
                    const programMatches = {};

                    Object.entries(program.requirements.prerequisites).forEach(([prereqName, prereqData]) => {
                        const savedPrereq = savedPrereqsLookup[prereqName.toLowerCase()];
                        let coursesToUse = [];
                        let isSaved = false;

                        // Check if this prerequisite category has saved courses, with special handling for A&P
                        let foundSaved = false;
                        if (savedPrereq && savedPrereq.courses.length > 0) {
                            // Found direct match for saved category
                            coursesToUse = savedPrereq.courses;
                            foundSaved = true;
                        } else if (prereqName.toLowerCase().includes('anatomy') || prereqName.toLowerCase().includes('physiology')) {
                            // If Anatomy or Physiology required, check if combined A&P was saved
                            const combinedAPCategory = 'human anatomy and physiology';
                            const savedCombined = savedPrereqsLookup[combinedAPCategory];
                            if (savedCombined && savedCombined.courses.length > 0) {
                                coursesToUse = savedCombined.courses;
                                foundSaved = true;
                                console.log(`Using SAVED combined A&P courses for ${prereqName}`);
                            }
                        }

                        if (foundSaved) {
                            isSaved = true;
                            console.log(`Using SAVED courses for ${prereqName}:`, coursesToUse);
                        } else {
                            // If not saved (directly or via combined A&P), find SUGGESTED courses
                            coursesToUse = allTranscriptCourses.filter(course =>
                                courseMatchesPrereq(course.name, course.code, prereqName)
                            );
                            console.log(`Using SUGGESTED courses for ${prereqName}:`, coursesToUse);
                        }

                        // Calculate total credits from the courses being used (saved or suggested)
                        const totalCredits = coursesToUse.reduce((sum, course) =>
                            sum + parseFloat(course.credits || 0), 0
                        );

                        // Store the result
                        programMatches[prereqName] = {
                            totalCredits: totalCredits,
                            credits: prereqData.credits, // Required credits
                            timeframe: prereqData.timeframe,
                            lab_required: prereqData.lab_required,
                            courses: coursesToUse, // List of courses (either saved or suggested)
                            isSaved: isSaved // Flag to indicate if these are saved or suggested
                        };
                    });

                    programPrereqs[program.name] = programMatches;
                }
            });
            
            console.log('Final programPrereqs:', programPrereqs);
            
            const updatedTranscript = {
                calculated_gpa: caspaCalculatedGPA,
                science_gpa: caspaScienceGPA,
                prerequisites: programPrereqs
            };

            console.log('Setting userTranscript:', updatedTranscript);
            setUserTranscript(updatedTranscript);
            console.log('PAMatch: Finished processing, userTranscript set.'); // <-- Add log
            
            // After calculating everything, save results to backend
            await saveCurrentResults({
                programs: fetchedPrograms,
                userTranscript: updatedTranscript,
                profileData: profileResponse.data
            });
            
        } catch (err) {
            console.error('PAMatch: Error during data fetch or processing:', err); // <-- Add log
             console.error('PAMatch: Error Response Status:', err.response?.status); // <-- Log status
             console.error('PAMatch: Error Response Data:', err.response?.data); // <-- Log data
            if (err.response?.status === 401) {
                // Token is invalid or expired
                setError('Your session has expired. Please log in again.');
                 console.log('PAMatch: 401 Error - Navigating to /login'); // <-- Add log
                navigate('/login');
                return;
            }
            setError(err.response?.data?.error || 'Failed to fetch data');
             console.log('PAMatch: Non-401 Error occurred, setting error state.'); // <-- Add log
        }
    };

    // Function to save current results to backend
    const saveCurrentResults = async (dataToSave) => {
        console.log('PAMatch: Saving current matching results...');
        setSavingResults(true);
        
        try {
            const headers = {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
            };
            
            // Prepare data object to save
            const matchingResults = dataToSave || {
                programs: programs,
                userTranscript: userTranscript,
                profileData: profileData,
                manuallyApprovedPrograms: manuallyApprovedPrograms
            };
            
            // Save to backend
            const response = await axios.post(
                'http://127.0.0.1:8000/api/matching-results/save/',
                { matching_results: matchingResults },
                { headers }
            );
            
            console.log('PAMatch: Results saved successfully:', response.data);
            
            // Update state to reflect saved status
            setSavedResultsTimestamp(response.data.timestamp);
            setUsingSavedResults(true);
            return true;
        } catch (err) {
            console.error('PAMatch: Error saving matching results:', err);
            return false;
        } finally {
            setSavingResults(false);
        }
    };

    // Function to force recalculation of results
    const handleRecalculate = () => {
        setIsLoading(true);
        fetchAndCalculateResults()
            .finally(() => setIsLoading(false));
    };

    const courseMatchesPrereq = (courseName, courseCode, prereqName) => {
        courseName = courseName.toLowerCase();
        courseCode = courseCode.toLowerCase();
        prereqName = prereqName.toLowerCase();

        // Common course prefix patterns
        const chemistryPrefixes = ['chem', 'chm', 'chemy'];
        const biologyPrefixes = ['bio', 'biol', 'bsc'];
        const psychologyPrefixes = ['psy', 'psyc', 'psych'];
        const englishPrefixes = ['eng', 'engl', 'eh'];
        const statisticsPrefixes = ['stat', 'stats', 'sta', 'mth'];
        const sociologyPrefixes = ['soc', 'socy'];
        const anthropologyPrefixes = ['anth', 'ant'];

        // Check for combined anatomy & physiology courses
        if (prereqName.includes('anatomy & physiology') || 
            prereqName.includes('anatomy and physiology')) {
            const isMatch = (courseName.includes('anatomy') && courseName.includes('physiology')) ||
                       courseName.includes('a&p') ||
                       courseName.includes('anat & phys') ||
                   courseName.includes('anatomical and physiological') ||
                   courseCode.includes('a&p') ||
                   (courseCode.match(/\d+/) && (courseName.includes('human a & p') || courseName.includes('hum anat & phys')));
            if (isMatch) console.log('Matched A&P course:', courseName);
            return isMatch;
        }

        // Match based on common course patterns
        switch(true) {
             // --- Combined Chemistry Sequence ---
            case prereqName.includes('chemistry sequence (gen/org/biochem)'): // Match the new name
                const seq_isGenChem = chemistryPrefixes.some(prefix => courseCode.startsWith(prefix)) && // Renamed variable
                       (courseName.includes('general') || courseName.includes('inorganic') ||
                        courseCode.match(/\d+/)?.some(num => parseInt(num) < 200));
                const seq_isOrgChem = chemistryPrefixes.some(prefix => courseCode.startsWith(prefix)) && // Renamed variable
                       (courseName.includes('organic') ||
                        courseCode.match(/\d+/)?.some(num => parseInt(num) >= 200 && parseInt(num) < 400));
                const seq_isBiochem = courseCode.startsWith('bch') || courseCode.startsWith('bioc') || // Renamed variable
                       courseName.includes('biochem');
                return seq_isGenChem || seq_isOrgChem || seq_isBiochem; // Use renamed variables

            // --- Standard Categories ---
        case prereqName.includes('human anatomy'):
            const isAnatomy = (
                // Check for combined A&P courses first
                ((courseName.includes('anatomy') && courseName.includes('physiology')) ||
                courseName.includes('a&p') ||
                courseName.includes('anat & phys') ||
                courseName.includes('human a & p') ||
                courseName.includes('hum anat & phys')) ||
                // Then check for standalone anatomy courses
                (biologyPrefixes.some(prefix => courseCode.startsWith(prefix)) &&
                (courseName.includes('anatomy') || courseName.includes('anatomical')))
            );
            if (isAnatomy) console.log('Matched Anatomy course:', courseName);
            return isAnatomy;

        case prereqName.includes('human physiology'):
            const isPhysiology = (
                // Check for combined A&P courses first
                ((courseName.includes('anatomy') && courseName.includes('physiology')) ||
                courseName.includes('a&p') ||
                courseName.includes('anat & phys') ||
                courseName.includes('human a & p') ||
                courseName.includes('hum anat & phys')) ||
                // Then check for standalone physiology courses
                (biologyPrefixes.some(prefix => courseCode.startsWith(prefix)) &&
                (courseName.includes('physiology') || courseName.includes('physiological')))
            );
            if (isPhysiology) console.log('Matched Physiology course:', courseName);
            return isPhysiology;

        case prereqName.includes('microbiology'):
            const isMicro = (biologyPrefixes.some(prefix => courseCode.startsWith(prefix)) &&
                   courseName.includes('micro')) ||
                   courseName.includes('microbiology');
            if (isMicro) console.log('Matched Microbiology:', courseName);
            return isMicro;

            case prereqName.includes('general chemistry'):
            const isGenChem = chemistryPrefixes.some(prefix => courseCode.startsWith(prefix)) &&
                   (courseName.includes('general') || 
                    courseCode.match(/\d+/)?.some(num => parseInt(num) < 200));
            if (isGenChem) console.log('Matched General Chemistry:', courseName);
            return isGenChem;

            case prereqName.includes('organic chemistry'):
            const isOrgChem = chemistryPrefixes.some(prefix => courseCode.startsWith(prefix)) &&
                   (courseName.includes('organic') || 
                    courseCode.match(/\d+/)?.some(num => parseInt(num) >= 200 && parseInt(num) < 400));
            if (isOrgChem) console.log('Matched Organic Chemistry:', courseName);
            return isOrgChem;

            case prereqName.includes('biochemistry'):
            const isBiochem = courseCode.startsWith('bch') || courseCode.startsWith('bioc') ||
                       courseName.includes('biochem');
            if (isBiochem) console.log('Matched Biochemistry:', courseName);
            return isBiochem;

            case prereqName.includes('psychology'):
            const isPsych = psychologyPrefixes.some(prefix => courseCode.startsWith(prefix)) ||
                       courseName.includes('psychology');
            if (isPsych) console.log('Matched Psychology:', courseName);
            return isPsych;

            case prereqName.includes('statistics'):
            const isStats = statisticsPrefixes.some(prefix => courseCode.startsWith(prefix)) ||
                       courseName.includes('statistics') ||
                       courseName.includes('statistical');
            if (isStats) console.log('Matched Statistics:', courseName);
            return isStats;

        case prereqName.includes('medical terminology'):
            const isMedTerm = courseName.includes('medical terminology') ||
                   (courseCode.startsWith('nurs') && courseName.includes('terminology'));
            if (isMedTerm) console.log('Matched Medical Terminology:', courseName);
            return isMedTerm;

        case prereqName.includes('behavioral sciences'):
            const isBehavioralScience = (
                psychologyPrefixes.some(prefix => courseCode.startsWith(prefix)) ||
                courseName.includes('psychology') ||
                courseName.includes('behavioral') ||
                courseName.includes('cognitive') ||
                courseName.includes('developmental') ||
                courseName.includes('abnormal') ||
                courseName.includes('social psychology') ||
                courseName.includes('child psychology') ||
                courseName.includes('adolescent psychology') ||
                courseName.includes('human development') ||
                courseName.includes('life span') ||
                courseName.includes('counseling') ||
                courseName.includes('psychobiology') ||
                courseName.includes('neuropsychology') ||
                courseName.includes('brain and behavior') ||
                courseName.includes('human behavior') ||
                courseName.includes('behavioral science')
            );
            if (isBehavioralScience) console.log('Matched Behavioral Science:', courseName);
            return isBehavioralScience;

        case prereqName.includes('sociology'):
            const isSociology = (
                sociologyPrefixes.some(prefix => courseCode.startsWith(prefix)) ||
                courseName.includes('sociology') ||
                courseName.includes('social welfare') ||
                courseName.includes('social work') ||
                courseName.includes('criminology') ||
                courseName.includes('criminal justice') ||
                courseName.includes('marriage and family') ||
                courseName.includes('family studies') ||
                courseName.includes('social ecology')
            );
            if (isSociology) console.log('Matched Sociology:', courseName);
            return isSociology;

        case prereqName.includes('social sciences'):
            const isSocialScience = (
                // Include all sociology courses
                sociologyPrefixes.some(prefix => courseCode.startsWith(prefix)) ||
                courseName.includes('sociology') ||
                courseName.includes('social welfare') ||
                courseName.includes('social work') ||
                courseName.includes('criminology') ||
                courseName.includes('criminal justice') ||
                courseName.includes('marriage and family') ||
                courseName.includes('family studies') ||
                // Include all psychology courses
                psychologyPrefixes.some(prefix => courseCode.startsWith(prefix)) ||
                courseName.includes('psychology') ||
                courseName.includes('behavioral') ||
                courseName.includes('cognitive') ||
                courseName.includes('developmental') ||
                courseName.includes('abnormal') ||
                courseName.includes('social psychology') ||
                courseName.includes('child psychology') ||
                courseName.includes('adolescent psychology') ||
                // Include anthropology and other social sciences
                anthropologyPrefixes.some(prefix => courseCode.startsWith(prefix)) ||
                courseName.includes('anthropology') ||
                courseName.includes('cultural studies') ||
                courseName.includes('ethnic studies') ||
                courseName.includes('gender studies') ||
                courseName.includes('women studies') ||
                courseName.includes('african american studies') ||
                courseName.includes('asian american studies') ||
                courseName.includes('latino american studies') ||
                courseName.includes('native american studies') ||
                courseName.includes('social justice') ||
                courseName.includes('human development') ||
                courseName.includes('human sexuality') ||
                courseName.includes('death and dying') ||
                courseName.includes('multicultural') ||
                // Additional social science indicators
                courseName.includes('social science') ||
                courseName.includes('human behavior') ||
                courseName.includes('social ecology') ||
                courseName.includes('community') ||
                courseName.includes('cultural geography')
            );
            if (isSocialScience) console.log('Matched Social Science:', courseName);
            return isSocialScience;

            case prereqName.includes('english'):
            const isEnglish = englishPrefixes.some(prefix => courseCode.startsWith(prefix)) ||
                       courseName.includes('english') ||
                       courseName.includes('composition') ||
                       courseName.includes('writing');
            if (isEnglish) console.log('Matched English:', courseName);
            return isEnglish;

           case prereqName.includes('chemistry (general)'): // Add this case
               // This logic correctly identifies general/inorganic chemistry
               return chemistryPrefixes.some(prefix => courseCode.startsWith(prefix)) &&
                      (courseName.includes('general') || courseName.includes('inorganic') ||
                       courseCode.match(/\d+/)?.some(num => parseInt(num) < 200));

           case prereqName.includes('biology (general)'): // Add this case
               // Must have bio prefix but NOT contain keywords for more specific categories
               const isGeneralBio = biologyPrefixes.some(prefix => courseCode.startsWith(prefix)) &&
                      !courseName.includes('anatomy') &&
                      !courseName.includes('physiology') &&
                      !courseName.includes('micro') && // Exclude microbiology
                      !courseName.includes('genetic'); // Exclude genetics
               return isGeneralBio;

            case prereqName.includes('cpr'): // Add this case
                return courseName.includes('cpr') ||
                       courseName.includes('cardiopulmonary resuscitation') ||
                       courseName.includes('basic life support');

            case prereqName.includes('math'): // Added logic for Math
                // Exclude statistics as it's a separate category
                const isMath = (courseCode.startsWith('math') || courseCode.startsWith('mth')) &&
                               !courseName.includes('stat'); // Exclude statistics
                // Could add more specific checks for 'algebra', 'calculus', 'pre-calculus' if needed
                return isMath;

            case prereqName.includes('sociology'): // Broadened logic for Sociology
                 const isSociologyRelated = ( // Use a different variable name
                    sociologyPrefixes.some(prefix => courseCode.startsWith(prefix)) || // SOC code
                    anthropologyPrefixes.some(prefix => courseCode.startsWith(prefix)) || // ANTH code
                    courseName.includes('sociology') ||
                    courseName.includes('social welfare') ||
                    courseName.includes('social work') ||
                    courseName.includes('criminology') ||
                    courseName.includes('criminal justice') ||
                    courseName.includes('marriage and family') ||
                    courseName.includes('family studies') ||
                    courseName.includes('social ecology') ||
                    courseName.includes('anthropology') || // Added Anthropology
                    courseName.includes('gender studies') || // Added Gender Studies
                    courseName.includes('ethnic studies') // Added Ethnic Studies
                );
                return isSociologyRelated; // Use updated variable name

                default:
            const isDefaultMatch = courseName.includes(prereqName) ||
                   courseCode.includes(prereqName);
            if (isDefaultMatch) console.log('Matched Default:', courseName);
            return isDefaultMatch;
    }
};

const mapPrerequisites = (courses, prerequisites) => {
    const prereqMap = {};
    
    // Process each prerequisite
    Object.entries(prerequisites).forEach(([prereqName, prereqData]) => {
        console.log(`\nProcessing prerequisite: ${prereqName}`);
        console.log('Required credits:', prereqData.credits);
        
        // Find all matching courses across all transcripts
        const matchingCourses = courses.filter(course => 
            courseMatchesPrereq(course.name, course.code, prereqName) &&
            !['F', 'W', 'U'].includes(course.grade)
        );

        console.log('Found matching courses:', matchingCourses);

        // Special handling for Anatomy and Physiology
        const isAnatomyOrPhysiology = prereqName.toLowerCase().includes('anatomy') || 
                                    prereqName.toLowerCase().includes('physiology');
        
        // Sort matching courses by grade (best grades first)
        const sortedCourses = [...matchingCourses].sort((a, b) => {
            const gradeA = gradeToPoints(a.grade);
            const gradeB = gradeToPoints(b.grade);
            if (gradeB !== gradeA) return gradeB - gradeA;
            return parseInt(b.year) - parseInt(a.year);
        });

        console.log('Sorted matching courses:', sortedCourses);

        // Calculate total credits and select courses
        let totalCredits = 0;
        const requiredCredits = parseFloat(prereqData.credits) || 0;
        const selectedCourses = [];

        for (const course of sortedCourses) {
            const courseCredits = parseFloat(course.credits) || 0;
            
            // For combined A&P courses, count full credits for both requirements
            if (isAnatomyOrPhysiology && 
                (course.name.toLowerCase().includes('anatomy') && course.name.toLowerCase().includes('physiology'))) {
                selectedCourses.push({
                    code: course.code,
                    name: course.name,
                    credits: courseCredits,
                    grade: course.grade,
                    institution: course.institution,
                    term: course.term,
                    year: course.year
                });
                totalCredits += courseCredits;
            } else if (!isAnatomyOrPhysiology && totalCredits < requiredCredits) {
                // Normal handling for non-A&P courses
                selectedCourses.push({
                    code: course.code,
                    name: course.name,
                    credits: courseCredits,
                    grade: course.grade,
                    institution: course.institution,
                    term: course.term,
                    year: course.year
                });
                totalCredits += courseCredits;
            }
        }

        // Create prerequisite entry
        prereqMap[prereqName] = {
            totalCredits,
            credits: requiredCredits,
            timeframe: prereqData.timeframe,
            lab_required: prereqData.lab_required,
            courses: selectedCourses,
            all_matching_courses: matchingCourses
        };

        console.log(`Final prerequisite entry for ${prereqName}:`, prereqMap[prereqName]);
    });

    return prereqMap;
};

const openCourseSelection = (prereqName, program, allCourses, currentPrereqCourses) => {
    // Filter out courses that are already mapped to this prerequisite
    const mappedCourseCodes = new Set(currentPrereqCourses.map(course => course.code));
    
    // Use all_courses directly from userTranscript
    const validCourses = userTranscript.all_courses.filter(course => 
        !mappedCourseCodes.has(course.code) &&
        !['F', 'W', 'U'].includes(course.grade) &&
        courseMatchesPrereq(course.name, course.code, prereqName)
    );
    
    // Debug logging
    console.log('Available courses for selection:', validCourses);
    console.log('Mapped course codes:', mappedCourseCodes);
    console.log('Prerequisite name:', prereqName);
    
    setAvailableCourses(validCourses);
    setSelectedPrereq({ name: prereqName, program: program });
    setShowCourseModal(true);
};

const addCourseToPrereq = (course) => {
    if (!selectedPrereq) return;

    const updatedTranscript = { ...userTranscript };
    const prereqData = updatedTranscript.prerequisites[selectedPrereq.name];
    
    // Add the selected course to the prerequisite's courses
    prereqData.courses.push({
        code: course.code,
        name: course.name,
        credits: course.credits,
        grade: course.grade,
        institution: course.institution,
        term: course.term,
        year: course.year
    });

    // Update total credits
    prereqData.totalCredits += parseFloat(course.credits) || 0;

    // Recalculate all GPAs
    const {
        calculatedGPA,
        scienceGPA
    } = processMultipleTranscripts(updatedTranscript.transcript_data.institutions);

    updatedTranscript.calculated_gpa = calculatedGPA;
    updatedTranscript.science_gpa = scienceGPA;
    updatedTranscript.prerequisite_gpa = calculatePrereqGPA(updatedTranscript.prerequisites, selectedPrereq.program);

    setUserTranscript(updatedTranscript);
    setShowCourseModal(false);
};

const CourseSelectionModal = () => {
    if (!showCourseModal) return null;

    return (
        <div className="modal-overlay">
            <div className="modal-content">
                <h3>Select Course for {selectedPrereq?.name}</h3>
                <div className="course-list">
                    {availableCourses.map((course, index) => (
                        <div key={index} className="course-option" onClick={() => addCourseToPrereq(course)}>
                            <div className="course-option-header">
                            <span>{course.code} - {course.name}</span>
                                <span>{course.institution}</span>
                            </div>
                            <div className="course-option-details">
                            <span>Credits: {course.credits} | Grade: {course.grade}</span>
                                <span>{course.term} {course.year}</span>
                            </div>
                        </div>
                    ))}
                </div>
                <button className="close-modal" onClick={() => setShowCourseModal(false)}>Cancel</button>
            </div>
        </div>
    );
};

const renderPrerequisiteDetails = (prereqData, prereqName, programName) => {
    if (!userTranscript?.prerequisites?.[programName]?.[prereqName]) {
        return null;
    }

    const programPrereqData = userTranscript.prerequisites[programName][prereqName];
    
    // Get the matched courses
    const matchedCourses = programPrereqData.courses || [];
    
    // Sort matched courses by grade
    const sortedMatchedCourses = [...matchedCourses].sort((a, b) => 
        gradeToPoints(b.grade) - gradeToPoints(a.grade)
    );
    
    // Track which courses are used in GPA calculation
    const coursesUsedInGPA = new Set();
    let creditsUsed = 0;
    const requiredCredits = parseFloat(programPrereqData.credits) || 0;
    
    // Mark courses used in GPA calculation
    for (const course of sortedMatchedCourses) {
        if (creditsUsed < requiredCredits) {
            coursesUsedInGPA.add(course.code);
            creditsUsed += parseFloat(course.credits) || 0;
        }
    }

    const handleCourseRemove = (courseCode, programName, prereqName) => {
        const updatedTranscript = { ...userTranscript };
        
        // Check if prerequisites exist
        if (!updatedTranscript.prerequisites?.[programName]?.[prereqName]) {
            console.log('Prerequisites not found for:', { programName, prereqName });
            return;
        }

        const prereqData = updatedTranscript.prerequisites[programName][prereqName];
        
        // Remove the course
        prereqData.courses = prereqData.courses.filter(course => course.code !== courseCode);
        
        // Update total credits
        prereqData.totalCredits = prereqData.courses.reduce((total, course) => 
            total + (parseFloat(course.credits) || 0), 0
        );

        // Recalculate prerequisite GPA
        updatedTranscript.prerequisite_gpa = calculatePrereqGPA(updatedTranscript.prerequisites, programName);

        setUserTranscript(updatedTranscript);
    };
    
    return (
        <div className="prerequisite-courses">
            {sortedMatchedCourses.map((course, index) => (
                <div key={index} className={`course-detail ${coursesUsedInGPA.has(course.code) ? 'used-in-gpa' : ''}`}>
                    <div className="course-info">
                        <span className="course-name">
                            {course.code} - {course.name} ({course.credits} credits)
                        </span>
                        <span className="course-institution">
                            {course.institution} - {course.term} {course.year}
                        </span>
                    </div>
                    <div className="course-stats">
                        <span className="course-grade">Grade: {course.grade}</span>
                        <button 
                            onClick={() => handleCourseRemove(course.code, programName, prereqName)}
                            className="remove-course-btn"
                        >
                            ✕
                        </button>
                    </div>
                </div>
            ))}
        </div>
    );
};

const gradeToPoints = (grade) => {
    const gradePoints = {
        'A+': 4.0, 'A': 4.0, 'A-': 3.7,
        'B+': 3.3, 'B': 3.0, 'B-': 2.7,
        'C+': 2.3, 'C': 2.0, 'C-': 1.7,
        'D+': 1.3, 'D': 1.0, 'D-': 0.7,
        'F': 0.0
    };
    return gradePoints[grade] || 0;
};

const calculatePrereqGPA = useCallback((prerequisites, programName) => {
    if (!prerequisites?.[programName]) {
        return 'N/A';
    }

    let totalPoints = 0;
    let totalCredits = 0;
    
    // Process prerequisites for the specific program
    Object.values(prerequisites[programName]).forEach(prereqData => {
        if (!prereqData.courses || prereqData.courses.length === 0) return;
        
        // Sort courses by grade points in descending order
        const sortedCourses = [...prereqData.courses].sort((a, b) => 
            gradeToPoints(b.grade) - gradeToPoints(a.grade)
        );
        
        // Get the required credits for this prerequisite
        const requiredCredits = parseFloat(prereqData.credits) || 0;
        
        let creditsUsed = 0;
        let courseIndex = 0;
        
        // Use best grades until we meet the credit requirement
        while (creditsUsed < requiredCredits && courseIndex < sortedCourses.length) {
            const course = sortedCourses[courseIndex];
            const courseCredits = parseFloat(course.credits) || 0;
            const gradePoints = gradeToPoints(course.grade);
            
            totalPoints += gradePoints * courseCredits;
            totalCredits += courseCredits;
            creditsUsed += courseCredits;
            courseIndex++;
        }
    });
    
    return totalCredits > 0 ? (totalPoints / totalCredits).toFixed(2) : 'N/A';
}, []);

const checkPrerequisites = (program, userTranscript) => {
    if (!program.requirements.prerequisites || !userTranscript.prerequisites?.[program.name]) {
        return false;
    }
    
    return Object.entries(program.requirements.prerequisites)
        .every(([prereqName, req]) => {
            const programPrereqs = userTranscript.prerequisites[program.name];
            if (!programPrereqs[prereqName]) return false;
            
            const prereqData = programPrereqs[prereqName];
            const totalCredits = prereqData.totalCredits || 0;
            const requiredCredits = parseFloat(req.credits) || 0;
            
            // Special handling for Anatomy and Physiology
            if (prereqName.toLowerCase().includes('anatomy') || prereqName.toLowerCase().includes('physiology')) {
                // Find all A&P courses
                const combinedAPCourses = prereqData.courses.filter(course => 
                    course.name.toLowerCase().includes('anatomy') && 
                    course.name.toLowerCase().includes('physiology')
                );
                
                // Calculate total credits from combined A&P courses
                const combinedCredits = combinedAPCourses.reduce((sum, course) => 
                    sum + parseFloat(course.credits || 0), 0
                );
                
                // If we have combined A&P courses, use their full credits
                if (combinedAPCourses.length > 0) {
                    return combinedCredits >= requiredCredits;
                }
            }
            
            // Check if lab requirement is met when applicable
            const meetsLabRequirement = !req.lab_required || 
                prereqData.courses.some(course => course.name.toLowerCase().includes('lab'));
            
            // Check if timeframe requirement is met when applicable
            const meetsTimeframe = !req.timeframe || 
                prereqData.courses.every(course => {
                    const courseYear = parseInt(course.year);
                    const currentYear = new Date().getFullYear();
                    return (currentYear - courseYear) <= parseInt(req.timeframe);
                });
            
            return totalCredits >= requiredCredits && meetsLabRequirement && meetsTimeframe;
        });
};

const checkPatientCareHours = (program, profileData) => {
    const requiredHours = program.requirements.experience["Patient Care Hours"] || 0;
    // Handle both camelCase and snake_case field names
    const userHours = parseInt(profileData?.direct_patient_care_hours || profileData?.directPatientCareHours || 0);
    return userHours >= requiredHours;
};

const checkShadowingHours = (program, profileData) => {
    const requiredHours = program.requirements.experience["Shadowing Hours"] || 0;
    // Handle both camelCase and snake_case field names
    const userHours = parseInt(profileData?.shadowing_hours || profileData?.shadowingHours || 0);
    return userHours >= requiredHours;
};
const getUserExperienceHours = (profileData, experienceType) => {
    if (!profileData) return 0;
    if (experienceType === "Patient Care Hours") {
        return parseInt(profileData.direct_patient_care_hours || profileData.directPatientCareHours || 0);
    }
    if (experienceType === "Shadowing Hours") {
        return parseInt(profileData.shadowing_hours || profileData.shadowingHours || 0);
    }
    return 0;
};


const processMultipleTranscripts = (transcripts) => {
    if (!Array.isArray(transcripts)) {
        console.error('Invalid transcripts data:', transcripts);
        return {
            allCoursesWithSource: [],
            calculatedGPA: 0,
            scienceGPA: 0,
            totalCredits: 0,
            scienceCredits: 0
        };
    }

    // Combine all courses from all transcripts with institution info
    const allCoursesWithSource = transcripts.flatMap(transcript => {
        if (!transcript) {
            console.warn('Invalid transcript entry:', transcript);
            return [];
        }

        try {
            // Handle both initial transcript loading and course addition cases
            if (transcript.transcript_data?.semesters) {
                // Initial transcript loading structure
                const institution = transcript.transcript_data?.academic_summary?.institution || 'Unknown Institution';
                return transcript.transcript_data.semesters.flatMap(semester => {
                    if (!semester?.courses) {
                        console.warn('Invalid semester data:', semester);
                        return [];
                    }
                    return semester.courses.map(course => ({
                        ...course,
                        institution,
                        term: semester.term || 'Unknown Term',
                        year: semester.year || 'Unknown Year'
                    }));
                });
            } else if (transcript.semesters) {
                // Course addition structure (from institutions array)
                const institution = transcript.name || 'Unknown Institution';
                return transcript.semesters.flatMap(semester => {
                    if (!semester?.courses) {
                        console.warn('Invalid semester data:', semester);
                        return [];
                    }
                    return semester.courses.map(course => ({
                        ...course,
                        institution,
                        term: semester.term || 'Unknown Term',
                        year: semester.year || 'Unknown Year'
                    }));
                });
            }
        } catch (error) {
            console.error('Error processing transcript:', error);
            return [];
        }
        console.warn('Invalid transcript structure:', transcript);
        return [];
    });

    // Calculate overall GPA across all transcripts
    let totalPoints = 0;
    let totalCredits = 0;
    let sciencePoints = 0;
    let scienceCredits = 0;

    allCoursesWithSource.forEach(course => {
        try {
            if (course?.grade && !['F', 'W', 'U'].includes(course.grade)) {
                const credits = parseFloat(course.credits) || 0;
                const gradePoints = gradeToPoints(course.grade);
                
                if (!isNaN(credits) && !isNaN(gradePoints)) {
                    totalPoints += credits * gradePoints;
                    totalCredits += credits;

                    // Update science GPA if it's a science course
                    if (course.is_science) {
                        sciencePoints += credits * gradePoints;
                        scienceCredits += credits;
                    }
                } else {
                    console.warn('Invalid credits or grade points:', { course, credits, gradePoints });
                }
            }
        } catch (error) {
            console.error('Error processing course:', error, course);
        }
    });

    const calculatedGPA = totalCredits > 0 ? totalPoints / totalCredits : 0;
    const scienceGPA = scienceCredits > 0 ? sciencePoints / scienceCredits : 0;

    return {
        allCoursesWithSource,
        calculatedGPA,
        scienceGPA,
        totalCredits,
        scienceCredits
    };
};

const checkProgramEligibility = (program) => {
    // First check if program is manually approved
    if (manuallyApprovedPrograms.includes(program.name)) {
        return 'eligible';
    }
    
    // Original eligibility logic
    // Check if program has prerequisites defined
    const hasPrereqs = program.requirements.prerequisites && 
        Object.keys(program.requirements.prerequisites).length > 0;
    
    if (!hasPrereqs) {
        return 'incomplete';
    }

    // Check if prerequisites data exists for this program
    const programPrereqs = userTranscript?.prerequisites?.[program.name];
    if (!programPrereqs || Object.keys(programPrereqs).length === 0) {
        return 'incomplete';
    }

    // Check all requirements
    const meetsGPA = userTranscript.calculated_gpa >= program.requirements.gpa.overall;
    const meetsScienceGPA = parseFloat(userTranscript.science_gpa) >= program.requirements.gpa.science;
    
    const meetsPrereqs = Object.entries(program.requirements.prerequisites)
        .every(([prereqName, req]) => {
            if (!programPrereqs[prereqName]) return false; // No data for this prereq
            const totalCredits = programPrereqs[prereqName].totalCredits || 0;
            const requiredCreditsNum = parseFloat(req.credits); // Attempt to parse requirement

            // If requirement is not a number (e.g., "4 Semesters"), consider met if any courses match
            if (isNaN(requiredCreditsNum)) {
                 // Check if courses array exists and has items
                 return programPrereqs[prereqName].courses && programPrereqs[prereqName].courses.length > 0;
            }
            // Otherwise, perform the numeric comparison
            return totalCredits >= requiredCreditsNum;
        });
    
    // Convert to numbers and handle both camelCase and snake_case field names
    const patientCareHours = parseInt(profileData?.direct_patient_care_hours || profileData?.directPatientCareHours || 0);
    const shadowingHours = parseInt(profileData?.shadowing_hours || profileData?.shadowingHours || 0);
    
    const meetsPatientCare = patientCareHours >= 
        (program.requirements.experience["Patient Care Hours"] || 0);
    const meetsShadowing = shadowingHours >= 
        (program.requirements.experience["Shadowing Hours"] || 0);

    // --- Add Standardized Test Check ---
    let meetsStandardizedTests = true; // Assume met unless a required test is missing
    const testsRequiredString = program.requirements.standardized_tests?.tests_required || ""; // Get the string like "GRE, CASPER"
    const userProfile = profileData; // User profile data from state

    // Normalize the required tests string for easier checking
    const requiredTests = testsRequiredString.toLowerCase().split(/,|\(|\)/) // Split by comma or parentheses
                                          .map(t => t.trim())
                                          .filter(t => t); // Remove empty strings

    // Check GRE requirement
    if (requiredTests.includes('gre')) {
        if (!userProfile?.gre_verbal_score && !userProfile?.gre_quantitative_score && !userProfile?.gre_analytical_writing_score) {
            meetsStandardizedTests = false; // Required but user has no scores entered
        }
    }

    // Check CASPER requirement
    if (requiredTests.includes('casper')) {
        if (!userProfile?.has_taken_casper) {
            meetsStandardizedTests = false; // Required but user hasn't checked the box
        }
    }

    // Check PA-CAT requirement
    if (requiredTests.includes('pa-cat') || requiredTests.includes('pacat')) { // Check for variations
        if (!userProfile?.has_taken_pa_cat) {
            meetsStandardizedTests = false; // Required but user hasn't checked the box
        }
    }

    // --- Update Eligibility Condition ---
    // Note: We removed the specific meetsGRE check as it's now part of meetsStandardizedTests
    if (meetsGPA && meetsScienceGPA && meetsPrereqs && meetsPatientCare && meetsShadowing && meetsStandardizedTests) {
        return 'eligible';
    }

    // If any check failed, it's ineligible (assuming prerequisites are complete)
    return 'ineligible';
};

// Cache program eligibility results to avoid recalculation
const programEligibilityCache = React.useMemo(() => {
    if (!programs || !userTranscript) return {};
    
    const cache = {};
    programs.forEach(program => {
        cache[program.name] = checkProgramEligibility(program);
    });
    return cache;
}, [programs, userTranscript, profileData]);

const programCounts = React.useMemo(() => {
    if (!programs || !userTranscript) return { eligible: 0, ineligible: 0, incomplete: 0 };

    const categorizedPrograms = programs.reduce((acc, program) => {
        const status = programEligibilityCache[program.name];
        acc[status]++;
        return acc;
    }, { eligible: 0, ineligible: 0, incomplete: 0 });

    return categorizedPrograms;
}, [programEligibilityCache]);

// Enhanced requirement comparison with visual indicators
const renderRequirementComparison = (requirement, studentValue, program, prereqName) => {
    let isMet = false;
    let percentage = 0;
    
    if (program && prereqName) {
        if (prereqName === "Patient Care Hours" || prereqName === "Shadowing Hours") {
            // For experience requirements
            isMet = parseFloat(studentValue) >= parseFloat(requirement);
            percentage = Math.min((parseFloat(studentValue) / parseFloat(requirement)) * 100, 100);
        } else {
            // For prerequisites, check the specific program's prerequisite data
            const programPrereqs = userTranscript?.prerequisites?.[program.name];
            if (programPrereqs && programPrereqs[prereqName]) {
                const totalCredits = programPrereqs[prereqName].totalCredits || 0;
                const requiredCreditsNum = parseFloat(requirement); // Attempt to parse requirement

                // If requirement is not a number (e.g., "4 Semesters"), consider met if any courses match
                if (isNaN(requiredCreditsNum)) {
                    isMet = programPrereqs[prereqName].courses && programPrereqs[prereqName].courses.length > 0;
                    percentage = isMet ? 100 : 0;
                } else {
                    // Otherwise, perform the numeric comparison
                    isMet = totalCredits >= requiredCreditsNum;
                    percentage = Math.min((totalCredits / requiredCreditsNum) * 100, 100);
                }
            }
        }
    } else {
        // For non-prerequisite requirements (like GPA)
        isMet = studentValue >= requirement;
        percentage = Math.min((studentValue / requirement) * 100, 100);
    }
    
    return (
        <div className={`requirement-indicator ${isMet ? 'met' : 'not-met'}`}>
            <div className="requirement-progress">
                <div className="progress-bar">
                    <div 
                        className="progress-fill" 
                        style={{ width: `${percentage}%` }}
                    ></div>
                </div>
                <span className="progress-text">{percentage.toFixed(0)}%</span>
            </div>
            <div className="requirement-status-icon">
                {isMet ? '✅' : '❌'}
            </div>
        </div>
    );
};

// Enhanced GPA comparison component
const renderGPAComparison = (required, actual, label) => {
    const isMet = actual >= required;
    const percentage = Math.min((actual / required) * 100, 100);
    
    return (
        <div className="gpa-comparison-modern">
            <div className="gpa-header">
                <h4>{label}</h4>
                <div className={`gpa-status ${isMet ? 'met' : 'not-met'}`}>
                    {isMet ? '✅' : '❌'}
                </div>
            </div>
            <div className="gpa-details">
                <div className="gpa-values">
                    <span className="required">Required: {required.toFixed(2)}</span>
                    <span className="actual">Your GPA: {actual.toFixed(2)}</span>
                </div>
                <div className="gpa-progress">
                    <div className="progress-bar">
                        <div 
                            className="progress-fill" 
                            style={{ width: `${percentage}%` }}
                        ></div>
                    </div>
                    <span className="progress-text">{percentage.toFixed(0)}%</span>
                </div>
            </div>
        </div>
    );
};

// Enhanced experience comparison component
const renderExperienceComparison = (required, actual, label) => {
    const isMet = actual >= required;
    const percentage = Math.min((actual / required) * 100, 100);
    
    return (
        <div className="experience-comparison-modern">
            <div className="experience-header">
                <div className="experience-icon">
                    {label.includes('Patient Care') ? '🏥' : 
                     label.includes('Shadowing') ? '👨‍⚕️' : '💼'}
                </div>
                <h4>{label}</h4>
                <div className={`experience-status ${isMet ? 'met' : 'not-met'}`}>
                    {isMet ? '✅' : '❌'}
                </div>
            </div>
            <div className="experience-details">
                <div className="experience-values">
                    <span className="required">Required: {required} hrs</span>
                    <span className="actual">Your Hours: {actual} hrs</span>
                </div>
                <div className="experience-progress">
                    <div className="progress-bar">
                        <div 
                            className="progress-fill" 
                            style={{ width: `${percentage}%` }}
                        ></div>
                    </div>
                    <span className="progress-text">{percentage.toFixed(0)}%</span>
                </div>
            </div>
        </div>
    );
};

const toggleSection = (section) => {
    setExpandedSections(prev => ({
        ...prev,
        [section]: !prev[section]
    }));
};

const toggleProgram = (programName) => {
    setExpandedPrograms(prev => ({
        ...prev,
        [programName]: !prev[programName]
    }));
};

const setActiveTab = (programName, tabName) => {
    setActiveTabs(prev => ({
        ...prev,
        [programName]: tabName
    }));
};

// Add GPA calculation helper function
const calculateOverallGPAs = (prerequisites) => {
    let totalPoints = 0;
    let totalCredits = 0;
    let sciencePoints = 0;
    let scienceCredits = 0;

    // Iterate through all prerequisites and their courses
    Object.values(prerequisites || {}).forEach(programPrereqs => {
        Object.values(programPrereqs || {}).forEach(prereq => {
            (prereq.courses || []).forEach(course => {
                if (!['F', 'W', 'U'].includes(course.grade)) {
                    const credits = parseFloat(course.credits) || 0;
                    const gradePoints = gradeToPoints(course.grade);
                    
                    if (!isNaN(credits) && !isNaN(gradePoints)) {
                        totalPoints += credits * gradePoints;
                        totalCredits += credits;

                        if (course.is_science) {
                            sciencePoints += credits * gradePoints;
                            scienceCredits += credits;
                        }
                    }
                }
            });
        });
    });

    return {
        cumulativeGPA: totalCredits > 0 ? (totalPoints / totalCredits).toFixed(2) : 'N/A',
        scienceGPA: scienceCredits > 0 ? (sciencePoints / scienceCredits).toFixed(2) : 'N/A'
    };
};

// Function to manually approve a program
const handleManualApproval = (programName) => {
    // Add program to manually approved list if not already there
    if (!manuallyApprovedPrograms.includes(programName)) {
        const updatedApprovedPrograms = [...manuallyApprovedPrograms, programName];
        setManuallyApprovedPrograms(updatedApprovedPrograms);
        
        // Save the updated results to the database
        const updatedResults = {
            programs: programs,
            userTranscript: userTranscript,
            profileData: profileData,
            manuallyApprovedPrograms: updatedApprovedPrograms
        };
        saveCurrentResults(updatedResults);
    }
};

// Function to remove manual approval
const handleRemoveApproval = (programName) => {
    // Remove program from manually approved list
    const updatedApprovedPrograms = manuallyApprovedPrograms.filter(name => name !== programName);
    setManuallyApprovedPrograms(updatedApprovedPrograms);
    
    // Save the updated results to the database
    const updatedResults = {
        programs: programs,
        userTranscript: userTranscript,
        profileData: profileData,
        manuallyApprovedPrograms: updatedApprovedPrograms
    };
    saveCurrentResults(updatedResults);
};

// Get unique states from programs for filtering
const getUniqueStates = () => {
    if (!programs) return [];
    const states = new Set();
    programs.forEach(program => {
        if (program.school?.state) {
            states.add(program.school.state.toUpperCase());
        }
    });
    return Array.from(states).sort();
};

// Filter and sort programs based on current settings
const getFilteredAndSortedPrograms = () => {
    if (!programs || !userTranscript) return [];
    
    let filteredPrograms = [...programs];
    
    // Filter by search term
    if (searchTerm) {
        filteredPrograms = filteredPrograms.filter(program => 
            program.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            program.school?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            program.school?.city?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            program.school?.state?.toLowerCase().includes(searchTerm.toLowerCase())
        );
    }
    
    // Filter by eligibility
    if (filterBy !== 'all') {
        filteredPrograms = filteredPrograms.filter(program => 
            programEligibilityCache[program.name] === filterBy
        );
    }
    
    // Filter by region
    if (regionFilter !== 'all') {
        filteredPrograms = filteredPrograms.filter(program => {
            const programState = program.school?.state;
            if (!programState) return false;
            const programRegion = getStateRegion(programState);
            return programRegion === regionFilter;
        });
    }
    
    // Filter by specific state
    if (stateFilter !== 'all') {
        filteredPrograms = filteredPrograms.filter(program => {
            const programState = program.school?.state;
            return programState?.toUpperCase() === stateFilter.toUpperCase();
        });
    }
    
    // Sort programs
    filteredPrograms.sort((a, b) => {
        switch (sortBy) {
            case 'name':
                return a.name.localeCompare(b.name);
            case 'gpa':
                return (b.requirements?.gpa?.overall || 0) - (a.requirements?.gpa?.overall || 0);
            case 'eligibility':
                const eligibilityOrder = { 'eligible': 0, 'ineligible': 1, 'incomplete': 2 };
                return eligibilityOrder[programEligibilityCache[a.name]] - eligibilityOrder[programEligibilityCache[b.name]];
            case 'location':
                const stateA = a.school?.state || '';
                const stateB = b.school?.state || '';
                if (stateA !== stateB) return stateA.localeCompare(stateB);
                return (a.school?.city || '').localeCompare(b.school?.city || '');
            default:
                return 0;
        }
    });
    
    return filteredPrograms;
};

// Clean program list item component matching Figma design
const ProgramListItem = ({ program, isExpanded, onToggle }) => {
    const eligibility = programEligibilityCache[program.name];
    const isManuallyApproved = manuallyApprovedPrograms.includes(program.name);
    
    // Calculate prerequisites completion
    const totalPrereqs = Object.keys(program.requirements?.prerequisites || {}).length;
    const completedPrereqs = Object.entries(program.requirements?.prerequisites || {}).filter(([prereqName, req]) => {
        const programPrereqs = userTranscript?.prerequisites?.[program.name];
        if (!programPrereqs?.[prereqName]) return false;
        const totalCredits = programPrereqs[prereqName].totalCredits || 0;
        const requiredCreditsNum = parseFloat(req.credits);
        return !isNaN(requiredCreditsNum) ? totalCredits >= requiredCreditsNum : totalCredits > 0;
    }).length;
    
    // Calculate completion percentage
    const completionPercentage = totalPrereqs > 0 ? Math.round((completedPrereqs / totalPrereqs) * 100) : 100;
    
    return (
        <div className="program-list-item" onClick={onToggle}>
            <div className="program-item-header">
                <div className="program-main-info">
                    <h3 className="program-name">
                        <a 
                            href={program.program_url} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            onClick={(e) => e.stopPropagation()}
                        >
                            {program.name}
                        </a>
                    </h3>
                    <div className="program-location">
                        {program.school?.city && program.school?.state 
                            ? `${program.school.city}, ${program.school.state}` 
                            : program.school?.name || 'Unknown Location'
                        }
                    </div>
                </div>
                
                <div className="program-stats">
                    <div className="stat-group">
                        <span className="stat-label">Min GPA:</span>
                        <span className="stat-value">{program.requirements?.gpa?.overall?.toFixed(1) || 'N/A'}</span>
                    </div>
                    
                    <div className="stat-group">
                        <span className="stat-label">Prerequisites:</span>
                        <span className="stat-value">{completedPrereqs}/{totalPrereqs}</span>
                    </div>
                    
                    <div className="stat-group completion">
                        <span className="stat-label">Completion:</span>
                        <div className="completion-bar">
                            <div 
                                className="completion-fill" 
                                style={{ width: `${completionPercentage}%` }}
                            ></div>
                        </div>
                    </div>
                </div>
                
                <div className="program-status">
                    <div className={`status-badge ${eligibility}`}>
                        {eligibility === 'eligible' ? 'Eligible' : 
                         eligibility === 'ineligible' ? 'Need Prerequisites' : 
                         'Incomplete'}
                    </div>
                    <span className="expand-arrow">▸</span>
                </div>
            </div>
            
            {isExpanded && (
                <div className="program-item-details">
                    {/* Program Information Section */}
                    <div className="program-info-section">
                        <div className="info-tabs">
                            <div 
                                className={`tab ${(activeTabs[program.name] || 'overview') === 'overview' ? 'active' : ''}`}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    setActiveTab(program.name, 'overview');
                                }}
                            >
                                Overview
                            </div>
                            <div 
                                className={`tab ${activeTabs[program.name] === 'requirements' ? 'active' : ''}`}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    setActiveTab(program.name, 'requirements');
                                }}
                            >
                                Requirements
                            </div>
                        </div>
                        
                        <div className="tab-content">
                            {/* Overview Tab */}
                            {(activeTabs[program.name] || 'overview') === 'overview' && (
                                <div className="program-overview">
                                    {/* Quick Stats Cards */}
                                    <div className="quick-stats-grid">
                                        <div className="stat-card">
                                            <div className="stat-icon">🎓</div>
                                            <div className="stat-content">
                                                <div className="stat-label">Minimum GPA</div>
                                                <div className="stat-value">{program.requirements?.gpa?.overall?.toFixed(2) || 'N/A'}</div>
                                            </div>
                                        </div>
                                        
                                        <div className="stat-card">
                                            <div className="stat-icon">🔬</div>
                                            <div className="stat-content">
                                                <div className="stat-label">Science GPA</div>
                                                <div className="stat-value">{program.requirements?.gpa?.science?.toFixed(2) || 'N/A'}</div>
                                            </div>
                                        </div>
                                        
                                        <div className="stat-card">
                                            <div className="stat-icon">🏥</div>
                                            <div className="stat-content">
                                                <div className="stat-label">Patient Care Hours</div>
                                                <div className="stat-value">{program.requirements?.experience?.['Patient Care Hours'] || 0}</div>
                                            </div>
                                        </div>
                                        
                                        <div className="stat-card">
                                            <div className="stat-icon">👩‍⚕️</div>
                                            <div className="stat-content">
                                                <div className="stat-label">Shadowing Hours</div>
                                                <div className="stat-value">{program.requirements?.experience?.['Shadowing Hours'] || 0}</div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Enhanced Program Statistics */}
                                    {program.program_data && (
                                        <div className="enhanced-stats-section">
                                            <h4>📊 Program Statistics</h4>
                                            <div className="enhanced-stats-grid">
                                                {/* Class Profile Stats */}
                                                {program.program_data.class_profile && Object.keys(program.program_data.class_profile).length > 0 && (
                                                    <>
                                                        {program.program_data.class_profile.total_applications && program.program_data.class_profile.total_applications !== 'No information provided' && (
                                                            <div className="enhanced-stat-card">
                                                                <div className="stat-icon">📝</div>
                                                                <div className="stat-content">
                                                                    <div className="stat-label">Applications</div>
                                                                    <div className="stat-value">{program.program_data.class_profile.total_applications}</div>
                                                                </div>
                                                            </div>
                                                        )}

                                                        {program.program_data.class_profile.total_matriculants && program.program_data.class_profile.total_matriculants !== 'No information provided' && (
                                                            <div className="enhanced-stat-card">
                                                                <div className="stat-icon">🎓</div>
                                                                <div className="stat-content">
                                                                    <div className="stat-label">Matriculants</div>
                                                                    <div className="stat-value">{program.program_data.class_profile.total_matriculants}</div>
                                                                </div>
                                                            </div>
                                                        )}

                                                        {program.program_data.class_profile.average_overall_gpa && program.program_data.class_profile.average_overall_gpa !== 'No information provided' && (
                                                            <div className="enhanced-stat-card">
                                                                <div className="stat-icon">📈</div>
                                                                <div className="stat-content">
                                                                    <div className="stat-label">Avg Overall GPA</div>
                                                                    <div className="stat-value">{program.program_data.class_profile.average_overall_gpa}</div>
                                                                </div>
                                                            </div>
                                                        )}

                                                        {program.program_data.class_profile.average_science_gpa && program.program_data.class_profile.average_science_gpa !== 'No information provided' && (
                                                            <div className="enhanced-stat-card">
                                                                <div className="stat-icon">🔬</div>
                                                                <div className="stat-content">
                                                                    <div className="stat-label">Avg Science GPA</div>
                                                                    <div className="stat-value">{program.program_data.class_profile.average_science_gpa}</div>
                                                                </div>
                                                            </div>
                                                        )}
                                                    </>
                                                )}

                                                {/* PANCE Pass Rate */}
                                                {program.program_data.pance_pass_rates && Object.keys(program.program_data.pance_pass_rates).length > 0 && (
                                                    <>
                                                        {program.program_data.pance_pass_rates.program_pass_rate && program.program_data.pance_pass_rates.program_pass_rate !== 'No information provided' && (
                                                            <div className="enhanced-stat-card pance-stat">
                                                                <div className="stat-icon">🏆</div>
                                                                <div className="stat-content">
                                                                    <div className="stat-label">PANCE Pass Rate</div>
                                                                    <div className="stat-value">{program.program_data.pance_pass_rates.program_pass_rate}</div>
                                                                    {program.program_data.pance_pass_rates.national_pass_rate && program.program_data.pance_pass_rates.national_pass_rate !== 'No information provided' && (
                                                                        <div className="stat-subtext">National: {program.program_data.pance_pass_rates.national_pass_rate}</div>
                                                                    )}
                                                                </div>
                                                            </div>
                                                        )}

                                                        {program.program_data.pance_pass_rates.candidates_took_pance && (
                                                            <div className="enhanced-stat-card">
                                                                <div className="stat-icon">👥</div>
                                                                <div className="stat-content">
                                                                    <div className="stat-label">PANCE Candidates</div>
                                                                    <div className="stat-value">{program.program_data.pance_pass_rates.candidates_took_pance}</div>
                                                                </div>
                                                            </div>
                                                        )}
                                                    </>
                                                )}

                                                {/* Attrition Data */}
                                                {program.program_data.attrition && Object.keys(program.program_data.attrition).length > 0 && (
                                                    <>
                                                        {program.program_data.attrition.graduation_rate && program.program_data.attrition.graduation_rate !== 'No information provided' && (
                                                            <div className="enhanced-stat-card">
                                                                <div className="stat-icon">🎯</div>
                                                                <div className="stat-content">
                                                                    <div className="stat-label">Graduation Rate</div>
                                                                    <div className="stat-value">{program.program_data.attrition.graduation_rate}</div>
                                                                </div>
                                                            </div>
                                                        )}

                                                        {program.program_data.attrition.entering_class_size && (
                                                            <div className="enhanced-stat-card">
                                                                <div className="stat-icon">👨‍🎓</div>
                                                                <div className="stat-content">
                                                                    <div className="stat-label">Class Size</div>
                                                                    <div className="stat-value">{program.program_data.attrition.entering_class_size}</div>
                                                                </div>
                                                            </div>
                                                        )}
                                                    </>
                                                )}

                                                {/* Tuition Information */}
                                                {program.program_data.tuition_deposits && program.program_data.tuition_deposits.tuition && program.program_data.tuition_deposits.tuition !== 'No information provided' && (
                                                    <div className="enhanced-stat-card tuition-stat">
                                                        <div className="stat-icon">💰</div>
                                                        <div className="stat-content">
                                                            <div className="stat-label">Tuition</div>
                                                            <div className="stat-value">{program.program_data.tuition_deposits.tuition}</div>
                                                        </div>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    )}

                                    {/* Program Details */}
                                    <div className="program-details-grid">
                                        <div className="detail-section">
                                            <h4>📍 Program Information</h4>
                                            <div className="detail-items">
                                                <div className="detail-item">
                                                    <span className="detail-label">School:</span>
                                                    <span className="detail-value">{program.school?.name || 'N/A'}</span>
                                                </div>
                                                <div className="detail-item">
                                                    <span className="detail-label">Location:</span>
                                                    <span className="detail-value">
                                                        {program.school?.city && program.school?.state 
                                                            ? `${program.school.city}, ${program.school.state}` 
                                                            : 'N/A'
                                                        }
                                                    </span>
                                                </div>
                                                <div className="detail-item">
                                                    <span className="detail-label">Program Website:</span>
                                                    <a 
                                                        href={program.program_url} 
                                                        target="_blank" 
                                                        rel="noopener noreferrer"
                                                        className="detail-link"
                                                    >
                                                        View Program Details
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div className="detail-section">
                                            <h4>🏆 Your Eligibility</h4>
                                            <div className="eligibility-overview">
                                                <div className="eligibility-item">
                                                    <span className="eligibility-label">Overall GPA:</span>
                                                    <div className="eligibility-status">
                                                        <span>{userTranscript?.calculated_gpa?.toFixed(2) || 'N/A'}</span>
                                                        <span className={`status ${userTranscript?.calculated_gpa >= program.requirements?.gpa?.overall ? 'met' : 'not-met'}`}>
                                                            {userTranscript?.calculated_gpa >= program.requirements?.gpa?.overall ? '✓' : '✗'}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div className="eligibility-item">
                                                    <span className="eligibility-label">Science GPA:</span>
                                                    <div className="eligibility-status">
                                                        <span>{userTranscript?.science_gpa?.toFixed(2) || 'N/A'}</span>
                                                        <span className={`status ${userTranscript?.science_gpa >= program.requirements?.gpa?.science ? 'met' : 'not-met'}`}>
                                                            {userTranscript?.science_gpa >= program.requirements?.gpa?.science ? '✓' : '✗'}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div className="eligibility-item">
                                                    <span className="eligibility-label">Prerequisites:</span>
                                                    <div className="eligibility-status">
                                                        <span>{completedPrereqs}/{totalPrereqs} completed</span>
                                                        <span className={`status ${completedPrereqs === totalPrereqs ? 'met' : 'not-met'}`}>
                                                            {completedPrereqs === totalPrereqs ? '✓' : '✗'}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            )}

                            {/* Requirements Tab */}
                            {activeTabs[program.name] === 'requirements' && (
                                <div className="requirements-tab">
                                    {/* Prerequisites Section */}
                                    <div className="prerequisites-section">
                                        <h4>📚 Prerequisites Details</h4>
                                        <div className="prerequisites-list">
                                            {Object.entries(program.requirements?.prerequisites || {}).map(([course, req]) => {
                                                const programPrereqs = userTranscript?.prerequisites?.[program.name];
                                                const prereqData = programPrereqs?.[course];
                                                const totalCredits = prereqData?.totalCredits || 0;
                                                const requiredCreditsNum = parseFloat(req.credits);
                                                const isMet = !isNaN(requiredCreditsNum) ? totalCredits >= requiredCreditsNum : totalCredits > 0;
                                                const matchedCourses = prereqData?.courses || [];
                                                
                                                // Sort courses by grade (best grades first) and separate used vs unused
                                                const sortedCourses = [...matchedCourses].sort((a, b) => {
                                                    const gradeA = gradeToPoints(a.grade);
                                                    const gradeB = gradeToPoints(b.grade);
                                                    if (gradeB !== gradeA) return gradeB - gradeA;
                                                    return parseInt(b.year) - parseInt(a.year);
                                                });
                                                
                                                // Determine which courses are actually being used in calculation
                                                const usedCourses = [];
                                                const unusedCourses = [];
                                                let creditsUsed = 0;
                                                const requiredCredits = parseFloat(req.credits) || 0;
                                                
                                                sortedCourses.forEach(course => {
                                                    const courseCredits = parseFloat(course.credits) || 0;
                                                    if (creditsUsed < requiredCredits) {
                                                        usedCourses.push(course);
                                                        creditsUsed += courseCredits;
                                                    } else {
                                                        unusedCourses.push(course);
                                                    }
                                                });
                                                
                                                return (
                                                    <div key={course} className="prerequisite-item-detailed">
                                                        <div className="prerequisite-header">
                                                            <div className="prerequisite-info">
                                                                <span className="course-name">{course} ({req.credits} credits)</span>
                                                                {req.lab_required && <span className="lab-required">Lab Required</span>}
                                                            </div>
                                                            <span className={`status ${isMet ? 'met' : 'not-met'}`}>
                                                                {isMet ? '✓' : '✗'}
                                                            </span>
                                                        </div>
                                                        {matchedCourses.length > 0 && (
                                                            <div className="matched-courses">
                                                                {/* Used courses section - shown at top */}
                                                                {usedCourses.length > 0 && (
                                                                    <div className="used-courses-section">
                                                                        <div className="courses-header used">
                                                                            <span>✓ Used for calculation:</span>
                                                                            <span className="credits-summary">{creditsUsed.toFixed(1)} / {req.credits} credits</span>
                                                                        </div>
                                                                        <div className="course-cards">
                                                                            {usedCourses.map((matchedCourse, index) => (
                                                                                <div key={index} className="course-card used">
                                                                                    <div className="course-details">
                                                                                        <span className="course-code-name">
                                                                                            {matchedCourse.code} - {matchedCourse.name}
                                                                                        </span>
                                                                                        <span className="course-meta">
                                                                                            {matchedCourse.institution} • {matchedCourse.term} {matchedCourse.year}
                                                                                        </span>
                                                                                    </div>
                                                                                    <div className="course-stats">
                                                                                        <span className="course-credits">{matchedCourse.credits} credits</span>
                                                                                        <span className={`course-grade grade-${matchedCourse.grade?.replace(/[+\-]/, '')}`}>
                                                                                            {matchedCourse.grade}
                                                                                        </span>
                                                                                    </div>
                                                                                </div>
                                                                            ))}
                                                                        </div>
                                                                    </div>
                                                                )}
                                                                
                                                                {/* Unused courses section - shown at bottom */}
                                                                {unusedCourses.length > 0 && (
                                                                    <div className="unused-courses-section">
                                                                        <div className="courses-header unused">
                                                                            <span>Other matching courses (not used):</span>
                                                                            <span className="courses-count">{unusedCourses.length} course{unusedCourses.length !== 1 ? 's' : ''}</span>
                                                                        </div>
                                                                        <div className="course-cards">
                                                                            {unusedCourses.map((matchedCourse, index) => (
                                                                                <div key={index} className="course-card unused">
                                                                                    <div className="course-details">
                                                                                        <span className="course-code-name">
                                                                                            {matchedCourse.code} - {matchedCourse.name}
                                                                                        </span>
                                                                                        <span className="course-meta">
                                                                                            {matchedCourse.institution} • {matchedCourse.term} {matchedCourse.year}
                                                                                        </span>
                                                                                    </div>
                                                                                    <div className="course-stats">
                                                                                        <span className="course-credits">{matchedCourse.credits} credits</span>
                                                                                        <span className={`course-grade grade-${matchedCourse.grade?.replace(/[+\-]/, '')}`}>
                                                                                            {matchedCourse.grade}
                                                                                        </span>
                                                                                    </div>
                                                                                </div>
                                                                            ))}
                                                                        </div>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        )}
                                                        {matchedCourses.length === 0 && !isMet && (
                                                            <div className="no-courses">
                                                                <span className="no-courses-text">No matching courses found</span>
                                                            </div>
                                                        )}
                                                    </div>
                                                );
                                            })}
                                        </div>
                                    </div>
                                </div>
                            )}


                        </div>

                    </div>
                </div>
            )}
        </div>
    );
};

return (
    <div className="pamatch-container">
        <header className="pamatch-header">
            <div className="header-left">
                <div className="header-title">
                    <span className="header-icon">📖</span>
                    <h1>PA Program Explorer</h1>
                </div>
            </div>
            <div className="header-right">
                <button 
                    onClick={() => navigate('/home')}
                    className="back-home-btn"
                >
                    Back to Home
                </button>
            </div>
        </header>

        {/* Add saved results indicator */}
        {usingSavedResults && savedResultsTimestamp && (
            <div className="saved-results-banner">
                <span>
                    Using saved results from {new Date(savedResultsTimestamp).toLocaleDateString()} at {new Date(savedResultsTimestamp).toLocaleTimeString()}
                </span>
                <button 
                    onClick={handleRecalculate}
                    className="recalculate-btn"
                    disabled={isLoading || savingResults}
                >
                    {isLoading ? 'Calculating...' : 'Recalculate'}
                </button>
            </div>
        )}

        {/* View Controls */}
        {!isLoading && userTranscript && (
            <>
                <div className="search-and-controls">
                    <div className="search-bar">
                        <span className="search-icon">🔍</span>
                        <input
                            type="text"
                            placeholder="Search programs or schools..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="search-input"
                        />
                    </div>
                    
                    <div className="filter-controls">
                        <div className="filter-dropdown">
                            <span className="filter-icon">⚡</span>
                            <select
                                value={filterBy}
                                onChange={(e) => setFilterBy(e.target.value)}
                                className="filter-select"
                            >
                                <option value="all">All Programs</option>
                                <option value="eligible">Eligible Only</option>
                                <option value="ineligible">Need Prerequisites</option>
                                <option value="incomplete">Incomplete Info</option>
                            </select>
                        </div>
                        
                        <div className="region-dropdown">
                            <span className="location-icon">📍</span>
                            <select
                                value={regionFilter}
                                onChange={(e) => {
                                    setRegionFilter(e.target.value);
                                    if (e.target.value !== 'all') setStateFilter('all'); // Reset state filter when region is selected
                                }}
                                className="region-select"
                            >
                                <option value="all">All Regions</option>
                                <optgroup label="Census Regions">
                                    <option value="Northeast">Northeast (9 states)</option>
                                    <option value="Midwest">Midwest (12 states)</option>
                                    <option value="South">South (17 states)</option>
                                    <option value="West">West (13 states)</option>
                                </optgroup>
                                <optgroup label="Census Divisions">
                                    <option value="New England">New England (6 states)</option>
                                    <option value="Mid-Atlantic">Mid-Atlantic (3 states)</option>
                                    <option value="East North Central">East North Central (5 states)</option>
                                    <option value="West North Central">West North Central (7 states)</option>
                                    <option value="South Atlantic">South Atlantic (9 states)</option>
                                    <option value="East South Central">East South Central (4 states)</option>
                                    <option value="West South Central">West South Central (4 states)</option>
                                    <option value="Mountain">Mountain (8 states)</option>
                                    <option value="Pacific">Pacific (5 states)</option>
                                </optgroup>
                            </select>
                        </div>
                        
                        <div className="state-dropdown">
                            <select
                                value={stateFilter}
                                onChange={(e) => {
                                    setStateFilter(e.target.value);
                                    if (e.target.value !== 'all') setRegionFilter('all'); // Reset region filter when state is selected
                                }}
                                className="state-select"
                            >
                                <option value="all">All States</option>
                                {getUniqueStates().map(state => (
                                    <option key={state} value={state}>{state}</option>
                                ))}
                            </select>
                        </div>
                        
                        <div className="sort-dropdown">
                            <select
                                value={sortBy}
                                onChange={(e) => setSortBy(e.target.value)}
                                className="sort-select"
                            >
                                <option value="name">Name</option>
                                <option value="gpa">GPA</option>
                                <option value="eligibility">Eligibility</option>
                                <option value="location">Location</option>
                            </select>
                        </div>
                        
                        <div className="view-toggle">
                            <button
                                className={`view-btn ${viewMode === 'accordion' ? 'active' : ''}`}
                                onClick={() => setViewMode('accordion')}
                                title="List View"
                            >
                                ☰
                            </button>
                            <button
                                className={`view-btn ${viewMode === 'grid' ? 'active' : ''}`}
                                onClick={() => setViewMode('grid')}
                                title="Grid View"
                            >
                                ⊞
                            </button>
                        </div>
                    </div>
                </div>

                {/* Program Summary Cards */}
                <div className="program-summary-cards">
                    <div 
                        className={`summary-card total ${filterBy === 'all' ? 'active' : ''}`}
                        onClick={() => setFilterBy('all')}
                    >
                        <div className="card-content">
                            <div className="card-info">
                                <div className="card-title">Total Programs</div>
                                <div className="card-number">{programs.length}</div>
                            </div>
                            <div className="card-icon">📖</div>
                        </div>
                    </div>
                    
                    <div 
                        className={`summary-card eligible ${filterBy === 'eligible' ? 'active' : ''}`}
                        onClick={() => setFilterBy('eligible')}
                    >
                        <div className="card-content">
                            <div className="card-info">
                                <div className="card-title">Eligible</div>
                                <div className="card-number">{programCounts.eligible}</div>
                            </div>
                            <div className="card-icon">✓</div>
                        </div>
                    </div>
                    
                    <div 
                        className={`summary-card need-prerequisites ${filterBy === 'ineligible' ? 'active' : ''}`}
                        onClick={() => setFilterBy('ineligible')}
                    >
                        <div className="card-content">
                            <div className="card-info">
                                <div className="card-title">Need Prerequisites</div>
                                <div className="card-number">{programCounts.ineligible}</div>
                            </div>
                            <div className="card-icon">⚠</div>
                        </div>
                    </div>
                    
                    <div 
                        className={`summary-card incomplete ${filterBy === 'incomplete' ? 'active' : ''}`}
                        onClick={() => setFilterBy('incomplete')}
                    >
                        <div className="card-content">
                            <div className="card-info">
                                <div className="card-title">Incomplete</div>
                                <div className="card-number">{programCounts.incomplete}</div>
                            </div>
                            <div className="card-icon">🕐</div>
                        </div>
                    </div>
                </div>

                {/* Results Count */}
                <div className="results-info">
                    <span className="results-count">
                        Showing {getFilteredAndSortedPrograms().length} of {programs.length} programs
                        {(filterBy !== 'all' || regionFilter !== 'all' || stateFilter !== 'all') && (
                            <span className="filter-indicator">
                                {' '}• Filtered by: {[
                                    filterBy !== 'all' ? (
                                        filterBy === 'eligible' ? 'Eligible' :
                                        filterBy === 'ineligible' ? 'Need Prerequisites' :
                                        filterBy === 'incomplete' ? 'Incomplete' : ''
                                    ) : null,
                                    regionFilter !== 'all' ? `${regionFilter} Region` : null,
                                    stateFilter !== 'all' ? `${stateFilter} State` : null
                                ].filter(Boolean).join(', ')}
                            </span>
                        )}
                    </span>
                    {(filterBy !== 'all' || regionFilter !== 'all' || stateFilter !== 'all') && (
                        <button 
                            className="clear-filter-btn"
                            onClick={() => {
                                setFilterBy('all');
                                setRegionFilter('all');
                                setStateFilter('all');
                            }}
                        >
                            Clear All Filters
                        </button>
                    )}
                </div>
            </>
        )}

        <div className="academic-summary">
            <div className="summary-section-header">
                <span className="section-icon">👤</span>
                <h2>Academic Summary</h2>
            </div>
            {isLoading ? (
                <div className="gpa-cards-container">
                    <div className="gpa-card cumulative-gpa loading">
                        <div className="card-header">
                            <span className="card-label">Cumulative GPA</span>
                            <span className="card-icon">📊</span>
                        </div>
                        <div className="gpa-value skeleton-loader"></div>
                    </div>
                    <div className="gpa-card science-gpa loading">
                        <div className="card-header">
                            <span className="card-label">Science GPA</span>
                            <span className="card-icon">📊</span>
                        </div>
                        <div className="gpa-value skeleton-loader"></div>
                    </div>
                </div>
            ) : (
                <div className="gpa-cards-container">
                    <div className="gpa-card cumulative-gpa">
                        <div className="card-header">
                            <span className="card-label">Cumulative GPA</span>
                            <span className="card-icon">📊</span>
                        </div>
                        <div className="gpa-value">{userTranscript?.calculated_gpa?.toFixed(2) || 'N/A'}</div>
                    </div>
                    <div className="gpa-card science-gpa">
                        <div className="card-header">
                            <span className="card-label">Science GPA</span>
                            <span className="card-icon">📊</span>
                        </div>
                        <div className="gpa-value">{userTranscript?.science_gpa?.toFixed(2) || 'N/A'}</div>
                    </div>
                </div>
            )}
        </div>

        <div className="results-container">
            {isLoading && (
                <div className="programs-comparison">
                    <div className="eligible-programs">
                        <div className="section-header">
                            <h2>Eligible Programs</h2>
                        </div>
                        <ProgramSkeleton />
                        <ProgramSkeleton />
                    </div>
                    <div className="ineligible-programs">
                        <div className="section-header">
                            <h2>Programs Requiring Additional Prerequisites</h2>
                        </div>
                        <ProgramSkeleton />
                        <ProgramSkeleton />
                    </div>
                    <div className="incomplete-programs">
                        <div className="section-header">
                            <h2>Incomplete/Unknown Eligibility</h2>
                        </div>
                        <ProgramSkeleton />
                    </div>
                </div>
            )}

            {error && (
                <div className="error-message">
                    {error}
                </div>
            )}

            {userTranscript && !isLoading && (
                <div className="programs-list">
                    {getFilteredAndSortedPrograms().map((program, index) => (
                        <ProgramListItem
                            key={`${program.name}-${index}`}
                            program={program}
                            isExpanded={expandedPrograms[program.name]}
                            onToggle={() => toggleProgram(program.name)}
                        />
                    ))}
                </div>
            )}
            
            <CourseSelectionModal />
        </div>
    </div>
    );
};

export default PAMatch; 