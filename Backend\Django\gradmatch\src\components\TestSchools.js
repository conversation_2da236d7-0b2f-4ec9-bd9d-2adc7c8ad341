import React, { useState, useEffect } from 'react';
import axios from 'axios';

function TestSchools() {
    const [schools, setSchools] = useState([]);
    const [error, setError] = useState(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchSchools = async () => {
            try {
                const response = await axios.get('http://127.0.0.1:8000/api/schools/all');
                console.log('Raw response:', response);
                if (response.data.status === 'success') {
                    setSchools(response.data.schools);
                    setError(null);
                } else {
                    setError('Failed to load schools');
                }
            } catch (err) {
                setError(err.message);
                console.error('Error:', err);
            } finally {
                setLoading(false);
            }
        };

        fetchSchools();
    }, []);

    if (loading) return <div>Loading...</div>;
    if (error) return <div>Error: {error}</div>;

    return (
        <div>
            <h2>Test Schools Component</h2>
            <div style={{ border: '1px solid black', padding: '10px', margin: '10px' }}>
                <h3>Debug Info:</h3>
                <p>Number of schools: {schools.length}</p>
                <p>Schools array type: {Array.isArray(schools) ? 'Array' : typeof schools}</p>
            </div>
            <ul>
                {schools.map(school => (
                    <li key={school.id}>{school.name}</li>
                ))}
            </ul>
        </div>
    );
}

export default TestSchools; 