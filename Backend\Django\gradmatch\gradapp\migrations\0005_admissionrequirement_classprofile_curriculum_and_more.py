# Generated by Django 5.0.1 on 2024-12-18 06:20

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('gradapp', '0004_remove_classprofile_program_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='AdmissionRequirement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('requirements', models.JSONField()),
                ('program', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='gradapp.program')),
            ],
        ),
        migrations.CreateModel(
            name='ClassProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('entering_class_year', models.IntegerField()),
                ('number_of_applicants', models.IntegerField()),
                ('average_gpa', models.DecimalField(decimal_places=2, max_digits=3)),
                ('average_age', models.IntegerField()),
                ('in_state_students_percentage', models.DecimalField(decimal_places=2, max_digits=5)),
                ('program', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='gradapp.program')),
            ],
        ),
        migrations.CreateModel(
            name='Curriculum',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.CharField(max_length=100)),
                ('courses', models.JSONField()),
                ('program', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='gradapp.program')),
            ],
        ),
        migrations.CreateModel(
            name='ProgramCompetency',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('competencies', models.TextField()),
                ('program', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='gradapp.program')),
            ],
        ),
    ]
