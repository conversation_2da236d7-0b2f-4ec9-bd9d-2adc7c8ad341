import os
import boto3
import logging
from botocore.exceptions import Client<PERSON>rror
from django.conf import settings
from typing import Optional, BinaryIO, Tuple
import uuid
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class S3Service:
    def __init__(self):
        self.s3_client = boto3.client(
            's3',
            region_name=os.getenv('AWS_REGION', 'us-east-2'),
            aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
            aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY')
        )
        self.bucket = os.getenv('S3_BUCKET', 'gradmatch')

    def _get_user_prefix(self, user_id: int) -> str:
        """Generate the S3 prefix for a user's transcripts"""
        return f"transcripts/user_{user_id}"  # Remove trailing slash for consistency

    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize the filename to be safe for S3 while preserving the original name"""
        # Remove any path components
        filename = os.path.basename(filename)
        # Replace potentially problematic characters
        filename = filename.replace(' ', '_').lower()  # Convert to lowercase for consistency
        # Ensure it's a valid S3 key
        return ''.join(c for c in filename if c.isalnum() or c in '._-')

    def upload_transcript(self, user_id: int, file_obj: BinaryIO, original_filename: str) -> Tuple[str, int]:
        """
        Upload a transcript file to S3 with user-specific path
        Returns: (s3_key, file_size)
        """
        try:
            # Sanitize the filename while preserving the original name
            safe_filename = self._sanitize_filename(original_filename)
            # Format: transcripts/user_1/filename.pdf (no double slashes)
            s3_key = f"{self._get_user_prefix(user_id)}/{safe_filename}"

            # Get file size
            file_obj.seek(0, 2)  # Seek to end
            file_size = file_obj.tell()
            file_obj.seek(0)  # Reset to beginning

            # Upload to S3 with proper content type
            self.s3_client.upload_fileobj(
                file_obj,
                self.bucket,
                s3_key,
                ExtraArgs={
                    'ContentType': 'application/pdf',
                    'Metadata': {
                        'user_id': str(user_id),
                        'original_filename': original_filename,
                        'normalized_key': s3_key  # Store the normalized key in metadata
                    }
                }
            )
            
            logger.info(f"Successfully uploaded transcript for user {user_id}: {s3_key}")
            return s3_key, file_size

        except Exception as e:
            logger.error(f"Error uploading transcript for user {user_id}: {str(e)}")
            raise

    def get_transcript(self, user_id: int, s3_key: str) -> Optional[bytes]:
        """
        Securely retrieve a transcript file
        Verifies the user has access to the requested file
        """
        try:
            # Verify the file belongs to the user
            if not s3_key.startswith(self._get_user_prefix(user_id)):
                logger.warning(f"Unauthorized access attempt to {s3_key} by user {user_id}")
                return None

            response = self.s3_client.get_object(
                Bucket=self.bucket,
                Key=s3_key
            )
            return response['Body'].read()

        except ClientError as e:
            if e.response['Error']['Code'] == 'NoSuchKey':
                logger.warning(f"Transcript not found: {s3_key}")
                return None
            raise

    def delete_transcript(self, user_id: int, s3_key: str) -> bool:
        """
        Securely delete a transcript file
        Verifies the user has access to the file before deletion
        """
        try:
            # Verify the file belongs to the user
            if not s3_key.startswith(self._get_user_prefix(user_id)):
                logger.warning(f"Unauthorized deletion attempt of {s3_key} by user {user_id}")
                return False

            self.s3_client.delete_object(
                Bucket=self.bucket,
                Key=s3_key
            )
            logger.info(f"Successfully deleted transcript {s3_key} for user {user_id}")
            return True

        except Exception as e:
            logger.error(f"Error deleting transcript {s3_key}: {str(e)}")
            return False

    def generate_presigned_url(self, user_id: int, s3_key: str, expires_in: int = 3600) -> Optional[str]:
        """
        Generate a temporary pre-signed URL for transcript download
        Verifies the user has access to the requested file
        """
        try:
            # Verify the file belongs to the user
            if not s3_key.startswith(self._get_user_prefix(user_id)):
                logger.warning(f"Unauthorized URL generation attempt for {s3_key} by user {user_id}")
                return None

            url = self.s3_client.generate_presigned_url(
                'get_object',
                Params={
                    'Bucket': self.bucket,
                    'Key': s3_key
                },
                ExpiresIn=expires_in
            )
            return url

        except Exception as e:
            logger.error(f"Error generating presigned URL for {s3_key}: {str(e)}")
            return None

    def list_user_transcripts(self, user_id: int) -> list:
        """List all transcripts in the user's S3 folder"""
        try:
            prefix = self._get_user_prefix(user_id)
            response = self.s3_client.list_objects_v2(
                Bucket=self.bucket,
                Prefix=prefix
            )
            
            files = []
            if 'Contents' in response:
                for obj in response['Contents']:
                    # Get just the filename from the full path
                    filename = os.path.basename(obj['Key'])
                    if filename:  # Skip empty names (like directory markers)
                        files.append({
                            'filename': filename,
                            'upload_date': obj['LastModified'],
                            'size': obj['Size'],
                            'key': obj['Key']
                        })
            
            return files
            
        except ClientError as e:
            logger.error(f"Error listing files for user {user_id}: {str(e)}")
            raise 