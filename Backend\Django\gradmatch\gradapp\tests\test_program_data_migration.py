"""
Unit tests for Program Data Migration functionality
"""

import json
import tempfile
import unittest
from decimal import Decimal
from pathlib import Path
from unittest.mock import Mock, patch

from django.test import TestCase
from django.db import IntegrityError

from gradapp.models import Program, School
from gradapp.services.program_data_migration import (
    ProgramDataMigrationProcessor,
    DataValidator,
    DataValidationError,
    MigrationError
)


class DataValidatorTestCase(unittest.TestCase):
    """Test cases for DataValidator utility class"""
    
    def setUp(self):
        self.validator = DataValidator()
    
    def test_clean_null_values(self):
        """Test null value cleaning"""
        # Test None input
        self.assertIsNone(self.validator.clean_null_values(None))
        
        # Test null-like strings
        null_strings = [
            'No information provided',
            'NOT APPLICABLE',
            'n/a',
            'NA',
            'null',
            'NONE',
            '',
            '   ',
            'unknown',
            'Not specified',
            'not available'
        ]
        
        for null_str in null_strings:
            with self.subTest(null_str=null_str):
                self.assertIsNone(self.validator.clean_null_values(null_str))
        
        # Test valid values
        valid_values = ['Yes', 'No', 'Required', '3.5', 'Some text']
        for value in valid_values:
            with self.subTest(value=value):
                self.assertEqual(self.validator.clean_null_values(value), value)
    
    def test_convert_to_boolean(self):
        """Test boolean conversion"""
        # Test None input
        self.assertIsNone(self.validator.convert_to_boolean(None))
        
        # Test boolean input
        self.assertTrue(self.validator.convert_to_boolean(True))
        self.assertFalse(self.validator.convert_to_boolean(False))
        
        # Test true-like strings
        true_strings = ['yes', 'YES', 'True', 'TRUE', '1', 'required', 'ACCEPTED', 'available']
        for true_str in true_strings:
            with self.subTest(true_str=true_str):
                self.assertTrue(self.validator.convert_to_boolean(true_str))
        
        # Test false-like strings
        false_strings = ['no', 'NO', 'False', 'FALSE', '0', 'not required', 'NOT ACCEPTED', 'not available']
        for false_str in false_strings:
            with self.subTest(false_str=false_str):
                self.assertFalse(self.validator.convert_to_boolean(false_str))
        
        # Test invalid strings
        invalid_strings = ['maybe', 'sometimes', 'invalid']
        for invalid_str in invalid_strings:
            with self.subTest(invalid_str=invalid_str):
                self.assertIsNone(self.validator.convert_to_boolean(invalid_str))
    
    def test_extract_numeric_value(self):
        """Test numeric value extraction"""
        # Test None input
        self.assertIsNone(self.validator.extract_numeric_value(None))
        
        # Test numeric input
        self.assertEqual(self.validator.extract_numeric_value(42), 42)
        self.assertEqual(self.validator.extract_numeric_value(3.14), 3.14)
        
        # Test string numeric values
        self.assertEqual(self.validator.extract_numeric_value('42'), 42)
        self.assertEqual(self.validator.extract_numeric_value('3.14'), 3.14)
        self.assertEqual(self.validator.extract_numeric_value('-5'), -5)
        
        # Test strings with extra characters
        self.assertEqual(self.validator.extract_numeric_value('$42'), 42)
        self.assertEqual(self.validator.extract_numeric_value('3.5 minimum'), 3.5)
        self.assertEqual(self.validator.extract_numeric_value('Score: 85%'), 85)
        
        # Test invalid strings
        self.assertIsNone(self.validator.extract_numeric_value('no number here'))
        self.assertIsNone(self.validator.extract_numeric_value(''))
    
    def test_validate_gpa(self):
        """Test GPA validation"""
        # Test None input
        self.assertIsNone(self.validator.validate_gpa(None))
        
        # Test valid GPAs
        valid_gpas = [
            ('3.5', Decimal('3.5')),
            (3.5, Decimal('3.5')),
            ('4.0', Decimal('4.0')),
            ('0.0', Decimal('0.0')),
            ('3.75 minimum', Decimal('3.75')),
        ]
        
        for input_gpa, expected in valid_gpas:
            with self.subTest(input_gpa=input_gpa):
                result = self.validator.validate_gpa(input_gpa)
                self.assertEqual(result, expected)
        
        # Test invalid GPAs
        invalid_gpas = ['5.0', '-1.0', 'not a gpa', '', 'abc']
        for invalid_gpa in invalid_gpas:
            with self.subTest(invalid_gpa=invalid_gpa):
                self.assertIsNone(self.validator.validate_gpa(invalid_gpa))


class ProgramDataMigrationProcessorTestCase(TestCase):
    """Test cases for ProgramDataMigrationProcessor"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        self.temp_path = Path(self.temp_dir)
        
        # Create sample JSON data
        self.sample_program_data = {
            "general_information": {
                "program_name": "Test PA Program",
                "school_name": "Test University",
                "address": "123 Test St, Test City, TS 12345",
                "phone": "(*************",
                "website": "https://test.edu",
                "mission_statement": "To train excellent PAs",
                "estimated_class_size": "30",
                "program_length": "27 months",
                "start_month": "August"
            },
            "requirements": {
                "gpa_requirements": {
                    "minimum_overall": "3.0",
                    "minimum_science": "3.2"
                }
            }
        }
        
        # Create test JSON file
        self.test_json_file = self.temp_path / "test_program.json"
        with open(self.test_json_file, 'w') as f:
            json.dump(self.sample_program_data, f)
    
    def tearDown(self):
        """Clean up test fixtures"""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_processor_initialization(self):
        """Test processor initialization"""
        # Test valid directory
        processor = ProgramDataMigrationProcessor(self.temp_dir)
        self.assertEqual(processor.json_directory, self.temp_path)
        self.assertEqual(processor.stats['total_files'], 0)
        self.assertEqual(len(processor.errors), 0)
        
        # Test invalid directory
        with self.assertRaises(MigrationError):
            ProgramDataMigrationProcessor('/nonexistent/directory')
    
    def test_validate_json_structure(self):
        """Test JSON structure validation"""
        processor = ProgramDataMigrationProcessor(self.temp_dir)
        
        # Test valid structure
        valid_data = {
            "general_information": {
                "program_name": "Test Program"
            }
        }
        self.assertTrue(processor._validate_json_structure(valid_data, "test.json"))
        
        # Test missing general_information
        invalid_data = {"other_field": "value"}
        self.assertFalse(processor._validate_json_structure(invalid_data, "test.json"))
        
        # Test missing program_name
        invalid_data = {
            "general_information": {
                "other_field": "value"
            }
        }
        self.assertFalse(processor._validate_json_structure(invalid_data, "test.json"))
    
    def test_get_or_create_school(self):
        """Test school creation/retrieval"""
        processor = ProgramDataMigrationProcessor(self.temp_dir)
        
        general_info = {
            "school_name": "Test University",
            "address": "123 Test St",
            "website": "https://test.edu",
            "mission_statement": "Test mission"
        }
        
        # Test school creation
        school = processor._get_or_create_school(general_info)
        self.assertIsNotNone(school)
        self.assertEqual(school.name, "Test University")
        self.assertEqual(school.location, "123 Test St")
        
        # Test school retrieval (should get same school)
        school2 = processor._get_or_create_school(general_info)
        self.assertEqual(school.id, school2.id)
        
        # Test with missing school name
        general_info_no_name = {
            "program_name": "University of Test - PA Program"
        }
        school3 = processor._get_or_create_school(general_info_no_name)
        self.assertIsNotNone(school3)
        self.assertEqual(school3.name, "University of Test")
    
    def test_extract_class_size(self):
        """Test class size extraction"""
        processor = ProgramDataMigrationProcessor(self.temp_dir)
        
        # Test with estimated_class_size
        general_info = {"estimated_class_size": "30"}
        self.assertEqual(processor._extract_class_size(general_info), 30)
        
        # Test with class_size
        general_info = {"class_size": "25 students"}
        self.assertEqual(processor._extract_class_size(general_info), 25)
        
        # Test with no class size info
        general_info = {}
        self.assertEqual(processor._extract_class_size(general_info), 0)
        
        # Test with invalid class size
        general_info = {"estimated_class_size": "varies"}
        self.assertEqual(processor._extract_class_size(general_info), 0)
    
    def test_update_program_fields(self):
        """Test program field updates"""
        processor = ProgramDataMigrationProcessor(self.temp_dir)
        
        # Create a school and program
        school = School.objects.create(
            name="Test School",
            location="Test Location",
            mission_statement="Test Mission",
            website="https://test.edu"
        )
        
        program = Program.objects.create(
            name="Test Program",
            school=school,
            description="Test Description",
            url="https://test.edu/program",
            class_size=30
        )
        
        general_info = {
            "phone": "(*************",
            "address": "123 Test St",
            "mission_statement": "Updated mission",
            "program_length": "27 months",
            "start_month": "August",
            "part_time_option": "No",
            "distance_learning": "Yes"
        }
        
        processor._update_program_fields(program, general_info)
        
        self.assertEqual(program.phone, "(*************")
        self.assertEqual(program.address, "123 Test St")
        self.assertEqual(program.mission_statement, "Updated mission")
        self.assertEqual(program.program_length, "27 months")
        self.assertEqual(program.start_month, "August")
        self.assertEqual(program.part_time_option, "No")
        self.assertEqual(program.distance_learning, "Yes")
    
    def test_process_single_program_success(self):
        """Test successful single program processing"""
        processor = ProgramDataMigrationProcessor(self.temp_dir)
        
        # Process the test file
        result = processor.process_single_program(self.test_json_file)
        self.assertTrue(result)
        
        # Verify program was created
        program = Program.objects.get(name="Test PA Program")
        self.assertIsNotNone(program)
        self.assertEqual(program.school.name, "Test University")
        self.assertEqual(program.phone, "(*************")
        self.assertEqual(program.class_size, 30)
    
    def test_process_single_program_invalid_json(self):
        """Test processing with invalid JSON"""
        processor = ProgramDataMigrationProcessor(self.temp_dir)
        
        # Create invalid JSON file
        invalid_json_file = self.temp_path / "invalid.json"
        with open(invalid_json_file, 'w') as f:
            f.write("{ invalid json }")
        
        result = processor.process_single_program(invalid_json_file)
        self.assertFalse(result)
        self.assertEqual(len(processor.errors), 1)
        self.assertEqual(processor.errors[0]['type'], 'json_error')
    
    def test_process_all_programs(self):
        """Test processing all programs in directory"""
        processor = ProgramDataMigrationProcessor(self.temp_dir)
        
        # Create additional test files
        for i in range(3):
            program_data = self.sample_program_data.copy()
            program_data['general_information']['program_name'] = f"Test Program {i+1}"
            program_data['general_information']['school_name'] = f"Test School {i+1}"
            
            test_file = self.temp_path / f"program_{i+1}.json"
            with open(test_file, 'w') as f:
                json.dump(program_data, f)
        
        # Process all programs
        report = processor.process_all_programs(batch_size=2)
        
        # Verify results
        self.assertEqual(report['statistics']['total_files'], 4)  # 3 new + 1 original
        self.assertEqual(report['statistics']['processed_successfully'], 4)
        self.assertEqual(report['statistics']['failed_processing'], 0)
        self.assertEqual(report['statistics']['programs_created'], 4)
        self.assertEqual(report['success_rate'], 100.0)
        
        # Verify programs were created
        self.assertEqual(Program.objects.count(), 4)
    
    def test_generate_report(self):
        """Test report generation"""
        processor = ProgramDataMigrationProcessor(self.temp_dir)
        
        # Add some test data
        processor.stats['total_files'] = 10
        processor.stats['processed_successfully'] = 8
        processor.stats['failed_processing'] = 2
        processor.processed_programs = [1, 2, 3]
        processor.errors = [
            {'file': 'test1.json', 'error': 'Test error', 'type': 'test_error'}
        ]
        
        report = processor._generate_report()
        
        self.assertIn('timestamp', report)
        self.assertIn('statistics', report)
        self.assertIn('processed_programs', report)
        self.assertIn('errors', report)
        self.assertIn('success_rate', report)
        
        self.assertEqual(report['statistics']['total_files'], 10)
        self.assertEqual(report['success_rate'], 80.0)
        self.assertEqual(len(report['errors']), 1)
    
    def test_get_processing_summary(self):
        """Test processing summary generation"""
        processor = ProgramDataMigrationProcessor(self.temp_dir)
        
        # Add test data
        processor.stats.update({
            'total_files': 5,
            'processed_successfully': 4,
            'failed_processing': 1,
            'programs_created': 2,
            'programs_updated': 2,
            'schools_created': 1,
            'schools_updated': 1
        })
        
        processor.errors = [
            {'file': 'error1.json', 'error': 'Error 1', 'type': 'error'},
            {'file': 'error2.json', 'error': 'Error 2', 'type': 'error'}
        ]
        
        summary = processor.get_processing_summary()
        
        self.assertIn('Program Data Migration Summary', summary)
        self.assertIn('Total Files: 5', summary)
        self.assertIn('Successfully Processed: 4', summary)
        self.assertIn('Success Rate: 80.00%', summary)
        self.assertIn('Errors Encountered: 2', summary)


class MigrationIntegrationTestCase(TestCase):
    """Integration tests for the complete migration process"""
    
    def setUp(self):
        """Set up integration test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        self.temp_path = Path(self.temp_dir)
        
        # Create comprehensive test data
        self.comprehensive_program_data = {
            "general_information": {
                "program_name": "Comprehensive PA Program",
                "school_name": "Comprehensive University",
                "address": "456 Comprehensive Ave, Comp City, CC 67890",
                "phone": "(*************",
                "website": "https://comprehensive.edu",
                "mission_statement": "Comprehensive PA education",
                "estimated_class_size": "45",
                "program_length": "24 months",
                "start_month": "January",
                "part_time_option": "No",
                "distance_learning": "Yes",
                "on_campus_housing": "Available",
                "admission_type": "Rolling"
            },
            "requirements": {
                "gpa_requirements": {
                    "minimum_overall": "3.2",
                    "minimum_science": "3.0",
                    "minimum_prereq": "3.1"
                },
                "healthcare_experience": {
                    "required": "Yes",
                    "hours_needed": "1000",
                    "paid_accepted": "Yes",
                    "volunteer_accepted": "Yes"
                }
            },
            "pance_pass_rates": [
                {
                    "year": "2023",
                    "group": "First Time Takers",
                    "candidates_took_pance": "42",
                    "program_pass_rate": "95%",
                    "national_pass_rate": "93%"
                }
            ],
            "class_profile": {
                "year": "2023",
                "total_applications": "1200",
                "total_matriculants": "45",
                "average_overall_gpa": "3.65",
                "average_science_gpa": "3.58"
            }
        }
        
        # Create test file
        self.test_file = self.temp_path / "comprehensive_program.json"
        with open(self.test_file, 'w') as f:
            json.dump(self.comprehensive_program_data, f)
    
    def tearDown(self):
        """Clean up integration test fixtures"""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_complete_migration_workflow(self):
        """Test the complete migration workflow"""
        processor = ProgramDataMigrationProcessor(self.temp_dir)
        
        # Run migration
        report = processor.process_all_programs()
        
        # Verify successful processing
        self.assertEqual(report['statistics']['total_files'], 1)
        self.assertEqual(report['statistics']['processed_successfully'], 1)
        self.assertEqual(report['statistics']['failed_processing'], 0)
        self.assertEqual(report['success_rate'], 100.0)
        
        # Verify database objects were created
        self.assertEqual(School.objects.count(), 1)
        self.assertEqual(Program.objects.count(), 1)
        
        # Verify program details
        program = Program.objects.first()
        self.assertEqual(program.name, "Comprehensive PA Program")
        self.assertEqual(program.school.name, "Comprehensive University")
        self.assertEqual(program.phone, "(*************")
        self.assertEqual(program.class_size, 45)
        self.assertEqual(program.program_length, "24 months")
        self.assertEqual(program.start_month, "January")
        self.assertEqual(program.distance_learning, "Yes")
        
        # Verify school details
        school = program.school
        self.assertEqual(school.location, "456 Comprehensive Ave, Comp City, CC 67890")
        self.assertEqual(school.website, "https://comprehensive.edu")