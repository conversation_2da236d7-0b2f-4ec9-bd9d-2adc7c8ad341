from django.core.management.base import BaseCommand
import json
from gradapp.models import School, Program  # Make sure this import matches your app and model names

class Command(BaseCommand):
    help = 'Import data from a JSON file into the database'

    def handle(self, *args, **options):
        # Correct file path for your JSON data file
        file_path = r'C:\Users\<USER>\OneDrive\Desktop\RFUandUNRPA.json'
        
        with open(file_path, 'r') as file:
            schools_data = json.load(file)  # Assuming the file is a list of schools
            
            for school_data in schools_data:  # Directly iterate over the list
                school, created = School.objects.get_or_create(
                    name=school_data['name'],
                    defaults={
                        'location': school_data.get('location', ''),
                        'mission_statement': school_data.get('mission_statement', ''),
                        'website': school_data.get('website', '')
                    }
                )
                
                if created:
                    print(f'Created new school: {school.name}')
                else:
                    print(f'School already exists: {school.name}')

                # Continue with additional logic as necessary
