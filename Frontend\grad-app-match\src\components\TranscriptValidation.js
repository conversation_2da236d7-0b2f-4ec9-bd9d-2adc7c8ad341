import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

const TranscriptValidation = ({ 
    transcriptData, 
    validatedTranscript, 
    validationStatus,
    isProcessing,
    onValidate 
}) => {
    const navigate = useNavigate();
    const [editedData, setEditedData] = useState([]);
    const [error, setError] = useState(null);

    // Extract academic summary and semester data with null checking
    const academicSummary = validatedTranscript?.academic_summary || transcriptData?.academic_summary || {};
    const semesters = validatedTranscript?.semesters || transcriptData?.semesters || [];

    useEffect(() => {
        if (!transcriptData || !transcriptData.semesters || !Array.isArray(transcriptData.semesters)) {
            setError('Invalid transcript data format');
            console.error('Invalid transcript data format:', transcriptData);
            return;
        }

        try {
            // Initialize editedData with science course flags and lab flags
            const dataWithFlags = transcriptData.semesters.map(semester => ({
                ...semester,
                courses: semester.courses.map(course => ({
                    ...course,
                    is_science: isScienceGPACourse(course.code, course.name),
                    has_lab: course.has_lab || hasLabComponent(course.code, course.name)
                }))
            }));
            setEditedData(dataWithFlags);
            setError(null);
        } catch (err) {
            setError('Error processing transcript data');
            console.error('Error in transcript data processing:', err);
        }
    }, [transcriptData]);

    useEffect(() => {
        return () => {
            setEditedData([]);
            setError(null);
        };
    }, []);

    const handleCourseEdit = (semesterIndex, courseIndex, field, value) => {
        try {
            const newData = [...editedData];
            newData[semesterIndex].courses[courseIndex][field] = value;
            setEditedData(newData);
            setError(null);
        } catch (err) {
            setError('Error updating course data');
            console.error('Error in handleCourseEdit:', err);
        }
    };

    const handleScienceCourseToggle = (semesterIndex, courseIndex) => {
        const newData = [...editedData];
        newData[semesterIndex].courses[courseIndex].is_science = 
            !newData[semesterIndex].courses[courseIndex].is_science;
        setEditedData(newData);
    };

    const handleLabToggle = (semesterIndex, courseIndex) => {
        const newData = [...editedData];
        newData[semesterIndex].courses[courseIndex].has_lab = 
            !newData[semesterIndex].courses[courseIndex].has_lab;
        setEditedData(newData);
    };

    // Helper function to identify courses that count towards Science GPA according to CASPA
    const isScienceGPACourse = (courseCode, courseName = '') => {
        const code = courseCode?.toUpperCase() || '';
        const name = courseName?.toUpperCase() || '';
        
        // Extract the prefix (letters before any numbers or spaces)
        const prefix = code.split(/[\s\d]/)[0];
        
        // Biology/Zoology prefixes
        const bioPrefixes = [
            'BIO', 'BIOL', 'ANAT', 'PHSL', 'PHYS', 'ZOOL', 'MBIO', 'MICR', 'GENE', 
            'IMMUN', 'NEUR', 'PATH', 'HIST', 'EMBR', 'ENDO', 'BOT', 'CELL',
            'ECOL', 'EVOL', 'GENET', 'GENOM', 'HEMAT', 'PARA', 'VIROL'
        ];
        
        // Chemistry prefixes
        const chemPrefixes = [
            'CHEM', 'CHM', 'BIOCHEM', 'BIOC', 'BCH', 'INOR', 'ORGC', 'PCHEM',
            'ANALC', 'MEDCHEM', 'PHARM'
        ];
        
        // Physics prefixes
        const physicsPrefixes = [
            'PHYS', 'PHY', 'MECH', 'THERM', 'ELECT', 'MAGN', 'OPT'
        ];

        // Other Science prefixes
        const otherSciencePrefixes = [
            'EXSC', 'EXPH', 'KINE', 'KNES', 'ENVS', 'BIOE', 'BMEN',
            'NUTR', 'NTR', 'NTRI', 'NUTRI', 'NUSC', 'FN', 'FDNU',
            'HLTH', 'PUBH', 'EXER', 'SPMD', 'BIOM', 'BIOT',
            'CLIN', 'DIET', 'EPID', 'FORS', 'HESC', 'MEDT', 'NSCI',
            'OCCT', 'PATH', 'PHTH', 'RADT', 'RESP', 'SURG', 'TOXC'
        ];

        // Check if the course code starts with any of our prefixes
        const isScience = bioPrefixes.some(p => prefix === p) ||
                         chemPrefixes.some(p => prefix === p) ||
                         physicsPrefixes.some(p => prefix === p) ||
                         otherSciencePrefixes.some(p => prefix === p);

        // Check if the course name contains "BIOCHEMISTRY" or "NUTRITION"
        const hasKeywordInName = name.includes('BIOCHEMISTRY') || name.includes('NUTRITION');

        return isScience || hasKeywordInName;
    };
    
    // Helper function to identify courses that have a lab component
    const hasLabComponent = (courseCode, courseName = '') => {
        const code = courseCode?.toLowerCase() || '';
        const name = courseName?.toLowerCase() || '';
        
        // Check for lab indicators in course name
        const labKeywords = ['lab', 'laboratory', '& lab', '&amp; lab', 'and lab', 'with lab', 'practicum'];
        const hasLabInName = labKeywords.some(keyword => name.includes(keyword));
        
        // Check for lab indicators in course code
        const codeIndicators = [' l', ' l-', 'l ', '-l'];
        const hasLabInCode = codeIndicators.some(indicator => ` ${code} `.includes(indicator)) || 
                            code.endsWith('l');
        
        return hasLabInName || hasLabInCode;
    };

    // Modified GPA calculation function to include Science GPA
    const calculateGPAs = (courses) => {
        let totalPoints = 0;
        let totalCredits = 0;
        let sciencePoints = 0;
        let scienceCredits = 0;
        
        courses.forEach(course => {
            // Parse credits as float and handle null/undefined
            const credits = parseFloat(course.credits) || 0;
            
            // Get grade points based on letter grade
            const gradePoints = {
                'A': 4.0, 'A-': 3.7,
                'B+': 3.3, 'B': 3.0, 'B-': 2.7,
                'C+': 2.3, 'C': 2.0, 'C-': 1.7,
                'D+': 1.3, 'D': 1.0, 'D-': 0.7,
                'F': 0.0
            }[course.grade] || 0;

            // Only include in GPA calculation if it's a letter grade
            if (course.grade && course.grade !== 'W' && course.grade !== 'S' && course.grade !== 'U') {
                totalPoints += credits * gradePoints;
                totalCredits += credits;
                
                // Calculate Science GPA using the is_science flag
                if (course.is_science === true) {  // Explicit check for true
                    sciencePoints += credits * gradePoints;
                    scienceCredits += credits;
                    console.log(`Science course found: ${course.code} - Credits: ${credits}, Grade Points: ${gradePoints}`);
                }
            }
        });

        const cumulativeGPA = totalCredits > 0 ? (totalPoints / totalCredits) : 0;
        const scienceGPA = scienceCredits > 0 ? (sciencePoints / scienceCredits) : 0;

        // Log calculation results for debugging
        console.log("GPA Calculation Results:", {
            totalPoints,
            totalCredits,
            cumulativeGPA,
            sciencePoints,
            scienceCredits,
            scienceGPA
        });

        return {
            cumulativeGPA,
            scienceGPA,
            totalCredits,
            scienceCredits
        };
    };

    const handleValidate = async () => {
        try {
            if (!editedData || !Array.isArray(editedData)) {
                setError('Invalid edited data format');
                console.error('Invalid edited data format');
                return;
            }

            // Calculate term GPAs and overall GPAs before sending
            const processedData = editedData.map(semester => {
                const semesterGPAs = calculateGPAs(semester.courses);
                
                return {
                    ...semester,
                    term_gpa: semesterGPAs.cumulativeGPA,
                    courses: semester.courses.map(course => ({
                        ...course,
                        credits: parseFloat(course.credits) || 0,
                        is_science: course.is_science, // Ensure science flag is included
                        has_lab: course.has_lab // Ensure lab flag is included
                    }))
                };
            });

            // Calculate overall GPAs using all courses
            const allCourses = editedData.flatMap(semester => semester.courses);
            const overallGPAs = calculateGPAs(allCourses);

            // Create validation data with both GPAs
            const validationData = {
                isApproved: true,
                editedData: processedData,
                studentInfo: {
                    ...academicSummary,
                    cumulative_gpa: overallGPAs.cumulativeGPA,
                    science_gpa: overallGPAs.scienceGPA, // Make sure science GPA is included
                    total_credits_attempted: overallGPAs.totalCredits,
                    science_credits: overallGPAs.scienceCredits,
                    transcript_stated_gpa: academicSummary.transcript_stated_gpa
                },
                degreeInfo: {
                    institution: academicSummary.institution,
                    major: academicSummary.degree,
                    minor: academicSummary.minor,
                    final_gpa: overallGPAs.cumulativeGPA
                }
            };

            // Log the GPAs for debugging
            console.log("Calculated GPAs:", {
                cumulative: overallGPAs.cumulativeGPA,
                science: overallGPAs.scienceGPA,
                scienceCredits: overallGPAs.scienceCredits
            });

            console.log("Sending validation data:", validationData);
            await onValidate(validationData);
            
            console.log("Validation request sent successfully");
            
        } catch (err) {
            setError('Error processing validation');
            console.error('Error in handleValidate:', err);
        }
    };

    // Pre-validation view
    if (validationStatus !== 'approved') {
        return (
            <div className="transcript-validation">
                <h2>Transcript Analysis</h2>

                {error && (
                    <div className="error-message">
                        {error}
                    </div>
                )}

                <div className="academic-summary">
                    <h3>Academic Summary</h3>
                    <div className="summary-grid">
                        <div className="summary-item">
                            <label>School Name:</label>
                            <span>{academicSummary.institution || 'N/A'}</span>
                        </div>
                        <div className="summary-item">
                            <label>Student Name:</label>
                            <span>{academicSummary.student_name || 'N/A'}</span>
                        </div>
                        <div className="summary-item">
                            <label>Date of Birth:</label>
                            <span>{academicSummary.birth_date || 'N/A'}</span>
                        </div>
                        <div className="summary-item">
                            <label>Degree:</label>
                            <span>{academicSummary.degree || 'N/A'}</span>
                        </div>
                        <div className="summary-item">
                            <label>Minor:</label>
                            <span>{academicSummary.minor || 'N/A'}</span>
                        </div>
                        <div className="summary-item">
                            <label>Transcript GPA:</label>
                            <span>{academicSummary.transcript_stated_gpa || 'N/A'}</span>
                        </div>
                    </div>
                </div>

                {/* Editable course list */}
                <div className="semester-list">
                    {editedData.map((semester, semIndex) => (
                        <div key={semIndex} className="semester-section">
                            <h3>{semester.term} {semester.year}</h3>
                            <table className="transcript-table">
                                <thead>
                                    <tr>
                                        <th>Course Code</th>
                                        <th>Course Name</th>
                                        <th>Credits</th>
                                        <th>Grade</th>
                                        <th>Is Science</th>
                                        <th>Lab Included</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {semester.courses.map((course, courseIndex) => (
                                        <tr key={courseIndex} className={course.is_science ? 'science-course' : ''}>
                                            <td>
                                                <input
                                                    value={course.code || ''}
                                                    onChange={(e) => handleCourseEdit(semIndex, courseIndex, 'code', e.target.value)}
                                                    required
                                                    disabled={isProcessing}
                                                />
                                            </td>
                                            <td>
                                                <input
                                                    value={course.name || ''}
                                                    onChange={(e) => handleCourseEdit(semIndex, courseIndex, 'name', e.target.value)}
                                                    required
                                                    disabled={isProcessing}
                                                />
                                            </td>
                                            <td>
                                                <input
                                                    type="number"
                                                    step="0.5"
                                                    min="0"
                                                    max="6"
                                                    value={course.credits || 0}
                                                    onChange={(e) => handleCourseEdit(semIndex, courseIndex, 'credits', parseFloat(e.target.value))}
                                                    required
                                                    disabled={isProcessing}
                                                />
                                            </td>
                                            <td>
                                                <select
                                                    value={course.grade || 'A'}
                                                    onChange={(e) => handleCourseEdit(semIndex, courseIndex, 'grade', e.target.value)}
                                                    required
                                                    disabled={isProcessing}
                                                >
                                                    {['A', 'A-', 'B+', 'B', 'B-', 'C+', 'C', 'C-', 'D+', 'D', 'F', 'S', 'U', 'W'].map(grade => (
                                                        <option key={grade} value={grade}>{grade}</option>
                                                    ))}
                                                </select>
                                            </td>
                                            <td>
                                                <input
                                                    type="checkbox"
                                                    checked={course.is_science || false}
                                                    onChange={() => handleScienceCourseToggle(semIndex, courseIndex)}
                                                    disabled={isProcessing}
                                                />
                                            </td>
                                            <td>
                                                <input
                                                    type="checkbox"
                                                    checked={course.has_lab || false}
                                                    onChange={() => handleLabToggle(semIndex, courseIndex)}
                                                    disabled={isProcessing}
                                                />
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                            <div className="semester-summary">
                                <p>Term GPA: {typeof semester.term_gpa === 'number' ? semester.term_gpa.toFixed(2) : 'N/A'}</p>
                            </div>
                        </div>
                    ))}
                </div>

                <div className="validation-actions">
                    <button
                        className="approve-btn"
                        onClick={handleValidate}
                        disabled={!!error || isProcessing}
                    >
                        {isProcessing ? 'Processing...' : 'Validate Transcript'}
                    </button>
                </div>
            </div>
        );
    }

    // Post-validation view
    return (
        <div className="transcript-validation">
            <h2>Verified Transcript Analysis</h2>
            
            {error && (
                <div className="error-message">
                    {error}
                </div>
            )}

            <div className="academic-summary">
                <h3>Academic Summary</h3>
                <div className="summary-grid">
                    <div className="summary-item">
                        <label>School Name:</label>
                        <span>{academicSummary.institution || 'N/A'}</span>
                    </div>
                    <div className="summary-item">
                        <label>Student Name:</label>
                        <span>{academicSummary.student_name || 'N/A'}</span>
                    </div>
                    <div className="summary-item">
                        <label>Date of Birth:</label>
                        <span>{academicSummary.birth_date || 'N/A'}</span>
                    </div>
                    <div className="summary-item">
                        <label>Degree:</label>
                        <span>{academicSummary.degree || 'N/A'}</span>
                    </div>
                    <div className="summary-item">
                        <label>Minor:</label>
                        <span>{academicSummary.minor || 'N/A'}</span>
                    </div>
                    <div className="summary-item">
                        <label>Cumulative GPA:</label>
                        <span>{academicSummary.transcript_stated_gpa?.toFixed(2) || 'N/A'}</span>
                    </div>
                    <div className="summary-item">
                        <label>Science GPA:</label>
                        <span>{editedData.scienceGPA?.toFixed(2) || 'N/A'}</span>
                    </div>
                    <div className="summary-item">
                        <label>Science Credits:</label>
                        <span>{editedData.scienceCredits || 'N/A'}</span>
                    </div>
                </div>
            </div>

            <div className="semester-list">
                <h3>Course Information</h3>
                {editedData.map((semester, semIndex) => (
                    <div key={semIndex} className="semester-section">
                        <h4>{semester.term} {semester.year}</h4>
                        <table className="transcript-table">
                            <thead>
                                <tr>
                                    <th>Course Code</th>
                                    <th>Course Name</th>
                                    <th>Credits</th>
                                    <th>Grade</th>
                                    <th>Is Science</th>
                                    <th>Lab Included</th>
                                </tr>
                            </thead>
                            <tbody>
                                {semester.courses.map((course, courseIndex) => (
                                    <tr key={courseIndex} className={course.is_science ? 'science-course' : ''}>
                                        <td>{course.code}</td>
                                        <td>{course.name}</td>
                                        <td>{course.credits}</td>
                                        <td>{course.grade}</td>
                                        <td>{course.is_science ? 'Yes' : 'No'}</td>
                                        <td>{course.has_lab ? 'Yes' : 'No'}</td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                        <div className="semester-summary">
                            <p>Term GPA: {semester.term_gpa?.toFixed(2)}</p>
                            <p>Cumulative GPA: {semester.cumulative_gpa?.toFixed(2)}</p>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default TranscriptValidation;