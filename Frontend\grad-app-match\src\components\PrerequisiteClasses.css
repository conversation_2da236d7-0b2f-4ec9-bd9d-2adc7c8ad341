/* Styles for the prerequisite classes page */
.prerequisites-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 12px;
    padding: 0;
}

.prerequisites-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 10px;
}

.save-prerequisites {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.save-prerequisites-btn, .refresh-prereqs-btn {
    background-color: #2563eb;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    gap: 5px;
}

.save-prerequisites-btn:hover, .refresh-prereqs-btn:hover {
    background-color: #1e40af;
}

.save-prerequisites-btn:disabled {
    background-color: #94a3b8;
    cursor: not-allowed;
}

.refresh-prereqs-btn {
    background-color: #4b5563;
}

.refresh-prereqs-btn:hover {
    background-color: #374151;
}

.save-message {
    font-size: 0.85rem;
    padding: 4px 10px;
    border-radius: 4px;
    white-space: nowrap;
}

.save-message.success {
    background-color: #d1fae5;
    color: #065f46;
}

.save-message.error {
    background-color: #fee2e2;
    color: #b91c1c;
}

.prerequisite-item {
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 0;
    background-color: white;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    overflow: hidden;
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
}

.prerequisite-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 12px;
    background-color: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    cursor: pointer;
}

.prerequisite-header h3 {
    margin: 0;
    color: #334155;
    font-size: 0.95rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 6px;
}

.prerequisite-header .course-count {
    background-color: #dbeafe;
    color: #1e40af;
    font-size: 0.7rem;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 20px;
    text-align: center;
}

.prerequisite-content {
    padding: 0 12px 12px;
    background-color: white;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
}

.prerequisite-item.expanded .prerequisite-content {
    max-height: 500px;
    padding: 12px;
}

.prerequisite-actions {
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
}

.add-course-btn {
    background-color: #f1f5f9;
    color: #475569;
    border: 1px solid #cbd5e1;
    border-radius: 4px;
    padding: 4px 8px;
    cursor: pointer;
    font-size: 0.75rem;
    font-weight: 500;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 4px;
}

.add-course-btn:hover {
    background-color: #e2e8f0;
    color: #334155;
}

.add-course-btn svg {
    width: 12px;
    height: 12px;
}

.course-dropdown {
    margin-bottom: 10px;
}

.course-select {
    width: 100%;
    padding: 6px;
    border: 1px solid #cbd5e1;
    border-radius: 4px;
    font-size: 0.85rem;
    background-color: white;
}

.matching-courses h4 {
    margin: 0 0 8px 0;
    font-size: 0.85rem;
    color: #64748b;
    font-weight: 500;
}

.prerequisite-course-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 8px 10px;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    margin-bottom: 8px;
    background-color: #f9fafb;
    position: relative;
}

.prerequisite-course-item:last-child {
    margin-bottom: 0;
}

.course-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
    flex: 1;
    font-size: 0.85rem;
}

.course-info .course-title {
    font-weight: 600;
    color: #334155;
}

.course-info .course-details, 
.course-info .course-institution {
    font-size: 0.75rem;
    color: #64748b;
}

.delete-course-btn {
    background: none;
    border: none;
    color: #dc3545;
    cursor: pointer;
    padding: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: color 0.2s;
    position: absolute;
    top: 5px;
    right: 5px;
    opacity: 0.6;
}

.delete-course-btn:hover {
    color: #bd2130;
    opacity: 1;
}

.delete-course-btn svg {
    width: 14px;
    height: 14px;
}

.no-matches {
    color: #94a3b8;
    font-style: italic;
    font-size: 0.85rem;
    margin: 5px 0;
    text-align: center;
    padding: 10px;
}

/* Lab component styles */
.course-lab-status {
    display: flex;
    align-items: center;
    margin-top: 5px;
}

.course-lab-status input[type="checkbox"] {
    margin-right: 6px;
    cursor: pointer;
}

.form-check {
    display: flex;
    align-items: center;
    margin-top: 3px;
}

.form-check-input {
    margin-right: 5px;
}

.form-check-label {
    font-size: 0.75rem;
    color: #64748b;
}

/* Highlight courses that have labs */
.prerequisite-course-item.has-lab {
    border-left: 3px solid #22c55e;
}

/* Grade badges */
.grade-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: 600;
    width: 22px;
    height: 22px;
    border-radius: 50%;
    margin-left: 5px;
}

.grade-badge.grade-a {
    background-color: #dcfce7;
    color: #166534;
}

.grade-badge.grade-b {
    background-color: #dbeafe;
    color: #1e40af;
}

.grade-badge.grade-c {
    background-color: #fef9c3;
    color: #854d0e;
}

.grade-badge.grade-d {
    background-color: #fed7aa;
    color: #9a3412;
}

.grade-badge.grade-f {
    background-color: #fee2e2;
    color: #b91c1c;
}

.lab-badge {
    font-size: 0.65rem;
    font-weight: 600;
    padding: 1px 5px;
    border-radius: 3px;
    margin-left: 5px;
}

.lab-badge.has-lab {
    background-color: #d1fae5;
    color: #065f46;
}

.lab-badge.no-lab {
    background-color: #f1f5f9;
    color: #64748b;
}

/* Visual caret for expand/collapse */
.expand-icon {
    color: #64748b;
    transition: transform 0.3s ease;
}

.prerequisite-item.expanded .expand-icon {
    transform: rotate(180deg);
}

/* Responsive styles */
@media (max-width: 640px) {
    .prerequisites-container {
        display: flex;
        flex-direction: column;
    }
} 