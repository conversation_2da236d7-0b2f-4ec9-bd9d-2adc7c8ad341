# Generated by Django 5.0.1 on 2025-07-23 03:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('gradapp', '0028_school_city_school_state'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='school',
            name='city',
        ),
        migrations.RemoveField(
            model_name='school',
            name='state',
        ),
        migrations.AlterField(
            model_name='applicationrequirement',
            name='seat_deposit_amount',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='applicationrequirement',
            name='seat_deposit_refundable',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='applicationrequirement',
            name='seat_deposit_required',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='applicationrequirement',
            name='tuition_separate_rates',
            field=models.Char<PERSON>ield(blank=True, max_length=20, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='attritiondata',
            name='attrition_rate',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='attritiondata',
            name='graduation_rate',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='curriculum',
            name='didactic_term_type',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='enhancedclassprofile',
            name='average_overall_gpa',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='enhancedclassprofile',
            name='average_science_gpa',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='enhancedclassprofile',
            name='gre_analytical_percentile',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='enhancedclassprofile',
            name='gre_analytical_score',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='enhancedclassprofile',
            name='gre_quantitative_percentile',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='enhancedclassprofile',
            name='gre_quantitative_score',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='enhancedclassprofile',
            name='gre_verbal_percentile',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='enhancedclassprofile',
            name='gre_verbal_score',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='matriculantdemographics',
            name='american_indian_count',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='matriculantdemographics',
            name='asian_count',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='matriculantdemographics',
            name='black_count',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='matriculantdemographics',
            name='female_count',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='matriculantdemographics',
            name='gender_unknown_count',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='matriculantdemographics',
            name='hawaiian_pacific_count',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='matriculantdemographics',
            name='hispanic_count',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='matriculantdemographics',
            name='male_count',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='matriculantdemographics',
            name='non_binary_count',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='matriculantdemographics',
            name='other_count',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='matriculantdemographics',
            name='white_count',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='pancepassrate',
            name='national_pass_rate',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='pancepassrate',
            name='program_pass_rate',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='pancepassrate',
            name='ultimate_pass_rate',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='prerequisiteselection',
            name='year',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='program',
            name='admission_type',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='program',
            name='city_state',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='program',
            name='credentials_offered',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='program',
            name='distance_learning',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='program',
            name='doctorate_offered',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='program',
            name='estimated_class_size',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='program',
            name='masters_degree_type',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='program',
            name='on_campus_housing',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='program',
            name='part_time_option',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='program',
            name='phone',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='program',
            name='program_length',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='program',
            name='program_length_months',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='program',
            name='required_onsite_interview',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='program',
            name='start_month',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='tuitioninformation',
            name='seat_deposit_cost',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
    ]
