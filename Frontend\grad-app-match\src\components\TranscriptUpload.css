.transcript-upload-container {
    padding: 20px;
    max-width: 1000px;
    margin: 0 auto;
}

.transcript-upload-container h2 {
    color: #333;
    margin-bottom: 30px;
}

.transcript-upload-section {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.upload-controls {
    display: flex;
    gap: 15px;
    align-items: center;
    margin-bottom: 15px;
}

.file-input {
    flex: 1;
    padding: 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    background-color: white;
}

.upload-button {
    padding: 10px 20px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.upload-button:hover:not(:disabled) {
    background-color: #0056b3;
}

.upload-button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

.error-message {
    color: #dc3545;
    background-color: #f8d7da;
    padding: 10px;
    border-radius: 4px;
    margin-top: 10px;
}

.status-message {
    color: #28a745;
    background-color: #d4edda;
    padding: 10px;
    border-radius: 4px;
    margin-top: 10px;
}

.selected-file {
    margin-top: 10px;
    padding: 10px;
    background-color: #e9ecef;
    border-radius: 4px;
    font-size: 0.9em;
    color: #495057;
}

.process-button {
    padding: 6px 12px;
    background-color: #28a745;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9em;
}

.process-button:hover:not(:disabled) {
    background-color: #218838;
    transform: translateY(-1px);
}

.process-button:disabled {
    background-color: #e9ecef;
    color: #6c757d;
    cursor: not-allowed;
    transform: none;
}

.transcripts-list {
    margin-top: 30px;
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.transcripts-list h3 {
    margin-bottom: 15px;
    color: #333;
}

.transcripts-list table {
    width: 100%;
    border-collapse: collapse;
}

.transcripts-list th,
.transcripts-list td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.transcripts-list th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.active-transcript {
    background-color: #f8f9fa;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.85em;
    font-weight: 500;
}

.status-uploaded {
    background-color: #cff4fc;
    color: #055160;
}

.status-processing {
    background-color: #fff3cd;
    color: #664d03;
}

.status-validated {
    background-color: #d1e7dd;
    color: #0f5132;
}

.status-failed {
    background-color: #f8d7da;
    color: #842029;
}

.delete-button {
    padding: 6px 12px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.delete-button:hover:not(:disabled) {
    background-color: #bb2d3b;
    transform: translateY(-1px);
}

.delete-button:disabled {
    background-color: #e9ecef;
    color: #6c757d;
    cursor: not-allowed;
    transform: none;
}

.loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    color: #666;
    font-size: 1.1em;
}

.loading-spinner::after {
    content: '';
    width: 20px;
    height: 20px;
    margin-left: 10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
    .upload-controls {
        flex-direction: column;
    }
    
    .file-input,
    .upload-button {
        width: 100%;
    }
    
    .transcripts-list {
        overflow-x: auto;
    }
    
    .transcripts-list table {
        min-width: 600px;
    }
    
    .process-button {
        padding: 4px 8px;
        font-size: 0.85em;
    }
} 