import React, { useState } from 'react';

const TranscriptValidation = ({ transcriptData, onValidate }) => {
    const [editedData, setEditedData] = useState(transcriptData);
    
    const handleSemesterEdit = (semesterIndex, updatedSemester) => {
        const newData = {...editedData};
        newData.structured_data[semesterIndex] = updatedSemester;
        setEditedData(newData);
    };

    const handleSubmit = () => {
        onValidate({
            isApproved: true,
            editedData
        });
    };

    return (
        <div className="transcript-validation">
            <h2>Review Transcript Data</h2>
            
            {editedData.map((semester, index) => (
                <div key={index} className="semester-block">
                    <h3>{semester.term} {semester.year}</h3>
                    <table className="transcript-table">
                        <thead>
                            <tr>
                                <th>Course Code</th>
                                <th>Course Name</th>
                                <th>Credits</th>
                                <th>Grade</th>
                            </tr>
                        </thead>
                        <tbody>
                            {semester.courses.map((course, courseIndex) => (
                                <tr key={courseIndex}>
                                    <td>
                                        <input
                                            value={course.code}
                                            onChange={(e) => {
                                                const updatedSemester = {...semester};
                                                updatedSemester.courses[courseIndex].code = e.target.value;
                                                handleSemesterEdit(index, updatedSemester);
                                            }}
                                        />
                                    </td>
                                    <td>
                                        <input
                                            value={course.name}
                                            onChange={(e) => {
                                                const updatedSemester = {...semester};
                                                updatedSemester.courses[courseIndex].name = e.target.value;
                                                handleSemesterEdit(index, updatedSemester);
                                            }}
                                        />
                                    </td>
                                    <td>
                                        <input
                                            type="number"
                                            value={course.credits}
                                            onChange={(e) => {
                                                const updatedSemester = {...semester};
                                                updatedSemester.courses[courseIndex].credits = parseFloat(e.target.value);
                                                handleSemesterEdit(index, updatedSemester);
                                            }}
                                        />
                                    </td>
                                    <td>
                                        <input
                                            value={course.grade}
                                            onChange={(e) => {
                                                const updatedSemester = {...semester};
                                                updatedSemester.courses[courseIndex].grade = e.target.value;
                                                handleSemesterEdit(index, updatedSemester);
                                            }}
                                        />
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                    <div className="semester-summary">
                        <strong>Term GPA:</strong> {semester.term_gpa}
                        <strong>Career GPA:</strong> {semester.career_gpa}
                    </div>
                </div>
            ))}

            <div className="validation-buttons">
                <button onClick={handleSubmit} className="approve-btn">
                    Approve Changes
                </button>
            </div>
        </div>
    );
};

export default TranscriptValidation;