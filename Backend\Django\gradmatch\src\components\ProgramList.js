import React from 'react';
import PropTypes from 'prop-types';
import './ProgramList.css'; // We'll create this next

function ProgramList({ programs, onSelectProgram }) {
    if (!programs || programs.length === 0) {
        return <div className="program-list-empty">No programs available</div>;
    }

    return (
        <div className="program-list-container">
            <h2>Available Programs</h2>
            <ul className="program-list">
                {programs.map((program) => (
                    <li 
                        key={program.id} 
                        className="program-item"
                        onClick={() => onSelectProgram(program)}
                    >
                        <div className="program-name">{program.name}</div>
                        {program.institution && (
                            <div className="program-institution">{program.institution}</div>
                        )}
                    </li>
                ))}
            </ul>
        </div>
    );
}

ProgramList.propTypes = {
    programs: PropTypes.arrayOf(
        PropTypes.shape({
            id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
            name: PropTypes.string.isRequired,
            institution: PropTypes.string
        })
    ),
    onSelectProgram: PropTypes.func.isRequired
};

ProgramList.defaultProps = {
    programs: []
};

export default ProgramList; 