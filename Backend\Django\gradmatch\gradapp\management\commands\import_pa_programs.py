from django.core.management.base import BaseCommand
from django.db import transaction
from gradapp.models import (
    School, Program, PrerequisiteCourse, HealthcareExperience,
    PatientCareExperience, ShadowingRequirement, GRERequirement,
    GPARequirement, InterviewRequirement, ApplicationRequirement,
    OtherRequirement, RecommendationRequirement, ClassProfile
)
import json
import logging
from datetime import datetime, timedelta
from decimal import Decimal

logger = logging.getLogger(__name__)

def parse_date(date_str):
    """Parse date strings like 'January 2026' or return None"""
    if not date_str or date_str == "No information provided":
        return None
    try:
        return datetime.strptime(date_str, '%B %Y').date()
    except ValueError:
        return None

def parse_decimal(value_str):
    """Parse decimal values or return None"""
    if not value_str or value_str == "No information provided":
        return None
    try:
        cleaned = ''.join(c for c in value_str if c.isdigit() or c == '.')
        return Decimal(cleaned)
    except:
        return None

def parse_int(value_str):
    """Parse integer values or return None"""
    if not value_str or value_str == "No information provided" or value_str == "-":
        return None
    try:
        return int(''.join(filter(str.isdigit, value_str)))
    except:
        return None

def parse_credits(credit_str):
    """Parse credit string to extract credits and lab requirement"""
    if not credit_str or credit_str == "No information provided":
        return None, False
    
    credits = credit_str
    lab_required = "Lab Required" in credit_str
    
    # Extract just the number if it's in the format "4 Credit(s)"
    if "Credit(s)" in credit_str:
        credits = credit_str.split(" Credit")[0].strip()
    
    return credits, lab_required

class Command(BaseCommand):
    help = 'Import PA program data from JSON file'

    def add_arguments(self, parser):
        parser.add_argument('json_file', type=str, help='Path to the JSON file containing PA program data')

    def handle(self, *args, **options):
        json_file = options['json_file']
        try:
            with open(json_file, 'r') as f:
                data = json.load(f)

            with transaction.atomic():
                programs_created = 0
                for program_data in data:
                    try:
                        # Extract school name from program name
                        school_name = program_data.get('program_name', '').split('(')[0].strip()
                        
                        # Create or get school
                        school, _ = School.objects.get_or_create(
                            name=school_name,
                            defaults={
                                'location': f"{program_data['address'].get('city', '')}, {program_data['address'].get('state', '')}".strip(', '),
                                'website': program_data.get('program_website', ''),
                                'mission_statement': program_data.get('mission_statement', '')
                            }
                        )

                        # Parse dates
                        start_date = parse_date(program_data.get('start_month'))
                        app_deadline = datetime.strptime(program_data.get('application_deadline', ''), '%m/%d/%Y').date() if program_data.get('application_deadline') and program_data['application_deadline'] != "No information provided" else None
                        
                        # Set default dates if none provided
                        if start_date is None:
                            start_date = (datetime.now() + timedelta(days=365)).date()
                        if app_deadline is None:
                            app_deadline = (datetime.now() + timedelta(days=180)).date()

                        # Create program
                        program = Program.objects.create(
                            school=school,
                            name=program_data.get('program_name', ''),
                            description=program_data.get('unique_program_features', ''),
                            url=program_data.get('program_url', ''),
                            application_deadline=app_deadline,
                            program_start_date=start_date,
                            average_gpa=parse_decimal(program_data.get('minimum_overall_gpa_required')) or Decimal('0.00'),
                            class_size=parse_int(program_data.get('estimated_incoming_class_size')) or 0
                        )

                        # Dynamically create prerequisite courses from any field ending in _credits
                        for field_name, value in program_data.items():
                            if field_name.endswith('_credits'):
                                course_name = field_name.replace('_credits', '').replace('_', ' ').title()
                                credits, lab_required = parse_credits(value)
                                
                                if credits is not None:  # Only create if we have credit information
                                    PrerequisiteCourse.objects.create(
                                        program=program,
                                        course_name=course_name,
                                        credits=credits,
                                        lab_required=lab_required,
                                        time_limit=program_data.get('prereq_time_limit', 'N/A')
                                    )

                        # Create healthcare experience requirements
                        HealthcareExperience.objects.create(
                            program=program,
                            required=program_data.get('healthcare_experience') == 'Yes, required',
                            hours_needed=parse_int(program_data.get('hours_needed')),
                            time_limit=program_data.get('time_limit_to_complete_experience', 'N/A'),
                            paid_accepted=program_data.get('paid_health_care') in ['Required', 'Recommended but not required'],
                            volunteer_accepted=program_data.get('volunteer_community_service_health_care') in ['Required', 'Recommended but not required'],
                            virtual_accepted=program_data.get('virtual_health_care_accepted') == 'Yes',
                            status=program_data.get('healthcare_experience', 'N/A')
                        )

                        # Create patient care experience requirements
                        PatientCareExperience.objects.create(
                            program=program,
                            required=bool(program_data.get('direct_patient_care_hours_needed') and program_data.get('direct_patient_care_hours_needed') != 'No information provided'),
                            hours_needed=parse_int(program_data.get('direct_patient_care_hours_needed')),
                            time_limit=program_data.get('direct_patient_care_time_limit', 'N/A'),
                            paid_accepted=program_data.get('paid_direct_patient_care') in ['Required', 'Recommended but not required'],
                            volunteer_accepted=program_data.get('volunteer_direct_patient_care') in ['Required', 'Recommended but not required'],
                            paid_status=program_data.get('paid_direct_patient_care', 'N/A'),
                            volunteer_status=program_data.get('volunteer_direct_patient_care', 'N/A')
                        )

                        # Create shadowing requirements
                        ShadowingRequirement.objects.create(
                            program=program,
                            pa_shadowing=program_data.get('shadowing_a_pa', 'N/A'),
                            physician_shadowing=program_data.get('shadowing_a_physician', 'N/A'),
                            other_shadowing=program_data.get('shadowing_other_health_care_provider', 'N/A'),
                            virtual_accepted=program_data.get('virtual_shadowing_accepted') == 'Yes'
                        )

                        # Create GRE requirements
                        GRERequirement.objects.create(
                            program=program,
                            required=program_data.get('standardized_test_required', 'N/A'),
                            verbal_score=program_data.get('gre_average_scores', {}).get('average_verbal_reasoning_gre_score', 'N/A'),
                            quantitative_score=program_data.get('gre_average_scores', {}).get('average_quantitative_reasoning_gre_score', 'N/A'),
                            analytical_score=program_data.get('gre_average_scores', {}).get('average_analytical_writing_gre_score', 'N/A'),
                            verbal_percentile=program_data.get('gre_average_percentiles', {}).get('average_verbal_reasoning_gre_percentile', 'N/A'),
                            quantitative_percentile=program_data.get('gre_average_percentiles', {}).get('average_quantitative_reasoning_gre_percentile', 'N/A'),
                            analytical_percentile=program_data.get('gre_average_percentiles', {}).get('average_analytical_writing_gre_percentile', 'N/A')
                        )

                        # Create GPA requirements
                        GPARequirement.objects.create(
                            program=program,
                            minimum_overall=program_data.get('minimum_overall_gpa_required', 'N/A'),
                            minimum_prereq=program_data.get('minimum_prereq_gpa_required', 'N/A'),
                            minimum_science=program_data.get('minimum_science_gpa_required', 'N/A')
                        )

                        # Create interview requirements
                        InterviewRequirement.objects.create(
                            program=program,
                            required=program_data.get('required_onsite_interview') == 'Yes',
                            onsite_required=program_data.get('required_onsite_interview') == 'Yes',
                            mmi_used='Multiple Mini Interview (MMI)' in program_data.get('types_of_interviews', []),
                            interview_types=program_data.get('types_of_interviews', [])
                        )

                        # Create application requirements
                        ApplicationRequirement.objects.create(
                            program=program,
                            caspa_required=program_data.get('caspa_member') == 'Yes',
                            caspa_deadline=app_deadline,
                            caspa_verification_requirement=program_data.get('deadline_requirement', 'N/A'),
                            supplemental_required=program_data.get('supplemental_application') == 'Yes',
                            supplemental_deadline=datetime.strptime(program_data.get('supplemental_deadline', ''), '%m/%d/%Y').date() if program_data.get('supplemental_deadline') and program_data['supplemental_deadline'] != "No information provided" else None,
                            supplemental_fee=program_data.get('supplemental_application_fee', 'N/A'),
                            fee_waiver_available=program_data.get('supplemental_application_fee_waiver') == 'Yes',
                            admission_type=program_data.get('admission_type', 'N/A')
                        )

                        # Create other requirements
                        OtherRequirement.objects.create(
                            program=program,
                            daca_accepted=program_data.get('daca_status_applicants_considered') == 'Yes',
                            veteran_support=program_data.get('support_for_veterans', 'N/A'),
                            transfer_accepted=program_data.get('transfer_students_accepted') == 'Yes',
                            out_of_state_accepted=program_data.get('out_of_state_students_accepted') == 'Yes',
                            international_accepted=program_data.get('international_applicants_accepted') == 'Yes',
                            international_requirements=program_data.get('international_application_link', 'N/A')
                        )

                        # Create recommendation requirements
                        RecommendationRequirement.objects.create(
                            program=program,
                            number_required=program_data.get('minimum_number_of_reference_letters_required', 'N/A'),
                            types_required=program_data.get('types_of_references_required', []),
                            specific_requirements=program_data.get('reference_requirements', 'N/A')
                        )

                        # Create class profile
                        ClassProfile.objects.create(
                            program=program,
                            entering_class_year=datetime.now().year,
                            number_of_applicants=parse_int(program_data.get('number_of_applications_received')) or 0,
                            average_gpa=parse_decimal(program_data.get('average_overall_gpa_matriculants')) or Decimal('0.00'),
                            average_age=parse_int(program_data.get('average_age_matriculants')) or 0,
                            in_state_students_percentage=parse_decimal(program_data.get('in_state_students_percentage')) or Decimal('0.00')
                        )

                        programs_created += 1
                        self.stdout.write(f"Successfully imported {program.name}")

                    except Exception as e:
                        logger.error(f"Error importing program {program_data.get('program_name', 'Unknown')}: {str(e)}")
                        raise  # Re-raise the exception to trigger rollback

                self.stdout.write(
                    self.style.SUCCESS(f'Successfully imported {programs_created} PA programs')
                )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error importing PA programs: {str(e)}')
            ) 