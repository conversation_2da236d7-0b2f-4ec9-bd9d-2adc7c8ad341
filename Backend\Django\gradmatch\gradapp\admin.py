from django.contrib import admin
from django import forms
from .models import School, Program, TranscriptRecord

# Admin form for the Program model to customize the admin interface
class ProgramAdminForm(forms.ModelForm):
    class Meta:
        model = Program
        fields = '__all__'
        widgets = {
            'description': forms.Textarea(attrs={'cols': 80, 'rows': 3}),
            'prerequisites': forms.Textarea(attrs={'cols': 80, 'rows': 3}),
        }

# Admin class for the Program model
class ProgramAdmin(admin.ModelAdmin):
    form = ProgramAdminForm
    list_display = ('name', 'school', 'url', 'application_deadline', 'program_start_date', 'average_gpa', 'class_size')
    search_fields = ['name', 'description']
    list_filter = ['school', 'application_deadline']

# Admin class for the School model
class SchoolAdmin(admin.ModelAdmin):
    list_display = ('name', 'location', 'website')
    search_fields = ['name', 'location']
    list_filter = ['location']

# Register your models here with their respective admin classes
admin.site.register(School, SchoolAdmin)
admin.site.register(Program, ProgramAdmin)
admin.site.register(TranscriptRecord)

