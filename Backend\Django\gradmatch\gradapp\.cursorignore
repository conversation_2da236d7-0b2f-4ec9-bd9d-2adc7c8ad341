# Add directories or file patterns to ignore during indexing (e.g. foo/ or *.csv)
.json
.vs
.vs\gradapp
.vs\gradapp\FileContentIndex
.vs\gradapp\FileContentIndex\576d7eb7-5a71-4092-8a6c-71df6760e85d.vsidx
.vs\gradapp\FileContentIndex\61c97848-9270-48ba-99aa-0e8d5cb7047f.vsidx
.vs\gradapp\FileContentIndex\abd16442-3fe6-494d-ac04-6682be4c4936.vsidx
.vs\gradapp\FileContentIndex\bef24635-a7af-451c-9b7d-fb32cf1db44b.vsidx
.vs\gradapp\FileContentIndex\c56fd110-a540-4f70-ad33-bd23a806fc33.vsidx
.vs\gradapp\v17
.vs\gradapp\v17\.wsuo
.vs\gradapp\v17\DocumentLayout.json
.vs\ProjectSettings.json
.vs\slnx.sqlite
.vs\VSWorkspaceState.json
management
management\commands
management\commands\import_data.py
management\commands\import_school_data.py
management\commands\__init__.py
management\__init__.py
migrations
migrations\0001_initial.py
migrations\0002_userprofile.py
migrations\0003_userprofile_document.py
migrations\0004_remove_classprofile_program_and_more.py
migrations\0005_admissionrequirement_classprofile_curriculum_and_more.py
migrations\__init__.py
__init__.py
apps.py
