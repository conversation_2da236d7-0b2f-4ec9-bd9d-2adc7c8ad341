# Generated by Django 5.0.1 on 2024-02-10 08:33

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Program',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField()),
                ('url', models.URLField()),
                ('application_deadline', models.DateField()),
                ('program_start_date', models.DateField()),
                ('average_gpa', models.DecimalField(decimal_places=2, max_digits=3)),
                ('class_size', models.IntegerField()),
                ('prerequisites', models.JSONField()),
            ],
        ),
        migrations.CreateModel(
            name='School',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('location', models.Char<PERSON>ield(max_length=255)),
                ('mission_statement', models.TextField()),
                ('website', models.URLField()),
            ],
        ),
        migrations.CreateModel(
            name='Curriculum',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.CharField(max_length=100)),
                ('courses', models.JSONField()),
                ('program', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='gradapp.program')),
            ],
        ),
        migrations.CreateModel(
            name='ClassProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('entering_class_year', models.IntegerField()),
                ('number_of_applicants', models.IntegerField()),
                ('average_gpa', models.DecimalField(decimal_places=2, max_digits=3)),
                ('average_age', models.IntegerField()),
                ('in_state_students_percentage', models.DecimalField(decimal_places=2, max_digits=5)),
                ('program', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='gradapp.program')),
            ],
        ),
        migrations.CreateModel(
            name='AdmissionRequirement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('requirements', models.JSONField()),
                ('program', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='gradapp.program')),
            ],
        ),
        migrations.CreateModel(
            name='ProgramCompetency',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('competencies', models.TextField()),
                ('program', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='gradapp.program')),
            ],
        ),
        migrations.AddField(
            model_name='program',
            name='school',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='gradapp.school'),
        ),
    ]
