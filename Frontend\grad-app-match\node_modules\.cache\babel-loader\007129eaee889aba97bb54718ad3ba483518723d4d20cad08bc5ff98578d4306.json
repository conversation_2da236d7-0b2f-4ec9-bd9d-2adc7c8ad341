{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\MasterGradMatch\\\\Frontend\\\\grad-app-match\\\\src\\\\components\\\\PAMatch.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport axios from 'axios';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNavigate } from 'react-router-dom';\nimport ProgramSkeleton from './ProgramSkeleton';\nimport { normalizeProfileData } from '../utils/profileUtils';\nimport './PAMatch.css';\n\n// US Census Bureau Regions and Divisions\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst US_REGIONS = {\n  // Census Regions\n  'Northeast': ['CT', 'ME', 'MA', 'NH', 'RI', 'VT', 'NJ', 'NY', 'PA'],\n  'Midwest': ['IL', 'IN', 'MI', 'OH', 'WI', 'IA', 'KS', 'MN', 'MO', 'NE', 'ND', 'SD'],\n  'South': ['DE', 'DC', 'FL', 'GA', 'MD', 'NC', 'SC', 'VA', 'WV', 'AL', 'KY', 'MS', 'TN', 'AR', 'LA', 'OK', 'TX'],\n  'West': ['AZ', 'CO', 'ID', 'MT', 'NV', 'NM', 'UT', 'WY', 'AK', 'CA', 'HI', 'OR', 'WA'],\n  // Census Divisions\n  'New England': ['CT', 'ME', 'MA', 'NH', 'RI', 'VT'],\n  'Mid-Atlantic': ['NJ', 'NY', 'PA'],\n  'East North Central': ['IL', 'IN', 'MI', 'OH', 'WI'],\n  'West North Central': ['IA', 'KS', 'MN', 'MO', 'NE', 'ND', 'SD'],\n  'South Atlantic': ['DE', 'DC', 'FL', 'GA', 'MD', 'NC', 'SC', 'VA', 'WV'],\n  'East South Central': ['AL', 'KY', 'MS', 'TN'],\n  'West South Central': ['AR', 'LA', 'OK', 'TX'],\n  'Mountain': ['AZ', 'CO', 'ID', 'MT', 'NV', 'NM', 'UT', 'WY'],\n  'Pacific': ['AK', 'CA', 'HI', 'OR', 'WA']\n};\n\n// Helper function to get region for a state\nconst getStateRegion = state => {\n  if (!state) return null;\n  const stateCode = state.toUpperCase();\n  for (const [region, states] of Object.entries(US_REGIONS)) {\n    if (states.includes(stateCode)) {\n      return region;\n    }\n  }\n  return 'Other';\n};\nconst PAMatch = () => {\n  _s();\n  var _userTranscript$calcu2, _userTranscript$scien2;\n  console.log('PAMatch: Component Mounting...'); // <-- Add log\n  const {\n    token\n  } = useAuth();\n  const navigate = useNavigate();\n  const [userTranscript, setUserTranscript] = useState(null);\n  const [profileData, setProfileData] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [programs, setPrograms] = useState([]);\n  const [selectedPrereq, setSelectedPrereq] = useState(null);\n  const [showCourseModal, setShowCourseModal] = useState(false);\n  const [availableCourses, setAvailableCourses] = useState([]);\n  const [expandedSections, setExpandedSections] = useState({\n    eligible: false,\n    ineligible: false,\n    incomplete: false\n  });\n  const [expandedPrograms, setExpandedPrograms] = useState({});\n  const [activeTabs, setActiveTabs] = useState({}); // Track active tab for each program\n  // New state variables for saved results functionality\n  const [usingSavedResults, setUsingSavedResults] = useState(false);\n  const [savedResultsTimestamp, setSavedResultsTimestamp] = useState(null);\n  const [savingResults, setSavingResults] = useState(false);\n  // Add new state for manually approved programs\n  const [manuallyApprovedPrograms, setManuallyApprovedPrograms] = useState([]);\n\n  // New state for view options\n  const [viewMode, setViewMode] = useState('accordion'); // 'accordion' or 'grid'\n  const [searchTerm, setSearchTerm] = useState('');\n  const [sortBy, setSortBy] = useState('name'); // 'name', 'gpa', 'eligibility'\n  const [filterBy, setFilterBy] = useState('all'); // 'all', 'eligible', 'ineligible', 'incomplete'\n  const [stateFilter, setStateFilter] = useState('all'); // 'all', specific state, or region\n  const [regionFilter, setRegionFilter] = useState('all'); // 'all', 'northeast', 'southeast', etc.\n\n  // Check for token on mount and when token changes\n  useEffect(() => {\n    console.log('PAMatch: Token Check Effect - Token:', token); // <-- Add log\n    if (!token) {\n      console.log('PAMatch: No token found, navigating to /login'); // <-- Add log\n      navigate('/login');\n      return;\n    }\n  }, [token, navigate]);\n  useEffect(() => {\n    console.log('PAMatch: Data Fetch Effect - Running with token:', token); // <-- Add log\n\n    // Try to get saved results first, then fallback to fetching and calculating\n    const loadData = async () => {\n      setIsLoading(true);\n      setError(null);\n      try {\n        // Check for saved results first\n        await checkForSavedResults();\n      } catch (err) {\n        console.error('PAMatch: Error checking for saved results:', err);\n        // If error in getting saved results, fall back to normal data fetch\n        await fetchAndCalculateResults();\n      } finally {\n        setIsLoading(false);\n      }\n    };\n    if (token) {\n      loadData();\n    }\n  }, [token, navigate]);\n\n  // New function to check for saved matching results\n  const checkForSavedResults = async () => {\n    console.log('PAMatch: Checking for saved matching results...');\n    try {\n      const headers = {\n        Authorization: `Bearer ${token}`,\n        'Content-Type': 'application/json'\n      };\n      const response = await axios.get(`${process.env.REACT_APP_API_URL || 'http://127.0.0.1:8000'}/api/matching-results/`, {\n        headers\n      });\n      console.log('PAMatch: Saved results response:', response.data);\n      if (response.data.status === 'success') {\n        // We have valid saved results, use them\n        console.log('PAMatch: Using saved matching results from', response.data.timestamp);\n        const savedData = response.data.matching_results;\n\n        // Update state with saved data\n        setPrograms(savedData.programs || []);\n        setUserTranscript(savedData.userTranscript || null);\n        setProfileData(savedData.profileData || null);\n\n        // Load manually approved programs if they exist\n        if (savedData.manuallyApprovedPrograms) {\n          setManuallyApprovedPrograms(savedData.manuallyApprovedPrograms);\n        }\n\n        // Set metadata about saved results\n        setSavedResultsTimestamp(response.data.timestamp);\n        setUsingSavedResults(true);\n        return true;\n      } else if (response.data.status === 'stale') {\n        // Data has changed, need fresh calculation\n        console.log('PAMatch: Saved results are stale, calculating fresh results');\n        await fetchAndCalculateResults();\n        return false;\n      } else {\n        // No saved results found\n        console.log('PAMatch: No saved results found, calculating fresh results');\n        await fetchAndCalculateResults();\n        return false;\n      }\n    } catch (err) {\n      console.error('PAMatch: Error retrieving saved results:', err);\n      throw err;\n    }\n  };\n\n  // Refactored original data fetch into a separate function\n  const fetchAndCalculateResults = async () => {\n    console.log('PAMatch: Starting data fetch...'); // <-- Add log\n    setUsingSavedResults(false); // Mark that we're using fresh data\n\n    try {\n      if (!token) {\n        console.log('PAMatch: Token became null/undefined before API calls'); // <-- Add log\n        throw new Error('No authentication token available');\n      }\n      const headers = {\n        Authorization: `Bearer ${token}`,\n        'Content-Type': 'application/json'\n      };\n\n      // Fetch programs\n      console.log('PAMatch: Fetching programs...'); // <-- Add log\n      const programsResponse = await axios.get(`${process.env.REACT_APP_API_URL || 'http://127.0.0.1:8000'}/api/programs/`, {\n        headers\n      });\n      const fetchedPrograms = programsResponse.data.programs;\n      setPrograms(fetchedPrograms);\n      console.log('PAMatch: Fetched programs successfully.'); // <-- Add log\n\n      // Fetch saved prerequisites\n      console.log('PAMatch: Fetching saved prerequisites...'); // <-- Add log\n      const savedPrereqsResponse = await axios.get(`${process.env.REACT_APP_API_URL || 'http://127.0.0.1:8000'}/api/prerequisites/saved/`, {\n        headers\n      });\n      console.log('PAMatch: Fetched saved prerequisites successfully.'); // <-- Add log\n\n      // Fetch profile data\n      console.log('PAMatch: Fetching profile data...'); // <-- Add log\n      const profileResponse = await axios.get(`${process.env.REACT_APP_API_URL || 'http://127.0.0.1:8000'}/api/profile/`, {\n        headers\n      });\n      console.log('PAMatch: Profile data received:', profileResponse.data); // Debug log to see what fields are available\n\n      // Normalize profile data to ensure both camelCase and snake_case fields are available\n      const normalizedProfileData = normalizeProfileData(profileResponse.data);\n      console.log('PAMatch: Normalized profile data:', normalizedProfileData);\n      setProfileData(normalizedProfileData);\n      console.log('PAMatch: Fetched profile data successfully.'); // <-- Add log\n\n      // Fetch transcript data to get accurate GPAs\n      console.log('PAMatch: Fetching transcript data...'); // <-- Add log\n      const transcriptResponse = await axios.get(`${process.env.REACT_APP_API_URL || 'http://127.0.0.1:8000'}/api/transcript/get/`, {\n        headers\n      });\n      const transcripts = transcriptResponse.data.transcripts || [];\n      console.log('PAMatch: Fetched transcript data successfully.'); // <-- Add log\n\n      // Use the CASPA GPA values provided by the user\n      // These values come from the CASPA GPA calculator on the Dashboard page\n      console.log('PAMatch: Using user-provided CASPA GPAs');\n\n      // CASPA GPA values from the CASPA tab\n      const caspaCalculatedGPA = 3.41; // Overall GPA from CASPA calculation (from Overall row, Total column)\n      const caspaScienceGPA = 3.20; // Science GPA from CASPA calculation (from Overall row, Science column)\n\n      console.log('PAMatch: Using CASPA GPAs - Overall:', caspaCalculatedGPA, 'Science:', caspaScienceGPA);\n\n      // Calculate basic GPA for prerequisites only\n      let totalPoints = 0;\n      let totalCredits = 0;\n      let sciencePoints = 0;\n      let scienceCredits = 0;\n      transcripts.forEach(transcript => {\n        var _transcript$transcrip;\n        const semesters = ((_transcript$transcrip = transcript.transcript_data) === null || _transcript$transcrip === void 0 ? void 0 : _transcript$transcrip.semesters) || [];\n        semesters.forEach(semester => {\n          semester.courses.forEach(course => {\n            if (!['F', 'W', 'U'].includes(course.grade)) {\n              const credits = parseFloat(course.credits) || 0;\n              const gradePoints = gradeToPoints(course.grade);\n              if (!isNaN(credits) && !isNaN(gradePoints)) {\n                totalPoints += credits * gradePoints;\n                totalCredits += credits;\n                if (course.is_science) {\n                  sciencePoints += credits * gradePoints;\n                  scienceCredits += credits;\n                }\n              }\n            }\n          });\n        });\n      });\n\n      // These are just used for prerequisite mapping, not for program matching\n      const calculatedGPA = totalCredits > 0 ? totalPoints / totalCredits : 0;\n      const scienceGPA = scienceCredits > 0 ? sciencePoints / scienceCredits : 0;\n\n      // --- Prepare All Transcript Courses (with de-duplication) ---\n      let allTranscriptCourses = []; // Use let\n      const seenCourses = new Set(); // Use a Set to track unique courses\n\n      transcripts.forEach(transcript => {\n        var _transcript$transcrip2;\n        const institution = transcript.institution; // Get institution name\n        const semesters = ((_transcript$transcrip2 = transcript.transcript_data) === null || _transcript$transcrip2 === void 0 ? void 0 : _transcript$transcrip2.semesters) || [];\n        semesters.forEach(semester => {\n          const term = semester.term;\n          const year = semester.year;\n          semester.courses.forEach(course => {\n            // We only want to add *unique* courses based on code and name primarily,\n            // preferring the best grade if taken multiple times.\n            // Let's build a temporary map first.\n            // Key: `${normalizedInstitution}-${course.code}`\n            // Value: { bestCourseObject }\n            if (!['F', 'W', 'U'].includes(course.grade)) {\n              const normalizedInstitution = institution.toLowerCase().replace(/[,.]/g, '').trim();\n              const courseMapKey = `${normalizedInstitution}-${course.code}`;\n              const currentCourseData = {\n                ...course,\n                institution: institution,\n                term: term,\n                year: year,\n                gradePoints: gradeToPoints(course.grade) // Add grade points for comparison\n              };\n              if (!seenCourses[courseMapKey] || currentCourseData.gradePoints > seenCourses[courseMapKey].gradePoints) {\n                seenCourses[courseMapKey] = currentCourseData; // Store/replace with the better grade\n              }\n            }\n          });\n        });\n      });\n      // Now, convert the map values back into the final array\n      allTranscriptCourses = Object.values(seenCourses); // Assign to existing variable (no const)\n      console.log(\"De-duplicated (best grade) allTranscriptCourses:\", allTranscriptCourses);\n      // --- End Prepare All Transcript Courses ---\n\n      // Use only the saved prerequisites for program matching\n      const savedPrereqs = savedPrereqsResponse.data.prerequisites; // Assuming this is the correct structure from API\n      console.log('Saved prerequisites:', savedPrereqs);\n\n      // Create a case-insensitive lookup for SAVED prerequisites\n      // Assuming savedPrereqs is like: { \"Human Anatomy\": { courses: [...] }, ... }\n      const savedPrereqsLookup = Object.entries(savedPrereqs || {}).reduce((acc, [key, value]) => {\n        // Ensure value has a courses array\n        acc[key.toLowerCase()] = {\n          courses: Array.isArray(value === null || value === void 0 ? void 0 : value.courses) ? value.courses : []\n        };\n        return acc;\n      }, {});\n\n      // Map prerequisites for each program using SAVED and SUGGESTED courses\n      const programPrereqs = {};\n      fetchedPrograms.forEach(program => {\n        console.log(`Processing prerequisites for program: ${program.name}`);\n        if (program.requirements.prerequisites) {\n          const programMatches = {};\n          Object.entries(program.requirements.prerequisites).forEach(([prereqName, prereqData]) => {\n            const savedPrereq = savedPrereqsLookup[prereqName.toLowerCase()];\n            let coursesToUse = [];\n            let isSaved = false;\n\n            // Check if this prerequisite category has saved courses, with special handling for A&P\n            let foundSaved = false;\n            if (savedPrereq && savedPrereq.courses.length > 0) {\n              // Found direct match for saved category\n              coursesToUse = savedPrereq.courses;\n              foundSaved = true;\n            } else if (prereqName.toLowerCase().includes('anatomy') || prereqName.toLowerCase().includes('physiology')) {\n              // If Anatomy or Physiology required, check if combined A&P was saved\n              const combinedAPCategory = 'human anatomy and physiology';\n              const savedCombined = savedPrereqsLookup[combinedAPCategory];\n              if (savedCombined && savedCombined.courses.length > 0) {\n                coursesToUse = savedCombined.courses;\n                foundSaved = true;\n                console.log(`Using SAVED combined A&P courses for ${prereqName}`);\n              }\n            }\n            if (foundSaved) {\n              isSaved = true;\n              console.log(`Using SAVED courses for ${prereqName}:`, coursesToUse);\n            } else {\n              // If not saved (directly or via combined A&P), find SUGGESTED courses\n              coursesToUse = allTranscriptCourses.filter(course => courseMatchesPrereq(course.name, course.code, prereqName));\n              console.log(`Using SUGGESTED courses for ${prereqName}:`, coursesToUse);\n            }\n\n            // Calculate total credits from the courses being used (saved or suggested)\n            const totalCredits = coursesToUse.reduce((sum, course) => sum + parseFloat(course.credits || 0), 0);\n\n            // Store the result\n            programMatches[prereqName] = {\n              totalCredits: totalCredits,\n              credits: prereqData.credits,\n              // Required credits\n              timeframe: prereqData.timeframe,\n              lab_required: prereqData.lab_required,\n              courses: coursesToUse,\n              // List of courses (either saved or suggested)\n              isSaved: isSaved // Flag to indicate if these are saved or suggested\n            };\n          });\n          programPrereqs[program.name] = programMatches;\n        }\n      });\n      console.log('Final programPrereqs:', programPrereqs);\n      const updatedTranscript = {\n        calculated_gpa: caspaCalculatedGPA,\n        science_gpa: caspaScienceGPA,\n        prerequisites: programPrereqs\n      };\n      console.log('Setting userTranscript:', updatedTranscript);\n      setUserTranscript(updatedTranscript);\n      console.log('PAMatch: Finished processing, userTranscript set.'); // <-- Add log\n\n      // After calculating everything, save results to backend\n      await saveCurrentResults({\n        programs: fetchedPrograms,\n        userTranscript: updatedTranscript,\n        profileData: profileResponse.data\n      });\n    } catch (err) {\n      var _err$response, _err$response2, _err$response3, _err$response4, _err$response4$data;\n      console.error('PAMatch: Error during data fetch or processing:', err); // <-- Add log\n      console.error('PAMatch: Error Response Status:', (_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status); // <-- Log status\n      console.error('PAMatch: Error Response Data:', (_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.data); // <-- Log data\n      if (((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : _err$response3.status) === 401) {\n        // Token is invalid or expired\n        setError('Your session has expired. Please log in again.');\n        console.log('PAMatch: 401 Error - Navigating to /login'); // <-- Add log\n        navigate('/login');\n        return;\n      }\n      setError(((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : (_err$response4$data = _err$response4.data) === null || _err$response4$data === void 0 ? void 0 : _err$response4$data.error) || 'Failed to fetch data');\n      console.log('PAMatch: Non-401 Error occurred, setting error state.'); // <-- Add log\n    }\n  };\n\n  // Function to save current results to backend\n  const saveCurrentResults = async dataToSave => {\n    console.log('PAMatch: Saving current matching results...');\n    setSavingResults(true);\n    try {\n      const headers = {\n        Authorization: `Bearer ${token}`,\n        'Content-Type': 'application/json'\n      };\n\n      // Prepare data object to save\n      const matchingResults = dataToSave || {\n        programs: programs,\n        userTranscript: userTranscript,\n        profileData: profileData,\n        manuallyApprovedPrograms: manuallyApprovedPrograms\n      };\n\n      // Save to backend\n      const response = await axios.post('http://127.0.0.1:8000/api/matching-results/save/', {\n        matching_results: matchingResults\n      }, {\n        headers\n      });\n      console.log('PAMatch: Results saved successfully:', response.data);\n\n      // Update state to reflect saved status\n      setSavedResultsTimestamp(response.data.timestamp);\n      setUsingSavedResults(true);\n      return true;\n    } catch (err) {\n      console.error('PAMatch: Error saving matching results:', err);\n      return false;\n    } finally {\n      setSavingResults(false);\n    }\n  };\n\n  // Function to force recalculation of results\n  const handleRecalculate = () => {\n    setIsLoading(true);\n    fetchAndCalculateResults().finally(() => setIsLoading(false));\n  };\n  const courseMatchesPrereq = (courseName, courseCode, prereqName) => {\n    var _courseCode$match, _courseCode$match2, _courseCode$match3, _courseCode$match4, _courseCode$match5;\n    courseName = courseName.toLowerCase();\n    courseCode = courseCode.toLowerCase();\n    prereqName = prereqName.toLowerCase();\n\n    // Common course prefix patterns\n    const chemistryPrefixes = ['chem', 'chm', 'chemy'];\n    const biologyPrefixes = ['bio', 'biol', 'bsc'];\n    const psychologyPrefixes = ['psy', 'psyc', 'psych'];\n    const englishPrefixes = ['eng', 'engl', 'eh'];\n    const statisticsPrefixes = ['stat', 'stats', 'sta', 'mth'];\n    const sociologyPrefixes = ['soc', 'socy'];\n    const anthropologyPrefixes = ['anth', 'ant'];\n\n    // Check for combined anatomy & physiology courses\n    if (prereqName.includes('anatomy & physiology') || prereqName.includes('anatomy and physiology')) {\n      const isMatch = courseName.includes('anatomy') && courseName.includes('physiology') || courseName.includes('a&p') || courseName.includes('anat & phys') || courseName.includes('anatomical and physiological') || courseCode.includes('a&p') || courseCode.match(/\\d+/) && (courseName.includes('human a & p') || courseName.includes('hum anat & phys'));\n      if (isMatch) console.log('Matched A&P course:', courseName);\n      return isMatch;\n    }\n\n    // Match based on common course patterns\n    switch (true) {\n      // --- Combined Chemistry Sequence ---\n      case prereqName.includes('chemistry sequence (gen/org/biochem)'):\n        // Match the new name\n        const seq_isGenChem = chemistryPrefixes.some(prefix => courseCode.startsWith(prefix)) && (\n        // Renamed variable\n        courseName.includes('general') || courseName.includes('inorganic') || ((_courseCode$match = courseCode.match(/\\d+/)) === null || _courseCode$match === void 0 ? void 0 : _courseCode$match.some(num => parseInt(num) < 200)));\n        const seq_isOrgChem = chemistryPrefixes.some(prefix => courseCode.startsWith(prefix)) && (\n        // Renamed variable\n        courseName.includes('organic') || ((_courseCode$match2 = courseCode.match(/\\d+/)) === null || _courseCode$match2 === void 0 ? void 0 : _courseCode$match2.some(num => parseInt(num) >= 200 && parseInt(num) < 400)));\n        const seq_isBiochem = courseCode.startsWith('bch') || courseCode.startsWith('bioc') ||\n        // Renamed variable\n        courseName.includes('biochem');\n        return seq_isGenChem || seq_isOrgChem || seq_isBiochem;\n      // Use renamed variables\n\n      // --- Standard Categories ---\n      case prereqName.includes('human anatomy'):\n        const isAnatomy =\n        // Check for combined A&P courses first\n        courseName.includes('anatomy') && courseName.includes('physiology') || courseName.includes('a&p') || courseName.includes('anat & phys') || courseName.includes('human a & p') || courseName.includes('hum anat & phys') ||\n        // Then check for standalone anatomy courses\n        biologyPrefixes.some(prefix => courseCode.startsWith(prefix)) && (courseName.includes('anatomy') || courseName.includes('anatomical'));\n        if (isAnatomy) console.log('Matched Anatomy course:', courseName);\n        return isAnatomy;\n      case prereqName.includes('human physiology'):\n        const isPhysiology =\n        // Check for combined A&P courses first\n        courseName.includes('anatomy') && courseName.includes('physiology') || courseName.includes('a&p') || courseName.includes('anat & phys') || courseName.includes('human a & p') || courseName.includes('hum anat & phys') ||\n        // Then check for standalone physiology courses\n        biologyPrefixes.some(prefix => courseCode.startsWith(prefix)) && (courseName.includes('physiology') || courseName.includes('physiological'));\n        if (isPhysiology) console.log('Matched Physiology course:', courseName);\n        return isPhysiology;\n      case prereqName.includes('microbiology'):\n        const isMicro = biologyPrefixes.some(prefix => courseCode.startsWith(prefix)) && courseName.includes('micro') || courseName.includes('microbiology');\n        if (isMicro) console.log('Matched Microbiology:', courseName);\n        return isMicro;\n      case prereqName.includes('general chemistry'):\n        const isGenChem = chemistryPrefixes.some(prefix => courseCode.startsWith(prefix)) && (courseName.includes('general') || ((_courseCode$match3 = courseCode.match(/\\d+/)) === null || _courseCode$match3 === void 0 ? void 0 : _courseCode$match3.some(num => parseInt(num) < 200)));\n        if (isGenChem) console.log('Matched General Chemistry:', courseName);\n        return isGenChem;\n      case prereqName.includes('organic chemistry'):\n        const isOrgChem = chemistryPrefixes.some(prefix => courseCode.startsWith(prefix)) && (courseName.includes('organic') || ((_courseCode$match4 = courseCode.match(/\\d+/)) === null || _courseCode$match4 === void 0 ? void 0 : _courseCode$match4.some(num => parseInt(num) >= 200 && parseInt(num) < 400)));\n        if (isOrgChem) console.log('Matched Organic Chemistry:', courseName);\n        return isOrgChem;\n      case prereqName.includes('biochemistry'):\n        const isBiochem = courseCode.startsWith('bch') || courseCode.startsWith('bioc') || courseName.includes('biochem');\n        if (isBiochem) console.log('Matched Biochemistry:', courseName);\n        return isBiochem;\n      case prereqName.includes('psychology'):\n        const isPsych = psychologyPrefixes.some(prefix => courseCode.startsWith(prefix)) || courseName.includes('psychology');\n        if (isPsych) console.log('Matched Psychology:', courseName);\n        return isPsych;\n      case prereqName.includes('statistics'):\n        const isStats = statisticsPrefixes.some(prefix => courseCode.startsWith(prefix)) || courseName.includes('statistics') || courseName.includes('statistical');\n        if (isStats) console.log('Matched Statistics:', courseName);\n        return isStats;\n      case prereqName.includes('medical terminology'):\n        const isMedTerm = courseName.includes('medical terminology') || courseCode.startsWith('nurs') && courseName.includes('terminology');\n        if (isMedTerm) console.log('Matched Medical Terminology:', courseName);\n        return isMedTerm;\n      case prereqName.includes('behavioral sciences'):\n        const isBehavioralScience = psychologyPrefixes.some(prefix => courseCode.startsWith(prefix)) || courseName.includes('psychology') || courseName.includes('behavioral') || courseName.includes('cognitive') || courseName.includes('developmental') || courseName.includes('abnormal') || courseName.includes('social psychology') || courseName.includes('child psychology') || courseName.includes('adolescent psychology') || courseName.includes('human development') || courseName.includes('life span') || courseName.includes('counseling') || courseName.includes('psychobiology') || courseName.includes('neuropsychology') || courseName.includes('brain and behavior') || courseName.includes('human behavior') || courseName.includes('behavioral science');\n        if (isBehavioralScience) console.log('Matched Behavioral Science:', courseName);\n        return isBehavioralScience;\n      case prereqName.includes('sociology'):\n        const isSociology = sociologyPrefixes.some(prefix => courseCode.startsWith(prefix)) || courseName.includes('sociology') || courseName.includes('social welfare') || courseName.includes('social work') || courseName.includes('criminology') || courseName.includes('criminal justice') || courseName.includes('marriage and family') || courseName.includes('family studies') || courseName.includes('social ecology');\n        if (isSociology) console.log('Matched Sociology:', courseName);\n        return isSociology;\n      case prereqName.includes('social sciences'):\n        const isSocialScience =\n        // Include all sociology courses\n        sociologyPrefixes.some(prefix => courseCode.startsWith(prefix)) || courseName.includes('sociology') || courseName.includes('social welfare') || courseName.includes('social work') || courseName.includes('criminology') || courseName.includes('criminal justice') || courseName.includes('marriage and family') || courseName.includes('family studies') ||\n        // Include all psychology courses\n        psychologyPrefixes.some(prefix => courseCode.startsWith(prefix)) || courseName.includes('psychology') || courseName.includes('behavioral') || courseName.includes('cognitive') || courseName.includes('developmental') || courseName.includes('abnormal') || courseName.includes('social psychology') || courseName.includes('child psychology') || courseName.includes('adolescent psychology') ||\n        // Include anthropology and other social sciences\n        anthropologyPrefixes.some(prefix => courseCode.startsWith(prefix)) || courseName.includes('anthropology') || courseName.includes('cultural studies') || courseName.includes('ethnic studies') || courseName.includes('gender studies') || courseName.includes('women studies') || courseName.includes('african american studies') || courseName.includes('asian american studies') || courseName.includes('latino american studies') || courseName.includes('native american studies') || courseName.includes('social justice') || courseName.includes('human development') || courseName.includes('human sexuality') || courseName.includes('death and dying') || courseName.includes('multicultural') ||\n        // Additional social science indicators\n        courseName.includes('social science') || courseName.includes('human behavior') || courseName.includes('social ecology') || courseName.includes('community') || courseName.includes('cultural geography');\n        if (isSocialScience) console.log('Matched Social Science:', courseName);\n        return isSocialScience;\n      case prereqName.includes('english'):\n        const isEnglish = englishPrefixes.some(prefix => courseCode.startsWith(prefix)) || courseName.includes('english') || courseName.includes('composition') || courseName.includes('writing');\n        if (isEnglish) console.log('Matched English:', courseName);\n        return isEnglish;\n      case prereqName.includes('chemistry (general)'):\n        // Add this case\n        // This logic correctly identifies general/inorganic chemistry\n        return chemistryPrefixes.some(prefix => courseCode.startsWith(prefix)) && (courseName.includes('general') || courseName.includes('inorganic') || ((_courseCode$match5 = courseCode.match(/\\d+/)) === null || _courseCode$match5 === void 0 ? void 0 : _courseCode$match5.some(num => parseInt(num) < 200)));\n      case prereqName.includes('biology (general)'):\n        // Add this case\n        // Must have bio prefix but NOT contain keywords for more specific categories\n        const isGeneralBio = biologyPrefixes.some(prefix => courseCode.startsWith(prefix)) && !courseName.includes('anatomy') && !courseName.includes('physiology') && !courseName.includes('micro') &&\n        // Exclude microbiology\n        !courseName.includes('genetic'); // Exclude genetics\n        return isGeneralBio;\n      case prereqName.includes('cpr'):\n        // Add this case\n        return courseName.includes('cpr') || courseName.includes('cardiopulmonary resuscitation') || courseName.includes('basic life support');\n      case prereqName.includes('math'):\n        // Added logic for Math\n        // Exclude statistics as it's a separate category\n        const isMath = (courseCode.startsWith('math') || courseCode.startsWith('mth')) && !courseName.includes('stat'); // Exclude statistics\n        // Could add more specific checks for 'algebra', 'calculus', 'pre-calculus' if needed\n        return isMath;\n      case prereqName.includes('sociology'):\n        // Broadened logic for Sociology\n        const isSociologyRelated =\n        // Use a different variable name\n        sociologyPrefixes.some(prefix => courseCode.startsWith(prefix)) ||\n        // SOC code\n        anthropologyPrefixes.some(prefix => courseCode.startsWith(prefix)) ||\n        // ANTH code\n        courseName.includes('sociology') || courseName.includes('social welfare') || courseName.includes('social work') || courseName.includes('criminology') || courseName.includes('criminal justice') || courseName.includes('marriage and family') || courseName.includes('family studies') || courseName.includes('social ecology') || courseName.includes('anthropology') ||\n        // Added Anthropology\n        courseName.includes('gender studies') ||\n        // Added Gender Studies\n        courseName.includes('ethnic studies') // Added Ethnic Studies\n        ;\n        return isSociologyRelated;\n      // Use updated variable name\n\n      default:\n        const isDefaultMatch = courseName.includes(prereqName) || courseCode.includes(prereqName);\n        if (isDefaultMatch) console.log('Matched Default:', courseName);\n        return isDefaultMatch;\n    }\n  };\n  const mapPrerequisites = (courses, prerequisites) => {\n    const prereqMap = {};\n\n    // Process each prerequisite\n    Object.entries(prerequisites).forEach(([prereqName, prereqData]) => {\n      console.log(`\\nProcessing prerequisite: ${prereqName}`);\n      console.log('Required credits:', prereqData.credits);\n\n      // Find all matching courses across all transcripts\n      const matchingCourses = courses.filter(course => courseMatchesPrereq(course.name, course.code, prereqName) && !['F', 'W', 'U'].includes(course.grade));\n      console.log('Found matching courses:', matchingCourses);\n\n      // Special handling for Anatomy and Physiology\n      const isAnatomyOrPhysiology = prereqName.toLowerCase().includes('anatomy') || prereqName.toLowerCase().includes('physiology');\n\n      // Sort matching courses by grade (best grades first)\n      const sortedCourses = [...matchingCourses].sort((a, b) => {\n        const gradeA = gradeToPoints(a.grade);\n        const gradeB = gradeToPoints(b.grade);\n        if (gradeB !== gradeA) return gradeB - gradeA;\n        return parseInt(b.year) - parseInt(a.year);\n      });\n      console.log('Sorted matching courses:', sortedCourses);\n\n      // Calculate total credits and select courses\n      let totalCredits = 0;\n      const requiredCredits = parseFloat(prereqData.credits) || 0;\n      const selectedCourses = [];\n      for (const course of sortedCourses) {\n        const courseCredits = parseFloat(course.credits) || 0;\n\n        // For combined A&P courses, count full credits for both requirements\n        if (isAnatomyOrPhysiology && course.name.toLowerCase().includes('anatomy') && course.name.toLowerCase().includes('physiology')) {\n          selectedCourses.push({\n            code: course.code,\n            name: course.name,\n            credits: courseCredits,\n            grade: course.grade,\n            institution: course.institution,\n            term: course.term,\n            year: course.year\n          });\n          totalCredits += courseCredits;\n        } else if (!isAnatomyOrPhysiology && totalCredits < requiredCredits) {\n          // Normal handling for non-A&P courses\n          selectedCourses.push({\n            code: course.code,\n            name: course.name,\n            credits: courseCredits,\n            grade: course.grade,\n            institution: course.institution,\n            term: course.term,\n            year: course.year\n          });\n          totalCredits += courseCredits;\n        }\n      }\n\n      // Create prerequisite entry\n      prereqMap[prereqName] = {\n        totalCredits,\n        credits: requiredCredits,\n        timeframe: prereqData.timeframe,\n        lab_required: prereqData.lab_required,\n        courses: selectedCourses,\n        all_matching_courses: matchingCourses\n      };\n      console.log(`Final prerequisite entry for ${prereqName}:`, prereqMap[prereqName]);\n    });\n    return prereqMap;\n  };\n  const openCourseSelection = (prereqName, program, allCourses, currentPrereqCourses) => {\n    // Filter out courses that are already mapped to this prerequisite\n    const mappedCourseCodes = new Set(currentPrereqCourses.map(course => course.code));\n\n    // Use all_courses directly from userTranscript\n    const validCourses = userTranscript.all_courses.filter(course => !mappedCourseCodes.has(course.code) && !['F', 'W', 'U'].includes(course.grade) && courseMatchesPrereq(course.name, course.code, prereqName));\n\n    // Debug logging\n    console.log('Available courses for selection:', validCourses);\n    console.log('Mapped course codes:', mappedCourseCodes);\n    console.log('Prerequisite name:', prereqName);\n    setAvailableCourses(validCourses);\n    setSelectedPrereq({\n      name: prereqName,\n      program: program\n    });\n    setShowCourseModal(true);\n  };\n  const addCourseToPrereq = course => {\n    if (!selectedPrereq) return;\n    const updatedTranscript = {\n      ...userTranscript\n    };\n    const prereqData = updatedTranscript.prerequisites[selectedPrereq.name];\n\n    // Add the selected course to the prerequisite's courses\n    prereqData.courses.push({\n      code: course.code,\n      name: course.name,\n      credits: course.credits,\n      grade: course.grade,\n      institution: course.institution,\n      term: course.term,\n      year: course.year\n    });\n\n    // Update total credits\n    prereqData.totalCredits += parseFloat(course.credits) || 0;\n\n    // Recalculate all GPAs\n    const {\n      calculatedGPA,\n      scienceGPA\n    } = processMultipleTranscripts(updatedTranscript.transcript_data.institutions);\n    updatedTranscript.calculated_gpa = calculatedGPA;\n    updatedTranscript.science_gpa = scienceGPA;\n    updatedTranscript.prerequisite_gpa = calculatePrereqGPA(updatedTranscript.prerequisites, selectedPrereq.program);\n    setUserTranscript(updatedTranscript);\n    setShowCourseModal(false);\n  };\n  const CourseSelectionModal = () => {\n    if (!showCourseModal) return null;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [\"Select Course for \", selectedPrereq === null || selectedPrereq === void 0 ? void 0 : selectedPrereq.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 849,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"course-list\",\n          children: availableCourses.map((course, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"course-option\",\n            onClick: () => addCourseToPrereq(course),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"course-option-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: [course.code, \" - \", course.name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 854,\n                columnNumber: 29\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: course.institution\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 855,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 853,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"course-option-details\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Credits: \", course.credits, \" | Grade: \", course.grade]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 858,\n                columnNumber: 29\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [course.term, \" \", course.year]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 859,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 857,\n              columnNumber: 29\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 852,\n            columnNumber: 25\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 850,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"close-modal\",\n          onClick: () => setShowCourseModal(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 864,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 848,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 847,\n      columnNumber: 9\n    }, this);\n  };\n  const renderPrerequisiteDetails = (prereqData, prereqName, programName) => {\n    var _userTranscript$prere, _userTranscript$prere2;\n    if (!(userTranscript !== null && userTranscript !== void 0 && (_userTranscript$prere = userTranscript.prerequisites) !== null && _userTranscript$prere !== void 0 && (_userTranscript$prere2 = _userTranscript$prere[programName]) !== null && _userTranscript$prere2 !== void 0 && _userTranscript$prere2[prereqName])) {\n      return null;\n    }\n    const programPrereqData = userTranscript.prerequisites[programName][prereqName];\n\n    // Get the matched courses\n    const matchedCourses = programPrereqData.courses || [];\n\n    // Sort matched courses by grade\n    const sortedMatchedCourses = [...matchedCourses].sort((a, b) => gradeToPoints(b.grade) - gradeToPoints(a.grade));\n\n    // Track which courses are used in GPA calculation\n    const coursesUsedInGPA = new Set();\n    let creditsUsed = 0;\n    const requiredCredits = parseFloat(programPrereqData.credits) || 0;\n\n    // Mark courses used in GPA calculation\n    for (const course of sortedMatchedCourses) {\n      if (creditsUsed < requiredCredits) {\n        coursesUsedInGPA.add(course.code);\n        creditsUsed += parseFloat(course.credits) || 0;\n      }\n    }\n    const handleCourseRemove = (courseCode, programName, prereqName) => {\n      var _updatedTranscript$pr, _updatedTranscript$pr2;\n      const updatedTranscript = {\n        ...userTranscript\n      };\n\n      // Check if prerequisites exist\n      if (!((_updatedTranscript$pr = updatedTranscript.prerequisites) !== null && _updatedTranscript$pr !== void 0 && (_updatedTranscript$pr2 = _updatedTranscript$pr[programName]) !== null && _updatedTranscript$pr2 !== void 0 && _updatedTranscript$pr2[prereqName])) {\n        console.log('Prerequisites not found for:', {\n          programName,\n          prereqName\n        });\n        return;\n      }\n      const prereqData = updatedTranscript.prerequisites[programName][prereqName];\n\n      // Remove the course\n      prereqData.courses = prereqData.courses.filter(course => course.code !== courseCode);\n\n      // Update total credits\n      prereqData.totalCredits = prereqData.courses.reduce((total, course) => total + (parseFloat(course.credits) || 0), 0);\n\n      // Recalculate prerequisite GPA\n      updatedTranscript.prerequisite_gpa = calculatePrereqGPA(updatedTranscript.prerequisites, programName);\n      setUserTranscript(updatedTranscript);\n    };\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"prerequisite-courses\",\n      children: sortedMatchedCourses.map((course, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `course-detail ${coursesUsedInGPA.has(course.code) ? 'used-in-gpa' : ''}`,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"course-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"course-name\",\n            children: [course.code, \" - \", course.name, \" (\", course.credits, \" credits)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 928,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"course-institution\",\n            children: [course.institution, \" - \", course.term, \" \", course.year]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 931,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 927,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"course-stats\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"course-grade\",\n            children: [\"Grade: \", course.grade]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 936,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleCourseRemove(course.code, programName, prereqName),\n            className: \"remove-course-btn\",\n            children: \"\\u2715\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 937,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 935,\n          columnNumber: 21\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 926,\n        columnNumber: 17\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 924,\n      columnNumber: 9\n    }, this);\n  };\n  const gradeToPoints = grade => {\n    const gradePoints = {\n      'A+': 4.0,\n      'A': 4.0,\n      'A-': 3.7,\n      'B+': 3.3,\n      'B': 3.0,\n      'B-': 2.7,\n      'C+': 2.3,\n      'C': 2.0,\n      'C-': 1.7,\n      'D+': 1.3,\n      'D': 1.0,\n      'D-': 0.7,\n      'F': 0.0\n    };\n    return gradePoints[grade] || 0;\n  };\n  const calculatePrereqGPA = useCallback((prerequisites, programName) => {\n    if (!(prerequisites !== null && prerequisites !== void 0 && prerequisites[programName])) {\n      return 'N/A';\n    }\n    let totalPoints = 0;\n    let totalCredits = 0;\n\n    // Process prerequisites for the specific program\n    Object.values(prerequisites[programName]).forEach(prereqData => {\n      if (!prereqData.courses || prereqData.courses.length === 0) return;\n\n      // Sort courses by grade points in descending order\n      const sortedCourses = [...prereqData.courses].sort((a, b) => gradeToPoints(b.grade) - gradeToPoints(a.grade));\n\n      // Get the required credits for this prerequisite\n      const requiredCredits = parseFloat(prereqData.credits) || 0;\n      let creditsUsed = 0;\n      let courseIndex = 0;\n\n      // Use best grades until we meet the credit requirement\n      while (creditsUsed < requiredCredits && courseIndex < sortedCourses.length) {\n        const course = sortedCourses[courseIndex];\n        const courseCredits = parseFloat(course.credits) || 0;\n        const gradePoints = gradeToPoints(course.grade);\n        totalPoints += gradePoints * courseCredits;\n        totalCredits += courseCredits;\n        creditsUsed += courseCredits;\n        courseIndex++;\n      }\n    });\n    return totalCredits > 0 ? (totalPoints / totalCredits).toFixed(2) : 'N/A';\n  }, []);\n  const checkPrerequisites = (program, userTranscript) => {\n    var _userTranscript$prere3;\n    if (!program.requirements.prerequisites || !((_userTranscript$prere3 = userTranscript.prerequisites) !== null && _userTranscript$prere3 !== void 0 && _userTranscript$prere3[program.name])) {\n      return false;\n    }\n    return Object.entries(program.requirements.prerequisites).every(([prereqName, req]) => {\n      const programPrereqs = userTranscript.prerequisites[program.name];\n      if (!programPrereqs[prereqName]) return false;\n      const prereqData = programPrereqs[prereqName];\n      const totalCredits = prereqData.totalCredits || 0;\n      const requiredCredits = parseFloat(req.credits) || 0;\n\n      // Special handling for Anatomy and Physiology\n      if (prereqName.toLowerCase().includes('anatomy') || prereqName.toLowerCase().includes('physiology')) {\n        // Find all A&P courses\n        const combinedAPCourses = prereqData.courses.filter(course => course.name.toLowerCase().includes('anatomy') && course.name.toLowerCase().includes('physiology'));\n\n        // Calculate total credits from combined A&P courses\n        const combinedCredits = combinedAPCourses.reduce((sum, course) => sum + parseFloat(course.credits || 0), 0);\n\n        // If we have combined A&P courses, use their full credits\n        if (combinedAPCourses.length > 0) {\n          return combinedCredits >= requiredCredits;\n        }\n      }\n\n      // Check if lab requirement is met when applicable\n      const meetsLabRequirement = !req.lab_required || prereqData.courses.some(course => course.name.toLowerCase().includes('lab'));\n\n      // Check if timeframe requirement is met when applicable\n      const meetsTimeframe = !req.timeframe || prereqData.courses.every(course => {\n        const courseYear = parseInt(course.year);\n        const currentYear = new Date().getFullYear();\n        return currentYear - courseYear <= parseInt(req.timeframe);\n      });\n      return totalCredits >= requiredCredits && meetsLabRequirement && meetsTimeframe;\n    });\n  };\n  const checkPatientCareHours = (program, profileData) => {\n    const requiredHours = program.requirements.experience[\"Patient Care Hours\"] || 0;\n    // Handle both camelCase and snake_case field names\n    const userHours = parseInt((profileData === null || profileData === void 0 ? void 0 : profileData.direct_patient_care_hours) || (profileData === null || profileData === void 0 ? void 0 : profileData.directPatientCareHours) || 0);\n    return userHours >= requiredHours;\n  };\n  const checkShadowingHours = (program, profileData) => {\n    const requiredHours = program.requirements.experience[\"Shadowing Hours\"] || 0;\n    // Handle both camelCase and snake_case field names\n    const userHours = parseInt((profileData === null || profileData === void 0 ? void 0 : profileData.shadowing_hours) || (profileData === null || profileData === void 0 ? void 0 : profileData.shadowingHours) || 0);\n    return userHours >= requiredHours;\n  };\n  const getUserExperienceHours = (profileData, experienceType) => {\n    if (!profileData) return 0;\n    if (experienceType === \"Patient Care Hours\") {\n      return parseInt(profileData.direct_patient_care_hours || profileData.directPatientCareHours || 0);\n    }\n    if (experienceType === \"Shadowing Hours\") {\n      return parseInt(profileData.shadowing_hours || profileData.shadowingHours || 0);\n    }\n    return 0;\n  };\n  const processMultipleTranscripts = transcripts => {\n    if (!Array.isArray(transcripts)) {\n      console.error('Invalid transcripts data:', transcripts);\n      return {\n        allCoursesWithSource: [],\n        calculatedGPA: 0,\n        scienceGPA: 0,\n        totalCredits: 0,\n        scienceCredits: 0\n      };\n    }\n\n    // Combine all courses from all transcripts with institution info\n    const allCoursesWithSource = transcripts.flatMap(transcript => {\n      if (!transcript) {\n        console.warn('Invalid transcript entry:', transcript);\n        return [];\n      }\n      try {\n        var _transcript$transcrip3;\n        // Handle both initial transcript loading and course addition cases\n        if ((_transcript$transcrip3 = transcript.transcript_data) !== null && _transcript$transcrip3 !== void 0 && _transcript$transcrip3.semesters) {\n          var _transcript$transcrip4, _transcript$transcrip5;\n          // Initial transcript loading structure\n          const institution = ((_transcript$transcrip4 = transcript.transcript_data) === null || _transcript$transcrip4 === void 0 ? void 0 : (_transcript$transcrip5 = _transcript$transcrip4.academic_summary) === null || _transcript$transcrip5 === void 0 ? void 0 : _transcript$transcrip5.institution) || 'Unknown Institution';\n          return transcript.transcript_data.semesters.flatMap(semester => {\n            if (!(semester !== null && semester !== void 0 && semester.courses)) {\n              console.warn('Invalid semester data:', semester);\n              return [];\n            }\n            return semester.courses.map(course => ({\n              ...course,\n              institution,\n              term: semester.term || 'Unknown Term',\n              year: semester.year || 'Unknown Year'\n            }));\n          });\n        } else if (transcript.semesters) {\n          // Course addition structure (from institutions array)\n          const institution = transcript.name || 'Unknown Institution';\n          return transcript.semesters.flatMap(semester => {\n            if (!(semester !== null && semester !== void 0 && semester.courses)) {\n              console.warn('Invalid semester data:', semester);\n              return [];\n            }\n            return semester.courses.map(course => ({\n              ...course,\n              institution,\n              term: semester.term || 'Unknown Term',\n              year: semester.year || 'Unknown Year'\n            }));\n          });\n        }\n      } catch (error) {\n        console.error('Error processing transcript:', error);\n        return [];\n      }\n      console.warn('Invalid transcript structure:', transcript);\n      return [];\n    });\n\n    // Calculate overall GPA across all transcripts\n    let totalPoints = 0;\n    let totalCredits = 0;\n    let sciencePoints = 0;\n    let scienceCredits = 0;\n    allCoursesWithSource.forEach(course => {\n      try {\n        if (course !== null && course !== void 0 && course.grade && !['F', 'W', 'U'].includes(course.grade)) {\n          const credits = parseFloat(course.credits) || 0;\n          const gradePoints = gradeToPoints(course.grade);\n          if (!isNaN(credits) && !isNaN(gradePoints)) {\n            totalPoints += credits * gradePoints;\n            totalCredits += credits;\n\n            // Update science GPA if it's a science course\n            if (course.is_science) {\n              sciencePoints += credits * gradePoints;\n              scienceCredits += credits;\n            }\n          } else {\n            console.warn('Invalid credits or grade points:', {\n              course,\n              credits,\n              gradePoints\n            });\n          }\n        }\n      } catch (error) {\n        console.error('Error processing course:', error, course);\n      }\n    });\n    const calculatedGPA = totalCredits > 0 ? totalPoints / totalCredits : 0;\n    const scienceGPA = scienceCredits > 0 ? sciencePoints / scienceCredits : 0;\n    return {\n      allCoursesWithSource,\n      calculatedGPA,\n      scienceGPA,\n      totalCredits,\n      scienceCredits\n    };\n  };\n  const checkProgramEligibility = program => {\n    var _userTranscript$prere4, _program$requirements;\n    // First check if program is manually approved\n    if (manuallyApprovedPrograms.includes(program.name)) {\n      return 'eligible';\n    }\n\n    // Original eligibility logic\n    // Check if program has prerequisites defined\n    const hasPrereqs = program.requirements.prerequisites && Object.keys(program.requirements.prerequisites).length > 0;\n    if (!hasPrereqs) {\n      return 'incomplete';\n    }\n\n    // Check if prerequisites data exists for this program\n    const programPrereqs = userTranscript === null || userTranscript === void 0 ? void 0 : (_userTranscript$prere4 = userTranscript.prerequisites) === null || _userTranscript$prere4 === void 0 ? void 0 : _userTranscript$prere4[program.name];\n    if (!programPrereqs || Object.keys(programPrereqs).length === 0) {\n      return 'incomplete';\n    }\n\n    // Check all requirements\n    const meetsGPA = userTranscript.calculated_gpa >= program.requirements.gpa.overall;\n    const meetsScienceGPA = parseFloat(userTranscript.science_gpa) >= program.requirements.gpa.science;\n    const meetsPrereqs = Object.entries(program.requirements.prerequisites).every(([prereqName, req]) => {\n      if (!programPrereqs[prereqName]) return false; // No data for this prereq\n      const totalCredits = programPrereqs[prereqName].totalCredits || 0;\n      const requiredCreditsNum = parseFloat(req.credits); // Attempt to parse requirement\n\n      // If requirement is not a number (e.g., \"4 Semesters\"), consider met if any courses match\n      if (isNaN(requiredCreditsNum)) {\n        // Check if courses array exists and has items\n        return programPrereqs[prereqName].courses && programPrereqs[prereqName].courses.length > 0;\n      }\n      // Otherwise, perform the numeric comparison\n      return totalCredits >= requiredCreditsNum;\n    });\n\n    // Convert to numbers and handle both camelCase and snake_case field names\n    const patientCareHours = parseInt((profileData === null || profileData === void 0 ? void 0 : profileData.direct_patient_care_hours) || (profileData === null || profileData === void 0 ? void 0 : profileData.directPatientCareHours) || 0);\n    const shadowingHours = parseInt((profileData === null || profileData === void 0 ? void 0 : profileData.shadowing_hours) || (profileData === null || profileData === void 0 ? void 0 : profileData.shadowingHours) || 0);\n    const meetsPatientCare = patientCareHours >= (program.requirements.experience[\"Patient Care Hours\"] || 0);\n    const meetsShadowing = shadowingHours >= (program.requirements.experience[\"Shadowing Hours\"] || 0);\n\n    // --- Add Standardized Test Check ---\n    let meetsStandardizedTests = true; // Assume met unless a required test is missing\n    const testsRequiredString = ((_program$requirements = program.requirements.standardized_tests) === null || _program$requirements === void 0 ? void 0 : _program$requirements.tests_required) || \"\"; // Get the string like \"GRE, CASPER\"\n    const userProfile = profileData; // User profile data from state\n\n    // Normalize the required tests string for easier checking\n    const requiredTests = testsRequiredString.toLowerCase().split(/,|\\(|\\)/) // Split by comma or parentheses\n    .map(t => t.trim()).filter(t => t); // Remove empty strings\n\n    // Check GRE requirement\n    if (requiredTests.includes('gre')) {\n      if (!(userProfile !== null && userProfile !== void 0 && userProfile.gre_verbal_score) && !(userProfile !== null && userProfile !== void 0 && userProfile.gre_quantitative_score) && !(userProfile !== null && userProfile !== void 0 && userProfile.gre_analytical_writing_score)) {\n        meetsStandardizedTests = false; // Required but user has no scores entered\n      }\n    }\n\n    // Check CASPER requirement\n    if (requiredTests.includes('casper')) {\n      if (!(userProfile !== null && userProfile !== void 0 && userProfile.has_taken_casper)) {\n        meetsStandardizedTests = false; // Required but user hasn't checked the box\n      }\n    }\n\n    // Check PA-CAT requirement\n    if (requiredTests.includes('pa-cat') || requiredTests.includes('pacat')) {\n      // Check for variations\n      if (!(userProfile !== null && userProfile !== void 0 && userProfile.has_taken_pa_cat)) {\n        meetsStandardizedTests = false; // Required but user hasn't checked the box\n      }\n    }\n\n    // --- Update Eligibility Condition ---\n    // Note: We removed the specific meetsGRE check as it's now part of meetsStandardizedTests\n    if (meetsGPA && meetsScienceGPA && meetsPrereqs && meetsPatientCare && meetsShadowing && meetsStandardizedTests) {\n      return 'eligible';\n    }\n\n    // If any check failed, it's ineligible (assuming prerequisites are complete)\n    return 'ineligible';\n  };\n\n  // Cache program eligibility results to avoid recalculation\n  const programEligibilityCache = React.useMemo(() => {\n    if (!programs || !userTranscript) return {};\n    const cache = {};\n    programs.forEach(program => {\n      cache[program.name] = checkProgramEligibility(program);\n    });\n    return cache;\n  }, [programs, userTranscript, profileData]);\n  const programCounts = React.useMemo(() => {\n    if (!programs || !userTranscript) return {\n      eligible: 0,\n      ineligible: 0,\n      incomplete: 0\n    };\n    const categorizedPrograms = programs.reduce((acc, program) => {\n      const status = programEligibilityCache[program.name];\n      acc[status]++;\n      return acc;\n    }, {\n      eligible: 0,\n      ineligible: 0,\n      incomplete: 0\n    });\n    return categorizedPrograms;\n  }, [programEligibilityCache]);\n\n  // Enhanced requirement comparison with visual indicators\n  const renderRequirementComparison = (requirement, studentValue, program, prereqName) => {\n    let isMet = false;\n    let percentage = 0;\n    if (program && prereqName) {\n      if (prereqName === \"Patient Care Hours\" || prereqName === \"Shadowing Hours\") {\n        // For experience requirements\n        isMet = parseFloat(studentValue) >= parseFloat(requirement);\n        percentage = Math.min(parseFloat(studentValue) / parseFloat(requirement) * 100, 100);\n      } else {\n        var _userTranscript$prere5;\n        // For prerequisites, check the specific program's prerequisite data\n        const programPrereqs = userTranscript === null || userTranscript === void 0 ? void 0 : (_userTranscript$prere5 = userTranscript.prerequisites) === null || _userTranscript$prere5 === void 0 ? void 0 : _userTranscript$prere5[program.name];\n        if (programPrereqs && programPrereqs[prereqName]) {\n          const totalCredits = programPrereqs[prereqName].totalCredits || 0;\n          const requiredCreditsNum = parseFloat(requirement); // Attempt to parse requirement\n\n          // If requirement is not a number (e.g., \"4 Semesters\"), consider met if any courses match\n          if (isNaN(requiredCreditsNum)) {\n            isMet = programPrereqs[prereqName].courses && programPrereqs[prereqName].courses.length > 0;\n            percentage = isMet ? 100 : 0;\n          } else {\n            // Otherwise, perform the numeric comparison\n            isMet = totalCredits >= requiredCreditsNum;\n            percentage = Math.min(totalCredits / requiredCreditsNum * 100, 100);\n          }\n        }\n      }\n    } else {\n      // For non-prerequisite requirements (like GPA)\n      isMet = studentValue >= requirement;\n      percentage = Math.min(studentValue / requirement * 100, 100);\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `requirement-indicator ${isMet ? 'met' : 'not-met'}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"requirement-progress\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress-bar\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"progress-fill\",\n            style: {\n              width: `${percentage}%`\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1327,\n            columnNumber: 21\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1326,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"progress-text\",\n          children: [percentage.toFixed(0), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1332,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1325,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"requirement-status-icon\",\n        children: isMet ? '✅' : '❌'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1334,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1324,\n      columnNumber: 9\n    }, this);\n  };\n\n  // Enhanced GPA comparison component\n  const renderGPAComparison = (required, actual, label) => {\n    const isMet = actual >= required;\n    const percentage = Math.min(actual / required * 100, 100);\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"gpa-comparison-modern\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"gpa-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: label\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1349,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `gpa-status ${isMet ? 'met' : 'not-met'}`,\n          children: isMet ? '✅' : '❌'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1350,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1348,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"gpa-details\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"gpa-values\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"required\",\n            children: [\"Required: \", required.toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1356,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"actual\",\n            children: [\"Your GPA: \", actual.toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1357,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1355,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"gpa-progress\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"progress-bar\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"progress-fill\",\n              style: {\n                width: `${percentage}%`\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1361,\n              columnNumber: 25\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1360,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"progress-text\",\n            children: [percentage.toFixed(0), \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1366,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1359,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1354,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1347,\n      columnNumber: 9\n    }, this);\n  };\n\n  // Enhanced experience comparison component\n  const renderExperienceComparison = (required, actual, label) => {\n    const isMet = actual >= required;\n    const percentage = Math.min(actual / required * 100, 100);\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"experience-comparison-modern\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"experience-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"experience-icon\",\n          children: label.includes('Patient Care') ? '🏥' : label.includes('Shadowing') ? '👨‍⚕️' : '💼'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1381,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n          children: label\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1385,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `experience-status ${isMet ? 'met' : 'not-met'}`,\n          children: isMet ? '✅' : '❌'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1386,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1380,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"experience-details\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"experience-values\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"required\",\n            children: [\"Required: \", required, \" hrs\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1392,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"actual\",\n            children: [\"Your Hours: \", actual, \" hrs\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1393,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1391,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"experience-progress\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"progress-bar\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"progress-fill\",\n              style: {\n                width: `${percentage}%`\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1397,\n              columnNumber: 25\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1396,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"progress-text\",\n            children: [percentage.toFixed(0), \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1402,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1395,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1390,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1379,\n      columnNumber: 9\n    }, this);\n  };\n  const toggleSection = section => {\n    setExpandedSections(prev => ({\n      ...prev,\n      [section]: !prev[section]\n    }));\n  };\n  const toggleProgram = programName => {\n    setExpandedPrograms(prev => ({\n      ...prev,\n      [programName]: !prev[programName]\n    }));\n  };\n  const setActiveTab = (programName, tabName) => {\n    setActiveTabs(prev => ({\n      ...prev,\n      [programName]: tabName\n    }));\n  };\n\n  // Add GPA calculation helper function\n  const calculateOverallGPAs = prerequisites => {\n    let totalPoints = 0;\n    let totalCredits = 0;\n    let sciencePoints = 0;\n    let scienceCredits = 0;\n\n    // Iterate through all prerequisites and their courses\n    Object.values(prerequisites || {}).forEach(programPrereqs => {\n      Object.values(programPrereqs || {}).forEach(prereq => {\n        (prereq.courses || []).forEach(course => {\n          if (!['F', 'W', 'U'].includes(course.grade)) {\n            const credits = parseFloat(course.credits) || 0;\n            const gradePoints = gradeToPoints(course.grade);\n            if (!isNaN(credits) && !isNaN(gradePoints)) {\n              totalPoints += credits * gradePoints;\n              totalCredits += credits;\n              if (course.is_science) {\n                sciencePoints += credits * gradePoints;\n                scienceCredits += credits;\n              }\n            }\n          }\n        });\n      });\n    });\n    return {\n      cumulativeGPA: totalCredits > 0 ? (totalPoints / totalCredits).toFixed(2) : 'N/A',\n      scienceGPA: scienceCredits > 0 ? (sciencePoints / scienceCredits).toFixed(2) : 'N/A'\n    };\n  };\n\n  // Function to manually approve a program\n  const handleManualApproval = programName => {\n    // Add program to manually approved list if not already there\n    if (!manuallyApprovedPrograms.includes(programName)) {\n      const updatedApprovedPrograms = [...manuallyApprovedPrograms, programName];\n      setManuallyApprovedPrograms(updatedApprovedPrograms);\n\n      // Save the updated results to the database\n      const updatedResults = {\n        programs: programs,\n        userTranscript: userTranscript,\n        profileData: profileData,\n        manuallyApprovedPrograms: updatedApprovedPrograms\n      };\n      saveCurrentResults(updatedResults);\n    }\n  };\n\n  // Function to remove manual approval\n  const handleRemoveApproval = programName => {\n    // Remove program from manually approved list\n    const updatedApprovedPrograms = manuallyApprovedPrograms.filter(name => name !== programName);\n    setManuallyApprovedPrograms(updatedApprovedPrograms);\n\n    // Save the updated results to the database\n    const updatedResults = {\n      programs: programs,\n      userTranscript: userTranscript,\n      profileData: profileData,\n      manuallyApprovedPrograms: updatedApprovedPrograms\n    };\n    saveCurrentResults(updatedResults);\n  };\n\n  // Get unique states from programs for filtering\n  const getUniqueStates = () => {\n    if (!programs) return [];\n    const states = new Set();\n    programs.forEach(program => {\n      var _program$school;\n      if ((_program$school = program.school) !== null && _program$school !== void 0 && _program$school.state) {\n        states.add(program.school.state.toUpperCase());\n      }\n    });\n    return Array.from(states).sort();\n  };\n\n  // Filter and sort programs based on current settings\n  const getFilteredAndSortedPrograms = () => {\n    if (!programs || !userTranscript) return [];\n    let filteredPrograms = [...programs];\n\n    // Filter by search term\n    if (searchTerm) {\n      filteredPrograms = filteredPrograms.filter(program => {\n        var _program$school2, _program$school2$name, _program$school3, _program$school3$city, _program$school4, _program$school4$stat;\n        return program.name.toLowerCase().includes(searchTerm.toLowerCase()) || ((_program$school2 = program.school) === null || _program$school2 === void 0 ? void 0 : (_program$school2$name = _program$school2.name) === null || _program$school2$name === void 0 ? void 0 : _program$school2$name.toLowerCase().includes(searchTerm.toLowerCase())) || ((_program$school3 = program.school) === null || _program$school3 === void 0 ? void 0 : (_program$school3$city = _program$school3.city) === null || _program$school3$city === void 0 ? void 0 : _program$school3$city.toLowerCase().includes(searchTerm.toLowerCase())) || ((_program$school4 = program.school) === null || _program$school4 === void 0 ? void 0 : (_program$school4$stat = _program$school4.state) === null || _program$school4$stat === void 0 ? void 0 : _program$school4$stat.toLowerCase().includes(searchTerm.toLowerCase()));\n      });\n    }\n\n    // Filter by eligibility\n    if (filterBy !== 'all') {\n      filteredPrograms = filteredPrograms.filter(program => programEligibilityCache[program.name] === filterBy);\n    }\n\n    // Filter by region\n    if (regionFilter !== 'all') {\n      filteredPrograms = filteredPrograms.filter(program => {\n        var _program$school5;\n        const programState = (_program$school5 = program.school) === null || _program$school5 === void 0 ? void 0 : _program$school5.state;\n        if (!programState) return false;\n        const programRegion = getStateRegion(programState);\n        return programRegion === regionFilter;\n      });\n    }\n\n    // Filter by specific state\n    if (stateFilter !== 'all') {\n      filteredPrograms = filteredPrograms.filter(program => {\n        var _program$school6;\n        const programState = (_program$school6 = program.school) === null || _program$school6 === void 0 ? void 0 : _program$school6.state;\n        return (programState === null || programState === void 0 ? void 0 : programState.toUpperCase()) === stateFilter.toUpperCase();\n      });\n    }\n\n    // Sort programs\n    filteredPrograms.sort((a, b) => {\n      var _b$requirements, _b$requirements$gpa, _a$requirements, _a$requirements$gpa, _a$school, _b$school, _a$school2, _b$school2;\n      switch (sortBy) {\n        case 'name':\n          return a.name.localeCompare(b.name);\n        case 'gpa':\n          return (((_b$requirements = b.requirements) === null || _b$requirements === void 0 ? void 0 : (_b$requirements$gpa = _b$requirements.gpa) === null || _b$requirements$gpa === void 0 ? void 0 : _b$requirements$gpa.overall) || 0) - (((_a$requirements = a.requirements) === null || _a$requirements === void 0 ? void 0 : (_a$requirements$gpa = _a$requirements.gpa) === null || _a$requirements$gpa === void 0 ? void 0 : _a$requirements$gpa.overall) || 0);\n        case 'eligibility':\n          const eligibilityOrder = {\n            'eligible': 0,\n            'ineligible': 1,\n            'incomplete': 2\n          };\n          return eligibilityOrder[programEligibilityCache[a.name]] - eligibilityOrder[programEligibilityCache[b.name]];\n        case 'location':\n          const stateA = ((_a$school = a.school) === null || _a$school === void 0 ? void 0 : _a$school.state) || '';\n          const stateB = ((_b$school = b.school) === null || _b$school === void 0 ? void 0 : _b$school.state) || '';\n          if (stateA !== stateB) return stateA.localeCompare(stateB);\n          return (((_a$school2 = a.school) === null || _a$school2 === void 0 ? void 0 : _a$school2.city) || '').localeCompare(((_b$school2 = b.school) === null || _b$school2 === void 0 ? void 0 : _b$school2.city) || '');\n        default:\n          return 0;\n      }\n    });\n    return filteredPrograms;\n  };\n\n  // Clean program list item component matching Figma design\n  const ProgramListItem = ({\n    program,\n    isExpanded,\n    onToggle\n  }) => {\n    var _program$requirements2, _program$requirements3, _program$school7, _program$school8, _program$school9, _program$requirements4, _program$requirements5, _program$requirements6, _program$requirements7, _program$requirements8, _program$requirements9, _program$requirements10, _program$requirements11, _program$requirements12, _program$requirements13, _program$requirements14, _program$requirements15, _program$requirements16, _program$school10, _program$school11, _program$school12, _userTranscript$calcu, _program$requirements17, _program$requirements18, _program$requirements19, _program$requirements20, _userTranscript$scien, _program$requirements21, _program$requirements22, _program$requirements23, _program$requirements24, _program$requirements25;\n    const eligibility = programEligibilityCache[program.name];\n    const isManuallyApproved = manuallyApprovedPrograms.includes(program.name);\n\n    // Calculate prerequisites completion\n    const totalPrereqs = Object.keys(((_program$requirements2 = program.requirements) === null || _program$requirements2 === void 0 ? void 0 : _program$requirements2.prerequisites) || {}).length;\n    const completedPrereqs = Object.entries(((_program$requirements3 = program.requirements) === null || _program$requirements3 === void 0 ? void 0 : _program$requirements3.prerequisites) || {}).filter(([prereqName, req]) => {\n      var _userTranscript$prere6;\n      const programPrereqs = userTranscript === null || userTranscript === void 0 ? void 0 : (_userTranscript$prere6 = userTranscript.prerequisites) === null || _userTranscript$prere6 === void 0 ? void 0 : _userTranscript$prere6[program.name];\n      if (!(programPrereqs !== null && programPrereqs !== void 0 && programPrereqs[prereqName])) return false;\n      const totalCredits = programPrereqs[prereqName].totalCredits || 0;\n      const requiredCreditsNum = parseFloat(req.credits);\n      return !isNaN(requiredCreditsNum) ? totalCredits >= requiredCreditsNum : totalCredits > 0;\n    }).length;\n\n    // Calculate completion percentage\n    const completionPercentage = totalPrereqs > 0 ? Math.round(completedPrereqs / totalPrereqs * 100) : 100;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"program-list-item\",\n      onClick: onToggle,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"program-item-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"program-main-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"program-name\",\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: program.program_url,\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              onClick: e => e.stopPropagation(),\n              children: program.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1598,\n              columnNumber: 25\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1597,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"program-location\",\n            children: (_program$school7 = program.school) !== null && _program$school7 !== void 0 && _program$school7.city && (_program$school8 = program.school) !== null && _program$school8 !== void 0 && _program$school8.state ? `${program.school.city}, ${program.school.state}` : ((_program$school9 = program.school) === null || _program$school9 === void 0 ? void 0 : _program$school9.name) || 'Unknown Location'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1607,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1596,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"program-stats\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-label\",\n              children: \"Min GPA:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1617,\n              columnNumber: 25\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-value\",\n              children: ((_program$requirements4 = program.requirements) === null || _program$requirements4 === void 0 ? void 0 : (_program$requirements5 = _program$requirements4.gpa) === null || _program$requirements5 === void 0 ? void 0 : (_program$requirements6 = _program$requirements5.overall) === null || _program$requirements6 === void 0 ? void 0 : _program$requirements6.toFixed(1)) || 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1618,\n              columnNumber: 25\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1616,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-label\",\n              children: \"Prerequisites:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1622,\n              columnNumber: 25\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-value\",\n              children: [completedPrereqs, \"/\", totalPrereqs]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1623,\n              columnNumber: 25\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1621,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-group completion\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-label\",\n              children: \"Completion:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1627,\n              columnNumber: 25\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"completion-bar\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"completion-fill\",\n                style: {\n                  width: `${completionPercentage}%`\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1629,\n                columnNumber: 29\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1628,\n              columnNumber: 25\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1626,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1615,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"program-status\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `status-badge ${eligibility}`,\n            children: eligibility === 'eligible' ? 'Eligible' : eligibility === 'ineligible' ? 'Need Prerequisites' : 'Incomplete'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1638,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"expand-arrow\",\n            children: \"\\u25B8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1643,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1637,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1595,\n        columnNumber: 13\n      }, this), isExpanded && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"program-item-details\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"program-info-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-tabs\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `tab ${(activeTabs[program.name] || 'overview') === 'overview' ? 'active' : ''}`,\n              onClick: e => {\n                e.stopPropagation();\n                setActiveTab(program.name, 'overview');\n              },\n              children: \"Overview\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1652,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `tab ${activeTabs[program.name] === 'requirements' ? 'active' : ''}`,\n              onClick: e => {\n                e.stopPropagation();\n                setActiveTab(program.name, 'requirements');\n              },\n              children: \"Requirements\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1661,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1651,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tab-content\",\n            children: [(activeTabs[program.name] || 'overview') === 'overview' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"program-overview\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"quick-stats-grid\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"stat-card\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"stat-icon\",\n                    children: \"\\uD83C\\uDF93\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1679,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"stat-content\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"stat-label\",\n                      children: \"Minimum GPA\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1681,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"stat-value\",\n                      children: ((_program$requirements7 = program.requirements) === null || _program$requirements7 === void 0 ? void 0 : (_program$requirements8 = _program$requirements7.gpa) === null || _program$requirements8 === void 0 ? void 0 : (_program$requirements9 = _program$requirements8.overall) === null || _program$requirements9 === void 0 ? void 0 : _program$requirements9.toFixed(2)) || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1682,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1680,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1678,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"stat-card\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"stat-icon\",\n                    children: \"\\uD83D\\uDD2C\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1687,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"stat-content\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"stat-label\",\n                      children: \"Science GPA\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1689,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"stat-value\",\n                      children: ((_program$requirements10 = program.requirements) === null || _program$requirements10 === void 0 ? void 0 : (_program$requirements11 = _program$requirements10.gpa) === null || _program$requirements11 === void 0 ? void 0 : (_program$requirements12 = _program$requirements11.science) === null || _program$requirements12 === void 0 ? void 0 : _program$requirements12.toFixed(2)) || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1690,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1688,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1686,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"stat-card\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"stat-icon\",\n                    children: \"\\uD83C\\uDFE5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1695,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"stat-content\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"stat-label\",\n                      children: \"Patient Care Hours\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1697,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"stat-value\",\n                      children: ((_program$requirements13 = program.requirements) === null || _program$requirements13 === void 0 ? void 0 : (_program$requirements14 = _program$requirements13.experience) === null || _program$requirements14 === void 0 ? void 0 : _program$requirements14['Patient Care Hours']) || 0\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1698,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1696,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1694,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"stat-card\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"stat-icon\",\n                    children: \"\\uD83D\\uDC69\\u200D\\u2695\\uFE0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1703,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"stat-content\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"stat-label\",\n                      children: \"Shadowing Hours\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1705,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"stat-value\",\n                      children: ((_program$requirements15 = program.requirements) === null || _program$requirements15 === void 0 ? void 0 : (_program$requirements16 = _program$requirements15.experience) === null || _program$requirements16 === void 0 ? void 0 : _program$requirements16['Shadowing Hours']) || 0\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1706,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1704,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1702,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1677,\n                columnNumber: 37\n              }, this), program.program_data && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"enhanced-stats-section\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: \"\\uD83D\\uDCCA Program Statistics\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1714,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"enhanced-stats-grid\",\n                  children: [program.program_data.class_profile && Object.keys(program.program_data.class_profile).length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [program.program_data.class_profile.total_applications && program.program_data.class_profile.total_applications !== 'No information provided' && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"enhanced-stat-card\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"stat-icon\",\n                        children: \"\\uD83D\\uDCDD\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1721,\n                        columnNumber: 65\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"stat-content\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"stat-label\",\n                          children: \"Applications\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1723,\n                          columnNumber: 69\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"stat-value\",\n                          children: program.program_data.class_profile.total_applications\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1724,\n                          columnNumber: 69\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1722,\n                        columnNumber: 65\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1720,\n                      columnNumber: 61\n                    }, this), program.program_data.class_profile.total_matriculants && program.program_data.class_profile.total_matriculants !== 'No information provided' && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"enhanced-stat-card\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"stat-icon\",\n                        children: \"\\uD83C\\uDF93\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1731,\n                        columnNumber: 65\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"stat-content\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"stat-label\",\n                          children: \"Matriculants\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1733,\n                          columnNumber: 69\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"stat-value\",\n                          children: program.program_data.class_profile.total_matriculants\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1734,\n                          columnNumber: 69\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1732,\n                        columnNumber: 65\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1730,\n                      columnNumber: 61\n                    }, this), program.program_data.class_profile.average_overall_gpa && program.program_data.class_profile.average_overall_gpa !== 'No information provided' && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"enhanced-stat-card\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"stat-icon\",\n                        children: \"\\uD83D\\uDCC8\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1741,\n                        columnNumber: 65\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"stat-content\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"stat-label\",\n                          children: \"Avg Overall GPA\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1743,\n                          columnNumber: 69\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"stat-value\",\n                          children: program.program_data.class_profile.average_overall_gpa\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1744,\n                          columnNumber: 69\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1742,\n                        columnNumber: 65\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1740,\n                      columnNumber: 61\n                    }, this), program.program_data.class_profile.average_science_gpa && program.program_data.class_profile.average_science_gpa !== 'No information provided' && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"enhanced-stat-card\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"stat-icon\",\n                        children: \"\\uD83D\\uDD2C\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1751,\n                        columnNumber: 65\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"stat-content\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"stat-label\",\n                          children: \"Avg Science GPA\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1753,\n                          columnNumber: 69\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"stat-value\",\n                          children: program.program_data.class_profile.average_science_gpa\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1754,\n                          columnNumber: 69\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1752,\n                        columnNumber: 65\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1750,\n                      columnNumber: 61\n                    }, this)]\n                  }, void 0, true), program.program_data.pance_pass_rates && Object.keys(program.program_data.pance_pass_rates).length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [program.program_data.pance_pass_rates.program_pass_rate && program.program_data.pance_pass_rates.program_pass_rate !== 'No information provided' && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"enhanced-stat-card pance-stat\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"stat-icon\",\n                        children: \"\\uD83C\\uDFC6\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1766,\n                        columnNumber: 65\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"stat-content\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"stat-label\",\n                          children: \"PANCE Pass Rate\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1768,\n                          columnNumber: 69\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"stat-value\",\n                          children: program.program_data.pance_pass_rates.program_pass_rate\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1769,\n                          columnNumber: 69\n                        }, this), program.program_data.pance_pass_rates.national_pass_rate && program.program_data.pance_pass_rates.national_pass_rate !== 'No information provided' && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"stat-subtext\",\n                          children: [\"National: \", program.program_data.pance_pass_rates.national_pass_rate]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1771,\n                          columnNumber: 73\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1767,\n                        columnNumber: 65\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1765,\n                      columnNumber: 61\n                    }, this), program.program_data.pance_pass_rates.candidates_took_pance && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"enhanced-stat-card\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"stat-icon\",\n                        children: \"\\uD83D\\uDC65\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1779,\n                        columnNumber: 65\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"stat-content\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"stat-label\",\n                          children: \"PANCE Candidates\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1781,\n                          columnNumber: 69\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"stat-value\",\n                          children: program.program_data.pance_pass_rates.candidates_took_pance\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1782,\n                          columnNumber: 69\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1780,\n                        columnNumber: 65\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1778,\n                      columnNumber: 61\n                    }, this)]\n                  }, void 0, true), program.program_data.attrition && Object.keys(program.program_data.attrition).length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [program.program_data.attrition.graduation_rate && program.program_data.attrition.graduation_rate !== 'No information provided' && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"enhanced-stat-card\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"stat-icon\",\n                        children: \"\\uD83C\\uDFAF\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1794,\n                        columnNumber: 65\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"stat-content\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"stat-label\",\n                          children: \"Graduation Rate\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1796,\n                          columnNumber: 69\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"stat-value\",\n                          children: program.program_data.attrition.graduation_rate\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1797,\n                          columnNumber: 69\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1795,\n                        columnNumber: 65\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1793,\n                      columnNumber: 61\n                    }, this), program.program_data.attrition.entering_class_size && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"enhanced-stat-card\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"stat-icon\",\n                        children: \"\\uD83D\\uDC68\\u200D\\uD83C\\uDF93\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1804,\n                        columnNumber: 65\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"stat-content\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"stat-label\",\n                          children: \"Class Size\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1806,\n                          columnNumber: 69\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"stat-value\",\n                          children: program.program_data.attrition.entering_class_size\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1807,\n                          columnNumber: 69\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1805,\n                        columnNumber: 65\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1803,\n                      columnNumber: 61\n                    }, this)]\n                  }, void 0, true), program.program_data.tuition_deposits && program.program_data.tuition_deposits.tuition && program.program_data.tuition_deposits.tuition !== 'No information provided' && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"enhanced-stat-card tuition-stat\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"stat-icon\",\n                      children: \"\\uD83D\\uDCB0\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1817,\n                      columnNumber: 57\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"stat-content\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"stat-label\",\n                        children: \"Tuition\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1819,\n                        columnNumber: 61\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"stat-value\",\n                        children: program.program_data.tuition_deposits.tuition\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1820,\n                        columnNumber: 61\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1818,\n                      columnNumber: 57\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1816,\n                    columnNumber: 53\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1715,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1713,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"program-details-grid\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-section\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"\\uD83D\\uDCCD Program Information\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1831,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"detail-items\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"detail-item\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"detail-label\",\n                        children: \"School:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1834,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"detail-value\",\n                        children: ((_program$school10 = program.school) === null || _program$school10 === void 0 ? void 0 : _program$school10.name) || 'N/A'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1835,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1833,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"detail-item\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"detail-label\",\n                        children: \"Location:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1838,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"detail-value\",\n                        children: (_program$school11 = program.school) !== null && _program$school11 !== void 0 && _program$school11.city && (_program$school12 = program.school) !== null && _program$school12 !== void 0 && _program$school12.state ? `${program.school.city}, ${program.school.state}` : 'N/A'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1839,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1837,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"detail-item\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"detail-label\",\n                        children: \"Program Website:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1847,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                        href: program.program_url,\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        className: \"detail-link\",\n                        children: \"View Program Details\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1848,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1846,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1832,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1830,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-section\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"\\uD83C\\uDFC6 Your Eligibility\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1861,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"eligibility-overview\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"eligibility-item\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"eligibility-label\",\n                        children: \"Overall GPA:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1864,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"eligibility-status\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          children: (userTranscript === null || userTranscript === void 0 ? void 0 : (_userTranscript$calcu = userTranscript.calculated_gpa) === null || _userTranscript$calcu === void 0 ? void 0 : _userTranscript$calcu.toFixed(2)) || 'N/A'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1866,\n                          columnNumber: 57\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: `status ${(userTranscript === null || userTranscript === void 0 ? void 0 : userTranscript.calculated_gpa) >= ((_program$requirements17 = program.requirements) === null || _program$requirements17 === void 0 ? void 0 : (_program$requirements18 = _program$requirements17.gpa) === null || _program$requirements18 === void 0 ? void 0 : _program$requirements18.overall) ? 'met' : 'not-met'}`,\n                          children: (userTranscript === null || userTranscript === void 0 ? void 0 : userTranscript.calculated_gpa) >= ((_program$requirements19 = program.requirements) === null || _program$requirements19 === void 0 ? void 0 : (_program$requirements20 = _program$requirements19.gpa) === null || _program$requirements20 === void 0 ? void 0 : _program$requirements20.overall) ? '✓' : '✗'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1867,\n                          columnNumber: 57\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1865,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1863,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"eligibility-item\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"eligibility-label\",\n                        children: \"Science GPA:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1873,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"eligibility-status\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          children: (userTranscript === null || userTranscript === void 0 ? void 0 : (_userTranscript$scien = userTranscript.science_gpa) === null || _userTranscript$scien === void 0 ? void 0 : _userTranscript$scien.toFixed(2)) || 'N/A'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1875,\n                          columnNumber: 57\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: `status ${(userTranscript === null || userTranscript === void 0 ? void 0 : userTranscript.science_gpa) >= ((_program$requirements21 = program.requirements) === null || _program$requirements21 === void 0 ? void 0 : (_program$requirements22 = _program$requirements21.gpa) === null || _program$requirements22 === void 0 ? void 0 : _program$requirements22.science) ? 'met' : 'not-met'}`,\n                          children: (userTranscript === null || userTranscript === void 0 ? void 0 : userTranscript.science_gpa) >= ((_program$requirements23 = program.requirements) === null || _program$requirements23 === void 0 ? void 0 : (_program$requirements24 = _program$requirements23.gpa) === null || _program$requirements24 === void 0 ? void 0 : _program$requirements24.science) ? '✓' : '✗'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1876,\n                          columnNumber: 57\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1874,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1872,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"eligibility-item\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"eligibility-label\",\n                        children: \"Prerequisites:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1882,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"eligibility-status\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          children: [completedPrereqs, \"/\", totalPrereqs, \" completed\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1884,\n                          columnNumber: 57\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: `status ${completedPrereqs === totalPrereqs ? 'met' : 'not-met'}`,\n                          children: completedPrereqs === totalPrereqs ? '✓' : '✗'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1885,\n                          columnNumber: 57\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1883,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1881,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1862,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1860,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1829,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1675,\n              columnNumber: 33\n            }, this), activeTabs[program.name] === 'requirements' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"requirements-tab\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"prerequisites-section\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: \"\\uD83D\\uDCDA Prerequisites Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1901,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"prerequisites-list\",\n                  children: Object.entries(((_program$requirements25 = program.requirements) === null || _program$requirements25 === void 0 ? void 0 : _program$requirements25.prerequisites) || {}).map(([course, req]) => {\n                    var _userTranscript$prere7;\n                    const programPrereqs = userTranscript === null || userTranscript === void 0 ? void 0 : (_userTranscript$prere7 = userTranscript.prerequisites) === null || _userTranscript$prere7 === void 0 ? void 0 : _userTranscript$prere7[program.name];\n                    const prereqData = programPrereqs === null || programPrereqs === void 0 ? void 0 : programPrereqs[course];\n                    const totalCredits = (prereqData === null || prereqData === void 0 ? void 0 : prereqData.totalCredits) || 0;\n                    const requiredCreditsNum = parseFloat(req.credits);\n                    const isMet = !isNaN(requiredCreditsNum) ? totalCredits >= requiredCreditsNum : totalCredits > 0;\n                    const matchedCourses = (prereqData === null || prereqData === void 0 ? void 0 : prereqData.courses) || [];\n\n                    // Sort courses by grade (best grades first) and separate used vs unused\n                    const sortedCourses = [...matchedCourses].sort((a, b) => {\n                      const gradeA = gradeToPoints(a.grade);\n                      const gradeB = gradeToPoints(b.grade);\n                      if (gradeB !== gradeA) return gradeB - gradeA;\n                      return parseInt(b.year) - parseInt(a.year);\n                    });\n\n                    // Determine which courses are actually being used in calculation\n                    const usedCourses = [];\n                    const unusedCourses = [];\n                    let creditsUsed = 0;\n                    const requiredCredits = parseFloat(req.credits) || 0;\n                    sortedCourses.forEach(course => {\n                      const courseCredits = parseFloat(course.credits) || 0;\n                      if (creditsUsed < requiredCredits) {\n                        usedCourses.push(course);\n                        creditsUsed += courseCredits;\n                      } else {\n                        unusedCourses.push(course);\n                      }\n                    });\n                    return /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"prerequisite-item-detailed\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"prerequisite-header\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"prerequisite-info\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"course-name\",\n                            children: [course, \" (\", req.credits, \" credits)\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1939,\n                            columnNumber: 65\n                          }, this), req.lab_required && /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"lab-required\",\n                            children: \"Lab Required\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1940,\n                            columnNumber: 86\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1938,\n                          columnNumber: 61\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: `status ${isMet ? 'met' : 'not-met'}`,\n                          children: isMet ? '✓' : '✗'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1942,\n                          columnNumber: 61\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1937,\n                        columnNumber: 57\n                      }, this), matchedCourses.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"matched-courses\",\n                        children: [usedCourses.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"used-courses-section\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"courses-header used\",\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              children: \"\\u2713 Used for calculation:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1952,\n                              columnNumber: 77\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"credits-summary\",\n                              children: [creditsUsed.toFixed(1), \" / \", req.credits, \" credits\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1953,\n                              columnNumber: 77\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1951,\n                            columnNumber: 73\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"course-cards\",\n                            children: usedCourses.map((matchedCourse, index) => {\n                              var _matchedCourse$grade;\n                              return /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"course-card used\",\n                                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"course-details\",\n                                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                    className: \"course-code-name\",\n                                    children: [matchedCourse.code, \" - \", matchedCourse.name]\n                                  }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 1959,\n                                    columnNumber: 89\n                                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                    className: \"course-meta\",\n                                    children: [matchedCourse.institution, \" \\u2022 \", matchedCourse.term, \" \", matchedCourse.year]\n                                  }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 1962,\n                                    columnNumber: 89\n                                  }, this)]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1958,\n                                  columnNumber: 85\n                                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"course-stats\",\n                                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                    className: \"course-credits\",\n                                    children: [matchedCourse.credits, \" credits\"]\n                                  }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 1967,\n                                    columnNumber: 89\n                                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                    className: `course-grade grade-${(_matchedCourse$grade = matchedCourse.grade) === null || _matchedCourse$grade === void 0 ? void 0 : _matchedCourse$grade.replace(/[+\\-]/, '')}`,\n                                    children: matchedCourse.grade\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 1968,\n                                    columnNumber: 89\n                                  }, this)]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1966,\n                                  columnNumber: 85\n                                }, this)]\n                              }, index, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1957,\n                                columnNumber: 81\n                              }, this);\n                            })\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1955,\n                            columnNumber: 73\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1950,\n                          columnNumber: 69\n                        }, this), unusedCourses.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"unused-courses-section\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"courses-header unused\",\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              children: \"Other matching courses (not used):\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1982,\n                              columnNumber: 77\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"courses-count\",\n                              children: [unusedCourses.length, \" course\", unusedCourses.length !== 1 ? 's' : '']\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1983,\n                              columnNumber: 77\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1981,\n                            columnNumber: 73\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"course-cards\",\n                            children: unusedCourses.map((matchedCourse, index) => {\n                              var _matchedCourse$grade2;\n                              return /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"course-card unused\",\n                                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"course-details\",\n                                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                    className: \"course-code-name\",\n                                    children: [matchedCourse.code, \" - \", matchedCourse.name]\n                                  }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 1989,\n                                    columnNumber: 89\n                                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                    className: \"course-meta\",\n                                    children: [matchedCourse.institution, \" \\u2022 \", matchedCourse.term, \" \", matchedCourse.year]\n                                  }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 1992,\n                                    columnNumber: 89\n                                  }, this)]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1988,\n                                  columnNumber: 85\n                                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"course-stats\",\n                                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                    className: \"course-credits\",\n                                    children: [matchedCourse.credits, \" credits\"]\n                                  }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 1997,\n                                    columnNumber: 89\n                                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                    className: `course-grade grade-${(_matchedCourse$grade2 = matchedCourse.grade) === null || _matchedCourse$grade2 === void 0 ? void 0 : _matchedCourse$grade2.replace(/[+\\-]/, '')}`,\n                                    children: matchedCourse.grade\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 1998,\n                                    columnNumber: 89\n                                  }, this)]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1996,\n                                  columnNumber: 85\n                                }, this)]\n                              }, index, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1987,\n                                columnNumber: 81\n                              }, this);\n                            })\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1985,\n                            columnNumber: 73\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1980,\n                          columnNumber: 69\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1947,\n                        columnNumber: 61\n                      }, this), matchedCourses.length === 0 && !isMet && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"no-courses\",\n                        children: /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"no-courses-text\",\n                          children: \"No matching courses found\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2011,\n                          columnNumber: 65\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2010,\n                        columnNumber: 61\n                      }, this)]\n                    }, course, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1936,\n                      columnNumber: 53\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1902,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1900,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1898,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1672,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1650,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1648,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1594,\n      columnNumber: 9\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"pamatch-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"pamatch-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-left\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-title\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"header-icon\",\n            children: \"\\uD83D\\uDCD6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2037,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            children: \"PA Program Explorer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2038,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2036,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2035,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-right\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate('/home'),\n          className: \"back-home-btn\",\n          children: \"Back to Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2042,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2041,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2034,\n      columnNumber: 9\n    }, this), usingSavedResults && savedResultsTimestamp && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"saved-results-banner\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: [\"Using saved results from \", new Date(savedResultsTimestamp).toLocaleDateString(), \" at \", new Date(savedResultsTimestamp).toLocaleTimeString()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2054,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleRecalculate,\n        className: \"recalculate-btn\",\n        disabled: isLoading || savingResults,\n        children: isLoading ? 'Calculating...' : 'Recalculate'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2057,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2053,\n      columnNumber: 13\n    }, this), !isLoading && userTranscript && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-and-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-bar\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"search-icon\",\n            children: \"\\uD83D\\uDD0D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2072,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search programs or schools...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            className: \"search-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2073,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2071,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-controls\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-dropdown\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"filter-icon\",\n              children: \"\\u26A1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2084,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: filterBy,\n              onChange: e => setFilterBy(e.target.value),\n              className: \"filter-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"all\",\n                children: \"All Programs\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2090,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"eligible\",\n                children: \"Eligible Only\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2091,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"ineligible\",\n                children: \"Need Prerequisites\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2092,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"incomplete\",\n                children: \"Incomplete Info\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2093,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2085,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2083,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"region-dropdown\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"location-icon\",\n              children: \"\\uD83D\\uDCCD\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2098,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: regionFilter,\n              onChange: e => {\n                setRegionFilter(e.target.value);\n                if (e.target.value !== 'all') setStateFilter('all'); // Reset state filter when region is selected\n              },\n              className: \"region-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"all\",\n                children: \"All Regions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2107,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"optgroup\", {\n                label: \"Census Regions\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Northeast\",\n                  children: \"Northeast (9 states)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2109,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Midwest\",\n                  children: \"Midwest (12 states)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2110,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"South\",\n                  children: \"South (17 states)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2111,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"West\",\n                  children: \"West (13 states)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2112,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2108,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"optgroup\", {\n                label: \"Census Divisions\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"New England\",\n                  children: \"New England (6 states)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2115,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Mid-Atlantic\",\n                  children: \"Mid-Atlantic (3 states)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2116,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"East North Central\",\n                  children: \"East North Central (5 states)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2117,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"West North Central\",\n                  children: \"West North Central (7 states)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2118,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"South Atlantic\",\n                  children: \"South Atlantic (9 states)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2119,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"East South Central\",\n                  children: \"East South Central (4 states)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2120,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"West South Central\",\n                  children: \"West South Central (4 states)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2121,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Mountain\",\n                  children: \"Mountain (8 states)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2122,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Pacific\",\n                  children: \"Pacific (5 states)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2123,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2114,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2099,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2097,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"state-dropdown\",\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              value: stateFilter,\n              onChange: e => {\n                setStateFilter(e.target.value);\n                if (e.target.value !== 'all') setRegionFilter('all'); // Reset region filter when state is selected\n              },\n              className: \"state-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"all\",\n                children: \"All States\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2137,\n                columnNumber: 33\n              }, this), getUniqueStates().map(state => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: state,\n                children: state\n              }, state, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2139,\n                columnNumber: 37\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2129,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2128,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"sort-dropdown\",\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              value: sortBy,\n              onChange: e => setSortBy(e.target.value),\n              className: \"sort-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"name\",\n                children: \"Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2150,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"gpa\",\n                children: \"GPA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2151,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"eligibility\",\n                children: \"Eligibility\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2152,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"location\",\n                children: \"Location\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2153,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2145,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2144,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"view-toggle\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: `view-btn ${viewMode === 'accordion' ? 'active' : ''}`,\n              onClick: () => setViewMode('accordion'),\n              title: \"List View\",\n              children: \"\\u2630\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2158,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: `view-btn ${viewMode === 'grid' ? 'active' : ''}`,\n              onClick: () => setViewMode('grid'),\n              title: \"Grid View\",\n              children: \"\\u229E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2165,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2157,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2082,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2070,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"program-summary-cards\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `summary-card total ${filterBy === 'all' ? 'active' : ''}`,\n          onClick: () => setFilterBy('all'),\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-title\",\n                children: \"Total Programs\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2184,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-number\",\n                children: programs.length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2185,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2183,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-icon\",\n              children: \"\\uD83D\\uDCD6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2187,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2182,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2178,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `summary-card eligible ${filterBy === 'eligible' ? 'active' : ''}`,\n          onClick: () => setFilterBy('eligible'),\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-title\",\n                children: \"Eligible\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2197,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-number\",\n                children: programCounts.eligible\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2198,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2196,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-icon\",\n              children: \"\\u2713\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2200,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2195,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2191,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `summary-card need-prerequisites ${filterBy === 'ineligible' ? 'active' : ''}`,\n          onClick: () => setFilterBy('ineligible'),\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-title\",\n                children: \"Need Prerequisites\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2210,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-number\",\n                children: programCounts.ineligible\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2211,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2209,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-icon\",\n              children: \"\\u26A0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2213,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2208,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2204,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `summary-card incomplete ${filterBy === 'incomplete' ? 'active' : ''}`,\n          onClick: () => setFilterBy('incomplete'),\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-title\",\n                children: \"Incomplete\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2223,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-number\",\n                children: programCounts.incomplete\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2224,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2222,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-icon\",\n              children: \"\\uD83D\\uDD50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2226,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2221,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2217,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2177,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"results-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"results-count\",\n          children: [\"Showing \", getFilteredAndSortedPrograms().length, \" of \", programs.length, \" programs\", (filterBy !== 'all' || regionFilter !== 'all' || stateFilter !== 'all') && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"filter-indicator\",\n            children: [' ', \"\\u2022 Filtered by: \", [filterBy !== 'all' ? filterBy === 'eligible' ? 'Eligible' : filterBy === 'ineligible' ? 'Need Prerequisites' : filterBy === 'incomplete' ? 'Incomplete' : '' : null, regionFilter !== 'all' ? `${regionFilter} Region` : null, stateFilter !== 'all' ? `${stateFilter} State` : null].filter(Boolean).join(', ')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2236,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2233,\n          columnNumber: 21\n        }, this), (filterBy !== 'all' || regionFilter !== 'all' || stateFilter !== 'all') && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"clear-filter-btn\",\n          onClick: () => {\n            setFilterBy('all');\n            setRegionFilter('all');\n            setStateFilter('all');\n          },\n          children: \"Clear All Filters\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2250,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2232,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"academic-summary\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"summary-section-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"section-icon\",\n          children: \"\\uD83D\\uDC64\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2267,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Academic Summary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2268,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2266,\n        columnNumber: 13\n      }, this), isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"gpa-cards-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"gpa-card cumulative-gpa loading\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"card-label\",\n              children: \"Cumulative GPA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2274,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"card-icon\",\n              children: \"\\uD83D\\uDCCA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2275,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2273,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"gpa-value skeleton-loader\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2277,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2272,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"gpa-card science-gpa loading\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"card-label\",\n              children: \"Science GPA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2281,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"card-icon\",\n              children: \"\\uD83D\\uDCCA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2282,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2280,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"gpa-value skeleton-loader\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2284,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2279,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2271,\n        columnNumber: 17\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"gpa-cards-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"gpa-card cumulative-gpa\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"card-label\",\n              children: \"Cumulative GPA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2291,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"card-icon\",\n              children: \"\\uD83D\\uDCCA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2292,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2290,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"gpa-value\",\n            children: (userTranscript === null || userTranscript === void 0 ? void 0 : (_userTranscript$calcu2 = userTranscript.calculated_gpa) === null || _userTranscript$calcu2 === void 0 ? void 0 : _userTranscript$calcu2.toFixed(2)) || 'N/A'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2294,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2289,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"gpa-card science-gpa\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"card-label\",\n              children: \"Science GPA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2298,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"card-icon\",\n              children: \"\\uD83D\\uDCCA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2299,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2297,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"gpa-value\",\n            children: (userTranscript === null || userTranscript === void 0 ? void 0 : (_userTranscript$scien2 = userTranscript.science_gpa) === null || _userTranscript$scien2 === void 0 ? void 0 : _userTranscript$scien2.toFixed(2)) || 'N/A'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2301,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2296,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2288,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2265,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"results-container\",\n      children: [isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"programs-comparison\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"eligible-programs\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Eligible Programs\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2312,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2311,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(ProgramSkeleton, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2314,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(ProgramSkeleton, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2315,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2310,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"ineligible-programs\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Programs Requiring Additional Prerequisites\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2319,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2318,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(ProgramSkeleton, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2321,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(ProgramSkeleton, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2322,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2317,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"incomplete-programs\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Incomplete/Unknown Eligibility\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2326,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2325,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(ProgramSkeleton, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2328,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2324,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2309,\n        columnNumber: 17\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-message\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2334,\n        columnNumber: 17\n      }, this), userTranscript && !isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"programs-list\",\n        children: getFilteredAndSortedPrograms().map((program, index) => /*#__PURE__*/_jsxDEV(ProgramListItem, {\n          program: program,\n          isExpanded: expandedPrograms[program.name],\n          onToggle: () => toggleProgram(program.name)\n        }, `${program.name}-${index}`, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2342,\n          columnNumber: 25\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2340,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(CourseSelectionModal, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2352,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2307,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 2033,\n    columnNumber: 5\n  }, this);\n};\n_s(PAMatch, \"c4QZfl6zFL7OZOhN3FjnO6Zy/zM=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = PAMatch;\nexport default PAMatch;\nvar _c;\n$RefreshReg$(_c, \"PAMatch\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "axios", "useAuth", "useNavigate", "ProgramSkeleton", "normalizeProfileData", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "US_REGIONS", "getStateRegion", "state", "stateCode", "toUpperCase", "region", "states", "Object", "entries", "includes", "PAMatch", "_s", "_userTranscript$calcu2", "_userTranscript$scien2", "console", "log", "token", "navigate", "userTranscript", "setUserTranscript", "profileData", "setProfileData", "isLoading", "setIsLoading", "error", "setError", "programs", "setPrograms", "selectedPrereq", "setSelectedPrereq", "showCourseModal", "setShowCourseModal", "availableCourses", "setAvailableCourses", "expandedSections", "setExpandedSections", "eligible", "ineligible", "incomplete", "expandedPrograms", "setExpandedPrograms", "activeTabs", "setActiveTabs", "usingSavedResults", "setUsingSavedResults", "savedResultsTimestamp", "setSavedResultsTimestamp", "savingResults", "setSavingResults", "manuallyApprovedPrograms", "setManuallyApprovedPrograms", "viewMode", "setViewMode", "searchTerm", "setSearchTerm", "sortBy", "setSortBy", "filterBy", "setFilterBy", "stateFilter", "setStateFilter", "regionFilter", "setRegionFilter", "loadData", "checkForSavedResults", "err", "fetchAndCalculateResults", "headers", "Authorization", "response", "get", "process", "env", "REACT_APP_API_URL", "data", "status", "timestamp", "savedData", "matching_results", "Error", "programsResponse", "fetchedPrograms", "savedPrereqsResponse", "profileResponse", "normalizedProfileData", "transcriptResponse", "transcripts", "caspaCalculatedGPA", "caspaScienceGPA", "totalPoints", "totalCredits", "sciencePoints", "scienceCredits", "for<PERSON>ach", "transcript", "_transcript$transcrip", "semesters", "transcript_data", "semester", "courses", "course", "grade", "credits", "parseFloat", "gradePoints", "gradeToPoints", "isNaN", "is_science", "calculatedGPA", "scienceGPA", "allTranscriptCourses", "seenCourses", "Set", "_transcript$transcrip2", "institution", "term", "year", "normalizedInstitution", "toLowerCase", "replace", "trim", "courseMapKey", "code", "currentCourseData", "values", "savedPrereqs", "prerequisites", "savedPrereqsLookup", "reduce", "acc", "key", "value", "Array", "isArray", "programPrereqs", "program", "name", "requirements", "programMatches", "prereqName", "prereqData", "savedPrereq", "coursesToUse", "isSaved", "foundSaved", "length", "combinedAPCategory", "savedCombined", "filter", "courseMatchesPrereq", "sum", "timeframe", "lab_required", "updatedTranscript", "calculated_gpa", "science_gpa", "saveCurrentResults", "_err$response", "_err$response2", "_err$response3", "_err$response4", "_err$response4$data", "dataToSave", "matchingResults", "post", "handleRecalculate", "finally", "courseName", "courseCode", "_courseCode$match", "_courseCode$match2", "_courseCode$match3", "_courseCode$match4", "_courseCode$match5", "chemistryPrefixes", "biologyPrefixes", "psychologyPrefixes", "englishPrefixes", "statisticsPrefixes", "sociologyPrefixes", "anthropologyPrefixes", "isMatch", "match", "seq_isGenChem", "some", "prefix", "startsWith", "num", "parseInt", "seq_isOrgChem", "seq_isBiochem", "isAnatomy", "isPhysiology", "is<PERSON><PERSON><PERSON>", "isGenChem", "isOrgChem", "isBiochem", "isPsych", "isStats", "isMedTerm", "isBehavioralScience", "isSociology", "isSocialScience", "isEnglish", "isGeneralBio", "isMath", "isSociologyRelated", "isDefaultMatch", "mapPrerequisites", "prereqMap", "matchingCourses", "isAnatomyOrPhysiology", "sortedCourses", "sort", "a", "b", "gradeA", "gradeB", "requiredCredits", "selectedCourses", "courseCredits", "push", "all_matching_courses", "openCourseSelection", "allCourses", "currentPrereqCourses", "mappedCourseCodes", "map", "validCourses", "all_courses", "has", "addCourseToPrereq", "processMultipleTranscripts", "institutions", "prerequisite_gpa", "calculatePrereqGPA", "CourseSelectionModal", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "index", "onClick", "renderPrerequisiteDetails", "programName", "_userTranscript$prere", "_userTranscript$prere2", "programPrereqData", "matchedCourses", "sortedMatchedCourses", "coursesUsedInGPA", "creditsUsed", "add", "handleCourseRemove", "_updatedTranscript$pr", "_updatedTranscript$pr2", "total", "courseIndex", "toFixed", "checkPrerequisites", "_userTranscript$prere3", "every", "req", "combinedAPCourses", "combinedCredits", "meetsLabRequirement", "meetsTimeframe", "courseYear", "currentYear", "Date", "getFullYear", "checkPatientCareHours", "requiredHours", "experience", "userHours", "direct_patient_care_hours", "directPatientCareHours", "checkShadowingHours", "shadowing_hours", "shadowingHours", "getUserExperienceHours", "experienceType", "allCoursesWithSource", "flatMap", "warn", "_transcript$transcrip3", "_transcript$transcrip4", "_transcript$transcrip5", "academic_summary", "checkProgramEligibility", "_userTranscript$prere4", "_program$requirements", "hasPrereqs", "keys", "meetsGPA", "gpa", "overall", "meetsScienceGPA", "science", "meetsPrereqs", "requiredCreditsNum", "patientCareHours", "meetsPatientCare", "meetsShadowing", "meetsStandardizedTests", "testsRequiredString", "standardized_tests", "tests_required", "userProfile", "requiredTests", "split", "t", "gre_verbal_score", "gre_quantitative_score", "gre_analytical_writing_score", "has_taken_casper", "has_taken_pa_cat", "programEligibilityCache", "useMemo", "cache", "programCounts", "categorizedPrograms", "renderRequirementComparison", "requirement", "studentValue", "isMet", "percentage", "Math", "min", "_userTranscript$prere5", "style", "width", "renderGPAComparison", "required", "actual", "label", "renderExperienceComparison", "toggleSection", "section", "prev", "toggleProgram", "setActiveTab", "tabName", "calculateOverallGPAs", "prereq", "cumulativeGPA", "handleManualApproval", "updatedApprovedPrograms", "updatedResults", "handleRemoveApproval", "getUniqueStates", "_program$school", "school", "from", "getFilteredAndSortedPrograms", "filteredPrograms", "_program$school2", "_program$school2$name", "_program$school3", "_program$school3$city", "_program$school4", "_program$school4$stat", "city", "_program$school5", "programState", "programRegion", "_program$school6", "_b$requirements", "_b$requirements$gpa", "_a$requirements", "_a$requirements$gpa", "_a$school", "_b$school", "_a$school2", "_b$school2", "localeCompare", "eligibilityOrder", "stateA", "stateB", "ProgramListItem", "isExpanded", "onToggle", "_program$requirements2", "_program$requirements3", "_program$school7", "_program$school8", "_program$school9", "_program$requirements4", "_program$requirements5", "_program$requirements6", "_program$requirements7", "_program$requirements8", "_program$requirements9", "_program$requirements10", "_program$requirements11", "_program$requirements12", "_program$requirements13", "_program$requirements14", "_program$requirements15", "_program$requirements16", "_program$school10", "_program$school11", "_program$school12", "_userTranscript$calcu", "_program$requirements17", "_program$requirements18", "_program$requirements19", "_program$requirements20", "_userTranscript$scien", "_program$requirements21", "_program$requirements22", "_program$requirements23", "_program$requirements24", "_program$requirements25", "eligibility", "isManuallyApproved", "totalPrereqs", "completedPrereqs", "_userTranscript$prere6", "completionPercentage", "round", "href", "program_url", "target", "rel", "e", "stopPropagation", "program_data", "class_profile", "total_applications", "total_matriculants", "average_overall_gpa", "average_science_gpa", "pance_pass_rates", "program_pass_rate", "national_pass_rate", "candidates_took_pance", "attrition", "graduation_rate", "entering_class_size", "tuition_deposits", "tuition", "_userTranscript$prere7", "usedCourses", "unusedCourses", "matchedCourse", "_matchedCourse$grade", "_matchedCourse$grade2", "toLocaleDateString", "toLocaleTimeString", "disabled", "type", "placeholder", "onChange", "title", "Boolean", "join", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/MasterGradMatch/Frontend/grad-app-match/src/components/PAMatch.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport axios from 'axios';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNavigate } from 'react-router-dom';\nimport ProgramSkeleton from './ProgramSkeleton';\nimport { normalizeProfileData } from '../utils/profileUtils';\nimport './PAMatch.css';\n\n// US Census Bureau Regions and Divisions\nconst US_REGIONS = {\n    // Census Regions\n    'Northeast': ['CT', 'ME', 'MA', 'NH', 'RI', 'VT', 'NJ', 'NY', 'PA'],\n    'Midwest': ['IL', 'IN', 'MI', 'OH', 'WI', 'IA', 'KS', 'MN', 'MO', 'NE', 'ND', 'SD'],\n    'South': ['DE', 'DC', 'FL', 'GA', 'MD', 'NC', 'SC', 'VA', 'WV', 'AL', 'KY', 'MS', 'TN', 'AR', 'LA', 'OK', 'TX'],\n    'West': ['AZ', 'CO', 'ID', 'MT', 'NV', 'NM', 'UT', 'WY', 'AK', 'CA', 'HI', 'OR', 'WA'],\n    \n    // Census Divisions\n    'New England': ['CT', 'ME', 'MA', 'NH', 'RI', 'VT'],\n    'Mid-Atlantic': ['NJ', 'NY', 'PA'],\n    'East North Central': ['IL', 'IN', 'MI', 'OH', 'WI'],\n    'West North Central': ['IA', 'KS', 'MN', 'MO', 'NE', 'ND', 'SD'],\n    'South Atlantic': ['DE', 'DC', 'FL', 'GA', 'MD', 'NC', 'SC', 'VA', 'WV'],\n    'East South Central': ['AL', 'KY', 'MS', 'TN'],\n    'West South Central': ['AR', 'LA', 'OK', 'TX'],\n    'Mountain': ['AZ', 'CO', 'ID', 'MT', 'NV', 'NM', 'UT', 'WY'],\n    'Pacific': ['AK', 'CA', 'HI', 'OR', 'WA']\n};\n\n// Helper function to get region for a state\nconst getStateRegion = (state) => {\n    if (!state) return null;\n    const stateCode = state.toUpperCase();\n    for (const [region, states] of Object.entries(US_REGIONS)) {\n        if (states.includes(stateCode)) {\n            return region;\n        }\n    }\n    return 'Other';\n};\n\nconst PAMatch = () => {\n    console.log('PAMatch: Component Mounting...'); // <-- Add log\n    const { token } = useAuth();\n    const navigate = useNavigate();\n    const [userTranscript, setUserTranscript] = useState(null);\n    const [profileData, setProfileData] = useState(null);\n    const [isLoading, setIsLoading] = useState(false);\n    const [error, setError] = useState(null);\n    const [programs, setPrograms] = useState([]);\n    const [selectedPrereq, setSelectedPrereq] = useState(null);\n    const [showCourseModal, setShowCourseModal] = useState(false);\n    const [availableCourses, setAvailableCourses] = useState([]);\n    const [expandedSections, setExpandedSections] = useState({\n        eligible: false,\n        ineligible: false,\n        incomplete: false\n    });\n    const [expandedPrograms, setExpandedPrograms] = useState({});\n    const [activeTabs, setActiveTabs] = useState({}); // Track active tab for each program\n    // New state variables for saved results functionality\n    const [usingSavedResults, setUsingSavedResults] = useState(false);\n    const [savedResultsTimestamp, setSavedResultsTimestamp] = useState(null);\n    const [savingResults, setSavingResults] = useState(false);\n    // Add new state for manually approved programs\n    const [manuallyApprovedPrograms, setManuallyApprovedPrograms] = useState([]);\n    \n    // New state for view options\n    const [viewMode, setViewMode] = useState('accordion'); // 'accordion' or 'grid'\n    const [searchTerm, setSearchTerm] = useState('');\n    const [sortBy, setSortBy] = useState('name'); // 'name', 'gpa', 'eligibility'\n    const [filterBy, setFilterBy] = useState('all'); // 'all', 'eligible', 'ineligible', 'incomplete'\n    const [stateFilter, setStateFilter] = useState('all'); // 'all', specific state, or region\n    const [regionFilter, setRegionFilter] = useState('all'); // 'all', 'northeast', 'southeast', etc.\n\n    // Check for token on mount and when token changes\n    useEffect(() => {\n        console.log('PAMatch: Token Check Effect - Token:', token); // <-- Add log\n        if (!token) {\n            console.log('PAMatch: No token found, navigating to /login'); // <-- Add log\n            navigate('/login');\n            return;\n        }\n    }, [token, navigate]);\n\n    useEffect(() => {\n        console.log('PAMatch: Data Fetch Effect - Running with token:', token); // <-- Add log\n        \n        // Try to get saved results first, then fallback to fetching and calculating\n        const loadData = async () => {\n            setIsLoading(true);\n            setError(null);\n            \n            try {\n                // Check for saved results first\n                await checkForSavedResults();\n            } catch (err) {\n                console.error('PAMatch: Error checking for saved results:', err);\n                // If error in getting saved results, fall back to normal data fetch\n                await fetchAndCalculateResults();\n            } finally {\n                setIsLoading(false);\n            }\n        };\n        \n        if (token) {\n            loadData();\n        }\n    }, [token, navigate]);\n\n    // New function to check for saved matching results\n    const checkForSavedResults = async () => {\n        console.log('PAMatch: Checking for saved matching results...');\n        \n        try {\n            const headers = {\n                Authorization: `Bearer ${token}`,\n                'Content-Type': 'application/json'\n            };\n            \n            const response = await axios.get(`${process.env.REACT_APP_API_URL || 'http://127.0.0.1:8000'}/api/matching-results/`, { headers });\n            console.log('PAMatch: Saved results response:', response.data);\n            \n            if (response.data.status === 'success') {\n                // We have valid saved results, use them\n                console.log('PAMatch: Using saved matching results from', response.data.timestamp);\n                \n                const savedData = response.data.matching_results;\n                \n                // Update state with saved data\n                setPrograms(savedData.programs || []);\n                setUserTranscript(savedData.userTranscript || null);\n                setProfileData(savedData.profileData || null);\n                \n                // Load manually approved programs if they exist\n                if (savedData.manuallyApprovedPrograms) {\n                    setManuallyApprovedPrograms(savedData.manuallyApprovedPrograms);\n                }\n                \n                // Set metadata about saved results\n                setSavedResultsTimestamp(response.data.timestamp);\n                setUsingSavedResults(true);\n                return true;\n            } else if (response.data.status === 'stale') {\n                // Data has changed, need fresh calculation\n                console.log('PAMatch: Saved results are stale, calculating fresh results');\n                await fetchAndCalculateResults();\n                return false;\n            } else {\n                // No saved results found\n                console.log('PAMatch: No saved results found, calculating fresh results');\n                await fetchAndCalculateResults();\n                return false;\n            }\n        } catch (err) {\n            console.error('PAMatch: Error retrieving saved results:', err);\n            throw err;\n        }\n    };\n\n    // Refactored original data fetch into a separate function\n    const fetchAndCalculateResults = async () => {\n        console.log('PAMatch: Starting data fetch...'); // <-- Add log\n        setUsingSavedResults(false); // Mark that we're using fresh data\n        \n        try {\n            if (!token) {\n                 console.log('PAMatch: Token became null/undefined before API calls'); // <-- Add log\n                throw new Error('No authentication token available');\n            }\n\n            const headers = {\n                Authorization: `Bearer ${token}`,\n                'Content-Type': 'application/json'\n            };\n\n            // Fetch programs\n            console.log('PAMatch: Fetching programs...'); // <-- Add log\n            const programsResponse = await axios.get(`${process.env.REACT_APP_API_URL || 'http://127.0.0.1:8000'}/api/programs/`, { headers });\n            const fetchedPrograms = programsResponse.data.programs;\n            setPrograms(fetchedPrograms);\n            console.log('PAMatch: Fetched programs successfully.'); // <-- Add log\n\n            // Fetch saved prerequisites\n             console.log('PAMatch: Fetching saved prerequisites...'); // <-- Add log\n            const savedPrereqsResponse = await axios.get(`${process.env.REACT_APP_API_URL || 'http://127.0.0.1:8000'}/api/prerequisites/saved/`, { headers });\n             console.log('PAMatch: Fetched saved prerequisites successfully.'); // <-- Add log\n\n            // Fetch profile data\n            console.log('PAMatch: Fetching profile data...'); // <-- Add log\n            const profileResponse = await axios.get(`${process.env.REACT_APP_API_URL || 'http://127.0.0.1:8000'}/api/profile/`, { headers });\n            console.log('PAMatch: Profile data received:', profileResponse.data); // Debug log to see what fields are available\n            \n            // Normalize profile data to ensure both camelCase and snake_case fields are available\n            const normalizedProfileData = normalizeProfileData(profileResponse.data);\n            console.log('PAMatch: Normalized profile data:', normalizedProfileData);\n            \n            setProfileData(normalizedProfileData);\n            console.log('PAMatch: Fetched profile data successfully.'); // <-- Add log\n\n            // Fetch transcript data to get accurate GPAs\n            console.log('PAMatch: Fetching transcript data...'); // <-- Add log\n            const transcriptResponse = await axios.get(`${process.env.REACT_APP_API_URL || 'http://127.0.0.1:8000'}/api/transcript/get/`, { headers });\n            const transcripts = transcriptResponse.data.transcripts || [];\n            console.log('PAMatch: Fetched transcript data successfully.'); // <-- Add log\n            \n            // Use the CASPA GPA values provided by the user\n            // These values come from the CASPA GPA calculator on the Dashboard page\n            console.log('PAMatch: Using user-provided CASPA GPAs'); \n            \n            // CASPA GPA values from the CASPA tab\n            const caspaCalculatedGPA = 3.41; // Overall GPA from CASPA calculation (from Overall row, Total column)\n            const caspaScienceGPA = 3.20;   // Science GPA from CASPA calculation (from Overall row, Science column)\n            \n            console.log('PAMatch: Using CASPA GPAs - Overall:', caspaCalculatedGPA, 'Science:', caspaScienceGPA);\n\n            // Calculate basic GPA for prerequisites only\n            let totalPoints = 0;\n            let totalCredits = 0;\n            let sciencePoints = 0;\n            let scienceCredits = 0;\n\n            transcripts.forEach(transcript => {\n                const semesters = transcript.transcript_data?.semesters || [];\n                semesters.forEach(semester => {\n                    semester.courses.forEach(course => {\n                        if (!['F', 'W', 'U'].includes(course.grade)) {\n                            const credits = parseFloat(course.credits) || 0;\n                            const gradePoints = gradeToPoints(course.grade);\n                            \n                            if (!isNaN(credits) && !isNaN(gradePoints)) {\n                                totalPoints += credits * gradePoints;\n                                totalCredits += credits;\n\n                                if (course.is_science) {\n                                    sciencePoints += credits * gradePoints;\n                                    scienceCredits += credits;\n                                }\n                            }\n                        }\n                    });\n                });\n            });\n\n            // These are just used for prerequisite mapping, not for program matching\n            const calculatedGPA = totalCredits > 0 ? totalPoints / totalCredits : 0;\n            const scienceGPA = scienceCredits > 0 ? sciencePoints / scienceCredits : 0;\n\n            // --- Prepare All Transcript Courses (with de-duplication) ---\n            let allTranscriptCourses = []; // Use let\n            const seenCourses = new Set(); // Use a Set to track unique courses\n\n            transcripts.forEach(transcript => {\n                const institution = transcript.institution; // Get institution name\n                const semesters = transcript.transcript_data?.semesters || [];\n                semesters.forEach(semester => {\n                    const term = semester.term;\n                    const year = semester.year;\n                    semester.courses.forEach(course => {\n                        // We only want to add *unique* courses based on code and name primarily,\n                        // preferring the best grade if taken multiple times.\n                        // Let's build a temporary map first.\n                        // Key: `${normalizedInstitution}-${course.code}`\n                        // Value: { bestCourseObject }\n                        if (!['F', 'W', 'U'].includes(course.grade)) {\n                            const normalizedInstitution = institution.toLowerCase().replace(/[,.]/g, '').trim();\n                            const courseMapKey = `${normalizedInstitution}-${course.code}`;\n                            const currentCourseData = {\n                                ...course,\n                                institution: institution,\n                                term: term,\n                                year: year,\n                                gradePoints: gradeToPoints(course.grade) // Add grade points for comparison\n                            };\n\n                            if (!seenCourses[courseMapKey] || currentCourseData.gradePoints > seenCourses[courseMapKey].gradePoints) {\n                                 seenCourses[courseMapKey] = currentCourseData; // Store/replace with the better grade\n                            }\n                        }\n                    });\n                });\n            });\n            // Now, convert the map values back into the final array\n            allTranscriptCourses = Object.values(seenCourses); // Assign to existing variable (no const)\n            console.log(\"De-duplicated (best grade) allTranscriptCourses:\", allTranscriptCourses);\n            // --- End Prepare All Transcript Courses ---\n\n\n            // Use only the saved prerequisites for program matching\n            const savedPrereqs = savedPrereqsResponse.data.prerequisites; // Assuming this is the correct structure from API\n            console.log('Saved prerequisites:', savedPrereqs);\n\n            // Create a case-insensitive lookup for SAVED prerequisites\n            // Assuming savedPrereqs is like: { \"Human Anatomy\": { courses: [...] }, ... }\n            const savedPrereqsLookup = Object.entries(savedPrereqs || {}).reduce((acc, [key, value]) => {\n                // Ensure value has a courses array\n                acc[key.toLowerCase()] = { courses: Array.isArray(value?.courses) ? value.courses : [] };\n                return acc;\n            }, {});\n            \n            // Map prerequisites for each program using SAVED and SUGGESTED courses\n            const programPrereqs = {};\n            fetchedPrograms.forEach(program => {\n                console.log(`Processing prerequisites for program: ${program.name}`);\n                if (program.requirements.prerequisites) {\n                    const programMatches = {};\n\n                    Object.entries(program.requirements.prerequisites).forEach(([prereqName, prereqData]) => {\n                        const savedPrereq = savedPrereqsLookup[prereqName.toLowerCase()];\n                        let coursesToUse = [];\n                        let isSaved = false;\n\n                        // Check if this prerequisite category has saved courses, with special handling for A&P\n                        let foundSaved = false;\n                        if (savedPrereq && savedPrereq.courses.length > 0) {\n                            // Found direct match for saved category\n                            coursesToUse = savedPrereq.courses;\n                            foundSaved = true;\n                        } else if (prereqName.toLowerCase().includes('anatomy') || prereqName.toLowerCase().includes('physiology')) {\n                            // If Anatomy or Physiology required, check if combined A&P was saved\n                            const combinedAPCategory = 'human anatomy and physiology';\n                            const savedCombined = savedPrereqsLookup[combinedAPCategory];\n                            if (savedCombined && savedCombined.courses.length > 0) {\n                                coursesToUse = savedCombined.courses;\n                                foundSaved = true;\n                                console.log(`Using SAVED combined A&P courses for ${prereqName}`);\n                            }\n                        }\n\n                        if (foundSaved) {\n                            isSaved = true;\n                            console.log(`Using SAVED courses for ${prereqName}:`, coursesToUse);\n                        } else {\n                            // If not saved (directly or via combined A&P), find SUGGESTED courses\n                            coursesToUse = allTranscriptCourses.filter(course =>\n                                courseMatchesPrereq(course.name, course.code, prereqName)\n                            );\n                            console.log(`Using SUGGESTED courses for ${prereqName}:`, coursesToUse);\n                        }\n\n                        // Calculate total credits from the courses being used (saved or suggested)\n                        const totalCredits = coursesToUse.reduce((sum, course) =>\n                            sum + parseFloat(course.credits || 0), 0\n                        );\n\n                        // Store the result\n                        programMatches[prereqName] = {\n                            totalCredits: totalCredits,\n                            credits: prereqData.credits, // Required credits\n                            timeframe: prereqData.timeframe,\n                            lab_required: prereqData.lab_required,\n                            courses: coursesToUse, // List of courses (either saved or suggested)\n                            isSaved: isSaved // Flag to indicate if these are saved or suggested\n                        };\n                    });\n\n                    programPrereqs[program.name] = programMatches;\n                }\n            });\n            \n            console.log('Final programPrereqs:', programPrereqs);\n            \n            const updatedTranscript = {\n                calculated_gpa: caspaCalculatedGPA,\n                science_gpa: caspaScienceGPA,\n                prerequisites: programPrereqs\n            };\n\n            console.log('Setting userTranscript:', updatedTranscript);\n            setUserTranscript(updatedTranscript);\n            console.log('PAMatch: Finished processing, userTranscript set.'); // <-- Add log\n            \n            // After calculating everything, save results to backend\n            await saveCurrentResults({\n                programs: fetchedPrograms,\n                userTranscript: updatedTranscript,\n                profileData: profileResponse.data\n            });\n            \n        } catch (err) {\n            console.error('PAMatch: Error during data fetch or processing:', err); // <-- Add log\n             console.error('PAMatch: Error Response Status:', err.response?.status); // <-- Log status\n             console.error('PAMatch: Error Response Data:', err.response?.data); // <-- Log data\n            if (err.response?.status === 401) {\n                // Token is invalid or expired\n                setError('Your session has expired. Please log in again.');\n                 console.log('PAMatch: 401 Error - Navigating to /login'); // <-- Add log\n                navigate('/login');\n                return;\n            }\n            setError(err.response?.data?.error || 'Failed to fetch data');\n             console.log('PAMatch: Non-401 Error occurred, setting error state.'); // <-- Add log\n        }\n    };\n\n    // Function to save current results to backend\n    const saveCurrentResults = async (dataToSave) => {\n        console.log('PAMatch: Saving current matching results...');\n        setSavingResults(true);\n        \n        try {\n            const headers = {\n                Authorization: `Bearer ${token}`,\n                'Content-Type': 'application/json'\n            };\n            \n            // Prepare data object to save\n            const matchingResults = dataToSave || {\n                programs: programs,\n                userTranscript: userTranscript,\n                profileData: profileData,\n                manuallyApprovedPrograms: manuallyApprovedPrograms\n            };\n            \n            // Save to backend\n            const response = await axios.post(\n                'http://127.0.0.1:8000/api/matching-results/save/',\n                { matching_results: matchingResults },\n                { headers }\n            );\n            \n            console.log('PAMatch: Results saved successfully:', response.data);\n            \n            // Update state to reflect saved status\n            setSavedResultsTimestamp(response.data.timestamp);\n            setUsingSavedResults(true);\n            return true;\n        } catch (err) {\n            console.error('PAMatch: Error saving matching results:', err);\n            return false;\n        } finally {\n            setSavingResults(false);\n        }\n    };\n\n    // Function to force recalculation of results\n    const handleRecalculate = () => {\n        setIsLoading(true);\n        fetchAndCalculateResults()\n            .finally(() => setIsLoading(false));\n    };\n\n    const courseMatchesPrereq = (courseName, courseCode, prereqName) => {\n        courseName = courseName.toLowerCase();\n        courseCode = courseCode.toLowerCase();\n        prereqName = prereqName.toLowerCase();\n\n        // Common course prefix patterns\n        const chemistryPrefixes = ['chem', 'chm', 'chemy'];\n        const biologyPrefixes = ['bio', 'biol', 'bsc'];\n        const psychologyPrefixes = ['psy', 'psyc', 'psych'];\n        const englishPrefixes = ['eng', 'engl', 'eh'];\n        const statisticsPrefixes = ['stat', 'stats', 'sta', 'mth'];\n        const sociologyPrefixes = ['soc', 'socy'];\n        const anthropologyPrefixes = ['anth', 'ant'];\n\n        // Check for combined anatomy & physiology courses\n        if (prereqName.includes('anatomy & physiology') || \n            prereqName.includes('anatomy and physiology')) {\n            const isMatch = (courseName.includes('anatomy') && courseName.includes('physiology')) ||\n                       courseName.includes('a&p') ||\n                       courseName.includes('anat & phys') ||\n                   courseName.includes('anatomical and physiological') ||\n                   courseCode.includes('a&p') ||\n                   (courseCode.match(/\\d+/) && (courseName.includes('human a & p') || courseName.includes('hum anat & phys')));\n            if (isMatch) console.log('Matched A&P course:', courseName);\n            return isMatch;\n        }\n\n        // Match based on common course patterns\n        switch(true) {\n             // --- Combined Chemistry Sequence ---\n            case prereqName.includes('chemistry sequence (gen/org/biochem)'): // Match the new name\n                const seq_isGenChem = chemistryPrefixes.some(prefix => courseCode.startsWith(prefix)) && // Renamed variable\n                       (courseName.includes('general') || courseName.includes('inorganic') ||\n                        courseCode.match(/\\d+/)?.some(num => parseInt(num) < 200));\n                const seq_isOrgChem = chemistryPrefixes.some(prefix => courseCode.startsWith(prefix)) && // Renamed variable\n                       (courseName.includes('organic') ||\n                        courseCode.match(/\\d+/)?.some(num => parseInt(num) >= 200 && parseInt(num) < 400));\n                const seq_isBiochem = courseCode.startsWith('bch') || courseCode.startsWith('bioc') || // Renamed variable\n                       courseName.includes('biochem');\n                return seq_isGenChem || seq_isOrgChem || seq_isBiochem; // Use renamed variables\n\n            // --- Standard Categories ---\n        case prereqName.includes('human anatomy'):\n            const isAnatomy = (\n                // Check for combined A&P courses first\n                ((courseName.includes('anatomy') && courseName.includes('physiology')) ||\n                courseName.includes('a&p') ||\n                courseName.includes('anat & phys') ||\n                courseName.includes('human a & p') ||\n                courseName.includes('hum anat & phys')) ||\n                // Then check for standalone anatomy courses\n                (biologyPrefixes.some(prefix => courseCode.startsWith(prefix)) &&\n                (courseName.includes('anatomy') || courseName.includes('anatomical')))\n            );\n            if (isAnatomy) console.log('Matched Anatomy course:', courseName);\n            return isAnatomy;\n\n        case prereqName.includes('human physiology'):\n            const isPhysiology = (\n                // Check for combined A&P courses first\n                ((courseName.includes('anatomy') && courseName.includes('physiology')) ||\n                courseName.includes('a&p') ||\n                courseName.includes('anat & phys') ||\n                courseName.includes('human a & p') ||\n                courseName.includes('hum anat & phys')) ||\n                // Then check for standalone physiology courses\n                (biologyPrefixes.some(prefix => courseCode.startsWith(prefix)) &&\n                (courseName.includes('physiology') || courseName.includes('physiological')))\n            );\n            if (isPhysiology) console.log('Matched Physiology course:', courseName);\n            return isPhysiology;\n\n        case prereqName.includes('microbiology'):\n            const isMicro = (biologyPrefixes.some(prefix => courseCode.startsWith(prefix)) &&\n                   courseName.includes('micro')) ||\n                   courseName.includes('microbiology');\n            if (isMicro) console.log('Matched Microbiology:', courseName);\n            return isMicro;\n\n            case prereqName.includes('general chemistry'):\n            const isGenChem = chemistryPrefixes.some(prefix => courseCode.startsWith(prefix)) &&\n                   (courseName.includes('general') || \n                    courseCode.match(/\\d+/)?.some(num => parseInt(num) < 200));\n            if (isGenChem) console.log('Matched General Chemistry:', courseName);\n            return isGenChem;\n\n            case prereqName.includes('organic chemistry'):\n            const isOrgChem = chemistryPrefixes.some(prefix => courseCode.startsWith(prefix)) &&\n                   (courseName.includes('organic') || \n                    courseCode.match(/\\d+/)?.some(num => parseInt(num) >= 200 && parseInt(num) < 400));\n            if (isOrgChem) console.log('Matched Organic Chemistry:', courseName);\n            return isOrgChem;\n\n            case prereqName.includes('biochemistry'):\n            const isBiochem = courseCode.startsWith('bch') || courseCode.startsWith('bioc') ||\n                       courseName.includes('biochem');\n            if (isBiochem) console.log('Matched Biochemistry:', courseName);\n            return isBiochem;\n\n            case prereqName.includes('psychology'):\n            const isPsych = psychologyPrefixes.some(prefix => courseCode.startsWith(prefix)) ||\n                       courseName.includes('psychology');\n            if (isPsych) console.log('Matched Psychology:', courseName);\n            return isPsych;\n\n            case prereqName.includes('statistics'):\n            const isStats = statisticsPrefixes.some(prefix => courseCode.startsWith(prefix)) ||\n                       courseName.includes('statistics') ||\n                       courseName.includes('statistical');\n            if (isStats) console.log('Matched Statistics:', courseName);\n            return isStats;\n\n        case prereqName.includes('medical terminology'):\n            const isMedTerm = courseName.includes('medical terminology') ||\n                   (courseCode.startsWith('nurs') && courseName.includes('terminology'));\n            if (isMedTerm) console.log('Matched Medical Terminology:', courseName);\n            return isMedTerm;\n\n        case prereqName.includes('behavioral sciences'):\n            const isBehavioralScience = (\n                psychologyPrefixes.some(prefix => courseCode.startsWith(prefix)) ||\n                courseName.includes('psychology') ||\n                courseName.includes('behavioral') ||\n                courseName.includes('cognitive') ||\n                courseName.includes('developmental') ||\n                courseName.includes('abnormal') ||\n                courseName.includes('social psychology') ||\n                courseName.includes('child psychology') ||\n                courseName.includes('adolescent psychology') ||\n                courseName.includes('human development') ||\n                courseName.includes('life span') ||\n                courseName.includes('counseling') ||\n                courseName.includes('psychobiology') ||\n                courseName.includes('neuropsychology') ||\n                courseName.includes('brain and behavior') ||\n                courseName.includes('human behavior') ||\n                courseName.includes('behavioral science')\n            );\n            if (isBehavioralScience) console.log('Matched Behavioral Science:', courseName);\n            return isBehavioralScience;\n\n        case prereqName.includes('sociology'):\n            const isSociology = (\n                sociologyPrefixes.some(prefix => courseCode.startsWith(prefix)) ||\n                courseName.includes('sociology') ||\n                courseName.includes('social welfare') ||\n                courseName.includes('social work') ||\n                courseName.includes('criminology') ||\n                courseName.includes('criminal justice') ||\n                courseName.includes('marriage and family') ||\n                courseName.includes('family studies') ||\n                courseName.includes('social ecology')\n            );\n            if (isSociology) console.log('Matched Sociology:', courseName);\n            return isSociology;\n\n        case prereqName.includes('social sciences'):\n            const isSocialScience = (\n                // Include all sociology courses\n                sociologyPrefixes.some(prefix => courseCode.startsWith(prefix)) ||\n                courseName.includes('sociology') ||\n                courseName.includes('social welfare') ||\n                courseName.includes('social work') ||\n                courseName.includes('criminology') ||\n                courseName.includes('criminal justice') ||\n                courseName.includes('marriage and family') ||\n                courseName.includes('family studies') ||\n                // Include all psychology courses\n                psychologyPrefixes.some(prefix => courseCode.startsWith(prefix)) ||\n                courseName.includes('psychology') ||\n                courseName.includes('behavioral') ||\n                courseName.includes('cognitive') ||\n                courseName.includes('developmental') ||\n                courseName.includes('abnormal') ||\n                courseName.includes('social psychology') ||\n                courseName.includes('child psychology') ||\n                courseName.includes('adolescent psychology') ||\n                // Include anthropology and other social sciences\n                anthropologyPrefixes.some(prefix => courseCode.startsWith(prefix)) ||\n                courseName.includes('anthropology') ||\n                courseName.includes('cultural studies') ||\n                courseName.includes('ethnic studies') ||\n                courseName.includes('gender studies') ||\n                courseName.includes('women studies') ||\n                courseName.includes('african american studies') ||\n                courseName.includes('asian american studies') ||\n                courseName.includes('latino american studies') ||\n                courseName.includes('native american studies') ||\n                courseName.includes('social justice') ||\n                courseName.includes('human development') ||\n                courseName.includes('human sexuality') ||\n                courseName.includes('death and dying') ||\n                courseName.includes('multicultural') ||\n                // Additional social science indicators\n                courseName.includes('social science') ||\n                courseName.includes('human behavior') ||\n                courseName.includes('social ecology') ||\n                courseName.includes('community') ||\n                courseName.includes('cultural geography')\n            );\n            if (isSocialScience) console.log('Matched Social Science:', courseName);\n            return isSocialScience;\n\n            case prereqName.includes('english'):\n            const isEnglish = englishPrefixes.some(prefix => courseCode.startsWith(prefix)) ||\n                       courseName.includes('english') ||\n                       courseName.includes('composition') ||\n                       courseName.includes('writing');\n            if (isEnglish) console.log('Matched English:', courseName);\n            return isEnglish;\n\n           case prereqName.includes('chemistry (general)'): // Add this case\n               // This logic correctly identifies general/inorganic chemistry\n               return chemistryPrefixes.some(prefix => courseCode.startsWith(prefix)) &&\n                      (courseName.includes('general') || courseName.includes('inorganic') ||\n                       courseCode.match(/\\d+/)?.some(num => parseInt(num) < 200));\n\n           case prereqName.includes('biology (general)'): // Add this case\n               // Must have bio prefix but NOT contain keywords for more specific categories\n               const isGeneralBio = biologyPrefixes.some(prefix => courseCode.startsWith(prefix)) &&\n                      !courseName.includes('anatomy') &&\n                      !courseName.includes('physiology') &&\n                      !courseName.includes('micro') && // Exclude microbiology\n                      !courseName.includes('genetic'); // Exclude genetics\n               return isGeneralBio;\n\n            case prereqName.includes('cpr'): // Add this case\n                return courseName.includes('cpr') ||\n                       courseName.includes('cardiopulmonary resuscitation') ||\n                       courseName.includes('basic life support');\n\n            case prereqName.includes('math'): // Added logic for Math\n                // Exclude statistics as it's a separate category\n                const isMath = (courseCode.startsWith('math') || courseCode.startsWith('mth')) &&\n                               !courseName.includes('stat'); // Exclude statistics\n                // Could add more specific checks for 'algebra', 'calculus', 'pre-calculus' if needed\n                return isMath;\n\n            case prereqName.includes('sociology'): // Broadened logic for Sociology\n                 const isSociologyRelated = ( // Use a different variable name\n                    sociologyPrefixes.some(prefix => courseCode.startsWith(prefix)) || // SOC code\n                    anthropologyPrefixes.some(prefix => courseCode.startsWith(prefix)) || // ANTH code\n                    courseName.includes('sociology') ||\n                    courseName.includes('social welfare') ||\n                    courseName.includes('social work') ||\n                    courseName.includes('criminology') ||\n                    courseName.includes('criminal justice') ||\n                    courseName.includes('marriage and family') ||\n                    courseName.includes('family studies') ||\n                    courseName.includes('social ecology') ||\n                    courseName.includes('anthropology') || // Added Anthropology\n                    courseName.includes('gender studies') || // Added Gender Studies\n                    courseName.includes('ethnic studies') // Added Ethnic Studies\n                );\n                return isSociologyRelated; // Use updated variable name\n\n                default:\n            const isDefaultMatch = courseName.includes(prereqName) ||\n                   courseCode.includes(prereqName);\n            if (isDefaultMatch) console.log('Matched Default:', courseName);\n            return isDefaultMatch;\n    }\n};\n\nconst mapPrerequisites = (courses, prerequisites) => {\n    const prereqMap = {};\n    \n    // Process each prerequisite\n    Object.entries(prerequisites).forEach(([prereqName, prereqData]) => {\n        console.log(`\\nProcessing prerequisite: ${prereqName}`);\n        console.log('Required credits:', prereqData.credits);\n        \n        // Find all matching courses across all transcripts\n        const matchingCourses = courses.filter(course => \n            courseMatchesPrereq(course.name, course.code, prereqName) &&\n            !['F', 'W', 'U'].includes(course.grade)\n        );\n\n        console.log('Found matching courses:', matchingCourses);\n\n        // Special handling for Anatomy and Physiology\n        const isAnatomyOrPhysiology = prereqName.toLowerCase().includes('anatomy') || \n                                    prereqName.toLowerCase().includes('physiology');\n        \n        // Sort matching courses by grade (best grades first)\n        const sortedCourses = [...matchingCourses].sort((a, b) => {\n            const gradeA = gradeToPoints(a.grade);\n            const gradeB = gradeToPoints(b.grade);\n            if (gradeB !== gradeA) return gradeB - gradeA;\n            return parseInt(b.year) - parseInt(a.year);\n        });\n\n        console.log('Sorted matching courses:', sortedCourses);\n\n        // Calculate total credits and select courses\n        let totalCredits = 0;\n        const requiredCredits = parseFloat(prereqData.credits) || 0;\n        const selectedCourses = [];\n\n        for (const course of sortedCourses) {\n            const courseCredits = parseFloat(course.credits) || 0;\n            \n            // For combined A&P courses, count full credits for both requirements\n            if (isAnatomyOrPhysiology && \n                (course.name.toLowerCase().includes('anatomy') && course.name.toLowerCase().includes('physiology'))) {\n                selectedCourses.push({\n                    code: course.code,\n                    name: course.name,\n                    credits: courseCredits,\n                    grade: course.grade,\n                    institution: course.institution,\n                    term: course.term,\n                    year: course.year\n                });\n                totalCredits += courseCredits;\n            } else if (!isAnatomyOrPhysiology && totalCredits < requiredCredits) {\n                // Normal handling for non-A&P courses\n                selectedCourses.push({\n                    code: course.code,\n                    name: course.name,\n                    credits: courseCredits,\n                    grade: course.grade,\n                    institution: course.institution,\n                    term: course.term,\n                    year: course.year\n                });\n                totalCredits += courseCredits;\n            }\n        }\n\n        // Create prerequisite entry\n        prereqMap[prereqName] = {\n            totalCredits,\n            credits: requiredCredits,\n            timeframe: prereqData.timeframe,\n            lab_required: prereqData.lab_required,\n            courses: selectedCourses,\n            all_matching_courses: matchingCourses\n        };\n\n        console.log(`Final prerequisite entry for ${prereqName}:`, prereqMap[prereqName]);\n    });\n\n    return prereqMap;\n};\n\nconst openCourseSelection = (prereqName, program, allCourses, currentPrereqCourses) => {\n    // Filter out courses that are already mapped to this prerequisite\n    const mappedCourseCodes = new Set(currentPrereqCourses.map(course => course.code));\n    \n    // Use all_courses directly from userTranscript\n    const validCourses = userTranscript.all_courses.filter(course => \n        !mappedCourseCodes.has(course.code) &&\n        !['F', 'W', 'U'].includes(course.grade) &&\n        courseMatchesPrereq(course.name, course.code, prereqName)\n    );\n    \n    // Debug logging\n    console.log('Available courses for selection:', validCourses);\n    console.log('Mapped course codes:', mappedCourseCodes);\n    console.log('Prerequisite name:', prereqName);\n    \n    setAvailableCourses(validCourses);\n    setSelectedPrereq({ name: prereqName, program: program });\n    setShowCourseModal(true);\n};\n\nconst addCourseToPrereq = (course) => {\n    if (!selectedPrereq) return;\n\n    const updatedTranscript = { ...userTranscript };\n    const prereqData = updatedTranscript.prerequisites[selectedPrereq.name];\n    \n    // Add the selected course to the prerequisite's courses\n    prereqData.courses.push({\n        code: course.code,\n        name: course.name,\n        credits: course.credits,\n        grade: course.grade,\n        institution: course.institution,\n        term: course.term,\n        year: course.year\n    });\n\n    // Update total credits\n    prereqData.totalCredits += parseFloat(course.credits) || 0;\n\n    // Recalculate all GPAs\n    const {\n        calculatedGPA,\n        scienceGPA\n    } = processMultipleTranscripts(updatedTranscript.transcript_data.institutions);\n\n    updatedTranscript.calculated_gpa = calculatedGPA;\n    updatedTranscript.science_gpa = scienceGPA;\n    updatedTranscript.prerequisite_gpa = calculatePrereqGPA(updatedTranscript.prerequisites, selectedPrereq.program);\n\n    setUserTranscript(updatedTranscript);\n    setShowCourseModal(false);\n};\n\nconst CourseSelectionModal = () => {\n    if (!showCourseModal) return null;\n\n    return (\n        <div className=\"modal-overlay\">\n            <div className=\"modal-content\">\n                <h3>Select Course for {selectedPrereq?.name}</h3>\n                <div className=\"course-list\">\n                    {availableCourses.map((course, index) => (\n                        <div key={index} className=\"course-option\" onClick={() => addCourseToPrereq(course)}>\n                            <div className=\"course-option-header\">\n                            <span>{course.code} - {course.name}</span>\n                                <span>{course.institution}</span>\n                            </div>\n                            <div className=\"course-option-details\">\n                            <span>Credits: {course.credits} | Grade: {course.grade}</span>\n                                <span>{course.term} {course.year}</span>\n                            </div>\n                        </div>\n                    ))}\n                </div>\n                <button className=\"close-modal\" onClick={() => setShowCourseModal(false)}>Cancel</button>\n            </div>\n        </div>\n    );\n};\n\nconst renderPrerequisiteDetails = (prereqData, prereqName, programName) => {\n    if (!userTranscript?.prerequisites?.[programName]?.[prereqName]) {\n        return null;\n    }\n\n    const programPrereqData = userTranscript.prerequisites[programName][prereqName];\n    \n    // Get the matched courses\n    const matchedCourses = programPrereqData.courses || [];\n    \n    // Sort matched courses by grade\n    const sortedMatchedCourses = [...matchedCourses].sort((a, b) => \n        gradeToPoints(b.grade) - gradeToPoints(a.grade)\n    );\n    \n    // Track which courses are used in GPA calculation\n    const coursesUsedInGPA = new Set();\n    let creditsUsed = 0;\n    const requiredCredits = parseFloat(programPrereqData.credits) || 0;\n    \n    // Mark courses used in GPA calculation\n    for (const course of sortedMatchedCourses) {\n        if (creditsUsed < requiredCredits) {\n            coursesUsedInGPA.add(course.code);\n            creditsUsed += parseFloat(course.credits) || 0;\n        }\n    }\n\n    const handleCourseRemove = (courseCode, programName, prereqName) => {\n        const updatedTranscript = { ...userTranscript };\n        \n        // Check if prerequisites exist\n        if (!updatedTranscript.prerequisites?.[programName]?.[prereqName]) {\n            console.log('Prerequisites not found for:', { programName, prereqName });\n            return;\n        }\n\n        const prereqData = updatedTranscript.prerequisites[programName][prereqName];\n        \n        // Remove the course\n        prereqData.courses = prereqData.courses.filter(course => course.code !== courseCode);\n        \n        // Update total credits\n        prereqData.totalCredits = prereqData.courses.reduce((total, course) => \n            total + (parseFloat(course.credits) || 0), 0\n        );\n\n        // Recalculate prerequisite GPA\n        updatedTranscript.prerequisite_gpa = calculatePrereqGPA(updatedTranscript.prerequisites, programName);\n\n        setUserTranscript(updatedTranscript);\n    };\n    \n    return (\n        <div className=\"prerequisite-courses\">\n            {sortedMatchedCourses.map((course, index) => (\n                <div key={index} className={`course-detail ${coursesUsedInGPA.has(course.code) ? 'used-in-gpa' : ''}`}>\n                    <div className=\"course-info\">\n                        <span className=\"course-name\">\n                            {course.code} - {course.name} ({course.credits} credits)\n                        </span>\n                        <span className=\"course-institution\">\n                            {course.institution} - {course.term} {course.year}\n                        </span>\n                    </div>\n                    <div className=\"course-stats\">\n                        <span className=\"course-grade\">Grade: {course.grade}</span>\n                        <button \n                            onClick={() => handleCourseRemove(course.code, programName, prereqName)}\n                            className=\"remove-course-btn\"\n                        >\n                            ✕\n                        </button>\n                    </div>\n                </div>\n            ))}\n        </div>\n    );\n};\n\nconst gradeToPoints = (grade) => {\n    const gradePoints = {\n        'A+': 4.0, 'A': 4.0, 'A-': 3.7,\n        'B+': 3.3, 'B': 3.0, 'B-': 2.7,\n        'C+': 2.3, 'C': 2.0, 'C-': 1.7,\n        'D+': 1.3, 'D': 1.0, 'D-': 0.7,\n        'F': 0.0\n    };\n    return gradePoints[grade] || 0;\n};\n\nconst calculatePrereqGPA = useCallback((prerequisites, programName) => {\n    if (!prerequisites?.[programName]) {\n        return 'N/A';\n    }\n\n    let totalPoints = 0;\n    let totalCredits = 0;\n    \n    // Process prerequisites for the specific program\n    Object.values(prerequisites[programName]).forEach(prereqData => {\n        if (!prereqData.courses || prereqData.courses.length === 0) return;\n        \n        // Sort courses by grade points in descending order\n        const sortedCourses = [...prereqData.courses].sort((a, b) => \n            gradeToPoints(b.grade) - gradeToPoints(a.grade)\n        );\n        \n        // Get the required credits for this prerequisite\n        const requiredCredits = parseFloat(prereqData.credits) || 0;\n        \n        let creditsUsed = 0;\n        let courseIndex = 0;\n        \n        // Use best grades until we meet the credit requirement\n        while (creditsUsed < requiredCredits && courseIndex < sortedCourses.length) {\n            const course = sortedCourses[courseIndex];\n            const courseCredits = parseFloat(course.credits) || 0;\n            const gradePoints = gradeToPoints(course.grade);\n            \n            totalPoints += gradePoints * courseCredits;\n            totalCredits += courseCredits;\n            creditsUsed += courseCredits;\n            courseIndex++;\n        }\n    });\n    \n    return totalCredits > 0 ? (totalPoints / totalCredits).toFixed(2) : 'N/A';\n}, []);\n\nconst checkPrerequisites = (program, userTranscript) => {\n    if (!program.requirements.prerequisites || !userTranscript.prerequisites?.[program.name]) {\n        return false;\n    }\n    \n    return Object.entries(program.requirements.prerequisites)\n        .every(([prereqName, req]) => {\n            const programPrereqs = userTranscript.prerequisites[program.name];\n            if (!programPrereqs[prereqName]) return false;\n            \n            const prereqData = programPrereqs[prereqName];\n            const totalCredits = prereqData.totalCredits || 0;\n            const requiredCredits = parseFloat(req.credits) || 0;\n            \n            // Special handling for Anatomy and Physiology\n            if (prereqName.toLowerCase().includes('anatomy') || prereqName.toLowerCase().includes('physiology')) {\n                // Find all A&P courses\n                const combinedAPCourses = prereqData.courses.filter(course => \n                    course.name.toLowerCase().includes('anatomy') && \n                    course.name.toLowerCase().includes('physiology')\n                );\n                \n                // Calculate total credits from combined A&P courses\n                const combinedCredits = combinedAPCourses.reduce((sum, course) => \n                    sum + parseFloat(course.credits || 0), 0\n                );\n                \n                // If we have combined A&P courses, use their full credits\n                if (combinedAPCourses.length > 0) {\n                    return combinedCredits >= requiredCredits;\n                }\n            }\n            \n            // Check if lab requirement is met when applicable\n            const meetsLabRequirement = !req.lab_required || \n                prereqData.courses.some(course => course.name.toLowerCase().includes('lab'));\n            \n            // Check if timeframe requirement is met when applicable\n            const meetsTimeframe = !req.timeframe || \n                prereqData.courses.every(course => {\n                    const courseYear = parseInt(course.year);\n                    const currentYear = new Date().getFullYear();\n                    return (currentYear - courseYear) <= parseInt(req.timeframe);\n                });\n            \n            return totalCredits >= requiredCredits && meetsLabRequirement && meetsTimeframe;\n        });\n};\n\nconst checkPatientCareHours = (program, profileData) => {\n    const requiredHours = program.requirements.experience[\"Patient Care Hours\"] || 0;\n    // Handle both camelCase and snake_case field names\n    const userHours = parseInt(profileData?.direct_patient_care_hours || profileData?.directPatientCareHours || 0);\n    return userHours >= requiredHours;\n};\n\nconst checkShadowingHours = (program, profileData) => {\n    const requiredHours = program.requirements.experience[\"Shadowing Hours\"] || 0;\n    // Handle both camelCase and snake_case field names\n    const userHours = parseInt(profileData?.shadowing_hours || profileData?.shadowingHours || 0);\n    return userHours >= requiredHours;\n};\nconst getUserExperienceHours = (profileData, experienceType) => {\n    if (!profileData) return 0;\n    if (experienceType === \"Patient Care Hours\") {\n        return parseInt(profileData.direct_patient_care_hours || profileData.directPatientCareHours || 0);\n    }\n    if (experienceType === \"Shadowing Hours\") {\n        return parseInt(profileData.shadowing_hours || profileData.shadowingHours || 0);\n    }\n    return 0;\n};\n\n\nconst processMultipleTranscripts = (transcripts) => {\n    if (!Array.isArray(transcripts)) {\n        console.error('Invalid transcripts data:', transcripts);\n        return {\n            allCoursesWithSource: [],\n            calculatedGPA: 0,\n            scienceGPA: 0,\n            totalCredits: 0,\n            scienceCredits: 0\n        };\n    }\n\n    // Combine all courses from all transcripts with institution info\n    const allCoursesWithSource = transcripts.flatMap(transcript => {\n        if (!transcript) {\n            console.warn('Invalid transcript entry:', transcript);\n            return [];\n        }\n\n        try {\n            // Handle both initial transcript loading and course addition cases\n            if (transcript.transcript_data?.semesters) {\n                // Initial transcript loading structure\n                const institution = transcript.transcript_data?.academic_summary?.institution || 'Unknown Institution';\n                return transcript.transcript_data.semesters.flatMap(semester => {\n                    if (!semester?.courses) {\n                        console.warn('Invalid semester data:', semester);\n                        return [];\n                    }\n                    return semester.courses.map(course => ({\n                        ...course,\n                        institution,\n                        term: semester.term || 'Unknown Term',\n                        year: semester.year || 'Unknown Year'\n                    }));\n                });\n            } else if (transcript.semesters) {\n                // Course addition structure (from institutions array)\n                const institution = transcript.name || 'Unknown Institution';\n                return transcript.semesters.flatMap(semester => {\n                    if (!semester?.courses) {\n                        console.warn('Invalid semester data:', semester);\n                        return [];\n                    }\n                    return semester.courses.map(course => ({\n                        ...course,\n                        institution,\n                        term: semester.term || 'Unknown Term',\n                        year: semester.year || 'Unknown Year'\n                    }));\n                });\n            }\n        } catch (error) {\n            console.error('Error processing transcript:', error);\n            return [];\n        }\n        console.warn('Invalid transcript structure:', transcript);\n        return [];\n    });\n\n    // Calculate overall GPA across all transcripts\n    let totalPoints = 0;\n    let totalCredits = 0;\n    let sciencePoints = 0;\n    let scienceCredits = 0;\n\n    allCoursesWithSource.forEach(course => {\n        try {\n            if (course?.grade && !['F', 'W', 'U'].includes(course.grade)) {\n                const credits = parseFloat(course.credits) || 0;\n                const gradePoints = gradeToPoints(course.grade);\n                \n                if (!isNaN(credits) && !isNaN(gradePoints)) {\n                    totalPoints += credits * gradePoints;\n                    totalCredits += credits;\n\n                    // Update science GPA if it's a science course\n                    if (course.is_science) {\n                        sciencePoints += credits * gradePoints;\n                        scienceCredits += credits;\n                    }\n                } else {\n                    console.warn('Invalid credits or grade points:', { course, credits, gradePoints });\n                }\n            }\n        } catch (error) {\n            console.error('Error processing course:', error, course);\n        }\n    });\n\n    const calculatedGPA = totalCredits > 0 ? totalPoints / totalCredits : 0;\n    const scienceGPA = scienceCredits > 0 ? sciencePoints / scienceCredits : 0;\n\n    return {\n        allCoursesWithSource,\n        calculatedGPA,\n        scienceGPA,\n        totalCredits,\n        scienceCredits\n    };\n};\n\nconst checkProgramEligibility = (program) => {\n    // First check if program is manually approved\n    if (manuallyApprovedPrograms.includes(program.name)) {\n        return 'eligible';\n    }\n    \n    // Original eligibility logic\n    // Check if program has prerequisites defined\n    const hasPrereqs = program.requirements.prerequisites && \n        Object.keys(program.requirements.prerequisites).length > 0;\n    \n    if (!hasPrereqs) {\n        return 'incomplete';\n    }\n\n    // Check if prerequisites data exists for this program\n    const programPrereqs = userTranscript?.prerequisites?.[program.name];\n    if (!programPrereqs || Object.keys(programPrereqs).length === 0) {\n        return 'incomplete';\n    }\n\n    // Check all requirements\n    const meetsGPA = userTranscript.calculated_gpa >= program.requirements.gpa.overall;\n    const meetsScienceGPA = parseFloat(userTranscript.science_gpa) >= program.requirements.gpa.science;\n    \n    const meetsPrereqs = Object.entries(program.requirements.prerequisites)\n        .every(([prereqName, req]) => {\n            if (!programPrereqs[prereqName]) return false; // No data for this prereq\n            const totalCredits = programPrereqs[prereqName].totalCredits || 0;\n            const requiredCreditsNum = parseFloat(req.credits); // Attempt to parse requirement\n\n            // If requirement is not a number (e.g., \"4 Semesters\"), consider met if any courses match\n            if (isNaN(requiredCreditsNum)) {\n                 // Check if courses array exists and has items\n                 return programPrereqs[prereqName].courses && programPrereqs[prereqName].courses.length > 0;\n            }\n            // Otherwise, perform the numeric comparison\n            return totalCredits >= requiredCreditsNum;\n        });\n    \n    // Convert to numbers and handle both camelCase and snake_case field names\n    const patientCareHours = parseInt(profileData?.direct_patient_care_hours || profileData?.directPatientCareHours || 0);\n    const shadowingHours = parseInt(profileData?.shadowing_hours || profileData?.shadowingHours || 0);\n    \n    const meetsPatientCare = patientCareHours >= \n        (program.requirements.experience[\"Patient Care Hours\"] || 0);\n    const meetsShadowing = shadowingHours >= \n        (program.requirements.experience[\"Shadowing Hours\"] || 0);\n\n    // --- Add Standardized Test Check ---\n    let meetsStandardizedTests = true; // Assume met unless a required test is missing\n    const testsRequiredString = program.requirements.standardized_tests?.tests_required || \"\"; // Get the string like \"GRE, CASPER\"\n    const userProfile = profileData; // User profile data from state\n\n    // Normalize the required tests string for easier checking\n    const requiredTests = testsRequiredString.toLowerCase().split(/,|\\(|\\)/) // Split by comma or parentheses\n                                          .map(t => t.trim())\n                                          .filter(t => t); // Remove empty strings\n\n    // Check GRE requirement\n    if (requiredTests.includes('gre')) {\n        if (!userProfile?.gre_verbal_score && !userProfile?.gre_quantitative_score && !userProfile?.gre_analytical_writing_score) {\n            meetsStandardizedTests = false; // Required but user has no scores entered\n        }\n    }\n\n    // Check CASPER requirement\n    if (requiredTests.includes('casper')) {\n        if (!userProfile?.has_taken_casper) {\n            meetsStandardizedTests = false; // Required but user hasn't checked the box\n        }\n    }\n\n    // Check PA-CAT requirement\n    if (requiredTests.includes('pa-cat') || requiredTests.includes('pacat')) { // Check for variations\n        if (!userProfile?.has_taken_pa_cat) {\n            meetsStandardizedTests = false; // Required but user hasn't checked the box\n        }\n    }\n\n    // --- Update Eligibility Condition ---\n    // Note: We removed the specific meetsGRE check as it's now part of meetsStandardizedTests\n    if (meetsGPA && meetsScienceGPA && meetsPrereqs && meetsPatientCare && meetsShadowing && meetsStandardizedTests) {\n        return 'eligible';\n    }\n\n    // If any check failed, it's ineligible (assuming prerequisites are complete)\n    return 'ineligible';\n};\n\n// Cache program eligibility results to avoid recalculation\nconst programEligibilityCache = React.useMemo(() => {\n    if (!programs || !userTranscript) return {};\n    \n    const cache = {};\n    programs.forEach(program => {\n        cache[program.name] = checkProgramEligibility(program);\n    });\n    return cache;\n}, [programs, userTranscript, profileData]);\n\nconst programCounts = React.useMemo(() => {\n    if (!programs || !userTranscript) return { eligible: 0, ineligible: 0, incomplete: 0 };\n\n    const categorizedPrograms = programs.reduce((acc, program) => {\n        const status = programEligibilityCache[program.name];\n        acc[status]++;\n        return acc;\n    }, { eligible: 0, ineligible: 0, incomplete: 0 });\n\n    return categorizedPrograms;\n}, [programEligibilityCache]);\n\n// Enhanced requirement comparison with visual indicators\nconst renderRequirementComparison = (requirement, studentValue, program, prereqName) => {\n    let isMet = false;\n    let percentage = 0;\n    \n    if (program && prereqName) {\n        if (prereqName === \"Patient Care Hours\" || prereqName === \"Shadowing Hours\") {\n            // For experience requirements\n            isMet = parseFloat(studentValue) >= parseFloat(requirement);\n            percentage = Math.min((parseFloat(studentValue) / parseFloat(requirement)) * 100, 100);\n        } else {\n            // For prerequisites, check the specific program's prerequisite data\n            const programPrereqs = userTranscript?.prerequisites?.[program.name];\n            if (programPrereqs && programPrereqs[prereqName]) {\n                const totalCredits = programPrereqs[prereqName].totalCredits || 0;\n                const requiredCreditsNum = parseFloat(requirement); // Attempt to parse requirement\n\n                // If requirement is not a number (e.g., \"4 Semesters\"), consider met if any courses match\n                if (isNaN(requiredCreditsNum)) {\n                    isMet = programPrereqs[prereqName].courses && programPrereqs[prereqName].courses.length > 0;\n                    percentage = isMet ? 100 : 0;\n                } else {\n                    // Otherwise, perform the numeric comparison\n                    isMet = totalCredits >= requiredCreditsNum;\n                    percentage = Math.min((totalCredits / requiredCreditsNum) * 100, 100);\n                }\n            }\n        }\n    } else {\n        // For non-prerequisite requirements (like GPA)\n        isMet = studentValue >= requirement;\n        percentage = Math.min((studentValue / requirement) * 100, 100);\n    }\n    \n    return (\n        <div className={`requirement-indicator ${isMet ? 'met' : 'not-met'}`}>\n            <div className=\"requirement-progress\">\n                <div className=\"progress-bar\">\n                    <div \n                        className=\"progress-fill\" \n                        style={{ width: `${percentage}%` }}\n                    ></div>\n                </div>\n                <span className=\"progress-text\">{percentage.toFixed(0)}%</span>\n            </div>\n            <div className=\"requirement-status-icon\">\n                {isMet ? '✅' : '❌'}\n            </div>\n        </div>\n    );\n};\n\n// Enhanced GPA comparison component\nconst renderGPAComparison = (required, actual, label) => {\n    const isMet = actual >= required;\n    const percentage = Math.min((actual / required) * 100, 100);\n    \n    return (\n        <div className=\"gpa-comparison-modern\">\n            <div className=\"gpa-header\">\n                <h4>{label}</h4>\n                <div className={`gpa-status ${isMet ? 'met' : 'not-met'}`}>\n                    {isMet ? '✅' : '❌'}\n                </div>\n            </div>\n            <div className=\"gpa-details\">\n                <div className=\"gpa-values\">\n                    <span className=\"required\">Required: {required.toFixed(2)}</span>\n                    <span className=\"actual\">Your GPA: {actual.toFixed(2)}</span>\n                </div>\n                <div className=\"gpa-progress\">\n                    <div className=\"progress-bar\">\n                        <div \n                            className=\"progress-fill\" \n                            style={{ width: `${percentage}%` }}\n                        ></div>\n                    </div>\n                    <span className=\"progress-text\">{percentage.toFixed(0)}%</span>\n                </div>\n            </div>\n        </div>\n    );\n};\n\n// Enhanced experience comparison component\nconst renderExperienceComparison = (required, actual, label) => {\n    const isMet = actual >= required;\n    const percentage = Math.min((actual / required) * 100, 100);\n    \n    return (\n        <div className=\"experience-comparison-modern\">\n            <div className=\"experience-header\">\n                <div className=\"experience-icon\">\n                    {label.includes('Patient Care') ? '🏥' : \n                     label.includes('Shadowing') ? '👨‍⚕️' : '💼'}\n                </div>\n                <h4>{label}</h4>\n                <div className={`experience-status ${isMet ? 'met' : 'not-met'}`}>\n                    {isMet ? '✅' : '❌'}\n                </div>\n            </div>\n            <div className=\"experience-details\">\n                <div className=\"experience-values\">\n                    <span className=\"required\">Required: {required} hrs</span>\n                    <span className=\"actual\">Your Hours: {actual} hrs</span>\n                </div>\n                <div className=\"experience-progress\">\n                    <div className=\"progress-bar\">\n                        <div \n                            className=\"progress-fill\" \n                            style={{ width: `${percentage}%` }}\n                        ></div>\n                    </div>\n                    <span className=\"progress-text\">{percentage.toFixed(0)}%</span>\n                </div>\n            </div>\n        </div>\n    );\n};\n\nconst toggleSection = (section) => {\n    setExpandedSections(prev => ({\n        ...prev,\n        [section]: !prev[section]\n    }));\n};\n\nconst toggleProgram = (programName) => {\n    setExpandedPrograms(prev => ({\n        ...prev,\n        [programName]: !prev[programName]\n    }));\n};\n\nconst setActiveTab = (programName, tabName) => {\n    setActiveTabs(prev => ({\n        ...prev,\n        [programName]: tabName\n    }));\n};\n\n// Add GPA calculation helper function\nconst calculateOverallGPAs = (prerequisites) => {\n    let totalPoints = 0;\n    let totalCredits = 0;\n    let sciencePoints = 0;\n    let scienceCredits = 0;\n\n    // Iterate through all prerequisites and their courses\n    Object.values(prerequisites || {}).forEach(programPrereqs => {\n        Object.values(programPrereqs || {}).forEach(prereq => {\n            (prereq.courses || []).forEach(course => {\n                if (!['F', 'W', 'U'].includes(course.grade)) {\n                    const credits = parseFloat(course.credits) || 0;\n                    const gradePoints = gradeToPoints(course.grade);\n                    \n                    if (!isNaN(credits) && !isNaN(gradePoints)) {\n                        totalPoints += credits * gradePoints;\n                        totalCredits += credits;\n\n                        if (course.is_science) {\n                            sciencePoints += credits * gradePoints;\n                            scienceCredits += credits;\n                        }\n                    }\n                }\n            });\n        });\n    });\n\n    return {\n        cumulativeGPA: totalCredits > 0 ? (totalPoints / totalCredits).toFixed(2) : 'N/A',\n        scienceGPA: scienceCredits > 0 ? (sciencePoints / scienceCredits).toFixed(2) : 'N/A'\n    };\n};\n\n// Function to manually approve a program\nconst handleManualApproval = (programName) => {\n    // Add program to manually approved list if not already there\n    if (!manuallyApprovedPrograms.includes(programName)) {\n        const updatedApprovedPrograms = [...manuallyApprovedPrograms, programName];\n        setManuallyApprovedPrograms(updatedApprovedPrograms);\n        \n        // Save the updated results to the database\n        const updatedResults = {\n            programs: programs,\n            userTranscript: userTranscript,\n            profileData: profileData,\n            manuallyApprovedPrograms: updatedApprovedPrograms\n        };\n        saveCurrentResults(updatedResults);\n    }\n};\n\n// Function to remove manual approval\nconst handleRemoveApproval = (programName) => {\n    // Remove program from manually approved list\n    const updatedApprovedPrograms = manuallyApprovedPrograms.filter(name => name !== programName);\n    setManuallyApprovedPrograms(updatedApprovedPrograms);\n    \n    // Save the updated results to the database\n    const updatedResults = {\n        programs: programs,\n        userTranscript: userTranscript,\n        profileData: profileData,\n        manuallyApprovedPrograms: updatedApprovedPrograms\n    };\n    saveCurrentResults(updatedResults);\n};\n\n// Get unique states from programs for filtering\nconst getUniqueStates = () => {\n    if (!programs) return [];\n    const states = new Set();\n    programs.forEach(program => {\n        if (program.school?.state) {\n            states.add(program.school.state.toUpperCase());\n        }\n    });\n    return Array.from(states).sort();\n};\n\n// Filter and sort programs based on current settings\nconst getFilteredAndSortedPrograms = () => {\n    if (!programs || !userTranscript) return [];\n    \n    let filteredPrograms = [...programs];\n    \n    // Filter by search term\n    if (searchTerm) {\n        filteredPrograms = filteredPrograms.filter(program => \n            program.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n            program.school?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n            program.school?.city?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n            program.school?.state?.toLowerCase().includes(searchTerm.toLowerCase())\n        );\n    }\n    \n    // Filter by eligibility\n    if (filterBy !== 'all') {\n        filteredPrograms = filteredPrograms.filter(program => \n            programEligibilityCache[program.name] === filterBy\n        );\n    }\n    \n    // Filter by region\n    if (regionFilter !== 'all') {\n        filteredPrograms = filteredPrograms.filter(program => {\n            const programState = program.school?.state;\n            if (!programState) return false;\n            const programRegion = getStateRegion(programState);\n            return programRegion === regionFilter;\n        });\n    }\n    \n    // Filter by specific state\n    if (stateFilter !== 'all') {\n        filteredPrograms = filteredPrograms.filter(program => {\n            const programState = program.school?.state;\n            return programState?.toUpperCase() === stateFilter.toUpperCase();\n        });\n    }\n    \n    // Sort programs\n    filteredPrograms.sort((a, b) => {\n        switch (sortBy) {\n            case 'name':\n                return a.name.localeCompare(b.name);\n            case 'gpa':\n                return (b.requirements?.gpa?.overall || 0) - (a.requirements?.gpa?.overall || 0);\n            case 'eligibility':\n                const eligibilityOrder = { 'eligible': 0, 'ineligible': 1, 'incomplete': 2 };\n                return eligibilityOrder[programEligibilityCache[a.name]] - eligibilityOrder[programEligibilityCache[b.name]];\n            case 'location':\n                const stateA = a.school?.state || '';\n                const stateB = b.school?.state || '';\n                if (stateA !== stateB) return stateA.localeCompare(stateB);\n                return (a.school?.city || '').localeCompare(b.school?.city || '');\n            default:\n                return 0;\n        }\n    });\n    \n    return filteredPrograms;\n};\n\n// Clean program list item component matching Figma design\nconst ProgramListItem = ({ program, isExpanded, onToggle }) => {\n    const eligibility = programEligibilityCache[program.name];\n    const isManuallyApproved = manuallyApprovedPrograms.includes(program.name);\n    \n    // Calculate prerequisites completion\n    const totalPrereqs = Object.keys(program.requirements?.prerequisites || {}).length;\n    const completedPrereqs = Object.entries(program.requirements?.prerequisites || {}).filter(([prereqName, req]) => {\n        const programPrereqs = userTranscript?.prerequisites?.[program.name];\n        if (!programPrereqs?.[prereqName]) return false;\n        const totalCredits = programPrereqs[prereqName].totalCredits || 0;\n        const requiredCreditsNum = parseFloat(req.credits);\n        return !isNaN(requiredCreditsNum) ? totalCredits >= requiredCreditsNum : totalCredits > 0;\n    }).length;\n    \n    // Calculate completion percentage\n    const completionPercentage = totalPrereqs > 0 ? Math.round((completedPrereqs / totalPrereqs) * 100) : 100;\n    \n    return (\n        <div className=\"program-list-item\" onClick={onToggle}>\n            <div className=\"program-item-header\">\n                <div className=\"program-main-info\">\n                    <h3 className=\"program-name\">\n                        <a \n                            href={program.program_url} \n                            target=\"_blank\" \n                            rel=\"noopener noreferrer\"\n                            onClick={(e) => e.stopPropagation()}\n                        >\n                            {program.name}\n                        </a>\n                    </h3>\n                    <div className=\"program-location\">\n                        {program.school?.city && program.school?.state \n                            ? `${program.school.city}, ${program.school.state}` \n                            : program.school?.name || 'Unknown Location'\n                        }\n                    </div>\n                </div>\n                \n                <div className=\"program-stats\">\n                    <div className=\"stat-group\">\n                        <span className=\"stat-label\">Min GPA:</span>\n                        <span className=\"stat-value\">{program.requirements?.gpa?.overall?.toFixed(1) || 'N/A'}</span>\n                    </div>\n                    \n                    <div className=\"stat-group\">\n                        <span className=\"stat-label\">Prerequisites:</span>\n                        <span className=\"stat-value\">{completedPrereqs}/{totalPrereqs}</span>\n                    </div>\n                    \n                    <div className=\"stat-group completion\">\n                        <span className=\"stat-label\">Completion:</span>\n                        <div className=\"completion-bar\">\n                            <div \n                                className=\"completion-fill\" \n                                style={{ width: `${completionPercentage}%` }}\n                            ></div>\n                        </div>\n                    </div>\n                </div>\n                \n                <div className=\"program-status\">\n                    <div className={`status-badge ${eligibility}`}>\n                        {eligibility === 'eligible' ? 'Eligible' : \n                         eligibility === 'ineligible' ? 'Need Prerequisites' : \n                         'Incomplete'}\n                    </div>\n                    <span className=\"expand-arrow\">▸</span>\n                </div>\n            </div>\n            \n            {isExpanded && (\n                <div className=\"program-item-details\">\n                    {/* Program Information Section */}\n                    <div className=\"program-info-section\">\n                        <div className=\"info-tabs\">\n                            <div \n                                className={`tab ${(activeTabs[program.name] || 'overview') === 'overview' ? 'active' : ''}`}\n                                onClick={(e) => {\n                                    e.stopPropagation();\n                                    setActiveTab(program.name, 'overview');\n                                }}\n                            >\n                                Overview\n                            </div>\n                            <div \n                                className={`tab ${activeTabs[program.name] === 'requirements' ? 'active' : ''}`}\n                                onClick={(e) => {\n                                    e.stopPropagation();\n                                    setActiveTab(program.name, 'requirements');\n                                }}\n                            >\n                                Requirements\n                            </div>\n                        </div>\n                        \n                        <div className=\"tab-content\">\n                            {/* Overview Tab */}\n                            {(activeTabs[program.name] || 'overview') === 'overview' && (\n                                <div className=\"program-overview\">\n                                    {/* Quick Stats Cards */}\n                                    <div className=\"quick-stats-grid\">\n                                        <div className=\"stat-card\">\n                                            <div className=\"stat-icon\">🎓</div>\n                                            <div className=\"stat-content\">\n                                                <div className=\"stat-label\">Minimum GPA</div>\n                                                <div className=\"stat-value\">{program.requirements?.gpa?.overall?.toFixed(2) || 'N/A'}</div>\n                                            </div>\n                                        </div>\n                                        \n                                        <div className=\"stat-card\">\n                                            <div className=\"stat-icon\">🔬</div>\n                                            <div className=\"stat-content\">\n                                                <div className=\"stat-label\">Science GPA</div>\n                                                <div className=\"stat-value\">{program.requirements?.gpa?.science?.toFixed(2) || 'N/A'}</div>\n                                            </div>\n                                        </div>\n                                        \n                                        <div className=\"stat-card\">\n                                            <div className=\"stat-icon\">🏥</div>\n                                            <div className=\"stat-content\">\n                                                <div className=\"stat-label\">Patient Care Hours</div>\n                                                <div className=\"stat-value\">{program.requirements?.experience?.['Patient Care Hours'] || 0}</div>\n                                            </div>\n                                        </div>\n                                        \n                                        <div className=\"stat-card\">\n                                            <div className=\"stat-icon\">👩‍⚕️</div>\n                                            <div className=\"stat-content\">\n                                                <div className=\"stat-label\">Shadowing Hours</div>\n                                                <div className=\"stat-value\">{program.requirements?.experience?.['Shadowing Hours'] || 0}</div>\n                                            </div>\n                                        </div>\n                                    </div>\n\n                                    {/* Enhanced Program Statistics */}\n                                    {program.program_data && (\n                                        <div className=\"enhanced-stats-section\">\n                                            <h4>📊 Program Statistics</h4>\n                                            <div className=\"enhanced-stats-grid\">\n                                                {/* Class Profile Stats */}\n                                                {program.program_data.class_profile && Object.keys(program.program_data.class_profile).length > 0 && (\n                                                    <>\n                                                        {program.program_data.class_profile.total_applications && program.program_data.class_profile.total_applications !== 'No information provided' && (\n                                                            <div className=\"enhanced-stat-card\">\n                                                                <div className=\"stat-icon\">📝</div>\n                                                                <div className=\"stat-content\">\n                                                                    <div className=\"stat-label\">Applications</div>\n                                                                    <div className=\"stat-value\">{program.program_data.class_profile.total_applications}</div>\n                                                                </div>\n                                                            </div>\n                                                        )}\n\n                                                        {program.program_data.class_profile.total_matriculants && program.program_data.class_profile.total_matriculants !== 'No information provided' && (\n                                                            <div className=\"enhanced-stat-card\">\n                                                                <div className=\"stat-icon\">🎓</div>\n                                                                <div className=\"stat-content\">\n                                                                    <div className=\"stat-label\">Matriculants</div>\n                                                                    <div className=\"stat-value\">{program.program_data.class_profile.total_matriculants}</div>\n                                                                </div>\n                                                            </div>\n                                                        )}\n\n                                                        {program.program_data.class_profile.average_overall_gpa && program.program_data.class_profile.average_overall_gpa !== 'No information provided' && (\n                                                            <div className=\"enhanced-stat-card\">\n                                                                <div className=\"stat-icon\">📈</div>\n                                                                <div className=\"stat-content\">\n                                                                    <div className=\"stat-label\">Avg Overall GPA</div>\n                                                                    <div className=\"stat-value\">{program.program_data.class_profile.average_overall_gpa}</div>\n                                                                </div>\n                                                            </div>\n                                                        )}\n\n                                                        {program.program_data.class_profile.average_science_gpa && program.program_data.class_profile.average_science_gpa !== 'No information provided' && (\n                                                            <div className=\"enhanced-stat-card\">\n                                                                <div className=\"stat-icon\">🔬</div>\n                                                                <div className=\"stat-content\">\n                                                                    <div className=\"stat-label\">Avg Science GPA</div>\n                                                                    <div className=\"stat-value\">{program.program_data.class_profile.average_science_gpa}</div>\n                                                                </div>\n                                                            </div>\n                                                        )}\n                                                    </>\n                                                )}\n\n                                                {/* PANCE Pass Rate */}\n                                                {program.program_data.pance_pass_rates && Object.keys(program.program_data.pance_pass_rates).length > 0 && (\n                                                    <>\n                                                        {program.program_data.pance_pass_rates.program_pass_rate && program.program_data.pance_pass_rates.program_pass_rate !== 'No information provided' && (\n                                                            <div className=\"enhanced-stat-card pance-stat\">\n                                                                <div className=\"stat-icon\">🏆</div>\n                                                                <div className=\"stat-content\">\n                                                                    <div className=\"stat-label\">PANCE Pass Rate</div>\n                                                                    <div className=\"stat-value\">{program.program_data.pance_pass_rates.program_pass_rate}</div>\n                                                                    {program.program_data.pance_pass_rates.national_pass_rate && program.program_data.pance_pass_rates.national_pass_rate !== 'No information provided' && (\n                                                                        <div className=\"stat-subtext\">National: {program.program_data.pance_pass_rates.national_pass_rate}</div>\n                                                                    )}\n                                                                </div>\n                                                            </div>\n                                                        )}\n\n                                                        {program.program_data.pance_pass_rates.candidates_took_pance && (\n                                                            <div className=\"enhanced-stat-card\">\n                                                                <div className=\"stat-icon\">👥</div>\n                                                                <div className=\"stat-content\">\n                                                                    <div className=\"stat-label\">PANCE Candidates</div>\n                                                                    <div className=\"stat-value\">{program.program_data.pance_pass_rates.candidates_took_pance}</div>\n                                                                </div>\n                                                            </div>\n                                                        )}\n                                                    </>\n                                                )}\n\n                                                {/* Attrition Data */}\n                                                {program.program_data.attrition && Object.keys(program.program_data.attrition).length > 0 && (\n                                                    <>\n                                                        {program.program_data.attrition.graduation_rate && program.program_data.attrition.graduation_rate !== 'No information provided' && (\n                                                            <div className=\"enhanced-stat-card\">\n                                                                <div className=\"stat-icon\">🎯</div>\n                                                                <div className=\"stat-content\">\n                                                                    <div className=\"stat-label\">Graduation Rate</div>\n                                                                    <div className=\"stat-value\">{program.program_data.attrition.graduation_rate}</div>\n                                                                </div>\n                                                            </div>\n                                                        )}\n\n                                                        {program.program_data.attrition.entering_class_size && (\n                                                            <div className=\"enhanced-stat-card\">\n                                                                <div className=\"stat-icon\">👨‍🎓</div>\n                                                                <div className=\"stat-content\">\n                                                                    <div className=\"stat-label\">Class Size</div>\n                                                                    <div className=\"stat-value\">{program.program_data.attrition.entering_class_size}</div>\n                                                                </div>\n                                                            </div>\n                                                        )}\n                                                    </>\n                                                )}\n\n                                                {/* Tuition Information */}\n                                                {program.program_data.tuition_deposits && program.program_data.tuition_deposits.tuition && program.program_data.tuition_deposits.tuition !== 'No information provided' && (\n                                                    <div className=\"enhanced-stat-card tuition-stat\">\n                                                        <div className=\"stat-icon\">💰</div>\n                                                        <div className=\"stat-content\">\n                                                            <div className=\"stat-label\">Tuition</div>\n                                                            <div className=\"stat-value\">{program.program_data.tuition_deposits.tuition}</div>\n                                                        </div>\n                                                    </div>\n                                                )}\n                                            </div>\n                                        </div>\n                                    )}\n\n                                    {/* Program Details */}\n                                    <div className=\"program-details-grid\">\n                                        <div className=\"detail-section\">\n                                            <h4>📍 Program Information</h4>\n                                            <div className=\"detail-items\">\n                                                <div className=\"detail-item\">\n                                                    <span className=\"detail-label\">School:</span>\n                                                    <span className=\"detail-value\">{program.school?.name || 'N/A'}</span>\n                                                </div>\n                                                <div className=\"detail-item\">\n                                                    <span className=\"detail-label\">Location:</span>\n                                                    <span className=\"detail-value\">\n                                                        {program.school?.city && program.school?.state \n                                                            ? `${program.school.city}, ${program.school.state}` \n                                                            : 'N/A'\n                                                        }\n                                                    </span>\n                                                </div>\n                                                <div className=\"detail-item\">\n                                                    <span className=\"detail-label\">Program Website:</span>\n                                                    <a \n                                                        href={program.program_url} \n                                                        target=\"_blank\" \n                                                        rel=\"noopener noreferrer\"\n                                                        className=\"detail-link\"\n                                                    >\n                                                        View Program Details\n                                                    </a>\n                                                </div>\n                                            </div>\n                                        </div>\n                                        \n                                        <div className=\"detail-section\">\n                                            <h4>🏆 Your Eligibility</h4>\n                                            <div className=\"eligibility-overview\">\n                                                <div className=\"eligibility-item\">\n                                                    <span className=\"eligibility-label\">Overall GPA:</span>\n                                                    <div className=\"eligibility-status\">\n                                                        <span>{userTranscript?.calculated_gpa?.toFixed(2) || 'N/A'}</span>\n                                                        <span className={`status ${userTranscript?.calculated_gpa >= program.requirements?.gpa?.overall ? 'met' : 'not-met'}`}>\n                                                            {userTranscript?.calculated_gpa >= program.requirements?.gpa?.overall ? '✓' : '✗'}\n                                                        </span>\n                                                    </div>\n                                                </div>\n                                                <div className=\"eligibility-item\">\n                                                    <span className=\"eligibility-label\">Science GPA:</span>\n                                                    <div className=\"eligibility-status\">\n                                                        <span>{userTranscript?.science_gpa?.toFixed(2) || 'N/A'}</span>\n                                                        <span className={`status ${userTranscript?.science_gpa >= program.requirements?.gpa?.science ? 'met' : 'not-met'}`}>\n                                                            {userTranscript?.science_gpa >= program.requirements?.gpa?.science ? '✓' : '✗'}\n                                                        </span>\n                                                    </div>\n                                                </div>\n                                                <div className=\"eligibility-item\">\n                                                    <span className=\"eligibility-label\">Prerequisites:</span>\n                                                    <div className=\"eligibility-status\">\n                                                        <span>{completedPrereqs}/{totalPrereqs} completed</span>\n                                                        <span className={`status ${completedPrereqs === totalPrereqs ? 'met' : 'not-met'}`}>\n                                                            {completedPrereqs === totalPrereqs ? '✓' : '✗'}\n                                                        </span>\n                                                    </div>\n                                                </div>\n                                            </div>\n                                        </div>\n                                    </div>\n                                </div>\n                            )}\n\n                            {/* Requirements Tab */}\n                            {activeTabs[program.name] === 'requirements' && (\n                                <div className=\"requirements-tab\">\n                                    {/* Prerequisites Section */}\n                                    <div className=\"prerequisites-section\">\n                                        <h4>📚 Prerequisites Details</h4>\n                                        <div className=\"prerequisites-list\">\n                                            {Object.entries(program.requirements?.prerequisites || {}).map(([course, req]) => {\n                                                const programPrereqs = userTranscript?.prerequisites?.[program.name];\n                                                const prereqData = programPrereqs?.[course];\n                                                const totalCredits = prereqData?.totalCredits || 0;\n                                                const requiredCreditsNum = parseFloat(req.credits);\n                                                const isMet = !isNaN(requiredCreditsNum) ? totalCredits >= requiredCreditsNum : totalCredits > 0;\n                                                const matchedCourses = prereqData?.courses || [];\n                                                \n                                                // Sort courses by grade (best grades first) and separate used vs unused\n                                                const sortedCourses = [...matchedCourses].sort((a, b) => {\n                                                    const gradeA = gradeToPoints(a.grade);\n                                                    const gradeB = gradeToPoints(b.grade);\n                                                    if (gradeB !== gradeA) return gradeB - gradeA;\n                                                    return parseInt(b.year) - parseInt(a.year);\n                                                });\n                                                \n                                                // Determine which courses are actually being used in calculation\n                                                const usedCourses = [];\n                                                const unusedCourses = [];\n                                                let creditsUsed = 0;\n                                                const requiredCredits = parseFloat(req.credits) || 0;\n                                                \n                                                sortedCourses.forEach(course => {\n                                                    const courseCredits = parseFloat(course.credits) || 0;\n                                                    if (creditsUsed < requiredCredits) {\n                                                        usedCourses.push(course);\n                                                        creditsUsed += courseCredits;\n                                                    } else {\n                                                        unusedCourses.push(course);\n                                                    }\n                                                });\n                                                \n                                                return (\n                                                    <div key={course} className=\"prerequisite-item-detailed\">\n                                                        <div className=\"prerequisite-header\">\n                                                            <div className=\"prerequisite-info\">\n                                                                <span className=\"course-name\">{course} ({req.credits} credits)</span>\n                                                                {req.lab_required && <span className=\"lab-required\">Lab Required</span>}\n                                                            </div>\n                                                            <span className={`status ${isMet ? 'met' : 'not-met'}`}>\n                                                                {isMet ? '✓' : '✗'}\n                                                            </span>\n                                                        </div>\n                                                        {matchedCourses.length > 0 && (\n                                                            <div className=\"matched-courses\">\n                                                                {/* Used courses section - shown at top */}\n                                                                {usedCourses.length > 0 && (\n                                                                    <div className=\"used-courses-section\">\n                                                                        <div className=\"courses-header used\">\n                                                                            <span>✓ Used for calculation:</span>\n                                                                            <span className=\"credits-summary\">{creditsUsed.toFixed(1)} / {req.credits} credits</span>\n                                                                        </div>\n                                                                        <div className=\"course-cards\">\n                                                                            {usedCourses.map((matchedCourse, index) => (\n                                                                                <div key={index} className=\"course-card used\">\n                                                                                    <div className=\"course-details\">\n                                                                                        <span className=\"course-code-name\">\n                                                                                            {matchedCourse.code} - {matchedCourse.name}\n                                                                                        </span>\n                                                                                        <span className=\"course-meta\">\n                                                                                            {matchedCourse.institution} • {matchedCourse.term} {matchedCourse.year}\n                                                                                        </span>\n                                                                                    </div>\n                                                                                    <div className=\"course-stats\">\n                                                                                        <span className=\"course-credits\">{matchedCourse.credits} credits</span>\n                                                                                        <span className={`course-grade grade-${matchedCourse.grade?.replace(/[+\\-]/, '')}`}>\n                                                                                            {matchedCourse.grade}\n                                                                                        </span>\n                                                                                    </div>\n                                                                                </div>\n                                                                            ))}\n                                                                        </div>\n                                                                    </div>\n                                                                )}\n                                                                \n                                                                {/* Unused courses section - shown at bottom */}\n                                                                {unusedCourses.length > 0 && (\n                                                                    <div className=\"unused-courses-section\">\n                                                                        <div className=\"courses-header unused\">\n                                                                            <span>Other matching courses (not used):</span>\n                                                                            <span className=\"courses-count\">{unusedCourses.length} course{unusedCourses.length !== 1 ? 's' : ''}</span>\n                                                                        </div>\n                                                                        <div className=\"course-cards\">\n                                                                            {unusedCourses.map((matchedCourse, index) => (\n                                                                                <div key={index} className=\"course-card unused\">\n                                                                                    <div className=\"course-details\">\n                                                                                        <span className=\"course-code-name\">\n                                                                                            {matchedCourse.code} - {matchedCourse.name}\n                                                                                        </span>\n                                                                                        <span className=\"course-meta\">\n                                                                                            {matchedCourse.institution} • {matchedCourse.term} {matchedCourse.year}\n                                                                                        </span>\n                                                                                    </div>\n                                                                                    <div className=\"course-stats\">\n                                                                                        <span className=\"course-credits\">{matchedCourse.credits} credits</span>\n                                                                                        <span className={`course-grade grade-${matchedCourse.grade?.replace(/[+\\-]/, '')}`}>\n                                                                                            {matchedCourse.grade}\n                                                                                        </span>\n                                                                                    </div>\n                                                                                </div>\n                                                                            ))}\n                                                                        </div>\n                                                                    </div>\n                                                                )}\n                                                            </div>\n                                                        )}\n                                                        {matchedCourses.length === 0 && !isMet && (\n                                                            <div className=\"no-courses\">\n                                                                <span className=\"no-courses-text\">No matching courses found</span>\n                                                            </div>\n                                                        )}\n                                                    </div>\n                                                );\n                                            })}\n                                        </div>\n                                    </div>\n                                </div>\n                            )}\n\n\n                        </div>\n\n                    </div>\n                </div>\n            )}\n        </div>\n    );\n};\n\nreturn (\n    <div className=\"pamatch-container\">\n        <header className=\"pamatch-header\">\n            <div className=\"header-left\">\n                <div className=\"header-title\">\n                    <span className=\"header-icon\">📖</span>\n                    <h1>PA Program Explorer</h1>\n                </div>\n            </div>\n            <div className=\"header-right\">\n                <button \n                    onClick={() => navigate('/home')}\n                    className=\"back-home-btn\"\n                >\n                    Back to Home\n                </button>\n            </div>\n        </header>\n\n        {/* Add saved results indicator */}\n        {usingSavedResults && savedResultsTimestamp && (\n            <div className=\"saved-results-banner\">\n                <span>\n                    Using saved results from {new Date(savedResultsTimestamp).toLocaleDateString()} at {new Date(savedResultsTimestamp).toLocaleTimeString()}\n                </span>\n                <button \n                    onClick={handleRecalculate}\n                    className=\"recalculate-btn\"\n                    disabled={isLoading || savingResults}\n                >\n                    {isLoading ? 'Calculating...' : 'Recalculate'}\n                </button>\n            </div>\n        )}\n\n        {/* View Controls */}\n        {!isLoading && userTranscript && (\n            <>\n                <div className=\"search-and-controls\">\n                    <div className=\"search-bar\">\n                        <span className=\"search-icon\">🔍</span>\n                        <input\n                            type=\"text\"\n                            placeholder=\"Search programs or schools...\"\n                            value={searchTerm}\n                            onChange={(e) => setSearchTerm(e.target.value)}\n                            className=\"search-input\"\n                        />\n                    </div>\n                    \n                    <div className=\"filter-controls\">\n                        <div className=\"filter-dropdown\">\n                            <span className=\"filter-icon\">⚡</span>\n                            <select\n                                value={filterBy}\n                                onChange={(e) => setFilterBy(e.target.value)}\n                                className=\"filter-select\"\n                            >\n                                <option value=\"all\">All Programs</option>\n                                <option value=\"eligible\">Eligible Only</option>\n                                <option value=\"ineligible\">Need Prerequisites</option>\n                                <option value=\"incomplete\">Incomplete Info</option>\n                            </select>\n                        </div>\n                        \n                        <div className=\"region-dropdown\">\n                            <span className=\"location-icon\">📍</span>\n                            <select\n                                value={regionFilter}\n                                onChange={(e) => {\n                                    setRegionFilter(e.target.value);\n                                    if (e.target.value !== 'all') setStateFilter('all'); // Reset state filter when region is selected\n                                }}\n                                className=\"region-select\"\n                            >\n                                <option value=\"all\">All Regions</option>\n                                <optgroup label=\"Census Regions\">\n                                    <option value=\"Northeast\">Northeast (9 states)</option>\n                                    <option value=\"Midwest\">Midwest (12 states)</option>\n                                    <option value=\"South\">South (17 states)</option>\n                                    <option value=\"West\">West (13 states)</option>\n                                </optgroup>\n                                <optgroup label=\"Census Divisions\">\n                                    <option value=\"New England\">New England (6 states)</option>\n                                    <option value=\"Mid-Atlantic\">Mid-Atlantic (3 states)</option>\n                                    <option value=\"East North Central\">East North Central (5 states)</option>\n                                    <option value=\"West North Central\">West North Central (7 states)</option>\n                                    <option value=\"South Atlantic\">South Atlantic (9 states)</option>\n                                    <option value=\"East South Central\">East South Central (4 states)</option>\n                                    <option value=\"West South Central\">West South Central (4 states)</option>\n                                    <option value=\"Mountain\">Mountain (8 states)</option>\n                                    <option value=\"Pacific\">Pacific (5 states)</option>\n                                </optgroup>\n                            </select>\n                        </div>\n                        \n                        <div className=\"state-dropdown\">\n                            <select\n                                value={stateFilter}\n                                onChange={(e) => {\n                                    setStateFilter(e.target.value);\n                                    if (e.target.value !== 'all') setRegionFilter('all'); // Reset region filter when state is selected\n                                }}\n                                className=\"state-select\"\n                            >\n                                <option value=\"all\">All States</option>\n                                {getUniqueStates().map(state => (\n                                    <option key={state} value={state}>{state}</option>\n                                ))}\n                            </select>\n                        </div>\n                        \n                        <div className=\"sort-dropdown\">\n                            <select\n                                value={sortBy}\n                                onChange={(e) => setSortBy(e.target.value)}\n                                className=\"sort-select\"\n                            >\n                                <option value=\"name\">Name</option>\n                                <option value=\"gpa\">GPA</option>\n                                <option value=\"eligibility\">Eligibility</option>\n                                <option value=\"location\">Location</option>\n                            </select>\n                        </div>\n                        \n                        <div className=\"view-toggle\">\n                            <button\n                                className={`view-btn ${viewMode === 'accordion' ? 'active' : ''}`}\n                                onClick={() => setViewMode('accordion')}\n                                title=\"List View\"\n                            >\n                                ☰\n                            </button>\n                            <button\n                                className={`view-btn ${viewMode === 'grid' ? 'active' : ''}`}\n                                onClick={() => setViewMode('grid')}\n                                title=\"Grid View\"\n                            >\n                                ⊞\n                            </button>\n                        </div>\n                    </div>\n                </div>\n\n                {/* Program Summary Cards */}\n                <div className=\"program-summary-cards\">\n                    <div \n                        className={`summary-card total ${filterBy === 'all' ? 'active' : ''}`}\n                        onClick={() => setFilterBy('all')}\n                    >\n                        <div className=\"card-content\">\n                            <div className=\"card-info\">\n                                <div className=\"card-title\">Total Programs</div>\n                                <div className=\"card-number\">{programs.length}</div>\n                            </div>\n                            <div className=\"card-icon\">📖</div>\n                        </div>\n                    </div>\n                    \n                    <div \n                        className={`summary-card eligible ${filterBy === 'eligible' ? 'active' : ''}`}\n                        onClick={() => setFilterBy('eligible')}\n                    >\n                        <div className=\"card-content\">\n                            <div className=\"card-info\">\n                                <div className=\"card-title\">Eligible</div>\n                                <div className=\"card-number\">{programCounts.eligible}</div>\n                            </div>\n                            <div className=\"card-icon\">✓</div>\n                        </div>\n                    </div>\n                    \n                    <div \n                        className={`summary-card need-prerequisites ${filterBy === 'ineligible' ? 'active' : ''}`}\n                        onClick={() => setFilterBy('ineligible')}\n                    >\n                        <div className=\"card-content\">\n                            <div className=\"card-info\">\n                                <div className=\"card-title\">Need Prerequisites</div>\n                                <div className=\"card-number\">{programCounts.ineligible}</div>\n                            </div>\n                            <div className=\"card-icon\">⚠</div>\n                        </div>\n                    </div>\n                    \n                    <div \n                        className={`summary-card incomplete ${filterBy === 'incomplete' ? 'active' : ''}`}\n                        onClick={() => setFilterBy('incomplete')}\n                    >\n                        <div className=\"card-content\">\n                            <div className=\"card-info\">\n                                <div className=\"card-title\">Incomplete</div>\n                                <div className=\"card-number\">{programCounts.incomplete}</div>\n                            </div>\n                            <div className=\"card-icon\">🕐</div>\n                        </div>\n                    </div>\n                </div>\n\n                {/* Results Count */}\n                <div className=\"results-info\">\n                    <span className=\"results-count\">\n                        Showing {getFilteredAndSortedPrograms().length} of {programs.length} programs\n                        {(filterBy !== 'all' || regionFilter !== 'all' || stateFilter !== 'all') && (\n                            <span className=\"filter-indicator\">\n                                {' '}• Filtered by: {[\n                                    filterBy !== 'all' ? (\n                                        filterBy === 'eligible' ? 'Eligible' :\n                                        filterBy === 'ineligible' ? 'Need Prerequisites' :\n                                        filterBy === 'incomplete' ? 'Incomplete' : ''\n                                    ) : null,\n                                    regionFilter !== 'all' ? `${regionFilter} Region` : null,\n                                    stateFilter !== 'all' ? `${stateFilter} State` : null\n                                ].filter(Boolean).join(', ')}\n                            </span>\n                        )}\n                    </span>\n                    {(filterBy !== 'all' || regionFilter !== 'all' || stateFilter !== 'all') && (\n                        <button \n                            className=\"clear-filter-btn\"\n                            onClick={() => {\n                                setFilterBy('all');\n                                setRegionFilter('all');\n                                setStateFilter('all');\n                            }}\n                        >\n                            Clear All Filters\n                        </button>\n                    )}\n                </div>\n            </>\n        )}\n\n        <div className=\"academic-summary\">\n            <div className=\"summary-section-header\">\n                <span className=\"section-icon\">👤</span>\n                <h2>Academic Summary</h2>\n            </div>\n            {isLoading ? (\n                <div className=\"gpa-cards-container\">\n                    <div className=\"gpa-card cumulative-gpa loading\">\n                        <div className=\"card-header\">\n                            <span className=\"card-label\">Cumulative GPA</span>\n                            <span className=\"card-icon\">📊</span>\n                        </div>\n                        <div className=\"gpa-value skeleton-loader\"></div>\n                    </div>\n                    <div className=\"gpa-card science-gpa loading\">\n                        <div className=\"card-header\">\n                            <span className=\"card-label\">Science GPA</span>\n                            <span className=\"card-icon\">📊</span>\n                        </div>\n                        <div className=\"gpa-value skeleton-loader\"></div>\n                    </div>\n                </div>\n            ) : (\n                <div className=\"gpa-cards-container\">\n                    <div className=\"gpa-card cumulative-gpa\">\n                        <div className=\"card-header\">\n                            <span className=\"card-label\">Cumulative GPA</span>\n                            <span className=\"card-icon\">📊</span>\n                        </div>\n                        <div className=\"gpa-value\">{userTranscript?.calculated_gpa?.toFixed(2) || 'N/A'}</div>\n                    </div>\n                    <div className=\"gpa-card science-gpa\">\n                        <div className=\"card-header\">\n                            <span className=\"card-label\">Science GPA</span>\n                            <span className=\"card-icon\">📊</span>\n                        </div>\n                        <div className=\"gpa-value\">{userTranscript?.science_gpa?.toFixed(2) || 'N/A'}</div>\n                    </div>\n                </div>\n            )}\n        </div>\n\n        <div className=\"results-container\">\n            {isLoading && (\n                <div className=\"programs-comparison\">\n                    <div className=\"eligible-programs\">\n                        <div className=\"section-header\">\n                            <h2>Eligible Programs</h2>\n                        </div>\n                        <ProgramSkeleton />\n                        <ProgramSkeleton />\n                    </div>\n                    <div className=\"ineligible-programs\">\n                        <div className=\"section-header\">\n                            <h2>Programs Requiring Additional Prerequisites</h2>\n                        </div>\n                        <ProgramSkeleton />\n                        <ProgramSkeleton />\n                    </div>\n                    <div className=\"incomplete-programs\">\n                        <div className=\"section-header\">\n                            <h2>Incomplete/Unknown Eligibility</h2>\n                        </div>\n                        <ProgramSkeleton />\n                    </div>\n                </div>\n            )}\n\n            {error && (\n                <div className=\"error-message\">\n                    {error}\n                </div>\n            )}\n\n            {userTranscript && !isLoading && (\n                <div className=\"programs-list\">\n                    {getFilteredAndSortedPrograms().map((program, index) => (\n                        <ProgramListItem\n                            key={`${program.name}-${index}`}\n                            program={program}\n                            isExpanded={expandedPrograms[program.name]}\n                            onToggle={() => toggleProgram(program.name)}\n                        />\n                    ))}\n                </div>\n            )}\n            \n            <CourseSelectionModal />\n        </div>\n    </div>\n    );\n};\n\nexport default PAMatch; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SAASC,oBAAoB,QAAQ,uBAAuB;AAC5D,OAAO,eAAe;;AAEtB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,UAAU,GAAG;EACf;EACA,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACnE,SAAS,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACnF,OAAO,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC/G,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAEtF;EACA,aAAa,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACnD,cAAc,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAClC,oBAAoB,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACpD,oBAAoB,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAChE,gBAAgB,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACxE,oBAAoB,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC9C,oBAAoB,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC9C,UAAU,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC5D,SAAS,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;AAC5C,CAAC;;AAED;AACA,MAAMC,cAAc,GAAIC,KAAK,IAAK;EAC9B,IAAI,CAACA,KAAK,EAAE,OAAO,IAAI;EACvB,MAAMC,SAAS,GAAGD,KAAK,CAACE,WAAW,CAAC,CAAC;EACrC,KAAK,MAAM,CAACC,MAAM,EAAEC,MAAM,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACR,UAAU,CAAC,EAAE;IACvD,IAAIM,MAAM,CAACG,QAAQ,CAACN,SAAS,CAAC,EAAE;MAC5B,OAAOE,MAAM;IACjB;EACJ;EACA,OAAO,OAAO;AAClB,CAAC;AAED,MAAMK,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,sBAAA,EAAAC,sBAAA;EAClBC,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC,CAAC,CAAC;EAC/C,MAAM;IAAEC;EAAM,CAAC,GAAGxB,OAAO,CAAC,CAAC;EAC3B,MAAMyB,QAAQ,GAAGxB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACyB,cAAc,EAAEC,iBAAiB,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACgC,WAAW,EAAEC,cAAc,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACkC,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoC,KAAK,EAAEC,QAAQ,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACsC,QAAQ,EAAEC,WAAW,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACwC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC0C,eAAe,EAAEC,kBAAkB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC4C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC8C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/C,QAAQ,CAAC;IACrDgD,QAAQ,EAAE,KAAK;IACfC,UAAU,EAAE,KAAK;IACjBC,UAAU,EAAE;EAChB,CAAC,CAAC;EACF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpD,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACqD,UAAU,EAAEC,aAAa,CAAC,GAAGtD,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClD;EACA,MAAM,CAACuD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACyD,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;EACxE,MAAM,CAAC2D,aAAa,EAAEC,gBAAgB,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EACzD;EACA,MAAM,CAAC6D,wBAAwB,EAAEC,2BAA2B,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;;EAE5E;EACA,MAAM,CAAC+D,QAAQ,EAAEC,WAAW,CAAC,GAAGhE,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;EACvD,MAAM,CAACiE,UAAU,EAAEC,aAAa,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmE,MAAM,EAAEC,SAAS,CAAC,GAAGpE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EAC9C,MAAM,CAACqE,QAAQ,EAAEC,WAAW,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACjD,MAAM,CAACuE,WAAW,EAAEC,cAAc,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACvD,MAAM,CAACyE,YAAY,EAAEC,eAAe,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAEzD;EACAC,SAAS,CAAC,MAAM;IACZyB,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEC,KAAK,CAAC,CAAC,CAAC;IAC5D,IAAI,CAACA,KAAK,EAAE;MACRF,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC,CAAC,CAAC;MAC9DE,QAAQ,CAAC,QAAQ,CAAC;MAClB;IACJ;EACJ,CAAC,EAAE,CAACD,KAAK,EAAEC,QAAQ,CAAC,CAAC;EAErB5B,SAAS,CAAC,MAAM;IACZyB,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAEC,KAAK,CAAC,CAAC,CAAC;;IAExE;IACA,MAAM+C,QAAQ,GAAG,MAAAA,CAAA,KAAY;MACzBxC,YAAY,CAAC,IAAI,CAAC;MAClBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI;QACA;QACA,MAAMuC,oBAAoB,CAAC,CAAC;MAChC,CAAC,CAAC,OAAOC,GAAG,EAAE;QACVnD,OAAO,CAACU,KAAK,CAAC,4CAA4C,EAAEyC,GAAG,CAAC;QAChE;QACA,MAAMC,wBAAwB,CAAC,CAAC;MACpC,CAAC,SAAS;QACN3C,YAAY,CAAC,KAAK,CAAC;MACvB;IACJ,CAAC;IAED,IAAIP,KAAK,EAAE;MACP+C,QAAQ,CAAC,CAAC;IACd;EACJ,CAAC,EAAE,CAAC/C,KAAK,EAAEC,QAAQ,CAAC,CAAC;;EAErB;EACA,MAAM+C,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACrClD,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;IAE9D,IAAI;MACA,MAAMoD,OAAO,GAAG;QACZC,aAAa,EAAG,UAASpD,KAAM,EAAC;QAChC,cAAc,EAAE;MACpB,CAAC;MAED,MAAMqD,QAAQ,GAAG,MAAM9E,KAAK,CAAC+E,GAAG,CAAE,GAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAwB,wBAAuB,EAAE;QAAEN;MAAQ,CAAC,CAAC;MAClIrD,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEsD,QAAQ,CAACK,IAAI,CAAC;MAE9D,IAAIL,QAAQ,CAACK,IAAI,CAACC,MAAM,KAAK,SAAS,EAAE;QACpC;QACA7D,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEsD,QAAQ,CAACK,IAAI,CAACE,SAAS,CAAC;QAElF,MAAMC,SAAS,GAAGR,QAAQ,CAACK,IAAI,CAACI,gBAAgB;;QAEhD;QACAnD,WAAW,CAACkD,SAAS,CAACnD,QAAQ,IAAI,EAAE,CAAC;QACrCP,iBAAiB,CAAC0D,SAAS,CAAC3D,cAAc,IAAI,IAAI,CAAC;QACnDG,cAAc,CAACwD,SAAS,CAACzD,WAAW,IAAI,IAAI,CAAC;;QAE7C;QACA,IAAIyD,SAAS,CAAC5B,wBAAwB,EAAE;UACpCC,2BAA2B,CAAC2B,SAAS,CAAC5B,wBAAwB,CAAC;QACnE;;QAEA;QACAH,wBAAwB,CAACuB,QAAQ,CAACK,IAAI,CAACE,SAAS,CAAC;QACjDhC,oBAAoB,CAAC,IAAI,CAAC;QAC1B,OAAO,IAAI;MACf,CAAC,MAAM,IAAIyB,QAAQ,CAACK,IAAI,CAACC,MAAM,KAAK,OAAO,EAAE;QACzC;QACA7D,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;QAC1E,MAAMmD,wBAAwB,CAAC,CAAC;QAChC,OAAO,KAAK;MAChB,CAAC,MAAM;QACH;QACApD,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;QACzE,MAAMmD,wBAAwB,CAAC,CAAC;QAChC,OAAO,KAAK;MAChB;IACJ,CAAC,CAAC,OAAOD,GAAG,EAAE;MACVnD,OAAO,CAACU,KAAK,CAAC,0CAA0C,EAAEyC,GAAG,CAAC;MAC9D,MAAMA,GAAG;IACb;EACJ,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IACzCpD,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC,CAAC,CAAC;IAChD6B,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC;;IAE7B,IAAI;MACA,IAAI,CAAC5B,KAAK,EAAE;QACPF,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC,CAAC,CAAC;QACvE,MAAM,IAAIgE,KAAK,CAAC,mCAAmC,CAAC;MACxD;MAEA,MAAMZ,OAAO,GAAG;QACZC,aAAa,EAAG,UAASpD,KAAM,EAAC;QAChC,cAAc,EAAE;MACpB,CAAC;;MAED;MACAF,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC,CAAC,CAAC;MAC9C,MAAMiE,gBAAgB,GAAG,MAAMzF,KAAK,CAAC+E,GAAG,CAAE,GAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAwB,gBAAe,EAAE;QAAEN;MAAQ,CAAC,CAAC;MAClI,MAAMc,eAAe,GAAGD,gBAAgB,CAACN,IAAI,CAAChD,QAAQ;MACtDC,WAAW,CAACsD,eAAe,CAAC;MAC5BnE,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC,CAAC,CAAC;;MAExD;MACCD,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC,CAAC,CAAC;MAC1D,MAAMmE,oBAAoB,GAAG,MAAM3F,KAAK,CAAC+E,GAAG,CAAE,GAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAwB,2BAA0B,EAAE;QAAEN;MAAQ,CAAC,CAAC;MAChJrD,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC,CAAC,CAAC;;MAEpE;MACAD,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC,CAAC,CAAC;MAClD,MAAMoE,eAAe,GAAG,MAAM5F,KAAK,CAAC+E,GAAG,CAAE,GAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAwB,eAAc,EAAE;QAAEN;MAAQ,CAAC,CAAC;MAChIrD,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEoE,eAAe,CAACT,IAAI,CAAC,CAAC,CAAC;;MAEtE;MACA,MAAMU,qBAAqB,GAAGzF,oBAAoB,CAACwF,eAAe,CAACT,IAAI,CAAC;MACxE5D,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEqE,qBAAqB,CAAC;MAEvE/D,cAAc,CAAC+D,qBAAqB,CAAC;MACrCtE,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC,CAAC,CAAC;;MAE5D;MACAD,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC,CAAC,CAAC;MACrD,MAAMsE,kBAAkB,GAAG,MAAM9F,KAAK,CAAC+E,GAAG,CAAE,GAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAwB,sBAAqB,EAAE;QAAEN;MAAQ,CAAC,CAAC;MAC1I,MAAMmB,WAAW,GAAGD,kBAAkB,CAACX,IAAI,CAACY,WAAW,IAAI,EAAE;MAC7DxE,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC,CAAC,CAAC;;MAE/D;MACA;MACAD,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;;MAEtD;MACA,MAAMwE,kBAAkB,GAAG,IAAI,CAAC,CAAC;MACjC,MAAMC,eAAe,GAAG,IAAI,CAAC,CAAG;;MAEhC1E,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEwE,kBAAkB,EAAE,UAAU,EAAEC,eAAe,CAAC;;MAEpG;MACA,IAAIC,WAAW,GAAG,CAAC;MACnB,IAAIC,YAAY,GAAG,CAAC;MACpB,IAAIC,aAAa,GAAG,CAAC;MACrB,IAAIC,cAAc,GAAG,CAAC;MAEtBN,WAAW,CAACO,OAAO,CAACC,UAAU,IAAI;QAAA,IAAAC,qBAAA;QAC9B,MAAMC,SAAS,GAAG,EAAAD,qBAAA,GAAAD,UAAU,CAACG,eAAe,cAAAF,qBAAA,uBAA1BA,qBAAA,CAA4BC,SAAS,KAAI,EAAE;QAC7DA,SAAS,CAACH,OAAO,CAACK,QAAQ,IAAI;UAC1BA,QAAQ,CAACC,OAAO,CAACN,OAAO,CAACO,MAAM,IAAI;YAC/B,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC3F,QAAQ,CAAC2F,MAAM,CAACC,KAAK,CAAC,EAAE;cACzC,MAAMC,OAAO,GAAGC,UAAU,CAACH,MAAM,CAACE,OAAO,CAAC,IAAI,CAAC;cAC/C,MAAME,WAAW,GAAGC,aAAa,CAACL,MAAM,CAACC,KAAK,CAAC;cAE/C,IAAI,CAACK,KAAK,CAACJ,OAAO,CAAC,IAAI,CAACI,KAAK,CAACF,WAAW,CAAC,EAAE;gBACxCf,WAAW,IAAIa,OAAO,GAAGE,WAAW;gBACpCd,YAAY,IAAIY,OAAO;gBAEvB,IAAIF,MAAM,CAACO,UAAU,EAAE;kBACnBhB,aAAa,IAAIW,OAAO,GAAGE,WAAW;kBACtCZ,cAAc,IAAIU,OAAO;gBAC7B;cACJ;YACJ;UACJ,CAAC,CAAC;QACN,CAAC,CAAC;MACN,CAAC,CAAC;;MAEF;MACA,MAAMM,aAAa,GAAGlB,YAAY,GAAG,CAAC,GAAGD,WAAW,GAAGC,YAAY,GAAG,CAAC;MACvE,MAAMmB,UAAU,GAAGjB,cAAc,GAAG,CAAC,GAAGD,aAAa,GAAGC,cAAc,GAAG,CAAC;;MAE1E;MACA,IAAIkB,oBAAoB,GAAG,EAAE,CAAC,CAAC;MAC/B,MAAMC,WAAW,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC;;MAE/B1B,WAAW,CAACO,OAAO,CAACC,UAAU,IAAI;QAAA,IAAAmB,sBAAA;QAC9B,MAAMC,WAAW,GAAGpB,UAAU,CAACoB,WAAW,CAAC,CAAC;QAC5C,MAAMlB,SAAS,GAAG,EAAAiB,sBAAA,GAAAnB,UAAU,CAACG,eAAe,cAAAgB,sBAAA,uBAA1BA,sBAAA,CAA4BjB,SAAS,KAAI,EAAE;QAC7DA,SAAS,CAACH,OAAO,CAACK,QAAQ,IAAI;UAC1B,MAAMiB,IAAI,GAAGjB,QAAQ,CAACiB,IAAI;UAC1B,MAAMC,IAAI,GAAGlB,QAAQ,CAACkB,IAAI;UAC1BlB,QAAQ,CAACC,OAAO,CAACN,OAAO,CAACO,MAAM,IAAI;YAC/B;YACA;YACA;YACA;YACA;YACA,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC3F,QAAQ,CAAC2F,MAAM,CAACC,KAAK,CAAC,EAAE;cACzC,MAAMgB,qBAAqB,GAAGH,WAAW,CAACI,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC;cACnF,MAAMC,YAAY,GAAI,GAAEJ,qBAAsB,IAAGjB,MAAM,CAACsB,IAAK,EAAC;cAC9D,MAAMC,iBAAiB,GAAG;gBACtB,GAAGvB,MAAM;gBACTc,WAAW,EAAEA,WAAW;gBACxBC,IAAI,EAAEA,IAAI;gBACVC,IAAI,EAAEA,IAAI;gBACVZ,WAAW,EAAEC,aAAa,CAACL,MAAM,CAACC,KAAK,CAAC,CAAC;cAC7C,CAAC;cAED,IAAI,CAACU,WAAW,CAACU,YAAY,CAAC,IAAIE,iBAAiB,CAACnB,WAAW,GAAGO,WAAW,CAACU,YAAY,CAAC,CAACjB,WAAW,EAAE;gBACpGO,WAAW,CAACU,YAAY,CAAC,GAAGE,iBAAiB,CAAC,CAAC;cACpD;YACJ;UACJ,CAAC,CAAC;QACN,CAAC,CAAC;MACN,CAAC,CAAC;MACF;MACAb,oBAAoB,GAAGvG,MAAM,CAACqH,MAAM,CAACb,WAAW,CAAC,CAAC,CAAC;MACnDjG,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAE+F,oBAAoB,CAAC;MACrF;;MAGA;MACA,MAAMe,YAAY,GAAG3C,oBAAoB,CAACR,IAAI,CAACoD,aAAa,CAAC,CAAC;MAC9DhH,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE8G,YAAY,CAAC;;MAEjD;MACA;MACA,MAAME,kBAAkB,GAAGxH,MAAM,CAACC,OAAO,CAACqH,YAAY,IAAI,CAAC,CAAC,CAAC,CAACG,MAAM,CAAC,CAACC,GAAG,EAAE,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;QACxF;QACAF,GAAG,CAACC,GAAG,CAACZ,WAAW,CAAC,CAAC,CAAC,GAAG;UAAEnB,OAAO,EAAEiC,KAAK,CAACC,OAAO,CAACF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEhC,OAAO,CAAC,GAAGgC,KAAK,CAAChC,OAAO,GAAG;QAAG,CAAC;QACxF,OAAO8B,GAAG;MACd,CAAC,EAAE,CAAC,CAAC,CAAC;;MAEN;MACA,MAAMK,cAAc,GAAG,CAAC,CAAC;MACzBrD,eAAe,CAACY,OAAO,CAAC0C,OAAO,IAAI;QAC/BzH,OAAO,CAACC,GAAG,CAAE,yCAAwCwH,OAAO,CAACC,IAAK,EAAC,CAAC;QACpE,IAAID,OAAO,CAACE,YAAY,CAACX,aAAa,EAAE;UACpC,MAAMY,cAAc,GAAG,CAAC,CAAC;UAEzBnI,MAAM,CAACC,OAAO,CAAC+H,OAAO,CAACE,YAAY,CAACX,aAAa,CAAC,CAACjC,OAAO,CAAC,CAAC,CAAC8C,UAAU,EAAEC,UAAU,CAAC,KAAK;YACrF,MAAMC,WAAW,GAAGd,kBAAkB,CAACY,UAAU,CAACrB,WAAW,CAAC,CAAC,CAAC;YAChE,IAAIwB,YAAY,GAAG,EAAE;YACrB,IAAIC,OAAO,GAAG,KAAK;;YAEnB;YACA,IAAIC,UAAU,GAAG,KAAK;YACtB,IAAIH,WAAW,IAAIA,WAAW,CAAC1C,OAAO,CAAC8C,MAAM,GAAG,CAAC,EAAE;cAC/C;cACAH,YAAY,GAAGD,WAAW,CAAC1C,OAAO;cAClC6C,UAAU,GAAG,IAAI;YACrB,CAAC,MAAM,IAAIL,UAAU,CAACrB,WAAW,CAAC,CAAC,CAAC7G,QAAQ,CAAC,SAAS,CAAC,IAAIkI,UAAU,CAACrB,WAAW,CAAC,CAAC,CAAC7G,QAAQ,CAAC,YAAY,CAAC,EAAE;cACxG;cACA,MAAMyI,kBAAkB,GAAG,8BAA8B;cACzD,MAAMC,aAAa,GAAGpB,kBAAkB,CAACmB,kBAAkB,CAAC;cAC5D,IAAIC,aAAa,IAAIA,aAAa,CAAChD,OAAO,CAAC8C,MAAM,GAAG,CAAC,EAAE;gBACnDH,YAAY,GAAGK,aAAa,CAAChD,OAAO;gBACpC6C,UAAU,GAAG,IAAI;gBACjBlI,OAAO,CAACC,GAAG,CAAE,wCAAuC4H,UAAW,EAAC,CAAC;cACrE;YACJ;YAEA,IAAIK,UAAU,EAAE;cACZD,OAAO,GAAG,IAAI;cACdjI,OAAO,CAACC,GAAG,CAAE,2BAA0B4H,UAAW,GAAE,EAAEG,YAAY,CAAC;YACvE,CAAC,MAAM;cACH;cACAA,YAAY,GAAGhC,oBAAoB,CAACsC,MAAM,CAAChD,MAAM,IAC7CiD,mBAAmB,CAACjD,MAAM,CAACoC,IAAI,EAAEpC,MAAM,CAACsB,IAAI,EAAEiB,UAAU,CAC5D,CAAC;cACD7H,OAAO,CAACC,GAAG,CAAE,+BAA8B4H,UAAW,GAAE,EAAEG,YAAY,CAAC;YAC3E;;YAEA;YACA,MAAMpD,YAAY,GAAGoD,YAAY,CAACd,MAAM,CAAC,CAACsB,GAAG,EAAElD,MAAM,KACjDkD,GAAG,GAAG/C,UAAU,CAACH,MAAM,CAACE,OAAO,IAAI,CAAC,CAAC,EAAE,CAC3C,CAAC;;YAED;YACAoC,cAAc,CAACC,UAAU,CAAC,GAAG;cACzBjD,YAAY,EAAEA,YAAY;cAC1BY,OAAO,EAAEsC,UAAU,CAACtC,OAAO;cAAE;cAC7BiD,SAAS,EAAEX,UAAU,CAACW,SAAS;cAC/BC,YAAY,EAAEZ,UAAU,CAACY,YAAY;cACrCrD,OAAO,EAAE2C,YAAY;cAAE;cACvBC,OAAO,EAAEA,OAAO,CAAC;YACrB,CAAC;UACL,CAAC,CAAC;UAEFT,cAAc,CAACC,OAAO,CAACC,IAAI,CAAC,GAAGE,cAAc;QACjD;MACJ,CAAC,CAAC;MAEF5H,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEuH,cAAc,CAAC;MAEpD,MAAMmB,iBAAiB,GAAG;QACtBC,cAAc,EAAEnE,kBAAkB;QAClCoE,WAAW,EAAEnE,eAAe;QAC5BsC,aAAa,EAAEQ;MACnB,CAAC;MAEDxH,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE0I,iBAAiB,CAAC;MACzDtI,iBAAiB,CAACsI,iBAAiB,CAAC;MACpC3I,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC,CAAC,CAAC;;MAElE;MACA,MAAM6I,kBAAkB,CAAC;QACrBlI,QAAQ,EAAEuD,eAAe;QACzB/D,cAAc,EAAEuI,iBAAiB;QACjCrI,WAAW,EAAE+D,eAAe,CAACT;MACjC,CAAC,CAAC;IAEN,CAAC,CAAC,OAAOT,GAAG,EAAE;MAAA,IAAA4F,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA;MACVnJ,OAAO,CAACU,KAAK,CAAC,iDAAiD,EAAEyC,GAAG,CAAC,CAAC,CAAC;MACtEnD,OAAO,CAACU,KAAK,CAAC,iCAAiC,GAAAqI,aAAA,GAAE5F,GAAG,CAACI,QAAQ,cAAAwF,aAAA,uBAAZA,aAAA,CAAclF,MAAM,CAAC,CAAC,CAAC;MACxE7D,OAAO,CAACU,KAAK,CAAC,+BAA+B,GAAAsI,cAAA,GAAE7F,GAAG,CAACI,QAAQ,cAAAyF,cAAA,uBAAZA,cAAA,CAAcpF,IAAI,CAAC,CAAC,CAAC;MACrE,IAAI,EAAAqF,cAAA,GAAA9F,GAAG,CAACI,QAAQ,cAAA0F,cAAA,uBAAZA,cAAA,CAAcpF,MAAM,MAAK,GAAG,EAAE;QAC9B;QACAlD,QAAQ,CAAC,gDAAgD,CAAC;QACzDX,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC,CAAC,CAAC;QAC3DE,QAAQ,CAAC,QAAQ,CAAC;QAClB;MACJ;MACAQ,QAAQ,CAAC,EAAAuI,cAAA,GAAA/F,GAAG,CAACI,QAAQ,cAAA2F,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAActF,IAAI,cAAAuF,mBAAA,uBAAlBA,mBAAA,CAAoBzI,KAAK,KAAI,sBAAsB,CAAC;MAC5DV,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC,CAAC,CAAC;IAC3E;EACJ,CAAC;;EAED;EACA,MAAM6I,kBAAkB,GAAG,MAAOM,UAAU,IAAK;IAC7CpJ,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;IAC1DiC,gBAAgB,CAAC,IAAI,CAAC;IAEtB,IAAI;MACA,MAAMmB,OAAO,GAAG;QACZC,aAAa,EAAG,UAASpD,KAAM,EAAC;QAChC,cAAc,EAAE;MACpB,CAAC;;MAED;MACA,MAAMmJ,eAAe,GAAGD,UAAU,IAAI;QAClCxI,QAAQ,EAAEA,QAAQ;QAClBR,cAAc,EAAEA,cAAc;QAC9BE,WAAW,EAAEA,WAAW;QACxB6B,wBAAwB,EAAEA;MAC9B,CAAC;;MAED;MACA,MAAMoB,QAAQ,GAAG,MAAM9E,KAAK,CAAC6K,IAAI,CAC7B,kDAAkD,EAClD;QAAEtF,gBAAgB,EAAEqF;MAAgB,CAAC,EACrC;QAAEhG;MAAQ,CACd,CAAC;MAEDrD,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEsD,QAAQ,CAACK,IAAI,CAAC;;MAElE;MACA5B,wBAAwB,CAACuB,QAAQ,CAACK,IAAI,CAACE,SAAS,CAAC;MACjDhC,oBAAoB,CAAC,IAAI,CAAC;MAC1B,OAAO,IAAI;IACf,CAAC,CAAC,OAAOqB,GAAG,EAAE;MACVnD,OAAO,CAACU,KAAK,CAAC,yCAAyC,EAAEyC,GAAG,CAAC;MAC7D,OAAO,KAAK;IAChB,CAAC,SAAS;MACNjB,gBAAgB,CAAC,KAAK,CAAC;IAC3B;EACJ,CAAC;;EAED;EACA,MAAMqH,iBAAiB,GAAGA,CAAA,KAAM;IAC5B9I,YAAY,CAAC,IAAI,CAAC;IAClB2C,wBAAwB,CAAC,CAAC,CACrBoG,OAAO,CAAC,MAAM/I,YAAY,CAAC,KAAK,CAAC,CAAC;EAC3C,CAAC;EAED,MAAM8H,mBAAmB,GAAGA,CAACkB,UAAU,EAAEC,UAAU,EAAE7B,UAAU,KAAK;IAAA,IAAA8B,iBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA;IAChEN,UAAU,GAAGA,UAAU,CAACjD,WAAW,CAAC,CAAC;IACrCkD,UAAU,GAAGA,UAAU,CAAClD,WAAW,CAAC,CAAC;IACrCqB,UAAU,GAAGA,UAAU,CAACrB,WAAW,CAAC,CAAC;;IAErC;IACA,MAAMwD,iBAAiB,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC;IAClD,MAAMC,eAAe,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC;IAC9C,MAAMC,kBAAkB,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC;IACnD,MAAMC,eAAe,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC;IAC7C,MAAMC,kBAAkB,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC;IAC1D,MAAMC,iBAAiB,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC;IACzC,MAAMC,oBAAoB,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC;;IAE5C;IACA,IAAIzC,UAAU,CAAClI,QAAQ,CAAC,sBAAsB,CAAC,IAC3CkI,UAAU,CAAClI,QAAQ,CAAC,wBAAwB,CAAC,EAAE;MAC/C,MAAM4K,OAAO,GAAId,UAAU,CAAC9J,QAAQ,CAAC,SAAS,CAAC,IAAI8J,UAAU,CAAC9J,QAAQ,CAAC,YAAY,CAAC,IACzE8J,UAAU,CAAC9J,QAAQ,CAAC,KAAK,CAAC,IAC1B8J,UAAU,CAAC9J,QAAQ,CAAC,aAAa,CAAC,IACtC8J,UAAU,CAAC9J,QAAQ,CAAC,8BAA8B,CAAC,IACnD+J,UAAU,CAAC/J,QAAQ,CAAC,KAAK,CAAC,IACzB+J,UAAU,CAACc,KAAK,CAAC,KAAK,CAAC,KAAKf,UAAU,CAAC9J,QAAQ,CAAC,aAAa,CAAC,IAAI8J,UAAU,CAAC9J,QAAQ,CAAC,iBAAiB,CAAC,CAAE;MAClH,IAAI4K,OAAO,EAAEvK,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEwJ,UAAU,CAAC;MAC3D,OAAOc,OAAO;IAClB;;IAEA;IACA,QAAO,IAAI;MACN;MACD,KAAK1C,UAAU,CAAClI,QAAQ,CAAC,sCAAsC,CAAC;QAAE;QAC9D,MAAM8K,aAAa,GAAGT,iBAAiB,CAACU,IAAI,CAACC,MAAM,IAAIjB,UAAU,CAACkB,UAAU,CAACD,MAAM,CAAC,CAAC;QAAI;QACjFlB,UAAU,CAAC9J,QAAQ,CAAC,SAAS,CAAC,IAAI8J,UAAU,CAAC9J,QAAQ,CAAC,WAAW,CAAC,MAAAgK,iBAAA,GAClED,UAAU,CAACc,KAAK,CAAC,KAAK,CAAC,cAAAb,iBAAA,uBAAvBA,iBAAA,CAAyBe,IAAI,CAACG,GAAG,IAAIC,QAAQ,CAACD,GAAG,CAAC,GAAG,GAAG,CAAC,EAAC;QAClE,MAAME,aAAa,GAAGf,iBAAiB,CAACU,IAAI,CAACC,MAAM,IAAIjB,UAAU,CAACkB,UAAU,CAACD,MAAM,CAAC,CAAC;QAAI;QACjFlB,UAAU,CAAC9J,QAAQ,CAAC,SAAS,CAAC,MAAAiK,kBAAA,GAC9BF,UAAU,CAACc,KAAK,CAAC,KAAK,CAAC,cAAAZ,kBAAA,uBAAvBA,kBAAA,CAAyBc,IAAI,CAACG,GAAG,IAAIC,QAAQ,CAACD,GAAG,CAAC,IAAI,GAAG,IAAIC,QAAQ,CAACD,GAAG,CAAC,GAAG,GAAG,CAAC,EAAC;QAC1F,MAAMG,aAAa,GAAGtB,UAAU,CAACkB,UAAU,CAAC,KAAK,CAAC,IAAIlB,UAAU,CAACkB,UAAU,CAAC,MAAM,CAAC;QAAI;QAChFnB,UAAU,CAAC9J,QAAQ,CAAC,SAAS,CAAC;QACrC,OAAO8K,aAAa,IAAIM,aAAa,IAAIC,aAAa;MAAE;;MAE5D;MACJ,KAAKnD,UAAU,CAAClI,QAAQ,CAAC,eAAe,CAAC;QACrC,MAAMsL,SAAS;QACX;QACExB,UAAU,CAAC9J,QAAQ,CAAC,SAAS,CAAC,IAAI8J,UAAU,CAAC9J,QAAQ,CAAC,YAAY,CAAC,IACrE8J,UAAU,CAAC9J,QAAQ,CAAC,KAAK,CAAC,IAC1B8J,UAAU,CAAC9J,QAAQ,CAAC,aAAa,CAAC,IAClC8J,UAAU,CAAC9J,QAAQ,CAAC,aAAa,CAAC,IAClC8J,UAAU,CAAC9J,QAAQ,CAAC,iBAAiB,CAAC;QACtC;QACCsK,eAAe,CAACS,IAAI,CAACC,MAAM,IAAIjB,UAAU,CAACkB,UAAU,CAACD,MAAM,CAAC,CAAC,KAC7DlB,UAAU,CAAC9J,QAAQ,CAAC,SAAS,CAAC,IAAI8J,UAAU,CAAC9J,QAAQ,CAAC,YAAY,CAAC,CACvE;QACD,IAAIsL,SAAS,EAAEjL,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEwJ,UAAU,CAAC;QACjE,OAAOwB,SAAS;MAEpB,KAAKpD,UAAU,CAAClI,QAAQ,CAAC,kBAAkB,CAAC;QACxC,MAAMuL,YAAY;QACd;QACEzB,UAAU,CAAC9J,QAAQ,CAAC,SAAS,CAAC,IAAI8J,UAAU,CAAC9J,QAAQ,CAAC,YAAY,CAAC,IACrE8J,UAAU,CAAC9J,QAAQ,CAAC,KAAK,CAAC,IAC1B8J,UAAU,CAAC9J,QAAQ,CAAC,aAAa,CAAC,IAClC8J,UAAU,CAAC9J,QAAQ,CAAC,aAAa,CAAC,IAClC8J,UAAU,CAAC9J,QAAQ,CAAC,iBAAiB,CAAC;QACtC;QACCsK,eAAe,CAACS,IAAI,CAACC,MAAM,IAAIjB,UAAU,CAACkB,UAAU,CAACD,MAAM,CAAC,CAAC,KAC7DlB,UAAU,CAAC9J,QAAQ,CAAC,YAAY,CAAC,IAAI8J,UAAU,CAAC9J,QAAQ,CAAC,eAAe,CAAC,CAC7E;QACD,IAAIuL,YAAY,EAAElL,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEwJ,UAAU,CAAC;QACvE,OAAOyB,YAAY;MAEvB,KAAKrD,UAAU,CAAClI,QAAQ,CAAC,cAAc,CAAC;QACpC,MAAMwL,OAAO,GAAIlB,eAAe,CAACS,IAAI,CAACC,MAAM,IAAIjB,UAAU,CAACkB,UAAU,CAACD,MAAM,CAAC,CAAC,IACvElB,UAAU,CAAC9J,QAAQ,CAAC,OAAO,CAAC,IAC5B8J,UAAU,CAAC9J,QAAQ,CAAC,cAAc,CAAC;QAC1C,IAAIwL,OAAO,EAAEnL,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEwJ,UAAU,CAAC;QAC7D,OAAO0B,OAAO;MAEd,KAAKtD,UAAU,CAAClI,QAAQ,CAAC,mBAAmB,CAAC;QAC7C,MAAMyL,SAAS,GAAGpB,iBAAiB,CAACU,IAAI,CAACC,MAAM,IAAIjB,UAAU,CAACkB,UAAU,CAACD,MAAM,CAAC,CAAC,KACzElB,UAAU,CAAC9J,QAAQ,CAAC,SAAS,CAAC,MAAAkK,kBAAA,GAC9BH,UAAU,CAACc,KAAK,CAAC,KAAK,CAAC,cAAAX,kBAAA,uBAAvBA,kBAAA,CAAyBa,IAAI,CAACG,GAAG,IAAIC,QAAQ,CAACD,GAAG,CAAC,GAAG,GAAG,CAAC,EAAC;QAClE,IAAIO,SAAS,EAAEpL,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEwJ,UAAU,CAAC;QACpE,OAAO2B,SAAS;MAEhB,KAAKvD,UAAU,CAAClI,QAAQ,CAAC,mBAAmB,CAAC;QAC7C,MAAM0L,SAAS,GAAGrB,iBAAiB,CAACU,IAAI,CAACC,MAAM,IAAIjB,UAAU,CAACkB,UAAU,CAACD,MAAM,CAAC,CAAC,KACzElB,UAAU,CAAC9J,QAAQ,CAAC,SAAS,CAAC,MAAAmK,kBAAA,GAC9BJ,UAAU,CAACc,KAAK,CAAC,KAAK,CAAC,cAAAV,kBAAA,uBAAvBA,kBAAA,CAAyBY,IAAI,CAACG,GAAG,IAAIC,QAAQ,CAACD,GAAG,CAAC,IAAI,GAAG,IAAIC,QAAQ,CAACD,GAAG,CAAC,GAAG,GAAG,CAAC,EAAC;QAC1F,IAAIQ,SAAS,EAAErL,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEwJ,UAAU,CAAC;QACpE,OAAO4B,SAAS;MAEhB,KAAKxD,UAAU,CAAClI,QAAQ,CAAC,cAAc,CAAC;QACxC,MAAM2L,SAAS,GAAG5B,UAAU,CAACkB,UAAU,CAAC,KAAK,CAAC,IAAIlB,UAAU,CAACkB,UAAU,CAAC,MAAM,CAAC,IACpEnB,UAAU,CAAC9J,QAAQ,CAAC,SAAS,CAAC;QACzC,IAAI2L,SAAS,EAAEtL,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEwJ,UAAU,CAAC;QAC/D,OAAO6B,SAAS;MAEhB,KAAKzD,UAAU,CAAClI,QAAQ,CAAC,YAAY,CAAC;QACtC,MAAM4L,OAAO,GAAGrB,kBAAkB,CAACQ,IAAI,CAACC,MAAM,IAAIjB,UAAU,CAACkB,UAAU,CAACD,MAAM,CAAC,CAAC,IACrElB,UAAU,CAAC9J,QAAQ,CAAC,YAAY,CAAC;QAC5C,IAAI4L,OAAO,EAAEvL,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEwJ,UAAU,CAAC;QAC3D,OAAO8B,OAAO;MAEd,KAAK1D,UAAU,CAAClI,QAAQ,CAAC,YAAY,CAAC;QACtC,MAAM6L,OAAO,GAAGpB,kBAAkB,CAACM,IAAI,CAACC,MAAM,IAAIjB,UAAU,CAACkB,UAAU,CAACD,MAAM,CAAC,CAAC,IACrElB,UAAU,CAAC9J,QAAQ,CAAC,YAAY,CAAC,IACjC8J,UAAU,CAAC9J,QAAQ,CAAC,aAAa,CAAC;QAC7C,IAAI6L,OAAO,EAAExL,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEwJ,UAAU,CAAC;QAC3D,OAAO+B,OAAO;MAElB,KAAK3D,UAAU,CAAClI,QAAQ,CAAC,qBAAqB,CAAC;QAC3C,MAAM8L,SAAS,GAAGhC,UAAU,CAAC9J,QAAQ,CAAC,qBAAqB,CAAC,IACpD+J,UAAU,CAACkB,UAAU,CAAC,MAAM,CAAC,IAAInB,UAAU,CAAC9J,QAAQ,CAAC,aAAa,CAAE;QAC5E,IAAI8L,SAAS,EAAEzL,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEwJ,UAAU,CAAC;QACtE,OAAOgC,SAAS;MAEpB,KAAK5D,UAAU,CAAClI,QAAQ,CAAC,qBAAqB,CAAC;QAC3C,MAAM+L,mBAAmB,GACrBxB,kBAAkB,CAACQ,IAAI,CAACC,MAAM,IAAIjB,UAAU,CAACkB,UAAU,CAACD,MAAM,CAAC,CAAC,IAChElB,UAAU,CAAC9J,QAAQ,CAAC,YAAY,CAAC,IACjC8J,UAAU,CAAC9J,QAAQ,CAAC,YAAY,CAAC,IACjC8J,UAAU,CAAC9J,QAAQ,CAAC,WAAW,CAAC,IAChC8J,UAAU,CAAC9J,QAAQ,CAAC,eAAe,CAAC,IACpC8J,UAAU,CAAC9J,QAAQ,CAAC,UAAU,CAAC,IAC/B8J,UAAU,CAAC9J,QAAQ,CAAC,mBAAmB,CAAC,IACxC8J,UAAU,CAAC9J,QAAQ,CAAC,kBAAkB,CAAC,IACvC8J,UAAU,CAAC9J,QAAQ,CAAC,uBAAuB,CAAC,IAC5C8J,UAAU,CAAC9J,QAAQ,CAAC,mBAAmB,CAAC,IACxC8J,UAAU,CAAC9J,QAAQ,CAAC,WAAW,CAAC,IAChC8J,UAAU,CAAC9J,QAAQ,CAAC,YAAY,CAAC,IACjC8J,UAAU,CAAC9J,QAAQ,CAAC,eAAe,CAAC,IACpC8J,UAAU,CAAC9J,QAAQ,CAAC,iBAAiB,CAAC,IACtC8J,UAAU,CAAC9J,QAAQ,CAAC,oBAAoB,CAAC,IACzC8J,UAAU,CAAC9J,QAAQ,CAAC,gBAAgB,CAAC,IACrC8J,UAAU,CAAC9J,QAAQ,CAAC,oBAAoB,CAC3C;QACD,IAAI+L,mBAAmB,EAAE1L,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEwJ,UAAU,CAAC;QAC/E,OAAOiC,mBAAmB;MAE9B,KAAK7D,UAAU,CAAClI,QAAQ,CAAC,WAAW,CAAC;QACjC,MAAMgM,WAAW,GACbtB,iBAAiB,CAACK,IAAI,CAACC,MAAM,IAAIjB,UAAU,CAACkB,UAAU,CAACD,MAAM,CAAC,CAAC,IAC/DlB,UAAU,CAAC9J,QAAQ,CAAC,WAAW,CAAC,IAChC8J,UAAU,CAAC9J,QAAQ,CAAC,gBAAgB,CAAC,IACrC8J,UAAU,CAAC9J,QAAQ,CAAC,aAAa,CAAC,IAClC8J,UAAU,CAAC9J,QAAQ,CAAC,aAAa,CAAC,IAClC8J,UAAU,CAAC9J,QAAQ,CAAC,kBAAkB,CAAC,IACvC8J,UAAU,CAAC9J,QAAQ,CAAC,qBAAqB,CAAC,IAC1C8J,UAAU,CAAC9J,QAAQ,CAAC,gBAAgB,CAAC,IACrC8J,UAAU,CAAC9J,QAAQ,CAAC,gBAAgB,CACvC;QACD,IAAIgM,WAAW,EAAE3L,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEwJ,UAAU,CAAC;QAC9D,OAAOkC,WAAW;MAEtB,KAAK9D,UAAU,CAAClI,QAAQ,CAAC,iBAAiB,CAAC;QACvC,MAAMiM,eAAe;QACjB;QACAvB,iBAAiB,CAACK,IAAI,CAACC,MAAM,IAAIjB,UAAU,CAACkB,UAAU,CAACD,MAAM,CAAC,CAAC,IAC/DlB,UAAU,CAAC9J,QAAQ,CAAC,WAAW,CAAC,IAChC8J,UAAU,CAAC9J,QAAQ,CAAC,gBAAgB,CAAC,IACrC8J,UAAU,CAAC9J,QAAQ,CAAC,aAAa,CAAC,IAClC8J,UAAU,CAAC9J,QAAQ,CAAC,aAAa,CAAC,IAClC8J,UAAU,CAAC9J,QAAQ,CAAC,kBAAkB,CAAC,IACvC8J,UAAU,CAAC9J,QAAQ,CAAC,qBAAqB,CAAC,IAC1C8J,UAAU,CAAC9J,QAAQ,CAAC,gBAAgB,CAAC;QACrC;QACAuK,kBAAkB,CAACQ,IAAI,CAACC,MAAM,IAAIjB,UAAU,CAACkB,UAAU,CAACD,MAAM,CAAC,CAAC,IAChElB,UAAU,CAAC9J,QAAQ,CAAC,YAAY,CAAC,IACjC8J,UAAU,CAAC9J,QAAQ,CAAC,YAAY,CAAC,IACjC8J,UAAU,CAAC9J,QAAQ,CAAC,WAAW,CAAC,IAChC8J,UAAU,CAAC9J,QAAQ,CAAC,eAAe,CAAC,IACpC8J,UAAU,CAAC9J,QAAQ,CAAC,UAAU,CAAC,IAC/B8J,UAAU,CAAC9J,QAAQ,CAAC,mBAAmB,CAAC,IACxC8J,UAAU,CAAC9J,QAAQ,CAAC,kBAAkB,CAAC,IACvC8J,UAAU,CAAC9J,QAAQ,CAAC,uBAAuB,CAAC;QAC5C;QACA2K,oBAAoB,CAACI,IAAI,CAACC,MAAM,IAAIjB,UAAU,CAACkB,UAAU,CAACD,MAAM,CAAC,CAAC,IAClElB,UAAU,CAAC9J,QAAQ,CAAC,cAAc,CAAC,IACnC8J,UAAU,CAAC9J,QAAQ,CAAC,kBAAkB,CAAC,IACvC8J,UAAU,CAAC9J,QAAQ,CAAC,gBAAgB,CAAC,IACrC8J,UAAU,CAAC9J,QAAQ,CAAC,gBAAgB,CAAC,IACrC8J,UAAU,CAAC9J,QAAQ,CAAC,eAAe,CAAC,IACpC8J,UAAU,CAAC9J,QAAQ,CAAC,0BAA0B,CAAC,IAC/C8J,UAAU,CAAC9J,QAAQ,CAAC,wBAAwB,CAAC,IAC7C8J,UAAU,CAAC9J,QAAQ,CAAC,yBAAyB,CAAC,IAC9C8J,UAAU,CAAC9J,QAAQ,CAAC,yBAAyB,CAAC,IAC9C8J,UAAU,CAAC9J,QAAQ,CAAC,gBAAgB,CAAC,IACrC8J,UAAU,CAAC9J,QAAQ,CAAC,mBAAmB,CAAC,IACxC8J,UAAU,CAAC9J,QAAQ,CAAC,iBAAiB,CAAC,IACtC8J,UAAU,CAAC9J,QAAQ,CAAC,iBAAiB,CAAC,IACtC8J,UAAU,CAAC9J,QAAQ,CAAC,eAAe,CAAC;QACpC;QACA8J,UAAU,CAAC9J,QAAQ,CAAC,gBAAgB,CAAC,IACrC8J,UAAU,CAAC9J,QAAQ,CAAC,gBAAgB,CAAC,IACrC8J,UAAU,CAAC9J,QAAQ,CAAC,gBAAgB,CAAC,IACrC8J,UAAU,CAAC9J,QAAQ,CAAC,WAAW,CAAC,IAChC8J,UAAU,CAAC9J,QAAQ,CAAC,oBAAoB,CAC3C;QACD,IAAIiM,eAAe,EAAE5L,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEwJ,UAAU,CAAC;QACvE,OAAOmC,eAAe;MAEtB,KAAK/D,UAAU,CAAClI,QAAQ,CAAC,SAAS,CAAC;QACnC,MAAMkM,SAAS,GAAG1B,eAAe,CAACO,IAAI,CAACC,MAAM,IAAIjB,UAAU,CAACkB,UAAU,CAACD,MAAM,CAAC,CAAC,IACpElB,UAAU,CAAC9J,QAAQ,CAAC,SAAS,CAAC,IAC9B8J,UAAU,CAAC9J,QAAQ,CAAC,aAAa,CAAC,IAClC8J,UAAU,CAAC9J,QAAQ,CAAC,SAAS,CAAC;QACzC,IAAIkM,SAAS,EAAE7L,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEwJ,UAAU,CAAC;QAC1D,OAAOoC,SAAS;MAEjB,KAAKhE,UAAU,CAAClI,QAAQ,CAAC,qBAAqB,CAAC;QAAE;QAC7C;QACA,OAAOqK,iBAAiB,CAACU,IAAI,CAACC,MAAM,IAAIjB,UAAU,CAACkB,UAAU,CAACD,MAAM,CAAC,CAAC,KAC9DlB,UAAU,CAAC9J,QAAQ,CAAC,SAAS,CAAC,IAAI8J,UAAU,CAAC9J,QAAQ,CAAC,WAAW,CAAC,MAAAoK,kBAAA,GAClEL,UAAU,CAACc,KAAK,CAAC,KAAK,CAAC,cAAAT,kBAAA,uBAAvBA,kBAAA,CAAyBW,IAAI,CAACG,GAAG,IAAIC,QAAQ,CAACD,GAAG,CAAC,GAAG,GAAG,CAAC,EAAC;MAEtE,KAAKhD,UAAU,CAAClI,QAAQ,CAAC,mBAAmB,CAAC;QAAE;QAC3C;QACA,MAAMmM,YAAY,GAAG7B,eAAe,CAACS,IAAI,CAACC,MAAM,IAAIjB,UAAU,CAACkB,UAAU,CAACD,MAAM,CAAC,CAAC,IAC3E,CAAClB,UAAU,CAAC9J,QAAQ,CAAC,SAAS,CAAC,IAC/B,CAAC8J,UAAU,CAAC9J,QAAQ,CAAC,YAAY,CAAC,IAClC,CAAC8J,UAAU,CAAC9J,QAAQ,CAAC,OAAO,CAAC;QAAI;QACjC,CAAC8J,UAAU,CAAC9J,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;QACxC,OAAOmM,YAAY;MAEtB,KAAKjE,UAAU,CAAClI,QAAQ,CAAC,KAAK,CAAC;QAAE;QAC7B,OAAO8J,UAAU,CAAC9J,QAAQ,CAAC,KAAK,CAAC,IAC1B8J,UAAU,CAAC9J,QAAQ,CAAC,+BAA+B,CAAC,IACpD8J,UAAU,CAAC9J,QAAQ,CAAC,oBAAoB,CAAC;MAEpD,KAAKkI,UAAU,CAAClI,QAAQ,CAAC,MAAM,CAAC;QAAE;QAC9B;QACA,MAAMoM,MAAM,GAAG,CAACrC,UAAU,CAACkB,UAAU,CAAC,MAAM,CAAC,IAAIlB,UAAU,CAACkB,UAAU,CAAC,KAAK,CAAC,KAC9D,CAACnB,UAAU,CAAC9J,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;QAC7C;QACA,OAAOoM,MAAM;MAEjB,KAAKlE,UAAU,CAAClI,QAAQ,CAAC,WAAW,CAAC;QAAE;QAClC,MAAMqM,kBAAkB;QAAK;QAC1B3B,iBAAiB,CAACK,IAAI,CAACC,MAAM,IAAIjB,UAAU,CAACkB,UAAU,CAACD,MAAM,CAAC,CAAC;QAAI;QACnEL,oBAAoB,CAACI,IAAI,CAACC,MAAM,IAAIjB,UAAU,CAACkB,UAAU,CAACD,MAAM,CAAC,CAAC;QAAI;QACtElB,UAAU,CAAC9J,QAAQ,CAAC,WAAW,CAAC,IAChC8J,UAAU,CAAC9J,QAAQ,CAAC,gBAAgB,CAAC,IACrC8J,UAAU,CAAC9J,QAAQ,CAAC,aAAa,CAAC,IAClC8J,UAAU,CAAC9J,QAAQ,CAAC,aAAa,CAAC,IAClC8J,UAAU,CAAC9J,QAAQ,CAAC,kBAAkB,CAAC,IACvC8J,UAAU,CAAC9J,QAAQ,CAAC,qBAAqB,CAAC,IAC1C8J,UAAU,CAAC9J,QAAQ,CAAC,gBAAgB,CAAC,IACrC8J,UAAU,CAAC9J,QAAQ,CAAC,gBAAgB,CAAC,IACrC8J,UAAU,CAAC9J,QAAQ,CAAC,cAAc,CAAC;QAAI;QACvC8J,UAAU,CAAC9J,QAAQ,CAAC,gBAAgB,CAAC;QAAI;QACzC8J,UAAU,CAAC9J,QAAQ,CAAC,gBAAgB,CAAC,CAAC;QACzC;QACD,OAAOqM,kBAAkB;MAAE;;MAE3B;QACJ,MAAMC,cAAc,GAAGxC,UAAU,CAAC9J,QAAQ,CAACkI,UAAU,CAAC,IAC/C6B,UAAU,CAAC/J,QAAQ,CAACkI,UAAU,CAAC;QACtC,IAAIoE,cAAc,EAAEjM,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEwJ,UAAU,CAAC;QAC/D,OAAOwC,cAAc;IAC7B;EACJ,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAC7G,OAAO,EAAE2B,aAAa,KAAK;IACjD,MAAMmF,SAAS,GAAG,CAAC,CAAC;;IAEpB;IACA1M,MAAM,CAACC,OAAO,CAACsH,aAAa,CAAC,CAACjC,OAAO,CAAC,CAAC,CAAC8C,UAAU,EAAEC,UAAU,CAAC,KAAK;MAChE9H,OAAO,CAACC,GAAG,CAAE,8BAA6B4H,UAAW,EAAC,CAAC;MACvD7H,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE6H,UAAU,CAACtC,OAAO,CAAC;;MAEpD;MACA,MAAM4G,eAAe,GAAG/G,OAAO,CAACiD,MAAM,CAAChD,MAAM,IACzCiD,mBAAmB,CAACjD,MAAM,CAACoC,IAAI,EAAEpC,MAAM,CAACsB,IAAI,EAAEiB,UAAU,CAAC,IACzD,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAClI,QAAQ,CAAC2F,MAAM,CAACC,KAAK,CAC1C,CAAC;MAEDvF,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEmM,eAAe,CAAC;;MAEvD;MACA,MAAMC,qBAAqB,GAAGxE,UAAU,CAACrB,WAAW,CAAC,CAAC,CAAC7G,QAAQ,CAAC,SAAS,CAAC,IAC9CkI,UAAU,CAACrB,WAAW,CAAC,CAAC,CAAC7G,QAAQ,CAAC,YAAY,CAAC;;MAE3E;MACA,MAAM2M,aAAa,GAAG,CAAC,GAAGF,eAAe,CAAC,CAACG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QACtD,MAAMC,MAAM,GAAG/G,aAAa,CAAC6G,CAAC,CAACjH,KAAK,CAAC;QACrC,MAAMoH,MAAM,GAAGhH,aAAa,CAAC8G,CAAC,CAAClH,KAAK,CAAC;QACrC,IAAIoH,MAAM,KAAKD,MAAM,EAAE,OAAOC,MAAM,GAAGD,MAAM;QAC7C,OAAO5B,QAAQ,CAAC2B,CAAC,CAACnG,IAAI,CAAC,GAAGwE,QAAQ,CAAC0B,CAAC,CAAClG,IAAI,CAAC;MAC9C,CAAC,CAAC;MAEFtG,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEqM,aAAa,CAAC;;MAEtD;MACA,IAAI1H,YAAY,GAAG,CAAC;MACpB,MAAMgI,eAAe,GAAGnH,UAAU,CAACqC,UAAU,CAACtC,OAAO,CAAC,IAAI,CAAC;MAC3D,MAAMqH,eAAe,GAAG,EAAE;MAE1B,KAAK,MAAMvH,MAAM,IAAIgH,aAAa,EAAE;QAChC,MAAMQ,aAAa,GAAGrH,UAAU,CAACH,MAAM,CAACE,OAAO,CAAC,IAAI,CAAC;;QAErD;QACA,IAAI6G,qBAAqB,IACpB/G,MAAM,CAACoC,IAAI,CAAClB,WAAW,CAAC,CAAC,CAAC7G,QAAQ,CAAC,SAAS,CAAC,IAAI2F,MAAM,CAACoC,IAAI,CAAClB,WAAW,CAAC,CAAC,CAAC7G,QAAQ,CAAC,YAAY,CAAE,EAAE;UACrGkN,eAAe,CAACE,IAAI,CAAC;YACjBnG,IAAI,EAAEtB,MAAM,CAACsB,IAAI;YACjBc,IAAI,EAAEpC,MAAM,CAACoC,IAAI;YACjBlC,OAAO,EAAEsH,aAAa;YACtBvH,KAAK,EAAED,MAAM,CAACC,KAAK;YACnBa,WAAW,EAAEd,MAAM,CAACc,WAAW;YAC/BC,IAAI,EAAEf,MAAM,CAACe,IAAI;YACjBC,IAAI,EAAEhB,MAAM,CAACgB;UACjB,CAAC,CAAC;UACF1B,YAAY,IAAIkI,aAAa;QACjC,CAAC,MAAM,IAAI,CAACT,qBAAqB,IAAIzH,YAAY,GAAGgI,eAAe,EAAE;UACjE;UACAC,eAAe,CAACE,IAAI,CAAC;YACjBnG,IAAI,EAAEtB,MAAM,CAACsB,IAAI;YACjBc,IAAI,EAAEpC,MAAM,CAACoC,IAAI;YACjBlC,OAAO,EAAEsH,aAAa;YACtBvH,KAAK,EAAED,MAAM,CAACC,KAAK;YACnBa,WAAW,EAAEd,MAAM,CAACc,WAAW;YAC/BC,IAAI,EAAEf,MAAM,CAACe,IAAI;YACjBC,IAAI,EAAEhB,MAAM,CAACgB;UACjB,CAAC,CAAC;UACF1B,YAAY,IAAIkI,aAAa;QACjC;MACJ;;MAEA;MACAX,SAAS,CAACtE,UAAU,CAAC,GAAG;QACpBjD,YAAY;QACZY,OAAO,EAAEoH,eAAe;QACxBnE,SAAS,EAAEX,UAAU,CAACW,SAAS;QAC/BC,YAAY,EAAEZ,UAAU,CAACY,YAAY;QACrCrD,OAAO,EAAEwH,eAAe;QACxBG,oBAAoB,EAAEZ;MAC1B,CAAC;MAEDpM,OAAO,CAACC,GAAG,CAAE,gCAA+B4H,UAAW,GAAE,EAAEsE,SAAS,CAACtE,UAAU,CAAC,CAAC;IACrF,CAAC,CAAC;IAEF,OAAOsE,SAAS;EACpB,CAAC;EAED,MAAMc,mBAAmB,GAAGA,CAACpF,UAAU,EAAEJ,OAAO,EAAEyF,UAAU,EAAEC,oBAAoB,KAAK;IACnF;IACA,MAAMC,iBAAiB,GAAG,IAAIlH,GAAG,CAACiH,oBAAoB,CAACE,GAAG,CAAC/H,MAAM,IAAIA,MAAM,CAACsB,IAAI,CAAC,CAAC;;IAElF;IACA,MAAM0G,YAAY,GAAGlN,cAAc,CAACmN,WAAW,CAACjF,MAAM,CAAChD,MAAM,IACzD,CAAC8H,iBAAiB,CAACI,GAAG,CAAClI,MAAM,CAACsB,IAAI,CAAC,IACnC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAACjH,QAAQ,CAAC2F,MAAM,CAACC,KAAK,CAAC,IACvCgD,mBAAmB,CAACjD,MAAM,CAACoC,IAAI,EAAEpC,MAAM,CAACsB,IAAI,EAAEiB,UAAU,CAC5D,CAAC;;IAED;IACA7H,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEqN,YAAY,CAAC;IAC7DtN,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEmN,iBAAiB,CAAC;IACtDpN,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE4H,UAAU,CAAC;IAE7C1G,mBAAmB,CAACmM,YAAY,CAAC;IACjCvM,iBAAiB,CAAC;MAAE2G,IAAI,EAAEG,UAAU;MAAEJ,OAAO,EAAEA;IAAQ,CAAC,CAAC;IACzDxG,kBAAkB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAMwM,iBAAiB,GAAInI,MAAM,IAAK;IAClC,IAAI,CAACxE,cAAc,EAAE;IAErB,MAAM6H,iBAAiB,GAAG;MAAE,GAAGvI;IAAe,CAAC;IAC/C,MAAM0H,UAAU,GAAGa,iBAAiB,CAAC3B,aAAa,CAAClG,cAAc,CAAC4G,IAAI,CAAC;;IAEvE;IACAI,UAAU,CAACzC,OAAO,CAAC0H,IAAI,CAAC;MACpBnG,IAAI,EAAEtB,MAAM,CAACsB,IAAI;MACjBc,IAAI,EAAEpC,MAAM,CAACoC,IAAI;MACjBlC,OAAO,EAAEF,MAAM,CAACE,OAAO;MACvBD,KAAK,EAAED,MAAM,CAACC,KAAK;MACnBa,WAAW,EAAEd,MAAM,CAACc,WAAW;MAC/BC,IAAI,EAAEf,MAAM,CAACe,IAAI;MACjBC,IAAI,EAAEhB,MAAM,CAACgB;IACjB,CAAC,CAAC;;IAEF;IACAwB,UAAU,CAAClD,YAAY,IAAIa,UAAU,CAACH,MAAM,CAACE,OAAO,CAAC,IAAI,CAAC;;IAE1D;IACA,MAAM;MACFM,aAAa;MACbC;IACJ,CAAC,GAAG2H,0BAA0B,CAAC/E,iBAAiB,CAACxD,eAAe,CAACwI,YAAY,CAAC;IAE9EhF,iBAAiB,CAACC,cAAc,GAAG9C,aAAa;IAChD6C,iBAAiB,CAACE,WAAW,GAAG9C,UAAU;IAC1C4C,iBAAiB,CAACiF,gBAAgB,GAAGC,kBAAkB,CAAClF,iBAAiB,CAAC3B,aAAa,EAAElG,cAAc,CAAC2G,OAAO,CAAC;IAEhHpH,iBAAiB,CAACsI,iBAAiB,CAAC;IACpC1H,kBAAkB,CAAC,KAAK,CAAC;EAC7B,CAAC;EAED,MAAM6M,oBAAoB,GAAGA,CAAA,KAAM;IAC/B,IAAI,CAAC9M,eAAe,EAAE,OAAO,IAAI;IAEjC,oBACIjC,OAAA;MAAKgP,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC1BjP,OAAA;QAAKgP,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC1BjP,OAAA;UAAAiP,QAAA,GAAI,oBAAkB,EAAClN,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE4G,IAAI;QAAA;UAAAuG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACjDrP,OAAA;UAAKgP,SAAS,EAAC,aAAa;UAAAC,QAAA,EACvB9M,gBAAgB,CAACmM,GAAG,CAAC,CAAC/H,MAAM,EAAE+I,KAAK,kBAChCtP,OAAA;YAAiBgP,SAAS,EAAC,eAAe;YAACO,OAAO,EAAEA,CAAA,KAAMb,iBAAiB,CAACnI,MAAM,CAAE;YAAA0I,QAAA,gBAChFjP,OAAA;cAAKgP,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACrCjP,OAAA;gBAAAiP,QAAA,GAAO1I,MAAM,CAACsB,IAAI,EAAC,KAAG,EAACtB,MAAM,CAACoC,IAAI;cAAA;gBAAAuG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtCrP,OAAA;gBAAAiP,QAAA,EAAO1I,MAAM,CAACc;cAAW;gBAAA6H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC,eACNrP,OAAA;cAAKgP,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACtCjP,OAAA;gBAAAiP,QAAA,GAAM,WAAS,EAAC1I,MAAM,CAACE,OAAO,EAAC,YAAU,EAACF,MAAM,CAACC,KAAK;cAAA;gBAAA0I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1DrP,OAAA;gBAAAiP,QAAA,GAAO1I,MAAM,CAACe,IAAI,EAAC,GAAC,EAACf,MAAM,CAACgB,IAAI;cAAA;gBAAA2H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA,GARAC,KAAK;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASV,CACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNrP,OAAA;UAAQgP,SAAS,EAAC,aAAa;UAACO,OAAO,EAAEA,CAAA,KAAMrN,kBAAkB,CAAC,KAAK,CAAE;UAAA+M,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd,CAAC;EAED,MAAMG,yBAAyB,GAAGA,CAACzG,UAAU,EAAED,UAAU,EAAE2G,WAAW,KAAK;IAAA,IAAAC,qBAAA,EAAAC,sBAAA;IACvE,IAAI,EAACtO,cAAc,aAAdA,cAAc,gBAAAqO,qBAAA,GAAdrO,cAAc,CAAE4G,aAAa,cAAAyH,qBAAA,gBAAAC,sBAAA,GAA7BD,qBAAA,CAAgCD,WAAW,CAAC,cAAAE,sBAAA,eAA5CA,sBAAA,CAA+C7G,UAAU,CAAC,GAAE;MAC7D,OAAO,IAAI;IACf;IAEA,MAAM8G,iBAAiB,GAAGvO,cAAc,CAAC4G,aAAa,CAACwH,WAAW,CAAC,CAAC3G,UAAU,CAAC;;IAE/E;IACA,MAAM+G,cAAc,GAAGD,iBAAiB,CAACtJ,OAAO,IAAI,EAAE;;IAEtD;IACA,MAAMwJ,oBAAoB,GAAG,CAAC,GAAGD,cAAc,CAAC,CAACrC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KACvD9G,aAAa,CAAC8G,CAAC,CAAClH,KAAK,CAAC,GAAGI,aAAa,CAAC6G,CAAC,CAACjH,KAAK,CAClD,CAAC;;IAED;IACA,MAAMuJ,gBAAgB,GAAG,IAAI5I,GAAG,CAAC,CAAC;IAClC,IAAI6I,WAAW,GAAG,CAAC;IACnB,MAAMnC,eAAe,GAAGnH,UAAU,CAACkJ,iBAAiB,CAACnJ,OAAO,CAAC,IAAI,CAAC;;IAElE;IACA,KAAK,MAAMF,MAAM,IAAIuJ,oBAAoB,EAAE;MACvC,IAAIE,WAAW,GAAGnC,eAAe,EAAE;QAC/BkC,gBAAgB,CAACE,GAAG,CAAC1J,MAAM,CAACsB,IAAI,CAAC;QACjCmI,WAAW,IAAItJ,UAAU,CAACH,MAAM,CAACE,OAAO,CAAC,IAAI,CAAC;MAClD;IACJ;IAEA,MAAMyJ,kBAAkB,GAAGA,CAACvF,UAAU,EAAE8E,WAAW,EAAE3G,UAAU,KAAK;MAAA,IAAAqH,qBAAA,EAAAC,sBAAA;MAChE,MAAMxG,iBAAiB,GAAG;QAAE,GAAGvI;MAAe,CAAC;;MAE/C;MACA,IAAI,GAAA8O,qBAAA,GAACvG,iBAAiB,CAAC3B,aAAa,cAAAkI,qBAAA,gBAAAC,sBAAA,GAA/BD,qBAAA,CAAkCV,WAAW,CAAC,cAAAW,sBAAA,eAA9CA,sBAAA,CAAiDtH,UAAU,CAAC,GAAE;QAC/D7H,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE;UAAEuO,WAAW;UAAE3G;QAAW,CAAC,CAAC;QACxE;MACJ;MAEA,MAAMC,UAAU,GAAGa,iBAAiB,CAAC3B,aAAa,CAACwH,WAAW,CAAC,CAAC3G,UAAU,CAAC;;MAE3E;MACAC,UAAU,CAACzC,OAAO,GAAGyC,UAAU,CAACzC,OAAO,CAACiD,MAAM,CAAChD,MAAM,IAAIA,MAAM,CAACsB,IAAI,KAAK8C,UAAU,CAAC;;MAEpF;MACA5B,UAAU,CAAClD,YAAY,GAAGkD,UAAU,CAACzC,OAAO,CAAC6B,MAAM,CAAC,CAACkI,KAAK,EAAE9J,MAAM,KAC9D8J,KAAK,IAAI3J,UAAU,CAACH,MAAM,CAACE,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,CAC/C,CAAC;;MAED;MACAmD,iBAAiB,CAACiF,gBAAgB,GAAGC,kBAAkB,CAAClF,iBAAiB,CAAC3B,aAAa,EAAEwH,WAAW,CAAC;MAErGnO,iBAAiB,CAACsI,iBAAiB,CAAC;IACxC,CAAC;IAED,oBACI5J,OAAA;MAAKgP,SAAS,EAAC,sBAAsB;MAAAC,QAAA,EAChCa,oBAAoB,CAACxB,GAAG,CAAC,CAAC/H,MAAM,EAAE+I,KAAK,kBACpCtP,OAAA;QAAiBgP,SAAS,EAAG,iBAAgBe,gBAAgB,CAACtB,GAAG,CAAClI,MAAM,CAACsB,IAAI,CAAC,GAAG,aAAa,GAAG,EAAG,EAAE;QAAAoH,QAAA,gBAClGjP,OAAA;UAAKgP,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxBjP,OAAA;YAAMgP,SAAS,EAAC,aAAa;YAAAC,QAAA,GACxB1I,MAAM,CAACsB,IAAI,EAAC,KAAG,EAACtB,MAAM,CAACoC,IAAI,EAAC,IAAE,EAACpC,MAAM,CAACE,OAAO,EAAC,WACnD;UAAA;YAAAyI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPrP,OAAA;YAAMgP,SAAS,EAAC,oBAAoB;YAAAC,QAAA,GAC/B1I,MAAM,CAACc,WAAW,EAAC,KAAG,EAACd,MAAM,CAACe,IAAI,EAAC,GAAC,EAACf,MAAM,CAACgB,IAAI;UAAA;YAAA2H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNrP,OAAA;UAAKgP,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBjP,OAAA;YAAMgP,SAAS,EAAC,cAAc;YAAAC,QAAA,GAAC,SAAO,EAAC1I,MAAM,CAACC,KAAK;UAAA;YAAA0I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3DrP,OAAA;YACIuP,OAAO,EAAEA,CAAA,KAAMW,kBAAkB,CAAC3J,MAAM,CAACsB,IAAI,EAAE4H,WAAW,EAAE3G,UAAU,CAAE;YACxEkG,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAChC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA,GAjBAC,KAAK;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAkBV,CACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEd,CAAC;EAED,MAAMzI,aAAa,GAAIJ,KAAK,IAAK;IAC7B,MAAMG,WAAW,GAAG;MAChB,IAAI,EAAE,GAAG;MAAE,GAAG,EAAE,GAAG;MAAE,IAAI,EAAE,GAAG;MAC9B,IAAI,EAAE,GAAG;MAAE,GAAG,EAAE,GAAG;MAAE,IAAI,EAAE,GAAG;MAC9B,IAAI,EAAE,GAAG;MAAE,GAAG,EAAE,GAAG;MAAE,IAAI,EAAE,GAAG;MAC9B,IAAI,EAAE,GAAG;MAAE,GAAG,EAAE,GAAG;MAAE,IAAI,EAAE,GAAG;MAC9B,GAAG,EAAE;IACT,CAAC;IACD,OAAOA,WAAW,CAACH,KAAK,CAAC,IAAI,CAAC;EAClC,CAAC;EAED,MAAMsI,kBAAkB,GAAGrP,WAAW,CAAC,CAACwI,aAAa,EAAEwH,WAAW,KAAK;IACnE,IAAI,EAACxH,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAGwH,WAAW,CAAC,GAAE;MAC/B,OAAO,KAAK;IAChB;IAEA,IAAI7J,WAAW,GAAG,CAAC;IACnB,IAAIC,YAAY,GAAG,CAAC;;IAEpB;IACAnF,MAAM,CAACqH,MAAM,CAACE,aAAa,CAACwH,WAAW,CAAC,CAAC,CAACzJ,OAAO,CAAC+C,UAAU,IAAI;MAC5D,IAAI,CAACA,UAAU,CAACzC,OAAO,IAAIyC,UAAU,CAACzC,OAAO,CAAC8C,MAAM,KAAK,CAAC,EAAE;;MAE5D;MACA,MAAMmE,aAAa,GAAG,CAAC,GAAGxE,UAAU,CAACzC,OAAO,CAAC,CAACkH,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KACpD9G,aAAa,CAAC8G,CAAC,CAAClH,KAAK,CAAC,GAAGI,aAAa,CAAC6G,CAAC,CAACjH,KAAK,CAClD,CAAC;;MAED;MACA,MAAMqH,eAAe,GAAGnH,UAAU,CAACqC,UAAU,CAACtC,OAAO,CAAC,IAAI,CAAC;MAE3D,IAAIuJ,WAAW,GAAG,CAAC;MACnB,IAAIM,WAAW,GAAG,CAAC;;MAEnB;MACA,OAAON,WAAW,GAAGnC,eAAe,IAAIyC,WAAW,GAAG/C,aAAa,CAACnE,MAAM,EAAE;QACxE,MAAM7C,MAAM,GAAGgH,aAAa,CAAC+C,WAAW,CAAC;QACzC,MAAMvC,aAAa,GAAGrH,UAAU,CAACH,MAAM,CAACE,OAAO,CAAC,IAAI,CAAC;QACrD,MAAME,WAAW,GAAGC,aAAa,CAACL,MAAM,CAACC,KAAK,CAAC;QAE/CZ,WAAW,IAAIe,WAAW,GAAGoH,aAAa;QAC1ClI,YAAY,IAAIkI,aAAa;QAC7BiC,WAAW,IAAIjC,aAAa;QAC5BuC,WAAW,EAAE;MACjB;IACJ,CAAC,CAAC;IAEF,OAAOzK,YAAY,GAAG,CAAC,GAAG,CAACD,WAAW,GAAGC,YAAY,EAAE0K,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK;EAC7E,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,kBAAkB,GAAGA,CAAC9H,OAAO,EAAErH,cAAc,KAAK;IAAA,IAAAoP,sBAAA;IACpD,IAAI,CAAC/H,OAAO,CAACE,YAAY,CAACX,aAAa,IAAI,GAAAwI,sBAAA,GAACpP,cAAc,CAAC4G,aAAa,cAAAwI,sBAAA,eAA5BA,sBAAA,CAA+B/H,OAAO,CAACC,IAAI,CAAC,GAAE;MACtF,OAAO,KAAK;IAChB;IAEA,OAAOjI,MAAM,CAACC,OAAO,CAAC+H,OAAO,CAACE,YAAY,CAACX,aAAa,CAAC,CACpDyI,KAAK,CAAC,CAAC,CAAC5H,UAAU,EAAE6H,GAAG,CAAC,KAAK;MAC1B,MAAMlI,cAAc,GAAGpH,cAAc,CAAC4G,aAAa,CAACS,OAAO,CAACC,IAAI,CAAC;MACjE,IAAI,CAACF,cAAc,CAACK,UAAU,CAAC,EAAE,OAAO,KAAK;MAE7C,MAAMC,UAAU,GAAGN,cAAc,CAACK,UAAU,CAAC;MAC7C,MAAMjD,YAAY,GAAGkD,UAAU,CAAClD,YAAY,IAAI,CAAC;MACjD,MAAMgI,eAAe,GAAGnH,UAAU,CAACiK,GAAG,CAAClK,OAAO,CAAC,IAAI,CAAC;;MAEpD;MACA,IAAIqC,UAAU,CAACrB,WAAW,CAAC,CAAC,CAAC7G,QAAQ,CAAC,SAAS,CAAC,IAAIkI,UAAU,CAACrB,WAAW,CAAC,CAAC,CAAC7G,QAAQ,CAAC,YAAY,CAAC,EAAE;QACjG;QACA,MAAMgQ,iBAAiB,GAAG7H,UAAU,CAACzC,OAAO,CAACiD,MAAM,CAAChD,MAAM,IACtDA,MAAM,CAACoC,IAAI,CAAClB,WAAW,CAAC,CAAC,CAAC7G,QAAQ,CAAC,SAAS,CAAC,IAC7C2F,MAAM,CAACoC,IAAI,CAAClB,WAAW,CAAC,CAAC,CAAC7G,QAAQ,CAAC,YAAY,CACnD,CAAC;;QAED;QACA,MAAMiQ,eAAe,GAAGD,iBAAiB,CAACzI,MAAM,CAAC,CAACsB,GAAG,EAAElD,MAAM,KACzDkD,GAAG,GAAG/C,UAAU,CAACH,MAAM,CAACE,OAAO,IAAI,CAAC,CAAC,EAAE,CAC3C,CAAC;;QAED;QACA,IAAImK,iBAAiB,CAACxH,MAAM,GAAG,CAAC,EAAE;UAC9B,OAAOyH,eAAe,IAAIhD,eAAe;QAC7C;MACJ;;MAEA;MACA,MAAMiD,mBAAmB,GAAG,CAACH,GAAG,CAAChH,YAAY,IACzCZ,UAAU,CAACzC,OAAO,CAACqF,IAAI,CAACpF,MAAM,IAAIA,MAAM,CAACoC,IAAI,CAAClB,WAAW,CAAC,CAAC,CAAC7G,QAAQ,CAAC,KAAK,CAAC,CAAC;;MAEhF;MACA,MAAMmQ,cAAc,GAAG,CAACJ,GAAG,CAACjH,SAAS,IACjCX,UAAU,CAACzC,OAAO,CAACoK,KAAK,CAACnK,MAAM,IAAI;QAC/B,MAAMyK,UAAU,GAAGjF,QAAQ,CAACxF,MAAM,CAACgB,IAAI,CAAC;QACxC,MAAM0J,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QAC5C,OAAQF,WAAW,GAAGD,UAAU,IAAKjF,QAAQ,CAAC4E,GAAG,CAACjH,SAAS,CAAC;MAChE,CAAC,CAAC;MAEN,OAAO7D,YAAY,IAAIgI,eAAe,IAAIiD,mBAAmB,IAAIC,cAAc;IACnF,CAAC,CAAC;EACV,CAAC;EAED,MAAMK,qBAAqB,GAAGA,CAAC1I,OAAO,EAAEnH,WAAW,KAAK;IACpD,MAAM8P,aAAa,GAAG3I,OAAO,CAACE,YAAY,CAAC0I,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC;IAChF;IACA,MAAMC,SAAS,GAAGxF,QAAQ,CAAC,CAAAxK,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEiQ,yBAAyB,MAAIjQ,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEkQ,sBAAsB,KAAI,CAAC,CAAC;IAC9G,OAAOF,SAAS,IAAIF,aAAa;EACrC,CAAC;EAED,MAAMK,mBAAmB,GAAGA,CAAChJ,OAAO,EAAEnH,WAAW,KAAK;IAClD,MAAM8P,aAAa,GAAG3I,OAAO,CAACE,YAAY,CAAC0I,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC;IAC7E;IACA,MAAMC,SAAS,GAAGxF,QAAQ,CAAC,CAAAxK,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEoQ,eAAe,MAAIpQ,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEqQ,cAAc,KAAI,CAAC,CAAC;IAC5F,OAAOL,SAAS,IAAIF,aAAa;EACrC,CAAC;EACD,MAAMQ,sBAAsB,GAAGA,CAACtQ,WAAW,EAAEuQ,cAAc,KAAK;IAC5D,IAAI,CAACvQ,WAAW,EAAE,OAAO,CAAC;IAC1B,IAAIuQ,cAAc,KAAK,oBAAoB,EAAE;MACzC,OAAO/F,QAAQ,CAACxK,WAAW,CAACiQ,yBAAyB,IAAIjQ,WAAW,CAACkQ,sBAAsB,IAAI,CAAC,CAAC;IACrG;IACA,IAAIK,cAAc,KAAK,iBAAiB,EAAE;MACtC,OAAO/F,QAAQ,CAACxK,WAAW,CAACoQ,eAAe,IAAIpQ,WAAW,CAACqQ,cAAc,IAAI,CAAC,CAAC;IACnF;IACA,OAAO,CAAC;EACZ,CAAC;EAGD,MAAMjD,0BAA0B,GAAIlJ,WAAW,IAAK;IAChD,IAAI,CAAC8C,KAAK,CAACC,OAAO,CAAC/C,WAAW,CAAC,EAAE;MAC7BxE,OAAO,CAACU,KAAK,CAAC,2BAA2B,EAAE8D,WAAW,CAAC;MACvD,OAAO;QACHsM,oBAAoB,EAAE,EAAE;QACxBhL,aAAa,EAAE,CAAC;QAChBC,UAAU,EAAE,CAAC;QACbnB,YAAY,EAAE,CAAC;QACfE,cAAc,EAAE;MACpB,CAAC;IACL;;IAEA;IACA,MAAMgM,oBAAoB,GAAGtM,WAAW,CAACuM,OAAO,CAAC/L,UAAU,IAAI;MAC3D,IAAI,CAACA,UAAU,EAAE;QACbhF,OAAO,CAACgR,IAAI,CAAC,2BAA2B,EAAEhM,UAAU,CAAC;QACrD,OAAO,EAAE;MACb;MAEA,IAAI;QAAA,IAAAiM,sBAAA;QACA;QACA,KAAAA,sBAAA,GAAIjM,UAAU,CAACG,eAAe,cAAA8L,sBAAA,eAA1BA,sBAAA,CAA4B/L,SAAS,EAAE;UAAA,IAAAgM,sBAAA,EAAAC,sBAAA;UACvC;UACA,MAAM/K,WAAW,GAAG,EAAA8K,sBAAA,GAAAlM,UAAU,CAACG,eAAe,cAAA+L,sBAAA,wBAAAC,sBAAA,GAA1BD,sBAAA,CAA4BE,gBAAgB,cAAAD,sBAAA,uBAA5CA,sBAAA,CAA8C/K,WAAW,KAAI,qBAAqB;UACtG,OAAOpB,UAAU,CAACG,eAAe,CAACD,SAAS,CAAC6L,OAAO,CAAC3L,QAAQ,IAAI;YAC5D,IAAI,EAACA,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEC,OAAO,GAAE;cACpBrF,OAAO,CAACgR,IAAI,CAAC,wBAAwB,EAAE5L,QAAQ,CAAC;cAChD,OAAO,EAAE;YACb;YACA,OAAOA,QAAQ,CAACC,OAAO,CAACgI,GAAG,CAAC/H,MAAM,KAAK;cACnC,GAAGA,MAAM;cACTc,WAAW;cACXC,IAAI,EAAEjB,QAAQ,CAACiB,IAAI,IAAI,cAAc;cACrCC,IAAI,EAAElB,QAAQ,CAACkB,IAAI,IAAI;YAC3B,CAAC,CAAC,CAAC;UACP,CAAC,CAAC;QACN,CAAC,MAAM,IAAItB,UAAU,CAACE,SAAS,EAAE;UAC7B;UACA,MAAMkB,WAAW,GAAGpB,UAAU,CAAC0C,IAAI,IAAI,qBAAqB;UAC5D,OAAO1C,UAAU,CAACE,SAAS,CAAC6L,OAAO,CAAC3L,QAAQ,IAAI;YAC5C,IAAI,EAACA,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEC,OAAO,GAAE;cACpBrF,OAAO,CAACgR,IAAI,CAAC,wBAAwB,EAAE5L,QAAQ,CAAC;cAChD,OAAO,EAAE;YACb;YACA,OAAOA,QAAQ,CAACC,OAAO,CAACgI,GAAG,CAAC/H,MAAM,KAAK;cACnC,GAAGA,MAAM;cACTc,WAAW;cACXC,IAAI,EAAEjB,QAAQ,CAACiB,IAAI,IAAI,cAAc;cACrCC,IAAI,EAAElB,QAAQ,CAACkB,IAAI,IAAI;YAC3B,CAAC,CAAC,CAAC;UACP,CAAC,CAAC;QACN;MACJ,CAAC,CAAC,OAAO5F,KAAK,EAAE;QACZV,OAAO,CAACU,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD,OAAO,EAAE;MACb;MACAV,OAAO,CAACgR,IAAI,CAAC,+BAA+B,EAAEhM,UAAU,CAAC;MACzD,OAAO,EAAE;IACb,CAAC,CAAC;;IAEF;IACA,IAAIL,WAAW,GAAG,CAAC;IACnB,IAAIC,YAAY,GAAG,CAAC;IACpB,IAAIC,aAAa,GAAG,CAAC;IACrB,IAAIC,cAAc,GAAG,CAAC;IAEtBgM,oBAAoB,CAAC/L,OAAO,CAACO,MAAM,IAAI;MACnC,IAAI;QACA,IAAIA,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEC,KAAK,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC5F,QAAQ,CAAC2F,MAAM,CAACC,KAAK,CAAC,EAAE;UAC1D,MAAMC,OAAO,GAAGC,UAAU,CAACH,MAAM,CAACE,OAAO,CAAC,IAAI,CAAC;UAC/C,MAAME,WAAW,GAAGC,aAAa,CAACL,MAAM,CAACC,KAAK,CAAC;UAE/C,IAAI,CAACK,KAAK,CAACJ,OAAO,CAAC,IAAI,CAACI,KAAK,CAACF,WAAW,CAAC,EAAE;YACxCf,WAAW,IAAIa,OAAO,GAAGE,WAAW;YACpCd,YAAY,IAAIY,OAAO;;YAEvB;YACA,IAAIF,MAAM,CAACO,UAAU,EAAE;cACnBhB,aAAa,IAAIW,OAAO,GAAGE,WAAW;cACtCZ,cAAc,IAAIU,OAAO;YAC7B;UACJ,CAAC,MAAM;YACHxF,OAAO,CAACgR,IAAI,CAAC,kCAAkC,EAAE;cAAE1L,MAAM;cAAEE,OAAO;cAAEE;YAAY,CAAC,CAAC;UACtF;QACJ;MACJ,CAAC,CAAC,OAAOhF,KAAK,EAAE;QACZV,OAAO,CAACU,KAAK,CAAC,0BAA0B,EAAEA,KAAK,EAAE4E,MAAM,CAAC;MAC5D;IACJ,CAAC,CAAC;IAEF,MAAMQ,aAAa,GAAGlB,YAAY,GAAG,CAAC,GAAGD,WAAW,GAAGC,YAAY,GAAG,CAAC;IACvE,MAAMmB,UAAU,GAAGjB,cAAc,GAAG,CAAC,GAAGD,aAAa,GAAGC,cAAc,GAAG,CAAC;IAE1E,OAAO;MACHgM,oBAAoB;MACpBhL,aAAa;MACbC,UAAU;MACVnB,YAAY;MACZE;IACJ,CAAC;EACL,CAAC;EAED,MAAMuM,uBAAuB,GAAI5J,OAAO,IAAK;IAAA,IAAA6J,sBAAA,EAAAC,qBAAA;IACzC;IACA,IAAIpP,wBAAwB,CAACxC,QAAQ,CAAC8H,OAAO,CAACC,IAAI,CAAC,EAAE;MACjD,OAAO,UAAU;IACrB;;IAEA;IACA;IACA,MAAM8J,UAAU,GAAG/J,OAAO,CAACE,YAAY,CAACX,aAAa,IACjDvH,MAAM,CAACgS,IAAI,CAAChK,OAAO,CAACE,YAAY,CAACX,aAAa,CAAC,CAACmB,MAAM,GAAG,CAAC;IAE9D,IAAI,CAACqJ,UAAU,EAAE;MACb,OAAO,YAAY;IACvB;;IAEA;IACA,MAAMhK,cAAc,GAAGpH,cAAc,aAAdA,cAAc,wBAAAkR,sBAAA,GAAdlR,cAAc,CAAE4G,aAAa,cAAAsK,sBAAA,uBAA7BA,sBAAA,CAAgC7J,OAAO,CAACC,IAAI,CAAC;IACpE,IAAI,CAACF,cAAc,IAAI/H,MAAM,CAACgS,IAAI,CAACjK,cAAc,CAAC,CAACW,MAAM,KAAK,CAAC,EAAE;MAC7D,OAAO,YAAY;IACvB;;IAEA;IACA,MAAMuJ,QAAQ,GAAGtR,cAAc,CAACwI,cAAc,IAAInB,OAAO,CAACE,YAAY,CAACgK,GAAG,CAACC,OAAO;IAClF,MAAMC,eAAe,GAAGpM,UAAU,CAACrF,cAAc,CAACyI,WAAW,CAAC,IAAIpB,OAAO,CAACE,YAAY,CAACgK,GAAG,CAACG,OAAO;IAElG,MAAMC,YAAY,GAAGtS,MAAM,CAACC,OAAO,CAAC+H,OAAO,CAACE,YAAY,CAACX,aAAa,CAAC,CAClEyI,KAAK,CAAC,CAAC,CAAC5H,UAAU,EAAE6H,GAAG,CAAC,KAAK;MAC1B,IAAI,CAAClI,cAAc,CAACK,UAAU,CAAC,EAAE,OAAO,KAAK,CAAC,CAAC;MAC/C,MAAMjD,YAAY,GAAG4C,cAAc,CAACK,UAAU,CAAC,CAACjD,YAAY,IAAI,CAAC;MACjE,MAAMoN,kBAAkB,GAAGvM,UAAU,CAACiK,GAAG,CAAClK,OAAO,CAAC,CAAC,CAAC;;MAEpD;MACA,IAAII,KAAK,CAACoM,kBAAkB,CAAC,EAAE;QAC1B;QACA,OAAOxK,cAAc,CAACK,UAAU,CAAC,CAACxC,OAAO,IAAImC,cAAc,CAACK,UAAU,CAAC,CAACxC,OAAO,CAAC8C,MAAM,GAAG,CAAC;MAC/F;MACA;MACA,OAAOvD,YAAY,IAAIoN,kBAAkB;IAC7C,CAAC,CAAC;;IAEN;IACA,MAAMC,gBAAgB,GAAGnH,QAAQ,CAAC,CAAAxK,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEiQ,yBAAyB,MAAIjQ,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEkQ,sBAAsB,KAAI,CAAC,CAAC;IACrH,MAAMG,cAAc,GAAG7F,QAAQ,CAAC,CAAAxK,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEoQ,eAAe,MAAIpQ,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEqQ,cAAc,KAAI,CAAC,CAAC;IAEjG,MAAMuB,gBAAgB,GAAGD,gBAAgB,KACpCxK,OAAO,CAACE,YAAY,CAAC0I,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;IAChE,MAAM8B,cAAc,GAAGxB,cAAc,KAChClJ,OAAO,CAACE,YAAY,CAAC0I,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;;IAE7D;IACA,IAAI+B,sBAAsB,GAAG,IAAI,CAAC,CAAC;IACnC,MAAMC,mBAAmB,GAAG,EAAAd,qBAAA,GAAA9J,OAAO,CAACE,YAAY,CAAC2K,kBAAkB,cAAAf,qBAAA,uBAAvCA,qBAAA,CAAyCgB,cAAc,KAAI,EAAE,CAAC,CAAC;IAC3F,MAAMC,WAAW,GAAGlS,WAAW,CAAC,CAAC;;IAEjC;IACA,MAAMmS,aAAa,GAAGJ,mBAAmB,CAAC7L,WAAW,CAAC,CAAC,CAACkM,KAAK,CAAC,SAAS,CAAC,CAAC;IAAA,CAClCrF,GAAG,CAACsF,CAAC,IAAIA,CAAC,CAACjM,IAAI,CAAC,CAAC,CAAC,CAClB4B,MAAM,CAACqK,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC;;IAEvD;IACA,IAAIF,aAAa,CAAC9S,QAAQ,CAAC,KAAK,CAAC,EAAE;MAC/B,IAAI,EAAC6S,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEI,gBAAgB,KAAI,EAACJ,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEK,sBAAsB,KAAI,EAACL,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEM,4BAA4B,GAAE;QACtHV,sBAAsB,GAAG,KAAK,CAAC,CAAC;MACpC;IACJ;;IAEA;IACA,IAAIK,aAAa,CAAC9S,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAClC,IAAI,EAAC6S,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEO,gBAAgB,GAAE;QAChCX,sBAAsB,GAAG,KAAK,CAAC,CAAC;MACpC;IACJ;;IAEA;IACA,IAAIK,aAAa,CAAC9S,QAAQ,CAAC,QAAQ,CAAC,IAAI8S,aAAa,CAAC9S,QAAQ,CAAC,OAAO,CAAC,EAAE;MAAE;MACvE,IAAI,EAAC6S,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEQ,gBAAgB,GAAE;QAChCZ,sBAAsB,GAAG,KAAK,CAAC,CAAC;MACpC;IACJ;;IAEA;IACA;IACA,IAAIV,QAAQ,IAAIG,eAAe,IAAIE,YAAY,IAAIG,gBAAgB,IAAIC,cAAc,IAAIC,sBAAsB,EAAE;MAC7G,OAAO,UAAU;IACrB;;IAEA;IACA,OAAO,YAAY;EACvB,CAAC;;EAED;EACA,MAAMa,uBAAuB,GAAG5U,KAAK,CAAC6U,OAAO,CAAC,MAAM;IAChD,IAAI,CAACtS,QAAQ,IAAI,CAACR,cAAc,EAAE,OAAO,CAAC,CAAC;IAE3C,MAAM+S,KAAK,GAAG,CAAC,CAAC;IAChBvS,QAAQ,CAACmE,OAAO,CAAC0C,OAAO,IAAI;MACxB0L,KAAK,CAAC1L,OAAO,CAACC,IAAI,CAAC,GAAG2J,uBAAuB,CAAC5J,OAAO,CAAC;IAC1D,CAAC,CAAC;IACF,OAAO0L,KAAK;EAChB,CAAC,EAAE,CAACvS,QAAQ,EAAER,cAAc,EAAEE,WAAW,CAAC,CAAC;EAE3C,MAAM8S,aAAa,GAAG/U,KAAK,CAAC6U,OAAO,CAAC,MAAM;IACtC,IAAI,CAACtS,QAAQ,IAAI,CAACR,cAAc,EAAE,OAAO;MAAEkB,QAAQ,EAAE,CAAC;MAAEC,UAAU,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAE,CAAC;IAEtF,MAAM6R,mBAAmB,GAAGzS,QAAQ,CAACsG,MAAM,CAAC,CAACC,GAAG,EAAEM,OAAO,KAAK;MAC1D,MAAM5D,MAAM,GAAGoP,uBAAuB,CAACxL,OAAO,CAACC,IAAI,CAAC;MACpDP,GAAG,CAACtD,MAAM,CAAC,EAAE;MACb,OAAOsD,GAAG;IACd,CAAC,EAAE;MAAE7F,QAAQ,EAAE,CAAC;MAAEC,UAAU,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAE,CAAC,CAAC;IAEjD,OAAO6R,mBAAmB;EAC9B,CAAC,EAAE,CAACJ,uBAAuB,CAAC,CAAC;;EAE7B;EACA,MAAMK,2BAA2B,GAAGA,CAACC,WAAW,EAAEC,YAAY,EAAE/L,OAAO,EAAEI,UAAU,KAAK;IACpF,IAAI4L,KAAK,GAAG,KAAK;IACjB,IAAIC,UAAU,GAAG,CAAC;IAElB,IAAIjM,OAAO,IAAII,UAAU,EAAE;MACvB,IAAIA,UAAU,KAAK,oBAAoB,IAAIA,UAAU,KAAK,iBAAiB,EAAE;QACzE;QACA4L,KAAK,GAAGhO,UAAU,CAAC+N,YAAY,CAAC,IAAI/N,UAAU,CAAC8N,WAAW,CAAC;QAC3DG,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAEnO,UAAU,CAAC+N,YAAY,CAAC,GAAG/N,UAAU,CAAC8N,WAAW,CAAC,GAAI,GAAG,EAAE,GAAG,CAAC;MAC1F,CAAC,MAAM;QAAA,IAAAM,sBAAA;QACH;QACA,MAAMrM,cAAc,GAAGpH,cAAc,aAAdA,cAAc,wBAAAyT,sBAAA,GAAdzT,cAAc,CAAE4G,aAAa,cAAA6M,sBAAA,uBAA7BA,sBAAA,CAAgCpM,OAAO,CAACC,IAAI,CAAC;QACpE,IAAIF,cAAc,IAAIA,cAAc,CAACK,UAAU,CAAC,EAAE;UAC9C,MAAMjD,YAAY,GAAG4C,cAAc,CAACK,UAAU,CAAC,CAACjD,YAAY,IAAI,CAAC;UACjE,MAAMoN,kBAAkB,GAAGvM,UAAU,CAAC8N,WAAW,CAAC,CAAC,CAAC;;UAEpD;UACA,IAAI3N,KAAK,CAACoM,kBAAkB,CAAC,EAAE;YAC3ByB,KAAK,GAAGjM,cAAc,CAACK,UAAU,CAAC,CAACxC,OAAO,IAAImC,cAAc,CAACK,UAAU,CAAC,CAACxC,OAAO,CAAC8C,MAAM,GAAG,CAAC;YAC3FuL,UAAU,GAAGD,KAAK,GAAG,GAAG,GAAG,CAAC;UAChC,CAAC,MAAM;YACH;YACAA,KAAK,GAAG7O,YAAY,IAAIoN,kBAAkB;YAC1C0B,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAEhP,YAAY,GAAGoN,kBAAkB,GAAI,GAAG,EAAE,GAAG,CAAC;UACzE;QACJ;MACJ;IACJ,CAAC,MAAM;MACH;MACAyB,KAAK,GAAGD,YAAY,IAAID,WAAW;MACnCG,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAEJ,YAAY,GAAGD,WAAW,GAAI,GAAG,EAAE,GAAG,CAAC;IAClE;IAEA,oBACIxU,OAAA;MAAKgP,SAAS,EAAG,yBAAwB0F,KAAK,GAAG,KAAK,GAAG,SAAU,EAAE;MAAAzF,QAAA,gBACjEjP,OAAA;QAAKgP,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACjCjP,OAAA;UAAKgP,SAAS,EAAC,cAAc;UAAAC,QAAA,eACzBjP,OAAA;YACIgP,SAAS,EAAC,eAAe;YACzB+F,KAAK,EAAE;cAAEC,KAAK,EAAG,GAAEL,UAAW;YAAG;UAAE;YAAAzF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNrP,OAAA;UAAMgP,SAAS,EAAC,eAAe;UAAAC,QAAA,GAAE0F,UAAU,CAACpE,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;QAAA;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC,eACNrP,OAAA;QAAKgP,SAAS,EAAC,yBAAyB;QAAAC,QAAA,EACnCyF,KAAK,GAAG,GAAG,GAAG;MAAG;QAAAxF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd,CAAC;;EAED;EACA,MAAM4F,mBAAmB,GAAGA,CAACC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,KAAK;IACrD,MAAMV,KAAK,GAAGS,MAAM,IAAID,QAAQ;IAChC,MAAMP,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAEM,MAAM,GAAGD,QAAQ,GAAI,GAAG,EAAE,GAAG,CAAC;IAE3D,oBACIlV,OAAA;MAAKgP,SAAS,EAAC,uBAAuB;MAAAC,QAAA,gBAClCjP,OAAA;QAAKgP,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACvBjP,OAAA;UAAAiP,QAAA,EAAKmG;QAAK;UAAAlG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAChBrP,OAAA;UAAKgP,SAAS,EAAG,cAAa0F,KAAK,GAAG,KAAK,GAAG,SAAU,EAAE;UAAAzF,QAAA,EACrDyF,KAAK,GAAG,GAAG,GAAG;QAAG;UAAAxF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNrP,OAAA;QAAKgP,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxBjP,OAAA;UAAKgP,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvBjP,OAAA;YAAMgP,SAAS,EAAC,UAAU;YAAAC,QAAA,GAAC,YAAU,EAACiG,QAAQ,CAAC3E,OAAO,CAAC,CAAC,CAAC;UAAA;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjErP,OAAA;YAAMgP,SAAS,EAAC,QAAQ;YAAAC,QAAA,GAAC,YAAU,EAACkG,MAAM,CAAC5E,OAAO,CAAC,CAAC,CAAC;UAAA;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC,eACNrP,OAAA;UAAKgP,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBjP,OAAA;YAAKgP,SAAS,EAAC,cAAc;YAAAC,QAAA,eACzBjP,OAAA;cACIgP,SAAS,EAAC,eAAe;cACzB+F,KAAK,EAAE;gBAAEC,KAAK,EAAG,GAAEL,UAAW;cAAG;YAAE;cAAAzF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNrP,OAAA;YAAMgP,SAAS,EAAC,eAAe;YAAAC,QAAA,GAAE0F,UAAU,CAACpE,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;UAAA;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd,CAAC;;EAED;EACA,MAAMgG,0BAA0B,GAAGA,CAACH,QAAQ,EAAEC,MAAM,EAAEC,KAAK,KAAK;IAC5D,MAAMV,KAAK,GAAGS,MAAM,IAAID,QAAQ;IAChC,MAAMP,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAEM,MAAM,GAAGD,QAAQ,GAAI,GAAG,EAAE,GAAG,CAAC;IAE3D,oBACIlV,OAAA;MAAKgP,SAAS,EAAC,8BAA8B;MAAAC,QAAA,gBACzCjP,OAAA;QAAKgP,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAC9BjP,OAAA;UAAKgP,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAC3BmG,KAAK,CAACxU,QAAQ,CAAC,cAAc,CAAC,GAAG,IAAI,GACrCwU,KAAK,CAACxU,QAAQ,CAAC,WAAW,CAAC,GAAG,OAAO,GAAG;QAAI;UAAAsO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACNrP,OAAA;UAAAiP,QAAA,EAAKmG;QAAK;UAAAlG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAChBrP,OAAA;UAAKgP,SAAS,EAAG,qBAAoB0F,KAAK,GAAG,KAAK,GAAG,SAAU,EAAE;UAAAzF,QAAA,EAC5DyF,KAAK,GAAG,GAAG,GAAG;QAAG;UAAAxF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNrP,OAAA;QAAKgP,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBAC/BjP,OAAA;UAAKgP,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAC9BjP,OAAA;YAAMgP,SAAS,EAAC,UAAU;YAAAC,QAAA,GAAC,YAAU,EAACiG,QAAQ,EAAC,MAAI;UAAA;YAAAhG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1DrP,OAAA;YAAMgP,SAAS,EAAC,QAAQ;YAAAC,QAAA,GAAC,cAAY,EAACkG,MAAM,EAAC,MAAI;UAAA;YAAAjG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC,eACNrP,OAAA;UAAKgP,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAChCjP,OAAA;YAAKgP,SAAS,EAAC,cAAc;YAAAC,QAAA,eACzBjP,OAAA;cACIgP,SAAS,EAAC,eAAe;cACzB+F,KAAK,EAAE;gBAAEC,KAAK,EAAG,GAAEL,UAAW;cAAG;YAAE;cAAAzF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNrP,OAAA;YAAMgP,SAAS,EAAC,eAAe;YAAAC,QAAA,GAAE0F,UAAU,CAACpE,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;UAAA;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd,CAAC;EAED,MAAMiG,aAAa,GAAIC,OAAO,IAAK;IAC/BjT,mBAAmB,CAACkT,IAAI,KAAK;MACzB,GAAGA,IAAI;MACP,CAACD,OAAO,GAAG,CAACC,IAAI,CAACD,OAAO;IAC5B,CAAC,CAAC,CAAC;EACP,CAAC;EAED,MAAME,aAAa,GAAIhG,WAAW,IAAK;IACnC9M,mBAAmB,CAAC6S,IAAI,KAAK;MACzB,GAAGA,IAAI;MACP,CAAC/F,WAAW,GAAG,CAAC+F,IAAI,CAAC/F,WAAW;IACpC,CAAC,CAAC,CAAC;EACP,CAAC;EAED,MAAMiG,YAAY,GAAGA,CAACjG,WAAW,EAAEkG,OAAO,KAAK;IAC3C9S,aAAa,CAAC2S,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAAC/F,WAAW,GAAGkG;IACnB,CAAC,CAAC,CAAC;EACP,CAAC;;EAED;EACA,MAAMC,oBAAoB,GAAI3N,aAAa,IAAK;IAC5C,IAAIrC,WAAW,GAAG,CAAC;IACnB,IAAIC,YAAY,GAAG,CAAC;IACpB,IAAIC,aAAa,GAAG,CAAC;IACrB,IAAIC,cAAc,GAAG,CAAC;;IAEtB;IACArF,MAAM,CAACqH,MAAM,CAACE,aAAa,IAAI,CAAC,CAAC,CAAC,CAACjC,OAAO,CAACyC,cAAc,IAAI;MACzD/H,MAAM,CAACqH,MAAM,CAACU,cAAc,IAAI,CAAC,CAAC,CAAC,CAACzC,OAAO,CAAC6P,MAAM,IAAI;QAClD,CAACA,MAAM,CAACvP,OAAO,IAAI,EAAE,EAAEN,OAAO,CAACO,MAAM,IAAI;UACrC,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC3F,QAAQ,CAAC2F,MAAM,CAACC,KAAK,CAAC,EAAE;YACzC,MAAMC,OAAO,GAAGC,UAAU,CAACH,MAAM,CAACE,OAAO,CAAC,IAAI,CAAC;YAC/C,MAAME,WAAW,GAAGC,aAAa,CAACL,MAAM,CAACC,KAAK,CAAC;YAE/C,IAAI,CAACK,KAAK,CAACJ,OAAO,CAAC,IAAI,CAACI,KAAK,CAACF,WAAW,CAAC,EAAE;cACxCf,WAAW,IAAIa,OAAO,GAAGE,WAAW;cACpCd,YAAY,IAAIY,OAAO;cAEvB,IAAIF,MAAM,CAACO,UAAU,EAAE;gBACnBhB,aAAa,IAAIW,OAAO,GAAGE,WAAW;gBACtCZ,cAAc,IAAIU,OAAO;cAC7B;YACJ;UACJ;QACJ,CAAC,CAAC;MACN,CAAC,CAAC;IACN,CAAC,CAAC;IAEF,OAAO;MACHqP,aAAa,EAAEjQ,YAAY,GAAG,CAAC,GAAG,CAACD,WAAW,GAAGC,YAAY,EAAE0K,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK;MACjFvJ,UAAU,EAAEjB,cAAc,GAAG,CAAC,GAAG,CAACD,aAAa,GAAGC,cAAc,EAAEwK,OAAO,CAAC,CAAC,CAAC,GAAG;IACnF,CAAC;EACL,CAAC;;EAED;EACA,MAAMwF,oBAAoB,GAAItG,WAAW,IAAK;IAC1C;IACA,IAAI,CAACrM,wBAAwB,CAACxC,QAAQ,CAAC6O,WAAW,CAAC,EAAE;MACjD,MAAMuG,uBAAuB,GAAG,CAAC,GAAG5S,wBAAwB,EAAEqM,WAAW,CAAC;MAC1EpM,2BAA2B,CAAC2S,uBAAuB,CAAC;;MAEpD;MACA,MAAMC,cAAc,GAAG;QACnBpU,QAAQ,EAAEA,QAAQ;QAClBR,cAAc,EAAEA,cAAc;QAC9BE,WAAW,EAAEA,WAAW;QACxB6B,wBAAwB,EAAE4S;MAC9B,CAAC;MACDjM,kBAAkB,CAACkM,cAAc,CAAC;IACtC;EACJ,CAAC;;EAED;EACA,MAAMC,oBAAoB,GAAIzG,WAAW,IAAK;IAC1C;IACA,MAAMuG,uBAAuB,GAAG5S,wBAAwB,CAACmG,MAAM,CAACZ,IAAI,IAAIA,IAAI,KAAK8G,WAAW,CAAC;IAC7FpM,2BAA2B,CAAC2S,uBAAuB,CAAC;;IAEpD;IACA,MAAMC,cAAc,GAAG;MACnBpU,QAAQ,EAAEA,QAAQ;MAClBR,cAAc,EAAEA,cAAc;MAC9BE,WAAW,EAAEA,WAAW;MACxB6B,wBAAwB,EAAE4S;IAC9B,CAAC;IACDjM,kBAAkB,CAACkM,cAAc,CAAC;EACtC,CAAC;;EAED;EACA,MAAME,eAAe,GAAGA,CAAA,KAAM;IAC1B,IAAI,CAACtU,QAAQ,EAAE,OAAO,EAAE;IACxB,MAAMpB,MAAM,GAAG,IAAI0G,GAAG,CAAC,CAAC;IACxBtF,QAAQ,CAACmE,OAAO,CAAC0C,OAAO,IAAI;MAAA,IAAA0N,eAAA;MACxB,KAAAA,eAAA,GAAI1N,OAAO,CAAC2N,MAAM,cAAAD,eAAA,eAAdA,eAAA,CAAgB/V,KAAK,EAAE;QACvBI,MAAM,CAACwP,GAAG,CAACvH,OAAO,CAAC2N,MAAM,CAAChW,KAAK,CAACE,WAAW,CAAC,CAAC,CAAC;MAClD;IACJ,CAAC,CAAC;IACF,OAAOgI,KAAK,CAAC+N,IAAI,CAAC7V,MAAM,CAAC,CAAC+M,IAAI,CAAC,CAAC;EACpC,CAAC;;EAED;EACA,MAAM+I,4BAA4B,GAAGA,CAAA,KAAM;IACvC,IAAI,CAAC1U,QAAQ,IAAI,CAACR,cAAc,EAAE,OAAO,EAAE;IAE3C,IAAImV,gBAAgB,GAAG,CAAC,GAAG3U,QAAQ,CAAC;;IAEpC;IACA,IAAI2B,UAAU,EAAE;MACZgT,gBAAgB,GAAGA,gBAAgB,CAACjN,MAAM,CAACb,OAAO;QAAA,IAAA+N,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA;QAAA,OAC9CpO,OAAO,CAACC,IAAI,CAAClB,WAAW,CAAC,CAAC,CAAC7G,QAAQ,CAAC4C,UAAU,CAACiE,WAAW,CAAC,CAAC,CAAC,MAAAgP,gBAAA,GAC7D/N,OAAO,CAAC2N,MAAM,cAAAI,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB9N,IAAI,cAAA+N,qBAAA,uBAApBA,qBAAA,CAAsBjP,WAAW,CAAC,CAAC,CAAC7G,QAAQ,CAAC4C,UAAU,CAACiE,WAAW,CAAC,CAAC,CAAC,OAAAkP,gBAAA,GACtEjO,OAAO,CAAC2N,MAAM,cAAAM,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBI,IAAI,cAAAH,qBAAA,uBAApBA,qBAAA,CAAsBnP,WAAW,CAAC,CAAC,CAAC7G,QAAQ,CAAC4C,UAAU,CAACiE,WAAW,CAAC,CAAC,CAAC,OAAAoP,gBAAA,GACtEnO,OAAO,CAAC2N,MAAM,cAAAQ,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBxW,KAAK,cAAAyW,qBAAA,uBAArBA,qBAAA,CAAuBrP,WAAW,CAAC,CAAC,CAAC7G,QAAQ,CAAC4C,UAAU,CAACiE,WAAW,CAAC,CAAC,CAAC;MAAA,CAC3E,CAAC;IACL;;IAEA;IACA,IAAI7D,QAAQ,KAAK,KAAK,EAAE;MACpB4S,gBAAgB,GAAGA,gBAAgB,CAACjN,MAAM,CAACb,OAAO,IAC9CwL,uBAAuB,CAACxL,OAAO,CAACC,IAAI,CAAC,KAAK/E,QAC9C,CAAC;IACL;;IAEA;IACA,IAAII,YAAY,KAAK,KAAK,EAAE;MACxBwS,gBAAgB,GAAGA,gBAAgB,CAACjN,MAAM,CAACb,OAAO,IAAI;QAAA,IAAAsO,gBAAA;QAClD,MAAMC,YAAY,IAAAD,gBAAA,GAAGtO,OAAO,CAAC2N,MAAM,cAAAW,gBAAA,uBAAdA,gBAAA,CAAgB3W,KAAK;QAC1C,IAAI,CAAC4W,YAAY,EAAE,OAAO,KAAK;QAC/B,MAAMC,aAAa,GAAG9W,cAAc,CAAC6W,YAAY,CAAC;QAClD,OAAOC,aAAa,KAAKlT,YAAY;MACzC,CAAC,CAAC;IACN;;IAEA;IACA,IAAIF,WAAW,KAAK,KAAK,EAAE;MACvB0S,gBAAgB,GAAGA,gBAAgB,CAACjN,MAAM,CAACb,OAAO,IAAI;QAAA,IAAAyO,gBAAA;QAClD,MAAMF,YAAY,IAAAE,gBAAA,GAAGzO,OAAO,CAAC2N,MAAM,cAAAc,gBAAA,uBAAdA,gBAAA,CAAgB9W,KAAK;QAC1C,OAAO,CAAA4W,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE1W,WAAW,CAAC,CAAC,MAAKuD,WAAW,CAACvD,WAAW,CAAC,CAAC;MACpE,CAAC,CAAC;IACN;;IAEA;IACAiW,gBAAgB,CAAChJ,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAAA,IAAA0J,eAAA,EAAAC,mBAAA,EAAAC,eAAA,EAAAC,mBAAA,EAAAC,SAAA,EAAAC,SAAA,EAAAC,UAAA,EAAAC,UAAA;MAC5B,QAAQjU,MAAM;QACV,KAAK,MAAM;UACP,OAAO+J,CAAC,CAAC9E,IAAI,CAACiP,aAAa,CAAClK,CAAC,CAAC/E,IAAI,CAAC;QACvC,KAAK,KAAK;UACN,OAAO,CAAC,EAAAyO,eAAA,GAAA1J,CAAC,CAAC9E,YAAY,cAAAwO,eAAA,wBAAAC,mBAAA,GAAdD,eAAA,CAAgBxE,GAAG,cAAAyE,mBAAA,uBAAnBA,mBAAA,CAAqBxE,OAAO,KAAI,CAAC,KAAK,EAAAyE,eAAA,GAAA7J,CAAC,CAAC7E,YAAY,cAAA0O,eAAA,wBAAAC,mBAAA,GAAdD,eAAA,CAAgB1E,GAAG,cAAA2E,mBAAA,uBAAnBA,mBAAA,CAAqB1E,OAAO,KAAI,CAAC,CAAC;QACpF,KAAK,aAAa;UACd,MAAMgF,gBAAgB,GAAG;YAAE,UAAU,EAAE,CAAC;YAAE,YAAY,EAAE,CAAC;YAAE,YAAY,EAAE;UAAE,CAAC;UAC5E,OAAOA,gBAAgB,CAAC3D,uBAAuB,CAACzG,CAAC,CAAC9E,IAAI,CAAC,CAAC,GAAGkP,gBAAgB,CAAC3D,uBAAuB,CAACxG,CAAC,CAAC/E,IAAI,CAAC,CAAC;QAChH,KAAK,UAAU;UACX,MAAMmP,MAAM,GAAG,EAAAN,SAAA,GAAA/J,CAAC,CAAC4I,MAAM,cAAAmB,SAAA,uBAARA,SAAA,CAAUnX,KAAK,KAAI,EAAE;UACpC,MAAM0X,MAAM,GAAG,EAAAN,SAAA,GAAA/J,CAAC,CAAC2I,MAAM,cAAAoB,SAAA,uBAARA,SAAA,CAAUpX,KAAK,KAAI,EAAE;UACpC,IAAIyX,MAAM,KAAKC,MAAM,EAAE,OAAOD,MAAM,CAACF,aAAa,CAACG,MAAM,CAAC;UAC1D,OAAO,CAAC,EAAAL,UAAA,GAAAjK,CAAC,CAAC4I,MAAM,cAAAqB,UAAA,uBAARA,UAAA,CAAUX,IAAI,KAAI,EAAE,EAAEa,aAAa,CAAC,EAAAD,UAAA,GAAAjK,CAAC,CAAC2I,MAAM,cAAAsB,UAAA,uBAARA,UAAA,CAAUZ,IAAI,KAAI,EAAE,CAAC;QACrE;UACI,OAAO,CAAC;MAChB;IACJ,CAAC,CAAC;IAEF,OAAOP,gBAAgB;EAC3B,CAAC;;EAED;EACA,MAAMwB,eAAe,GAAGA,CAAC;IAAEtP,OAAO;IAAEuP,UAAU;IAAEC;EAAS,CAAC,KAAK;IAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,qBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;IAC3D,MAAMC,WAAW,GAAGjG,uBAAuB,CAACxL,OAAO,CAACC,IAAI,CAAC;IACzD,MAAMyR,kBAAkB,GAAGhX,wBAAwB,CAACxC,QAAQ,CAAC8H,OAAO,CAACC,IAAI,CAAC;;IAE1E;IACA,MAAM0R,YAAY,GAAG3Z,MAAM,CAACgS,IAAI,CAAC,EAAAyF,sBAAA,GAAAzP,OAAO,CAACE,YAAY,cAAAuP,sBAAA,uBAApBA,sBAAA,CAAsBlQ,aAAa,KAAI,CAAC,CAAC,CAAC,CAACmB,MAAM;IAClF,MAAMkR,gBAAgB,GAAG5Z,MAAM,CAACC,OAAO,CAAC,EAAAyX,sBAAA,GAAA1P,OAAO,CAACE,YAAY,cAAAwP,sBAAA,uBAApBA,sBAAA,CAAsBnQ,aAAa,KAAI,CAAC,CAAC,CAAC,CAACsB,MAAM,CAAC,CAAC,CAACT,UAAU,EAAE6H,GAAG,CAAC,KAAK;MAAA,IAAA4J,sBAAA;MAC7G,MAAM9R,cAAc,GAAGpH,cAAc,aAAdA,cAAc,wBAAAkZ,sBAAA,GAAdlZ,cAAc,CAAE4G,aAAa,cAAAsS,sBAAA,uBAA7BA,sBAAA,CAAgC7R,OAAO,CAACC,IAAI,CAAC;MACpE,IAAI,EAACF,cAAc,aAAdA,cAAc,eAAdA,cAAc,CAAGK,UAAU,CAAC,GAAE,OAAO,KAAK;MAC/C,MAAMjD,YAAY,GAAG4C,cAAc,CAACK,UAAU,CAAC,CAACjD,YAAY,IAAI,CAAC;MACjE,MAAMoN,kBAAkB,GAAGvM,UAAU,CAACiK,GAAG,CAAClK,OAAO,CAAC;MAClD,OAAO,CAACI,KAAK,CAACoM,kBAAkB,CAAC,GAAGpN,YAAY,IAAIoN,kBAAkB,GAAGpN,YAAY,GAAG,CAAC;IAC7F,CAAC,CAAC,CAACuD,MAAM;;IAET;IACA,MAAMoR,oBAAoB,GAAGH,YAAY,GAAG,CAAC,GAAGzF,IAAI,CAAC6F,KAAK,CAAEH,gBAAgB,GAAGD,YAAY,GAAI,GAAG,CAAC,GAAG,GAAG;IAEzG,oBACIra,OAAA;MAAKgP,SAAS,EAAC,mBAAmB;MAACO,OAAO,EAAE2I,QAAS;MAAAjJ,QAAA,gBACjDjP,OAAA;QAAKgP,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAChCjP,OAAA;UAAKgP,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAC9BjP,OAAA;YAAIgP,SAAS,EAAC,cAAc;YAAAC,QAAA,eACxBjP,OAAA;cACI0a,IAAI,EAAEhS,OAAO,CAACiS,WAAY;cAC1BC,MAAM,EAAC,QAAQ;cACfC,GAAG,EAAC,qBAAqB;cACzBtL,OAAO,EAAGuL,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;cAAA9L,QAAA,EAEnCvG,OAAO,CAACC;YAAI;cAAAuG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACLrP,OAAA;YAAKgP,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAC5B,CAAAoJ,gBAAA,GAAA3P,OAAO,CAAC2N,MAAM,cAAAgC,gBAAA,eAAdA,gBAAA,CAAgBtB,IAAI,KAAAuB,gBAAA,GAAI5P,OAAO,CAAC2N,MAAM,cAAAiC,gBAAA,eAAdA,gBAAA,CAAgBjY,KAAK,GACvC,GAAEqI,OAAO,CAAC2N,MAAM,CAACU,IAAK,KAAIrO,OAAO,CAAC2N,MAAM,CAAChW,KAAM,EAAC,GACjD,EAAAkY,gBAAA,GAAA7P,OAAO,CAAC2N,MAAM,cAAAkC,gBAAA,uBAAdA,gBAAA,CAAgB5P,IAAI,KAAI;UAAkB;YAAAuG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE/C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENrP,OAAA;UAAKgP,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC1BjP,OAAA;YAAKgP,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvBjP,OAAA;cAAMgP,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5CrP,OAAA;cAAMgP,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAE,EAAAuJ,sBAAA,GAAA9P,OAAO,CAACE,YAAY,cAAA4P,sBAAA,wBAAAC,sBAAA,GAApBD,sBAAA,CAAsB5F,GAAG,cAAA6F,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA2B5F,OAAO,cAAA6F,sBAAA,uBAAlCA,sBAAA,CAAoCnI,OAAO,CAAC,CAAC,CAAC,KAAI;YAAK;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5F,CAAC,eAENrP,OAAA;YAAKgP,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvBjP,OAAA;cAAMgP,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClDrP,OAAA;cAAMgP,SAAS,EAAC,YAAY;cAAAC,QAAA,GAAEqL,gBAAgB,EAAC,GAAC,EAACD,YAAY;YAAA;cAAAnL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eAENrP,OAAA;YAAKgP,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBAClCjP,OAAA;cAAMgP,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/CrP,OAAA;cAAKgP,SAAS,EAAC,gBAAgB;cAAAC,QAAA,eAC3BjP,OAAA;gBACIgP,SAAS,EAAC,iBAAiB;gBAC3B+F,KAAK,EAAE;kBAAEC,KAAK,EAAG,GAAEwF,oBAAqB;gBAAG;cAAE;gBAAAtL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENrP,OAAA;UAAKgP,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC3BjP,OAAA;YAAKgP,SAAS,EAAG,gBAAemL,WAAY,EAAE;YAAAlL,QAAA,EACzCkL,WAAW,KAAK,UAAU,GAAG,UAAU,GACvCA,WAAW,KAAK,YAAY,GAAG,oBAAoB,GACnD;UAAY;YAAAjL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACNrP,OAAA;YAAMgP,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,EAEL4I,UAAU,iBACPjY,OAAA;QAAKgP,SAAS,EAAC,sBAAsB;QAAAC,QAAA,eAEjCjP,OAAA;UAAKgP,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACjCjP,OAAA;YAAKgP,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtBjP,OAAA;cACIgP,SAAS,EAAG,OAAM,CAACpM,UAAU,CAAC8F,OAAO,CAACC,IAAI,CAAC,IAAI,UAAU,MAAM,UAAU,GAAG,QAAQ,GAAG,EAAG,EAAE;cAC5F4G,OAAO,EAAGuL,CAAC,IAAK;gBACZA,CAAC,CAACC,eAAe,CAAC,CAAC;gBACnBrF,YAAY,CAAChN,OAAO,CAACC,IAAI,EAAE,UAAU,CAAC;cAC1C,CAAE;cAAAsG,QAAA,EACL;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNrP,OAAA;cACIgP,SAAS,EAAG,OAAMpM,UAAU,CAAC8F,OAAO,CAACC,IAAI,CAAC,KAAK,cAAc,GAAG,QAAQ,GAAG,EAAG,EAAE;cAChF4G,OAAO,EAAGuL,CAAC,IAAK;gBACZA,CAAC,CAACC,eAAe,CAAC,CAAC;gBACnBrF,YAAY,CAAChN,OAAO,CAACC,IAAI,EAAE,cAAc,CAAC;cAC9C,CAAE;cAAAsG,QAAA,EACL;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENrP,OAAA;YAAKgP,SAAS,EAAC,aAAa;YAAAC,QAAA,GAEvB,CAACrM,UAAU,CAAC8F,OAAO,CAACC,IAAI,CAAC,IAAI,UAAU,MAAM,UAAU,iBACpD3I,OAAA;cAAKgP,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAE7BjP,OAAA;gBAAKgP,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC7BjP,OAAA;kBAAKgP,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACtBjP,OAAA;oBAAKgP,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnCrP,OAAA;oBAAKgP,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBACzBjP,OAAA;sBAAKgP,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAC;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC7CrP,OAAA;sBAAKgP,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAE,EAAA0J,sBAAA,GAAAjQ,OAAO,CAACE,YAAY,cAAA+P,sBAAA,wBAAAC,sBAAA,GAApBD,sBAAA,CAAsB/F,GAAG,cAAAgG,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA2B/F,OAAO,cAAAgG,sBAAA,uBAAlCA,sBAAA,CAAoCtI,OAAO,CAAC,CAAC,CAAC,KAAI;oBAAK;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1F,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eAENrP,OAAA;kBAAKgP,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACtBjP,OAAA;oBAAKgP,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnCrP,OAAA;oBAAKgP,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBACzBjP,OAAA;sBAAKgP,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAC;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC7CrP,OAAA;sBAAKgP,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAE,EAAA6J,uBAAA,GAAApQ,OAAO,CAACE,YAAY,cAAAkQ,uBAAA,wBAAAC,uBAAA,GAApBD,uBAAA,CAAsBlG,GAAG,cAAAmG,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BhG,OAAO,cAAAiG,uBAAA,uBAAlCA,uBAAA,CAAoCzI,OAAO,CAAC,CAAC,CAAC,KAAI;oBAAK;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1F,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eAENrP,OAAA;kBAAKgP,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACtBjP,OAAA;oBAAKgP,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnCrP,OAAA;oBAAKgP,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBACzBjP,OAAA;sBAAKgP,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAC;oBAAkB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACpDrP,OAAA;sBAAKgP,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAE,EAAAgK,uBAAA,GAAAvQ,OAAO,CAACE,YAAY,cAAAqQ,uBAAA,wBAAAC,uBAAA,GAApBD,uBAAA,CAAsB3H,UAAU,cAAA4H,uBAAA,uBAAhCA,uBAAA,CAAmC,oBAAoB,CAAC,KAAI;oBAAC;sBAAAhK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eAENrP,OAAA;kBAAKgP,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACtBjP,OAAA;oBAAKgP,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACtCrP,OAAA;oBAAKgP,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBACzBjP,OAAA;sBAAKgP,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAC;oBAAe;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACjDrP,OAAA;sBAAKgP,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAE,EAAAkK,uBAAA,GAAAzQ,OAAO,CAACE,YAAY,cAAAuQ,uBAAA,wBAAAC,uBAAA,GAApBD,uBAAA,CAAsB7H,UAAU,cAAA8H,uBAAA,uBAAhCA,uBAAA,CAAmC,iBAAiB,CAAC,KAAI;oBAAC;sBAAAlK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7F,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,EAGL3G,OAAO,CAACsS,YAAY,iBACjBhb,OAAA;gBAAKgP,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACnCjP,OAAA;kBAAAiP,QAAA,EAAI;gBAAqB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9BrP,OAAA;kBAAKgP,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,GAE/BvG,OAAO,CAACsS,YAAY,CAACC,aAAa,IAAIva,MAAM,CAACgS,IAAI,CAAChK,OAAO,CAACsS,YAAY,CAACC,aAAa,CAAC,CAAC7R,MAAM,GAAG,CAAC,iBAC7FpJ,OAAA,CAAAE,SAAA;oBAAA+O,QAAA,GACKvG,OAAO,CAACsS,YAAY,CAACC,aAAa,CAACC,kBAAkB,IAAIxS,OAAO,CAACsS,YAAY,CAACC,aAAa,CAACC,kBAAkB,KAAK,yBAAyB,iBACzIlb,OAAA;sBAAKgP,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,gBAC/BjP,OAAA;wBAAKgP,SAAS,EAAC,WAAW;wBAAAC,QAAA,EAAC;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACnCrP,OAAA;wBAAKgP,SAAS,EAAC,cAAc;wBAAAC,QAAA,gBACzBjP,OAAA;0BAAKgP,SAAS,EAAC,YAAY;0BAAAC,QAAA,EAAC;wBAAY;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAC9CrP,OAAA;0BAAKgP,SAAS,EAAC,YAAY;0BAAAC,QAAA,EAAEvG,OAAO,CAACsS,YAAY,CAACC,aAAa,CAACC;wBAAkB;0BAAAhM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CACR,EAEA3G,OAAO,CAACsS,YAAY,CAACC,aAAa,CAACE,kBAAkB,IAAIzS,OAAO,CAACsS,YAAY,CAACC,aAAa,CAACE,kBAAkB,KAAK,yBAAyB,iBACzInb,OAAA;sBAAKgP,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,gBAC/BjP,OAAA;wBAAKgP,SAAS,EAAC,WAAW;wBAAAC,QAAA,EAAC;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACnCrP,OAAA;wBAAKgP,SAAS,EAAC,cAAc;wBAAAC,QAAA,gBACzBjP,OAAA;0BAAKgP,SAAS,EAAC,YAAY;0BAAAC,QAAA,EAAC;wBAAY;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAC9CrP,OAAA;0BAAKgP,SAAS,EAAC,YAAY;0BAAAC,QAAA,EAAEvG,OAAO,CAACsS,YAAY,CAACC,aAAa,CAACE;wBAAkB;0BAAAjM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CACR,EAEA3G,OAAO,CAACsS,YAAY,CAACC,aAAa,CAACG,mBAAmB,IAAI1S,OAAO,CAACsS,YAAY,CAACC,aAAa,CAACG,mBAAmB,KAAK,yBAAyB,iBAC3Ipb,OAAA;sBAAKgP,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,gBAC/BjP,OAAA;wBAAKgP,SAAS,EAAC,WAAW;wBAAAC,QAAA,EAAC;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACnCrP,OAAA;wBAAKgP,SAAS,EAAC,cAAc;wBAAAC,QAAA,gBACzBjP,OAAA;0BAAKgP,SAAS,EAAC,YAAY;0BAAAC,QAAA,EAAC;wBAAe;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACjDrP,OAAA;0BAAKgP,SAAS,EAAC,YAAY;0BAAAC,QAAA,EAAEvG,OAAO,CAACsS,YAAY,CAACC,aAAa,CAACG;wBAAmB;0BAAAlM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CACR,EAEA3G,OAAO,CAACsS,YAAY,CAACC,aAAa,CAACI,mBAAmB,IAAI3S,OAAO,CAACsS,YAAY,CAACC,aAAa,CAACI,mBAAmB,KAAK,yBAAyB,iBAC3Irb,OAAA;sBAAKgP,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,gBAC/BjP,OAAA;wBAAKgP,SAAS,EAAC,WAAW;wBAAAC,QAAA,EAAC;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACnCrP,OAAA;wBAAKgP,SAAS,EAAC,cAAc;wBAAAC,QAAA,gBACzBjP,OAAA;0BAAKgP,SAAS,EAAC,YAAY;0BAAAC,QAAA,EAAC;wBAAe;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACjDrP,OAAA;0BAAKgP,SAAS,EAAC,YAAY;0BAAAC,QAAA,EAAEvG,OAAO,CAACsS,YAAY,CAACC,aAAa,CAACI;wBAAmB;0BAAAnM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CACR;kBAAA,eACH,CACL,EAGA3G,OAAO,CAACsS,YAAY,CAACM,gBAAgB,IAAI5a,MAAM,CAACgS,IAAI,CAAChK,OAAO,CAACsS,YAAY,CAACM,gBAAgB,CAAC,CAAClS,MAAM,GAAG,CAAC,iBACnGpJ,OAAA,CAAAE,SAAA;oBAAA+O,QAAA,GACKvG,OAAO,CAACsS,YAAY,CAACM,gBAAgB,CAACC,iBAAiB,IAAI7S,OAAO,CAACsS,YAAY,CAACM,gBAAgB,CAACC,iBAAiB,KAAK,yBAAyB,iBAC7Ivb,OAAA;sBAAKgP,SAAS,EAAC,+BAA+B;sBAAAC,QAAA,gBAC1CjP,OAAA;wBAAKgP,SAAS,EAAC,WAAW;wBAAAC,QAAA,EAAC;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACnCrP,OAAA;wBAAKgP,SAAS,EAAC,cAAc;wBAAAC,QAAA,gBACzBjP,OAAA;0BAAKgP,SAAS,EAAC,YAAY;0BAAAC,QAAA,EAAC;wBAAe;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACjDrP,OAAA;0BAAKgP,SAAS,EAAC,YAAY;0BAAAC,QAAA,EAAEvG,OAAO,CAACsS,YAAY,CAACM,gBAAgB,CAACC;wBAAiB;0BAAArM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,EAC1F3G,OAAO,CAACsS,YAAY,CAACM,gBAAgB,CAACE,kBAAkB,IAAI9S,OAAO,CAACsS,YAAY,CAACM,gBAAgB,CAACE,kBAAkB,KAAK,yBAAyB,iBAC/Ixb,OAAA;0BAAKgP,SAAS,EAAC,cAAc;0BAAAC,QAAA,GAAC,YAAU,EAACvG,OAAO,CAACsS,YAAY,CAACM,gBAAgB,CAACE,kBAAkB;wBAAA;0BAAAtM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAC1G;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CACR,EAEA3G,OAAO,CAACsS,YAAY,CAACM,gBAAgB,CAACG,qBAAqB,iBACxDzb,OAAA;sBAAKgP,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,gBAC/BjP,OAAA;wBAAKgP,SAAS,EAAC,WAAW;wBAAAC,QAAA,EAAC;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACnCrP,OAAA;wBAAKgP,SAAS,EAAC,cAAc;wBAAAC,QAAA,gBACzBjP,OAAA;0BAAKgP,SAAS,EAAC,YAAY;0BAAAC,QAAA,EAAC;wBAAgB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAClDrP,OAAA;0BAAKgP,SAAS,EAAC,YAAY;0BAAAC,QAAA,EAAEvG,OAAO,CAACsS,YAAY,CAACM,gBAAgB,CAACG;wBAAqB;0BAAAvM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9F,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CACR;kBAAA,eACH,CACL,EAGA3G,OAAO,CAACsS,YAAY,CAACU,SAAS,IAAIhb,MAAM,CAACgS,IAAI,CAAChK,OAAO,CAACsS,YAAY,CAACU,SAAS,CAAC,CAACtS,MAAM,GAAG,CAAC,iBACrFpJ,OAAA,CAAAE,SAAA;oBAAA+O,QAAA,GACKvG,OAAO,CAACsS,YAAY,CAACU,SAAS,CAACC,eAAe,IAAIjT,OAAO,CAACsS,YAAY,CAACU,SAAS,CAACC,eAAe,KAAK,yBAAyB,iBAC3H3b,OAAA;sBAAKgP,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,gBAC/BjP,OAAA;wBAAKgP,SAAS,EAAC,WAAW;wBAAAC,QAAA,EAAC;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACnCrP,OAAA;wBAAKgP,SAAS,EAAC,cAAc;wBAAAC,QAAA,gBACzBjP,OAAA;0BAAKgP,SAAS,EAAC,YAAY;0BAAAC,QAAA,EAAC;wBAAe;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACjDrP,OAAA;0BAAKgP,SAAS,EAAC,YAAY;0BAAAC,QAAA,EAAEvG,OAAO,CAACsS,YAAY,CAACU,SAAS,CAACC;wBAAe;0BAAAzM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CACR,EAEA3G,OAAO,CAACsS,YAAY,CAACU,SAAS,CAACE,mBAAmB,iBAC/C5b,OAAA;sBAAKgP,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,gBAC/BjP,OAAA;wBAAKgP,SAAS,EAAC,WAAW;wBAAAC,QAAA,EAAC;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACtCrP,OAAA;wBAAKgP,SAAS,EAAC,cAAc;wBAAAC,QAAA,gBACzBjP,OAAA;0BAAKgP,SAAS,EAAC,YAAY;0BAAAC,QAAA,EAAC;wBAAU;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAC5CrP,OAAA;0BAAKgP,SAAS,EAAC,YAAY;0BAAAC,QAAA,EAAEvG,OAAO,CAACsS,YAAY,CAACU,SAAS,CAACE;wBAAmB;0BAAA1M,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CACR;kBAAA,eACH,CACL,EAGA3G,OAAO,CAACsS,YAAY,CAACa,gBAAgB,IAAInT,OAAO,CAACsS,YAAY,CAACa,gBAAgB,CAACC,OAAO,IAAIpT,OAAO,CAACsS,YAAY,CAACa,gBAAgB,CAACC,OAAO,KAAK,yBAAyB,iBAClK9b,OAAA;oBAAKgP,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,gBAC5CjP,OAAA;sBAAKgP,SAAS,EAAC,WAAW;sBAAAC,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACnCrP,OAAA;sBAAKgP,SAAS,EAAC,cAAc;sBAAAC,QAAA,gBACzBjP,OAAA;wBAAKgP,SAAS,EAAC,YAAY;wBAAAC,QAAA,EAAC;sBAAO;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACzCrP,OAAA;wBAAKgP,SAAS,EAAC,YAAY;wBAAAC,QAAA,EAAEvG,OAAO,CAACsS,YAAY,CAACa,gBAAgB,CAACC;sBAAO;wBAAA5M,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CACR;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CACR,eAGDrP,OAAA;gBAAKgP,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBACjCjP,OAAA;kBAAKgP,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC3BjP,OAAA;oBAAAiP,QAAA,EAAI;kBAAsB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/BrP,OAAA;oBAAKgP,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBACzBjP,OAAA;sBAAKgP,SAAS,EAAC,aAAa;sBAAAC,QAAA,gBACxBjP,OAAA;wBAAMgP,SAAS,EAAC,cAAc;wBAAAC,QAAA,EAAC;sBAAO;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC7CrP,OAAA;wBAAMgP,SAAS,EAAC,cAAc;wBAAAC,QAAA,EAAE,EAAAoK,iBAAA,GAAA3Q,OAAO,CAAC2N,MAAM,cAAAgD,iBAAA,uBAAdA,iBAAA,CAAgB1Q,IAAI,KAAI;sBAAK;wBAAAuG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpE,CAAC,eACNrP,OAAA;sBAAKgP,SAAS,EAAC,aAAa;sBAAAC,QAAA,gBACxBjP,OAAA;wBAAMgP,SAAS,EAAC,cAAc;wBAAAC,QAAA,EAAC;sBAAS;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC/CrP,OAAA;wBAAMgP,SAAS,EAAC,cAAc;wBAAAC,QAAA,EACzB,CAAAqK,iBAAA,GAAA5Q,OAAO,CAAC2N,MAAM,cAAAiD,iBAAA,eAAdA,iBAAA,CAAgBvC,IAAI,KAAAwC,iBAAA,GAAI7Q,OAAO,CAAC2N,MAAM,cAAAkD,iBAAA,eAAdA,iBAAA,CAAgBlZ,KAAK,GACvC,GAAEqI,OAAO,CAAC2N,MAAM,CAACU,IAAK,KAAIrO,OAAO,CAAC2N,MAAM,CAAChW,KAAM,EAAC,GACjD;sBAAK;wBAAA6O,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAET,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,eACNrP,OAAA;sBAAKgP,SAAS,EAAC,aAAa;sBAAAC,QAAA,gBACxBjP,OAAA;wBAAMgP,SAAS,EAAC,cAAc;wBAAAC,QAAA,EAAC;sBAAgB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACtDrP,OAAA;wBACI0a,IAAI,EAAEhS,OAAO,CAACiS,WAAY;wBAC1BC,MAAM,EAAC,QAAQ;wBACfC,GAAG,EAAC,qBAAqB;wBACzB7L,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAC1B;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eAENrP,OAAA;kBAAKgP,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC3BjP,OAAA;oBAAAiP,QAAA,EAAI;kBAAmB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5BrP,OAAA;oBAAKgP,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,gBACjCjP,OAAA;sBAAKgP,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,gBAC7BjP,OAAA;wBAAMgP,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,EAAC;sBAAY;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACvDrP,OAAA;wBAAKgP,SAAS,EAAC,oBAAoB;wBAAAC,QAAA,gBAC/BjP,OAAA;0BAAAiP,QAAA,EAAO,CAAA5N,cAAc,aAAdA,cAAc,wBAAAmY,qBAAA,GAAdnY,cAAc,CAAEwI,cAAc,cAAA2P,qBAAA,uBAA9BA,qBAAA,CAAgCjJ,OAAO,CAAC,CAAC,CAAC,KAAI;wBAAK;0BAAArB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eAClErP,OAAA;0BAAMgP,SAAS,EAAG,UAAS,CAAA3N,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEwI,cAAc,OAAA4P,uBAAA,GAAI/Q,OAAO,CAACE,YAAY,cAAA6Q,uBAAA,wBAAAC,uBAAA,GAApBD,uBAAA,CAAsB7G,GAAG,cAAA8G,uBAAA,uBAAzBA,uBAAA,CAA2B7G,OAAO,IAAG,KAAK,GAAG,SAAU,EAAE;0BAAA5D,QAAA,EACjH,CAAA5N,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEwI,cAAc,OAAA8P,uBAAA,GAAIjR,OAAO,CAACE,YAAY,cAAA+Q,uBAAA,wBAAAC,uBAAA,GAApBD,uBAAA,CAAsB/G,GAAG,cAAAgH,uBAAA,uBAAzBA,uBAAA,CAA2B/G,OAAO,IAAG,GAAG,GAAG;wBAAG;0BAAA3D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/E,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACNrP,OAAA;sBAAKgP,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,gBAC7BjP,OAAA;wBAAMgP,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,EAAC;sBAAY;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACvDrP,OAAA;wBAAKgP,SAAS,EAAC,oBAAoB;wBAAAC,QAAA,gBAC/BjP,OAAA;0BAAAiP,QAAA,EAAO,CAAA5N,cAAc,aAAdA,cAAc,wBAAAwY,qBAAA,GAAdxY,cAAc,CAAEyI,WAAW,cAAA+P,qBAAA,uBAA3BA,qBAAA,CAA6BtJ,OAAO,CAAC,CAAC,CAAC,KAAI;wBAAK;0BAAArB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eAC/DrP,OAAA;0BAAMgP,SAAS,EAAG,UAAS,CAAA3N,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEyI,WAAW,OAAAgQ,uBAAA,GAAIpR,OAAO,CAACE,YAAY,cAAAkR,uBAAA,wBAAAC,uBAAA,GAApBD,uBAAA,CAAsBlH,GAAG,cAAAmH,uBAAA,uBAAzBA,uBAAA,CAA2BhH,OAAO,IAAG,KAAK,GAAG,SAAU,EAAE;0BAAA9D,QAAA,EAC9G,CAAA5N,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEyI,WAAW,OAAAkQ,uBAAA,GAAItR,OAAO,CAACE,YAAY,cAAAoR,uBAAA,wBAAAC,uBAAA,GAApBD,uBAAA,CAAsBpH,GAAG,cAAAqH,uBAAA,uBAAzBA,uBAAA,CAA2BlH,OAAO,IAAG,GAAG,GAAG;wBAAG;0BAAA7D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5E,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACNrP,OAAA;sBAAKgP,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,gBAC7BjP,OAAA;wBAAMgP,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,EAAC;sBAAc;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACzDrP,OAAA;wBAAKgP,SAAS,EAAC,oBAAoB;wBAAAC,QAAA,gBAC/BjP,OAAA;0BAAAiP,QAAA,GAAOqL,gBAAgB,EAAC,GAAC,EAACD,YAAY,EAAC,YAAU;wBAAA;0BAAAnL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACxDrP,OAAA;0BAAMgP,SAAS,EAAG,UAASsL,gBAAgB,KAAKD,YAAY,GAAG,KAAK,GAAG,SAAU,EAAE;0BAAApL,QAAA,EAC9EqL,gBAAgB,KAAKD,YAAY,GAAG,GAAG,GAAG;wBAAG;0BAAAnL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5C,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACR,EAGAzM,UAAU,CAAC8F,OAAO,CAACC,IAAI,CAAC,KAAK,cAAc,iBACxC3I,OAAA;cAAKgP,SAAS,EAAC,kBAAkB;cAAAC,QAAA,eAE7BjP,OAAA;gBAAKgP,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBAClCjP,OAAA;kBAAAiP,QAAA,EAAI;gBAAwB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjCrP,OAAA;kBAAKgP,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAC9BvO,MAAM,CAACC,OAAO,CAAC,EAAAuZ,uBAAA,GAAAxR,OAAO,CAACE,YAAY,cAAAsR,uBAAA,uBAApBA,uBAAA,CAAsBjS,aAAa,KAAI,CAAC,CAAC,CAAC,CAACqG,GAAG,CAAC,CAAC,CAAC/H,MAAM,EAAEoK,GAAG,CAAC,KAAK;oBAAA,IAAAoL,sBAAA;oBAC9E,MAAMtT,cAAc,GAAGpH,cAAc,aAAdA,cAAc,wBAAA0a,sBAAA,GAAd1a,cAAc,CAAE4G,aAAa,cAAA8T,sBAAA,uBAA7BA,sBAAA,CAAgCrT,OAAO,CAACC,IAAI,CAAC;oBACpE,MAAMI,UAAU,GAAGN,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAGlC,MAAM,CAAC;oBAC3C,MAAMV,YAAY,GAAG,CAAAkD,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAElD,YAAY,KAAI,CAAC;oBAClD,MAAMoN,kBAAkB,GAAGvM,UAAU,CAACiK,GAAG,CAAClK,OAAO,CAAC;oBAClD,MAAMiO,KAAK,GAAG,CAAC7N,KAAK,CAACoM,kBAAkB,CAAC,GAAGpN,YAAY,IAAIoN,kBAAkB,GAAGpN,YAAY,GAAG,CAAC;oBAChG,MAAMgK,cAAc,GAAG,CAAA9G,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEzC,OAAO,KAAI,EAAE;;oBAEhD;oBACA,MAAMiH,aAAa,GAAG,CAAC,GAAGsC,cAAc,CAAC,CAACrC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;sBACrD,MAAMC,MAAM,GAAG/G,aAAa,CAAC6G,CAAC,CAACjH,KAAK,CAAC;sBACrC,MAAMoH,MAAM,GAAGhH,aAAa,CAAC8G,CAAC,CAAClH,KAAK,CAAC;sBACrC,IAAIoH,MAAM,KAAKD,MAAM,EAAE,OAAOC,MAAM,GAAGD,MAAM;sBAC7C,OAAO5B,QAAQ,CAAC2B,CAAC,CAACnG,IAAI,CAAC,GAAGwE,QAAQ,CAAC0B,CAAC,CAAClG,IAAI,CAAC;oBAC9C,CAAC,CAAC;;oBAEF;oBACA,MAAMyU,WAAW,GAAG,EAAE;oBACtB,MAAMC,aAAa,GAAG,EAAE;oBACxB,IAAIjM,WAAW,GAAG,CAAC;oBACnB,MAAMnC,eAAe,GAAGnH,UAAU,CAACiK,GAAG,CAAClK,OAAO,CAAC,IAAI,CAAC;oBAEpD8G,aAAa,CAACvH,OAAO,CAACO,MAAM,IAAI;sBAC5B,MAAMwH,aAAa,GAAGrH,UAAU,CAACH,MAAM,CAACE,OAAO,CAAC,IAAI,CAAC;sBACrD,IAAIuJ,WAAW,GAAGnC,eAAe,EAAE;wBAC/BmO,WAAW,CAAChO,IAAI,CAACzH,MAAM,CAAC;wBACxByJ,WAAW,IAAIjC,aAAa;sBAChC,CAAC,MAAM;wBACHkO,aAAa,CAACjO,IAAI,CAACzH,MAAM,CAAC;sBAC9B;oBACJ,CAAC,CAAC;oBAEF,oBACIvG,OAAA;sBAAkBgP,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,gBACpDjP,OAAA;wBAAKgP,SAAS,EAAC,qBAAqB;wBAAAC,QAAA,gBAChCjP,OAAA;0BAAKgP,SAAS,EAAC,mBAAmB;0BAAAC,QAAA,gBAC9BjP,OAAA;4BAAMgP,SAAS,EAAC,aAAa;4BAAAC,QAAA,GAAE1I,MAAM,EAAC,IAAE,EAACoK,GAAG,CAAClK,OAAO,EAAC,WAAS;0BAAA;4BAAAyI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,EACpEsB,GAAG,CAAChH,YAAY,iBAAI3J,OAAA;4BAAMgP,SAAS,EAAC,cAAc;4BAAAC,QAAA,EAAC;0BAAY;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtE,CAAC,eACNrP,OAAA;0BAAMgP,SAAS,EAAG,UAAS0F,KAAK,GAAG,KAAK,GAAG,SAAU,EAAE;0BAAAzF,QAAA,EAClDyF,KAAK,GAAG,GAAG,GAAG;wBAAG;0BAAAxF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChB,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC,EACLQ,cAAc,CAACzG,MAAM,GAAG,CAAC,iBACtBpJ,OAAA;wBAAKgP,SAAS,EAAC,iBAAiB;wBAAAC,QAAA,GAE3B+M,WAAW,CAAC5S,MAAM,GAAG,CAAC,iBACnBpJ,OAAA;0BAAKgP,SAAS,EAAC,sBAAsB;0BAAAC,QAAA,gBACjCjP,OAAA;4BAAKgP,SAAS,EAAC,qBAAqB;4BAAAC,QAAA,gBAChCjP,OAAA;8BAAAiP,QAAA,EAAM;4BAAuB;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,eACpCrP,OAAA;8BAAMgP,SAAS,EAAC,iBAAiB;8BAAAC,QAAA,GAAEe,WAAW,CAACO,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG,EAACI,GAAG,CAAClK,OAAO,EAAC,UAAQ;4BAAA;8BAAAyI,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxF,CAAC,eACNrP,OAAA;4BAAKgP,SAAS,EAAC,cAAc;4BAAAC,QAAA,EACxB+M,WAAW,CAAC1N,GAAG,CAAC,CAAC4N,aAAa,EAAE5M,KAAK;8BAAA,IAAA6M,oBAAA;8BAAA,oBAClCnc,OAAA;gCAAiBgP,SAAS,EAAC,kBAAkB;gCAAAC,QAAA,gBACzCjP,OAAA;kCAAKgP,SAAS,EAAC,gBAAgB;kCAAAC,QAAA,gBAC3BjP,OAAA;oCAAMgP,SAAS,EAAC,kBAAkB;oCAAAC,QAAA,GAC7BiN,aAAa,CAACrU,IAAI,EAAC,KAAG,EAACqU,aAAa,CAACvT,IAAI;kCAAA;oCAAAuG,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACxC,CAAC,eACPrP,OAAA;oCAAMgP,SAAS,EAAC,aAAa;oCAAAC,QAAA,GACxBiN,aAAa,CAAC7U,WAAW,EAAC,UAAG,EAAC6U,aAAa,CAAC5U,IAAI,EAAC,GAAC,EAAC4U,aAAa,CAAC3U,IAAI;kCAAA;oCAAA2H,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACpE,CAAC;gCAAA;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACN,CAAC,eACNrP,OAAA;kCAAKgP,SAAS,EAAC,cAAc;kCAAAC,QAAA,gBACzBjP,OAAA;oCAAMgP,SAAS,EAAC,gBAAgB;oCAAAC,QAAA,GAAEiN,aAAa,CAACzV,OAAO,EAAC,UAAQ;kCAAA;oCAAAyI,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAM,CAAC,eACvErP,OAAA;oCAAMgP,SAAS,EAAG,sBAAmB,CAAAmN,oBAAA,GAAED,aAAa,CAAC1V,KAAK,cAAA2V,oBAAA,uBAAnBA,oBAAA,CAAqBzU,OAAO,CAAC,OAAO,EAAE,EAAE,CAAE,EAAE;oCAAAuH,QAAA,EAC9EiN,aAAa,CAAC1V;kCAAK;oCAAA0I,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAClB,CAAC;gCAAA;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACN,CAAC;8BAAA,GAdAC,KAAK;gCAAAJ,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAeV,CAAC;4BAAA,CACT;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CACR,EAGA4M,aAAa,CAAC7S,MAAM,GAAG,CAAC,iBACrBpJ,OAAA;0BAAKgP,SAAS,EAAC,wBAAwB;0BAAAC,QAAA,gBACnCjP,OAAA;4BAAKgP,SAAS,EAAC,uBAAuB;4BAAAC,QAAA,gBAClCjP,OAAA;8BAAAiP,QAAA,EAAM;4BAAkC;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,eAC/CrP,OAAA;8BAAMgP,SAAS,EAAC,eAAe;8BAAAC,QAAA,GAAEgN,aAAa,CAAC7S,MAAM,EAAC,SAAO,EAAC6S,aAAa,CAAC7S,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;4BAAA;8BAAA8F,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC1G,CAAC,eACNrP,OAAA;4BAAKgP,SAAS,EAAC,cAAc;4BAAAC,QAAA,EACxBgN,aAAa,CAAC3N,GAAG,CAAC,CAAC4N,aAAa,EAAE5M,KAAK;8BAAA,IAAA8M,qBAAA;8BAAA,oBACpCpc,OAAA;gCAAiBgP,SAAS,EAAC,oBAAoB;gCAAAC,QAAA,gBAC3CjP,OAAA;kCAAKgP,SAAS,EAAC,gBAAgB;kCAAAC,QAAA,gBAC3BjP,OAAA;oCAAMgP,SAAS,EAAC,kBAAkB;oCAAAC,QAAA,GAC7BiN,aAAa,CAACrU,IAAI,EAAC,KAAG,EAACqU,aAAa,CAACvT,IAAI;kCAAA;oCAAAuG,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACxC,CAAC,eACPrP,OAAA;oCAAMgP,SAAS,EAAC,aAAa;oCAAAC,QAAA,GACxBiN,aAAa,CAAC7U,WAAW,EAAC,UAAG,EAAC6U,aAAa,CAAC5U,IAAI,EAAC,GAAC,EAAC4U,aAAa,CAAC3U,IAAI;kCAAA;oCAAA2H,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACpE,CAAC;gCAAA;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACN,CAAC,eACNrP,OAAA;kCAAKgP,SAAS,EAAC,cAAc;kCAAAC,QAAA,gBACzBjP,OAAA;oCAAMgP,SAAS,EAAC,gBAAgB;oCAAAC,QAAA,GAAEiN,aAAa,CAACzV,OAAO,EAAC,UAAQ;kCAAA;oCAAAyI,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAM,CAAC,eACvErP,OAAA;oCAAMgP,SAAS,EAAG,sBAAmB,CAAAoN,qBAAA,GAAEF,aAAa,CAAC1V,KAAK,cAAA4V,qBAAA,uBAAnBA,qBAAA,CAAqB1U,OAAO,CAAC,OAAO,EAAE,EAAE,CAAE,EAAE;oCAAAuH,QAAA,EAC9EiN,aAAa,CAAC1V;kCAAK;oCAAA0I,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAClB,CAAC;gCAAA;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACN,CAAC;8BAAA,GAdAC,KAAK;gCAAAJ,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAeV,CAAC;4BAAA,CACT;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CACR;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CACR,EACAQ,cAAc,CAACzG,MAAM,KAAK,CAAC,IAAI,CAACsL,KAAK,iBAClC1U,OAAA;wBAAKgP,SAAS,EAAC,YAAY;wBAAAC,QAAA,eACvBjP,OAAA;0BAAMgP,SAAS,EAAC,iBAAiB;0BAAAC,QAAA,EAAC;wBAAyB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjE,CACR;oBAAA,GA7EK9I,MAAM;sBAAA2I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA8EX,CAAC;kBAEd,CAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAEd,CAAC;EAED,oBACIrP,OAAA;IAAKgP,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAC9BjP,OAAA;MAAQgP,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC9BjP,OAAA;QAAKgP,SAAS,EAAC,aAAa;QAAAC,QAAA,eACxBjP,OAAA;UAAKgP,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBjP,OAAA;YAAMgP,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvCrP,OAAA;YAAAiP,QAAA,EAAI;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNrP,OAAA;QAAKgP,SAAS,EAAC,cAAc;QAAAC,QAAA,eACzBjP,OAAA;UACIuP,OAAO,EAAEA,CAAA,KAAMnO,QAAQ,CAAC,OAAO,CAAE;UACjC4N,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC5B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAGRvM,iBAAiB,IAAIE,qBAAqB,iBACvChD,OAAA;MAAKgP,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBACjCjP,OAAA;QAAAiP,QAAA,GAAM,2BACuB,EAAC,IAAIiC,IAAI,CAAClO,qBAAqB,CAAC,CAACqZ,kBAAkB,CAAC,CAAC,EAAC,MAAI,EAAC,IAAInL,IAAI,CAAClO,qBAAqB,CAAC,CAACsZ,kBAAkB,CAAC,CAAC;MAAA;QAAApN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtI,CAAC,eACPrP,OAAA;QACIuP,OAAO,EAAE/E,iBAAkB;QAC3BwE,SAAS,EAAC,iBAAiB;QAC3BuN,QAAQ,EAAE9a,SAAS,IAAIyB,aAAc;QAAA+L,QAAA,EAEpCxN,SAAS,GAAG,gBAAgB,GAAG;MAAa;QAAAyN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACR,EAGA,CAAC5N,SAAS,IAAIJ,cAAc,iBACzBrB,OAAA,CAAAE,SAAA;MAAA+O,QAAA,gBACIjP,OAAA;QAAKgP,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAChCjP,OAAA;UAAKgP,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvBjP,OAAA;YAAMgP,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvCrP,OAAA;YACIwc,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,+BAA+B;YAC3CnU,KAAK,EAAE9E,UAAW;YAClBkZ,QAAQ,EAAG5B,CAAC,IAAKrX,aAAa,CAACqX,CAAC,CAACF,MAAM,CAACtS,KAAK,CAAE;YAC/C0G,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENrP,OAAA;UAAKgP,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC5BjP,OAAA;YAAKgP,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC5BjP,OAAA;cAAMgP,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtCrP,OAAA;cACIsI,KAAK,EAAE1E,QAAS;cAChB8Y,QAAQ,EAAG5B,CAAC,IAAKjX,WAAW,CAACiX,CAAC,CAACF,MAAM,CAACtS,KAAK,CAAE;cAC7C0G,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAEzBjP,OAAA;gBAAQsI,KAAK,EAAC,KAAK;gBAAA2G,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACzCrP,OAAA;gBAAQsI,KAAK,EAAC,UAAU;gBAAA2G,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC/CrP,OAAA;gBAAQsI,KAAK,EAAC,YAAY;gBAAA2G,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtDrP,OAAA;gBAAQsI,KAAK,EAAC,YAAY;gBAAA2G,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAENrP,OAAA;YAAKgP,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC5BjP,OAAA;cAAMgP,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzCrP,OAAA;cACIsI,KAAK,EAAEtE,YAAa;cACpB0Y,QAAQ,EAAG5B,CAAC,IAAK;gBACb7W,eAAe,CAAC6W,CAAC,CAACF,MAAM,CAACtS,KAAK,CAAC;gBAC/B,IAAIwS,CAAC,CAACF,MAAM,CAACtS,KAAK,KAAK,KAAK,EAAEvE,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;cACzD,CAAE;cACFiL,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAEzBjP,OAAA;gBAAQsI,KAAK,EAAC,KAAK;gBAAA2G,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCrP,OAAA;gBAAUoV,KAAK,EAAC,gBAAgB;gBAAAnG,QAAA,gBAC5BjP,OAAA;kBAAQsI,KAAK,EAAC,WAAW;kBAAA2G,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvDrP,OAAA;kBAAQsI,KAAK,EAAC,SAAS;kBAAA2G,QAAA,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpDrP,OAAA;kBAAQsI,KAAK,EAAC,OAAO;kBAAA2G,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChDrP,OAAA;kBAAQsI,KAAK,EAAC,MAAM;kBAAA2G,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,eACXrP,OAAA;gBAAUoV,KAAK,EAAC,kBAAkB;gBAAAnG,QAAA,gBAC9BjP,OAAA;kBAAQsI,KAAK,EAAC,aAAa;kBAAA2G,QAAA,EAAC;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC3DrP,OAAA;kBAAQsI,KAAK,EAAC,cAAc;kBAAA2G,QAAA,EAAC;gBAAuB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC7DrP,OAAA;kBAAQsI,KAAK,EAAC,oBAAoB;kBAAA2G,QAAA,EAAC;gBAA6B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzErP,OAAA;kBAAQsI,KAAK,EAAC,oBAAoB;kBAAA2G,QAAA,EAAC;gBAA6B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzErP,OAAA;kBAAQsI,KAAK,EAAC,gBAAgB;kBAAA2G,QAAA,EAAC;gBAAyB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjErP,OAAA;kBAAQsI,KAAK,EAAC,oBAAoB;kBAAA2G,QAAA,EAAC;gBAA6B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzErP,OAAA;kBAAQsI,KAAK,EAAC,oBAAoB;kBAAA2G,QAAA,EAAC;gBAA6B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzErP,OAAA;kBAAQsI,KAAK,EAAC,UAAU;kBAAA2G,QAAA,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrDrP,OAAA;kBAAQsI,KAAK,EAAC,SAAS;kBAAA2G,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAENrP,OAAA;YAAKgP,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC3BjP,OAAA;cACIsI,KAAK,EAAExE,WAAY;cACnB4Y,QAAQ,EAAG5B,CAAC,IAAK;gBACb/W,cAAc,CAAC+W,CAAC,CAACF,MAAM,CAACtS,KAAK,CAAC;gBAC9B,IAAIwS,CAAC,CAACF,MAAM,CAACtS,KAAK,KAAK,KAAK,EAAErE,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;cAC1D,CAAE;cACF+K,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAExBjP,OAAA;gBAAQsI,KAAK,EAAC,KAAK;gBAAA2G,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACtC8G,eAAe,CAAC,CAAC,CAAC7H,GAAG,CAACjO,KAAK,iBACxBL,OAAA;gBAAoBsI,KAAK,EAAEjI,KAAM;gBAAA4O,QAAA,EAAE5O;cAAK,GAA3BA,KAAK;gBAAA6O,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA+B,CACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAENrP,OAAA;YAAKgP,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC1BjP,OAAA;cACIsI,KAAK,EAAE5E,MAAO;cACdgZ,QAAQ,EAAG5B,CAAC,IAAKnX,SAAS,CAACmX,CAAC,CAACF,MAAM,CAACtS,KAAK,CAAE;cAC3C0G,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAEvBjP,OAAA;gBAAQsI,KAAK,EAAC,MAAM;gBAAA2G,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClCrP,OAAA;gBAAQsI,KAAK,EAAC,KAAK;gBAAA2G,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChCrP,OAAA;gBAAQsI,KAAK,EAAC,aAAa;gBAAA2G,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChDrP,OAAA;gBAAQsI,KAAK,EAAC,UAAU;gBAAA2G,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAENrP,OAAA;YAAKgP,SAAS,EAAC,aAAa;YAAAC,QAAA,gBACxBjP,OAAA;cACIgP,SAAS,EAAG,YAAW1L,QAAQ,KAAK,WAAW,GAAG,QAAQ,GAAG,EAAG,EAAE;cAClEiM,OAAO,EAAEA,CAAA,KAAMhM,WAAW,CAAC,WAAW,CAAE;cACxCoZ,KAAK,EAAC,WAAW;cAAA1N,QAAA,EACpB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTrP,OAAA;cACIgP,SAAS,EAAG,YAAW1L,QAAQ,KAAK,MAAM,GAAG,QAAQ,GAAG,EAAG,EAAE;cAC7DiM,OAAO,EAAEA,CAAA,KAAMhM,WAAW,CAAC,MAAM,CAAE;cACnCoZ,KAAK,EAAC,WAAW;cAAA1N,QAAA,EACpB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNrP,OAAA;QAAKgP,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBAClCjP,OAAA;UACIgP,SAAS,EAAG,sBAAqBpL,QAAQ,KAAK,KAAK,GAAG,QAAQ,GAAG,EAAG,EAAE;UACtE2L,OAAO,EAAEA,CAAA,KAAM1L,WAAW,CAAC,KAAK,CAAE;UAAAoL,QAAA,eAElCjP,OAAA;YAAKgP,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBjP,OAAA;cAAKgP,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACtBjP,OAAA;gBAAKgP,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChDrP,OAAA;gBAAKgP,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEpN,QAAQ,CAACuH;cAAM;gBAAA8F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eACNrP,OAAA;cAAKgP,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENrP,OAAA;UACIgP,SAAS,EAAG,yBAAwBpL,QAAQ,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAG,EAAE;UAC9E2L,OAAO,EAAEA,CAAA,KAAM1L,WAAW,CAAC,UAAU,CAAE;UAAAoL,QAAA,eAEvCjP,OAAA;YAAKgP,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBjP,OAAA;cAAKgP,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACtBjP,OAAA;gBAAKgP,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1CrP,OAAA;gBAAKgP,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEoF,aAAa,CAAC9R;cAAQ;gBAAA2M,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACNrP,OAAA;cAAKgP,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENrP,OAAA;UACIgP,SAAS,EAAG,mCAAkCpL,QAAQ,KAAK,YAAY,GAAG,QAAQ,GAAG,EAAG,EAAE;UAC1F2L,OAAO,EAAEA,CAAA,KAAM1L,WAAW,CAAC,YAAY,CAAE;UAAAoL,QAAA,eAEzCjP,OAAA;YAAKgP,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBjP,OAAA;cAAKgP,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACtBjP,OAAA;gBAAKgP,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpDrP,OAAA;gBAAKgP,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEoF,aAAa,CAAC7R;cAAU;gBAAA0M,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eACNrP,OAAA;cAAKgP,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENrP,OAAA;UACIgP,SAAS,EAAG,2BAA0BpL,QAAQ,KAAK,YAAY,GAAG,QAAQ,GAAG,EAAG,EAAE;UAClF2L,OAAO,EAAEA,CAAA,KAAM1L,WAAW,CAAC,YAAY,CAAE;UAAAoL,QAAA,eAEzCjP,OAAA;YAAKgP,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBjP,OAAA;cAAKgP,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACtBjP,OAAA;gBAAKgP,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5CrP,OAAA;gBAAKgP,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEoF,aAAa,CAAC5R;cAAU;gBAAAyM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eACNrP,OAAA;cAAKgP,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNrP,OAAA;QAAKgP,SAAS,EAAC,cAAc;QAAAC,QAAA,gBACzBjP,OAAA;UAAMgP,SAAS,EAAC,eAAe;UAAAC,QAAA,GAAC,UACpB,EAACsH,4BAA4B,CAAC,CAAC,CAACnN,MAAM,EAAC,MAAI,EAACvH,QAAQ,CAACuH,MAAM,EAAC,WACpE,EAAC,CAACxF,QAAQ,KAAK,KAAK,IAAII,YAAY,KAAK,KAAK,IAAIF,WAAW,KAAK,KAAK,kBACnE9D,OAAA;YAAMgP,SAAS,EAAC,kBAAkB;YAAAC,QAAA,GAC7B,GAAG,EAAC,sBAAe,EAAC,CACjBrL,QAAQ,KAAK,KAAK,GACdA,QAAQ,KAAK,UAAU,GAAG,UAAU,GACpCA,QAAQ,KAAK,YAAY,GAAG,oBAAoB,GAChDA,QAAQ,KAAK,YAAY,GAAG,YAAY,GAAG,EAAE,GAC7C,IAAI,EACRI,YAAY,KAAK,KAAK,GAAI,GAAEA,YAAa,SAAQ,GAAG,IAAI,EACxDF,WAAW,KAAK,KAAK,GAAI,GAAEA,WAAY,QAAO,GAAG,IAAI,CACxD,CAACyF,MAAM,CAACqT,OAAO,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;UAAA;YAAA3N,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EACN,CAACzL,QAAQ,KAAK,KAAK,IAAII,YAAY,KAAK,KAAK,IAAIF,WAAW,KAAK,KAAK,kBACnE9D,OAAA;UACIgP,SAAS,EAAC,kBAAkB;UAC5BO,OAAO,EAAEA,CAAA,KAAM;YACX1L,WAAW,CAAC,KAAK,CAAC;YAClBI,eAAe,CAAC,KAAK,CAAC;YACtBF,cAAc,CAAC,KAAK,CAAC;UACzB,CAAE;UAAAkL,QAAA,EACL;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACX;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA,eACR,CACL,eAEDrP,OAAA;MAAKgP,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC7BjP,OAAA;QAAKgP,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACnCjP,OAAA;UAAMgP,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxCrP,OAAA;UAAAiP,QAAA,EAAI;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC,EACL5N,SAAS,gBACNzB,OAAA;QAAKgP,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAChCjP,OAAA;UAAKgP,SAAS,EAAC,iCAAiC;UAAAC,QAAA,gBAC5CjP,OAAA;YAAKgP,SAAS,EAAC,aAAa;YAAAC,QAAA,gBACxBjP,OAAA;cAAMgP,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClDrP,OAAA;cAAMgP,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACNrP,OAAA;YAAKgP,SAAS,EAAC;UAA2B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eACNrP,OAAA;UAAKgP,SAAS,EAAC,8BAA8B;UAAAC,QAAA,gBACzCjP,OAAA;YAAKgP,SAAS,EAAC,aAAa;YAAAC,QAAA,gBACxBjP,OAAA;cAAMgP,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/CrP,OAAA;cAAMgP,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACNrP,OAAA;YAAKgP,SAAS,EAAC;UAA2B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,gBAENrP,OAAA;QAAKgP,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAChCjP,OAAA;UAAKgP,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACpCjP,OAAA;YAAKgP,SAAS,EAAC,aAAa;YAAAC,QAAA,gBACxBjP,OAAA;cAAMgP,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClDrP,OAAA;cAAMgP,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACNrP,OAAA;YAAKgP,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAE,CAAA5N,cAAc,aAAdA,cAAc,wBAAAN,sBAAA,GAAdM,cAAc,CAAEwI,cAAc,cAAA9I,sBAAA,uBAA9BA,sBAAA,CAAgCwP,OAAO,CAAC,CAAC,CAAC,KAAI;UAAK;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrF,CAAC,eACNrP,OAAA;UAAKgP,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACjCjP,OAAA;YAAKgP,SAAS,EAAC,aAAa;YAAAC,QAAA,gBACxBjP,OAAA;cAAMgP,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/CrP,OAAA;cAAMgP,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACNrP,OAAA;YAAKgP,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAE,CAAA5N,cAAc,aAAdA,cAAc,wBAAAL,sBAAA,GAAdK,cAAc,CAAEyI,WAAW,cAAA9I,sBAAA,uBAA3BA,sBAAA,CAA6BuP,OAAO,CAAC,CAAC,CAAC,KAAI;UAAK;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAENrP,OAAA;MAAKgP,SAAS,EAAC,mBAAmB;MAAAC,QAAA,GAC7BxN,SAAS,iBACNzB,OAAA;QAAKgP,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAChCjP,OAAA;UAAKgP,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAC9BjP,OAAA;YAAKgP,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC3BjP,OAAA;cAAAiP,QAAA,EAAI;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,eACNrP,OAAA,CAACH,eAAe;YAAAqP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnBrP,OAAA,CAACH,eAAe;YAAAqP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eACNrP,OAAA;UAAKgP,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAChCjP,OAAA;YAAKgP,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC3BjP,OAAA;cAAAiP,QAAA,EAAI;YAA2C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eACNrP,OAAA,CAACH,eAAe;YAAAqP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnBrP,OAAA,CAACH,eAAe;YAAAqP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eACNrP,OAAA;UAAKgP,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAChCjP,OAAA;YAAKgP,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC3BjP,OAAA;cAAAiP,QAAA,EAAI;YAA8B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eACNrP,OAAA,CAACH,eAAe;YAAAqP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACR,EAEA1N,KAAK,iBACF3B,OAAA;QAAKgP,SAAS,EAAC,eAAe;QAAAC,QAAA,EACzBtN;MAAK;QAAAuN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACR,EAEAhO,cAAc,IAAI,CAACI,SAAS,iBACzBzB,OAAA;QAAKgP,SAAS,EAAC,eAAe;QAAAC,QAAA,EACzBsH,4BAA4B,CAAC,CAAC,CAACjI,GAAG,CAAC,CAAC5F,OAAO,EAAE4G,KAAK,kBAC/CtP,OAAA,CAACgY,eAAe;UAEZtP,OAAO,EAAEA,OAAQ;UACjBuP,UAAU,EAAEvV,gBAAgB,CAACgG,OAAO,CAACC,IAAI,CAAE;UAC3CuP,QAAQ,EAAEA,CAAA,KAAMzC,aAAa,CAAC/M,OAAO,CAACC,IAAI;QAAE,GAHtC,GAAED,OAAO,CAACC,IAAK,IAAG2G,KAAM,EAAC;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAIlC,CACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,eAEDrP,OAAA,CAAC+O,oBAAoB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACvO,EAAA,CA3wEID,OAAO;EAAA,QAESlB,OAAO,EACRC,WAAW;AAAA;AAAAkd,EAAA,GAH1Bjc,OAAO;AA6wEb,eAAeA,OAAO;AAAC,IAAAic,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}