import React from 'react';
import './ProgramMatchingSkeleton.css';

const ProgramMatchingSkeleton = ({ showProgress = false, progress = 0 }) => {
    return (
        <div className="program-matching-skeleton">
            {showProgress && (
                <div className="matching-progress">
                    <div className="progress-header">
                        <h3>Analyzing Your Program Matches...</h3>
                        <span className="progress-text">{Math.round(progress)}% Complete</span>
                    </div>
                    <div className="progress-bar">
                        <div 
                            className="progress-fill" 
                            style={{ width: `${progress}%` }}
                        />
                    </div>
                    <p className="progress-description">
                        Comparing your qualifications against program requirements
                    </p>
                </div>
            )}
            
            <div className="skeleton-sections">
                {/* Eligible Programs Skeleton */}
                <div className="skeleton-section">
                    <div className="skeleton-header">
                        <div className="skeleton-title" />
                        <div className="skeleton-count" />
                    </div>
                    <div className="skeleton-programs">
                        {[...Array(3)].map((_, i) => (
                            <div key={i} className="skeleton-program">
                                <div className="skeleton-program-header">
                                    <div className="skeleton-program-name" />
                                    <div className="skeleton-status-badge" />
                                </div>
                                <div className="skeleton-requirements">
                                    {[...Array(4)].map((_, j) => (
                                        <div key={j} className="skeleton-requirement">
                                            <div className="skeleton-req-label" />
                                            <div className="skeleton-req-value" />
                                        </div>
                                    ))}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Ineligible Programs Skeleton */}
                <div className="skeleton-section">
                    <div className="skeleton-header">
                        <div className="skeleton-title" />
                        <div className="skeleton-count" />
                    </div>
                    <div className="skeleton-programs">
                        {[...Array(2)].map((_, i) => (
                            <div key={i} className="skeleton-program">
                                <div className="skeleton-program-header">
                                    <div className="skeleton-program-name" />
                                    <div className="skeleton-status-badge" />
                                </div>
                                <div className="skeleton-requirements">
                                    {[...Array(3)].map((_, j) => (
                                        <div key={j} className="skeleton-requirement">
                                            <div className="skeleton-req-label" />
                                            <div className="skeleton-req-value" />
                                        </div>
                                    ))}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ProgramMatchingSkeleton;