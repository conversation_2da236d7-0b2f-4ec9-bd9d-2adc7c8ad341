import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import styled from '@emotion/styled';

const Container = styled.div`
  max-width: 400px;
  margin: 2rem auto;
  padding: 2rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
`;

const Title = styled.h2`
  text-align: center;
  color: #2c3e50;
  margin-bottom: 1.5rem;
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const Input = styled.input`
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  font-size: 1rem;

  &:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 1px #3498db;
  }
`;

const Button = styled.button`
  padding: 0.75rem;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover:not(:disabled) {
    background-color: #2980b9;
  }

  &:disabled {
    background-color: #bdc3c7;
    cursor: not-allowed;
  }
`;

const Message = styled.div`
  padding: 0.75rem;
  border-radius: 0.375rem;
  text-align: center;
  margin-bottom: 1rem;
`;

const ErrorMessage = styled(Message)`
  color: #e74c3c;
  background-color: #fdecea;
  border: 1px solid #e74c3c;
`;

const SuccessMessage = styled(Message)`
  color: #27ae60;
  background-color: #eafaf1;
  border: 1px solid #27ae60;
`;

const InfoMessage = styled(Message)`
  color: #3498db;
  background-color: #ebf3fd;
  border: 1px solid #3498db;
`;

const UpdatePassword = () => {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isValidSession, setIsValidSession] = useState(false);
  
  const { updateUserPassword, currentUser, supabase } = useAuth();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  useEffect(() => {
    // Check if we have a valid session for password update
    const checkSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error('Session error:', error);
          setError('Invalid or expired reset link. Please request a new password reset.');
          return;
        }

        if (session && session.user) {
          setIsValidSession(true);
          setMessage('Please enter your new password below.');
        } else {
          setError('Invalid or expired reset link. Please request a new password reset.');
        }
      } catch (err) {
        console.error('Error checking session:', err);
        setError('Unable to verify reset link. Please try again.');
      }
    };

    checkSession();
  }, [supabase]);

  const validatePassword = (password) => {
    if (password.length < 6) {
      return 'Password must be at least 6 characters long.';
    }
    if (!/\d/.test(password)) {
      return 'Password must include at least one number.';
    }
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      return 'Password must include at least one special character.';
    }
    return null;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setMessage('');

    // Validation
    if (password !== confirmPassword) {
      setError('Passwords do not match.');
      return;
    }

    const passwordError = validatePassword(password);
    if (passwordError) {
      setError(passwordError);
      return;
    }

    setIsLoading(true);

    try {
      const { data, error: updateError } = await updateUserPassword(password);

      if (updateError) {
        throw updateError;
      }

      setMessage('Password updated successfully! Redirecting to login...');
      
      // Sign out the user and redirect to login
      setTimeout(async () => {
        try {
          await supabase.auth.signOut();
          navigate('/login');
        } catch (signOutError) {
          console.error('Sign out error:', signOutError);
          navigate('/login');
        }
      }, 2000);

    } catch (err) {
      console.error('Password update error:', err);
      
      if (err.message?.includes('session_not_found')) {
        setError('Your session has expired. Please request a new password reset link.');
      } else if (err.message?.includes('same_password')) {
        setError('New password must be different from your current password.');
      } else {
        setError('Failed to update password. Please try again or request a new reset link.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleRequestNewReset = () => {
    navigate('/login');
  };

  if (!isValidSession && !error) {
    return (
      <Container>
        <Title>Verifying Reset Link...</Title>
        <InfoMessage>Please wait while we verify your password reset link.</InfoMessage>
      </Container>
    );
  }

  return (
    <Container>
      <Title>Update Your Password</Title>
      
      {error && (
        <div>
          <ErrorMessage>{error}</ErrorMessage>
          <Button type="button" onClick={handleRequestNewReset}>
            Request New Password Reset
          </Button>
        </div>
      )}
      
      {message && <SuccessMessage>{message}</SuccessMessage>}
      
      {isValidSession && (
        <Form onSubmit={handleSubmit}>
          <Input
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            placeholder="New Password (min 6 chars, 1 number, 1 special char)"
            required
            disabled={isLoading}
            autoComplete="new-password"
          />
          
          <Input
            type="password"
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            placeholder="Confirm New Password"
            required
            disabled={isLoading}
            autoComplete="new-password"
          />
          
          <Button type="submit" disabled={isLoading || !password || !confirmPassword}>
            {isLoading ? 'Updating Password...' : 'Update Password'}
          </Button>
        </Form>
      )}
    </Container>
  );
};

export default UpdatePassword;