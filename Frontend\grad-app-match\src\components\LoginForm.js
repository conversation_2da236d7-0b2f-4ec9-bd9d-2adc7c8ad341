// src/components/LoginForm.js
import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom'; // Import Link
import { useAuth } from '../contexts/AuthContext';

const LoginForm = () => {
    const navigate = useNavigate();
    // Get signIn and resetPasswordForEmail from context
    const { signIn, resetPasswordForEmail, currentUser } = useAuth();
    const [formData, setFormData] = useState({
        email: '',
        password: ''
    });
    const [error, setError] = useState('');
    const [message, setMessage] = useState(''); // For success/info messages
    const [isLoading, setIsLoading] = useState(false);

    // Redirect if already logged in
    React.useEffect(() => {
        if (currentUser) {
            navigate('/home'); // Or your desired dashboard route
        }
    }, [currentUser, navigate]);


    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
        setError(''); // Clear errors and messages on change
        setMessage('');
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setIsLoading(true);
        setError('');
        setMessage('');

        try {
            const { data, error: signInError } = await signIn(formData.email, formData.password);

            if (signInError) {
                throw signInError; // Throw error to be caught below
            }

            console.log('Login successful:', data);
            // Navigation should be handled by AuthContext listener or ProtectedRoute
            // navigate('/home'); // Remove navigation from here

        } catch (err) {
            console.error('Login error:', err);
            // Check for Supabase specific error codes/messages
            if (err.message === 'Invalid login credentials') {
                 setError('Invalid email or password. Please check your credentials and try again.');
            } else if (err.message === 'Email not confirmed' || err.message.includes('email_not_confirmed')) {
                 setError('Please verify your email address before logging in. Check your inbox (and spam folder) for the verification email. If you need a new verification email, please register again with the same email.');
            }
             else {
                setError('Failed to login. Please try again.');
            }
        } finally {
            setIsLoading(false);
        }
    };

    const handlePasswordReset = async () => {
        const emailToReset = formData.email; // Grab the email from the form state
        if (!emailToReset) {
            setError('Please enter your email address in the field above first.');
            return;
        }
        setIsLoading(true);
        setError('');
        setMessage('');
        try {
            console.log(`Attempting password reset for: ${emailToReset}`); // Log the email being used
            const { data, error: resetError } = await resetPasswordForEmail(emailToReset);

            console.log('Password reset response:', { data, error: resetError }); // Debug log

            if (resetError) {
                console.error('Reset error details:', resetError);
                throw resetError;
            }

            // If successful (no error thrown)
            setMessage('Password reset request sent. Please check your inbox and spam folder. If you don\'t receive an email, the account may not exist or email sending may not be configured.');

        } catch (err) {
            console.error('Password reset error:', err);
            console.error('Error details:', {
                message: err.message,
                status: err.status,
                statusText: err.statusText
            });
            
            if (err.message && err.message.toLowerCase().includes('rate limit')) {
                 setError('Too many password reset attempts. Please wait a few minutes and try again.');
            } else if (err.message && err.message.toLowerCase().includes('user not found')) {
                 setError('No account found with that email address. Please check the email or register a new account.');
            } else {
                setError(`Password reset failed: ${err.message || 'Unknown error'}. Please check your Supabase email configuration.`);
            }
        } finally {
            setIsLoading(false);
        }
    };


    return (
        <div className="auth-form-container">
            <h2>Login</h2>
            {error && <div className="error-message">{error}</div>}
            {message && <div className="success-message">{message}</div>} {/* Display success/info messages */}
            <form onSubmit={handleSubmit} className="auth-form">
                <div className="form-group">
                    <label htmlFor="email">Email:</label>
                    <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        required
                        disabled={isLoading}
                        autoComplete="username"
                        placeholder="Enter your email"
                    />
                </div>
                <div className="form-group">
                    <label htmlFor="password">Password:</label>
                    <input
                        type="password"
                        id="password"
                        name="password"
                        value={formData.password}
                        onChange={handleChange}
                        required
                        disabled={isLoading}
                        autoComplete="current-password"
                        placeholder="Enter your password"
                    />
                </div>
                <button
                    type="submit"
                    disabled={isLoading || !formData.email || !formData.password}
                    className={isLoading ? 'loading' : ''}
                >
                    {isLoading ? 'Logging in...' : 'Login'}
                </button>
                 {/* Add Forgot Password Link */}
                 <div className="forgot-password-link">
                    <button type="button" onClick={handlePasswordReset} disabled={isLoading || !formData.email}>
                        Forgot Password?
                    </button>
                </div>
                 {/* Link to Registration */}
                 <div className="register-link">
                    Don't have an account? <Link to="/register">Register here</Link>
                </div>
            </form>
        </div>
    );
};

export default LoginForm;
