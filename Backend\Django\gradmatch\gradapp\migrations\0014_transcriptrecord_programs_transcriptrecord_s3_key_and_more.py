# Generated by Django 5.0.1 on 2025-01-28 04:07

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('gradapp', '0013_usertranscript'),
    ]

    operations = [
        migrations.AddField(
            model_name='transcriptrecord',
            name='programs',
            field=models.ManyToManyField(related_name='transcripts', to='gradapp.program'),
        ),
        migrations.AddField(
            model_name='transcriptrecord',
            name='s3_key',
            field=models.CharField(max_length=512, null=True),
        ),
        migrations.AddField(
            model_name='transcriptrecord',
            name='science_gpa',
            field=models.DecimalField(decimal_places=2, max_digits=4, null=True),
        ),
    ]
