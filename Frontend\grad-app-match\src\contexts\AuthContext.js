import React, { createContext, useContext, useState, useEffect } from 'react';
import axios from 'axios';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = 'https://qjejqyadcfeixnvvebkm.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.wA3_lGwzsu6jEfHisTe_Bbpz2cWKtklhFj0kD6Q3v5k';
const supabase = createClient(supabaseUrl, supabaseAnonKey);

const AuthContext = createContext();

export const useAuth = () => useContext(AuthContext);

export const AuthProvider = ({ children }) => {
    const [currentUser, setCurrentUser] = useState(null);
    const [token, setToken] = useState(null);
    const [loading, setLoading] = useState(true); // Add loading state

    useEffect(() => {
        // Check initial session
        const getSession = async () => {
            const { data: { session } } = await supabase.auth.getSession();
            const currentToken = session?.access_token ?? null;
            setCurrentUser(session?.user ?? null);
            setToken(currentToken);
            if (currentToken) {
                axios.defaults.headers.common['Authorization'] = `Bearer ${currentToken}`;
            } else {
                delete axios.defaults.headers.common['Authorization'];
            }
            setLoading(false);
        };
        getSession();

        // Listen for auth state changes
        const { data: { subscription } } = supabase.auth.onAuthStateChange(
            async (event, session) => {
                console.log('Supabase Auth Event:', event, session); // For debugging
                const currentToken = session?.access_token ?? null;
                setCurrentUser(session?.user ?? null);
                setToken(currentToken);
                if (currentToken) {
                    axios.defaults.headers.common['Authorization'] = `Bearer ${currentToken}`;
                } else {
                    delete axios.defaults.headers.common['Authorization'];
                }
                setLoading(false); // Ensure loading is false after state change
            }
        );

        // Cleanup subscription on unmount
        return () => {
            subscription?.unsubscribe();
        };
    }, []);

    const signIn = async (email, password) => {
        try {
            const { data, error } = await supabase.auth.signInWithPassword({
                email,
                password,
            });
            if (error) throw error;
            return data;
        } catch (error) {
            console.error('Supabase Sign In Error:', error);
            throw error;
        }
    };

    const signUp = async (email, password, options = {}) => {
        try {
            // Store email for potential verification resend
            localStorage.setItem('pendingVerificationEmail', email);
            
            const { data, error } = await supabase.auth.signUp({
                email,
                password,
                options: {
                    ...options,
                    emailRedirectTo: `${window.location.origin}/verify-email`
                }
            });
            if (error) throw error;
            
            // Note: Supabase requires email confirmation by default.
            // The user object might be null until confirmed.
            return data;
        } catch (error) {
            console.error('Supabase Sign Up Error:', error);
            throw error;
        }
    };

    const signOut = async () => {
        try {
            const { error } = await supabase.auth.signOut();
            if (error) throw error;
            // State updates handled by onAuthStateChange listener
        } catch (error) {
            console.error('Supabase Sign Out Error:', error);
            throw error;
        }
    };

    const resetPasswordForEmail = async (email) => {
        try {
            // Specify the redirect URL for the password reset link
            const redirectUrl = window.location.origin + '/update-password'; // Adjust as needed
            const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
                redirectTo: redirectUrl,
            });
            if (error) throw error;
            return data;
        } catch (error) {
            console.error('Supabase Password Reset Error:', error);
            throw error;
        }
    };

    // Function to update password (used after clicking reset link)
    const updateUserPassword = async (newPassword) => {
        try {
            const { data, error } = await supabase.auth.updateUser({
                password: newPassword
            });
            if (error) throw error;
            return data;
        } catch (error) {
            console.error('Supabase Update Password Error:', error);
            throw error;
        }
    };


    const value = {
        currentUser,
        token,
        signIn,
        signUp,
        signOut,
        resetPasswordForEmail,
        updateUserPassword, // Add this function
        supabase, // Expose supabase client if needed directly
        loading // Expose loading state
    };

    // Render children only when not loading initial auth state
    return (
        <AuthContext.Provider value={value}>
            {!loading && children}
        </AuthContext.Provider>
    );
};
