"""
Migration Logging Utilities

Provides comprehensive logging setup and utilities for database migration processes.
"""

import json
import logging
import os
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, Tuple


class ColoredFormatter(logging.Formatter):
    """Custom formatter with color support for console output"""
    
    COLORS = {
        'DEBUG': '\033[36m',    # Cyan
        'INFO': '\033[32m',     # Green
        'WARNING': '\033[33m',  # Yellow
        'ERROR': '\033[31m',    # Red
        'CRITICAL': '\033[35m', # Magenta
    }
    RESET = '\033[0m'
    
    def format(self, record):
        if hasattr(record, 'use_colors') and record.use_colors:
            color = self.COLORS.get(record.levelname, '')
            record.levelname = f"{color}{record.levelname}{self.RESET}"
            record.msg = f"{color}{record.msg}{self.RESET}"
        
        return super().format(record)


class MigrationProgressHandler:
    """Handles progress tracking and statistics for migration processes"""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.start_time = time.time()
        self.stats = {
            'log_entries': 0,
            'errors': 0,
            'warnings': 0,
            'info_messages': 0,
            'debug_messages': 0
        }
        self.errors = []
        self.warnings = []
    
    def log_progress(self, message: str, level: str = 'INFO'):
        """Log progress message and update statistics"""
        self.stats['log_entries'] += 1
        
        if level == 'ERROR':
            self.stats['errors'] += 1
            self.errors.append({
                'message': message,
                'timestamp': datetime.now().isoformat(),
                'elapsed_time': time.time() - self.start_time
            })
        elif level == 'WARNING':
            self.stats['warnings'] += 1
            self.warnings.append({
                'message': message,
                'timestamp': datetime.now().isoformat(),
                'elapsed_time': time.time() - self.start_time
            })
        elif level == 'INFO':
            self.stats['info_messages'] += 1
        elif level == 'DEBUG':
            self.stats['debug_messages'] += 1
        
        # Log the message
        getattr(self.logger, level.lower())(message)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get current statistics"""
        return {
            **self.stats,
            'elapsed_time': time.time() - self.start_time,
            'errors_list': self.errors,
            'warnings_list': self.warnings
        }


class MigrationTimer:
    """Context manager for timing migration operations"""
    
    def __init__(self, logger: logging.Logger, operation_name: str):
        self.logger = logger
        self.operation_name = operation_name
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        self.logger.info(f"Starting {self.operation_name}...")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = time.time() - self.start_time
        if exc_type is None:
            self.logger.info(f"Completed {self.operation_name} in {duration:.2f} seconds")
        else:
            self.logger.error(f"Failed {self.operation_name} after {duration:.2f} seconds")


def setup_migration_logging(
    log_level: str = 'INFO',
    log_file: Optional[str] = None,
    console_output: bool = True,
    use_colors: bool = True
) -> Tuple[logging.Logger, MigrationProgressHandler]:
    """
    Setup comprehensive logging for migration processes
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR)
        log_file: Path to log file (optional)
        console_output: Whether to output to console
        use_colors: Whether to use colored console output
        
    Returns:
        Tuple of (logger, progress_handler)
    """
    # Create logger
    logger = logging.getLogger('migration')
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # Clear existing handlers
    logger.handlers.clear()
    
    # Create formatters
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    console_formatter = ColoredFormatter(
        '%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S'
    ) if use_colors else logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S'
    )
    
    # Add file handler if log file specified
    if log_file:
        # Ensure log directory exists
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)  # Always log everything to file
        file_handler.setFormatter(detailed_formatter)
        logger.addHandler(file_handler)
    
    # Add console handler if requested
    if console_output:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(getattr(logging, log_level.upper()))
        console_handler.setFormatter(console_formatter)
        
        # Add color support flag to records
        if use_colors:
            def add_color_flag(record):
                record.use_colors = True
                return True
            console_handler.addFilter(add_color_flag)
        
        logger.addHandler(console_handler)
    
    # Create progress handler
    progress_handler = MigrationProgressHandler(logger)
    
    return logger, progress_handler


def create_migration_log_file(operation_name: str, log_dir: str = 'logs') -> str:
    """
    Create a timestamped log file for migration operations
    
    Args:
        operation_name: Name of the migration operation
        log_dir: Directory to store log files
        
    Returns:
        Path to the created log file
    """
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_filename = f"{operation_name}_{timestamp}.log"
    log_path = Path(log_dir) / log_filename
    
    # Ensure log directory exists
    log_path.parent.mkdir(parents=True, exist_ok=True)
    
    return str(log_path)


def log_system_info(logger: logging.Logger):
    """Log system information for debugging purposes"""
    import platform
    import django
    from django.conf import settings
    
    logger.info("System Information:")
    logger.info(f"  Platform: {platform.platform()}")
    logger.info(f"  Python: {platform.python_version()}")
    logger.info(f"  Django: {django.get_version()}")
    logger.info(f"  Database: {settings.DATABASES['default']['ENGINE']}")
    logger.info(f"  Working Directory: {os.getcwd()}")


def create_error_report(progress_handler: MigrationProgressHandler, report_file: str):
    """
    Create a detailed error report from migration progress
    
    Args:
        progress_handler: Progress handler with collected statistics
        report_file: Path to save the error report
    """
    stats = progress_handler.get_stats()
    
    error_report = {
        'timestamp': datetime.now().isoformat(),
        'summary': {
            'total_log_entries': stats['log_entries'],
            'total_errors': stats['errors'],
            'total_warnings': stats['warnings'],
            'elapsed_time': stats['elapsed_time']
        },
        'errors': stats['errors_list'],
        'warnings': stats['warnings_list']
    }
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(error_report, f, indent=2, ensure_ascii=False)
    except Exception as e:
        progress_handler.logger.error(f"Failed to create error report: {str(e)}")