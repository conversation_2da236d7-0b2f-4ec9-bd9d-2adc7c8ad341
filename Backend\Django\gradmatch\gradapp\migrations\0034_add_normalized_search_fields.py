# Generated by Django 5.0.1 on 2025-07-28 04:02

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('gradapp', '0033_alter_program_distance_learning_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='program',
            name='city',
            field=models.CharField(blank=True, db_index=True, help_text='City name', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='class_size_max',
            field=models.IntegerField(blank=True, db_index=True, help_text='Maximum class size as integer', null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='class_size_min',
            field=models.IntegerField(blank=True, db_index=True, help_text='Minimum class size as integer', null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='has_distance_learning',
            field=models.BooleanField(blank=True, db_index=True, help_text='True if distance learning available', null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='has_on_campus_housing',
            field=models.BooleanField(blank=True, db_index=True, help_text='True if on-campus housing available', null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='has_part_time_option',
            field=models.BooleanField(blank=True, db_index=True, help_text='True if part-time option available', null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='min_gpa_overall',
            field=models.DecimalField(blank=True, db_index=True, decimal_places=2, help_text='Minimum overall GPA required', max_digits=3, null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='min_gpa_prereq',
            field=models.DecimalField(blank=True, db_index=True, decimal_places=2, help_text='Minimum prerequisite GPA required', max_digits=3, null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='min_gpa_science',
            field=models.DecimalField(blank=True, db_index=True, decimal_places=2, help_text='Minimum science GPA required', max_digits=3, null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='min_patient_care_hours',
            field=models.IntegerField(blank=True, db_index=True, help_text='Minimum patient care hours required', null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='min_shadowing_hours',
            field=models.IntegerField(blank=True, db_index=True, help_text='Minimum shadowing hours required', null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='offers_doctorate',
            field=models.BooleanField(blank=True, db_index=True, help_text='True if doctorate degree offered', null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='program_length_months_max',
            field=models.IntegerField(blank=True, db_index=True, help_text='Maximum program length if range', null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='program_length_months_min',
            field=models.IntegerField(blank=True, db_index=True, help_text='Program length in months as integer', null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='requires_onsite_interview',
            field=models.BooleanField(blank=True, db_index=True, help_text='True if onsite interview required', null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='state',
            field=models.CharField(blank=True, db_index=True, help_text='Two-letter state code', max_length=2, null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='tuition_in_state',
            field=models.DecimalField(blank=True, db_index=True, decimal_places=2, help_text='In-state tuition amount', max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='program',
            name='tuition_out_of_state',
            field=models.DecimalField(blank=True, db_index=True, decimal_places=2, help_text='Out-of-state tuition amount', max_digits=10, null=True),
        ),
    ]
