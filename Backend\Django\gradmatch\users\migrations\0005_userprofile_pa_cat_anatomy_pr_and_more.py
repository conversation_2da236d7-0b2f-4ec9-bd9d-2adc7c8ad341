# Generated by Django 5.0.1 on 2025-04-09 04:10

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0004_userprofile_has_taken_casper_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='userprofile',
            name='pa_cat_anatomy_pr',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='pa_cat_anatomy_ss',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='pa_cat_biology_pr',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='pa_cat_biology_ss',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='pa_cat_chemistry_pr',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='pa_cat_chemistry_ss',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='pa_cat_composite_pr',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='pa_cat_composite_ss',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='pa_cat_physiology_pr',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='pa_cat_physiology_ss',
            field=models.IntegerField(blank=True, null=True),
        ),
    ]
