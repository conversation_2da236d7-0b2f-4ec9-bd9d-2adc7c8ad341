# Generated by Django 5.0.1 on 2025-05-05 03:02

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('gradapp', '0023_delete_programmatchresult'),
        ('users', '0008_userprofile_supabase_user_id'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserProgramMatch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('eligibility_status', models.CharField(choices=[('ELIGIBLE', 'Eligible'), ('INELIGIBLE', 'Ineligible'), ('NEAR_MATCH', 'Near Match'), ('INFO_MISSING', 'Info Missing'), ('ERROR', 'Error Calculating')], default='INFO_MISSING', max_length=20)),
                ('match_details', models.J<PERSON>NField(blank=True, help_text='Stores structured details of the match (e.g., met/unmet prereqs, GPA status)', null=True)),
                ('last_calculated', models.DateTimeField(default=django.utils.timezone.now)),
                ('program', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_matches', to='gradapp.program')),
                ('user_profile', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='program_matches', to='users.userprofile')),
            ],
            options={
                'ordering': ['-last_calculated'],
                'unique_together': {('user_profile', 'program')},
            },
        ),
    ]
