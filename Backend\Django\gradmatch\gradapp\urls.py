# myapp/urls.py
# gradmatch/gradmatch/urls.py THIS IS APP SPECIFIC

from django.urls import path
from . import views

urlpatterns = [
    path('', views.home, name='home'),
    path('api/schools/', views.list_all_schools, name='list_schools'),
    path('api/transcript/', views.process_transcript, name='process_transcript'),
    path('api/validate-transcript/', views.validate_transcript, name='validate_transcript'),
    path('api/chat/', views.chat, name='chat'),
    path('api/match-programs/', views.match_programs, name='match_programs'),
    path('api/transcript/get/', views.get_user_transcripts, name='get_transcripts'),
    path('api/programs/', views.get_program_list, name='get_program_list'),
    path('transcripts/upload/', views.upload_transcript, name='upload_transcript'),
    path('transcripts/', views.list_transcripts, name='list_transcripts'),
    path('transcripts/<int:transcript_id>/', views.delete_transcript, name='delete_transcript'),
    path('api/s3-transcripts/', views.list_s3_transcripts, name='list_s3_transcripts'),
    path('api/transcript/manual/', views.manual_transcript, name='manual_transcript'),
    path('api/transcript/record/<int:transcript_id>/', views.delete_transcript_record, name='delete_transcript_record'),
    path('api/prerequisites/', views.handle_prerequisites, name='handle_prerequisites'),
    path('api/prerequisites/saved/', views.get_saved_prerequisites, name='get_saved_prerequisites'),
    path('api/courses/<int:course_id>/', views.update_course_academic_level, name='update_course_academic_level'),
    path('api/matching-results/', views.get_matching_results, name='get_matching_results'),
    path('api/matching-results/save/', views.save_matching_results, name='save_matching_results'),
]
