.prerequisite-courses {
    margin-top: 8px;
    padding: 8px 16px;
    background-color: #f8fafc;
    border-left: 3px solid #e2e8f0;
    font-size: 0.9em;
}

.course-detail {
    padding: 8px 0;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #edf2f7;
}

.course-detail:last-child {
    border-bottom: none;
}

.course-info {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
}

.course-stats {
    display: flex;
    align-items: center;
    gap: 24px;
    color: #4a5568;
}

.course-code {
    font-weight: 600;
    color: #2c5282;
    min-width: 120px;
}

.course-name {
    color: #4a5568;
    flex: 1;
}

.course-grade {
    color: #2d3748;
    font-weight: 500;
    min-width: 80px;
    text-align: center;
}

.course-credits {
    color: #718096;
    min-width: 80px;
    text-align: right;
}

.requirement-header {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    background-color: #f8fafc;
    border-bottom: 2px solid #e2e8f0;
    font-size: 0.95em;
}

.requirement-item {
    margin-bottom: 20px;
    background-color: white;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.requirement-value {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: auto;
    padding-left: 16px;
    font-size: 0.95em;
}

.student-value {
    font-weight: 500;
    color: #2d3748;
}

.requirement-status {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.met .requirement-status {
    color: #048c2c;
}

.not-met .requirement-status {
    color: #e53e3e;
}

.timeframe {
    color: #718096;
    font-size: 0.9em;
    margin-left: 16px;
    padding-left: 16px;
    border-left: 1px solid #e2e8f0;
}

.pamatch-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: none;
    margin: 0 auto;
    padding: 20px;
}

.programs-comparison {
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: none;
    margin: 0 auto;
}

.program-column {
    width: 100%;
    background-color: white;
    padding: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 32px;
}

.requirements-section {
    display: flex;
    flex-direction: column;
    gap: 24px;
    width: 100%;
}

.results-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

.requirement-group {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 24px;
}

.lab-required {
    color: #2b6cb0;
    font-size: 0.9em;
    margin-left: 16px;
    padding-left: 16px;
    border-left: 1px solid #e2e8f0;
    font-weight: 500;
}

.program-url {
    color: #4a5568;
    font-size: 0.9em;
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 2px solid #e2e8f0;
    word-break: break-all;
    text-align: center;
    display: flex;
    justify-content: center;
}

.program-url a {
    color: #2b6cb0;
    text-decoration: none;
    transition: color 0.2s;
    display: inline-block;
}

.program-url a:hover {
    color: #2c5282;
    text-decoration: underline;
}

.test-details {
    padding: 16px;
    color: #4a5568;
    font-size: 0.95em;
}

.test-score {
    display: flex;
    align-items: center;
    color: #4a5568;
}

.test-score span:first-child {
    font-weight: 500;
    min-width: 100px;
}

.test-requirement {
    color: #718096;
    font-size: 0.95em;
    margin-left: auto;
}

.percentile {
    color: #718096;
    font-size: 0.9em;
    margin-left: 8px;
} 