# Generated by Django 5.0.1 on 2025-07-27 04:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('gradapp', '0029_remove_school_city_remove_school_state_and_more'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='attritiondata',
            name='class_year',
            field=models.Char<PERSON>ield(max_length=10),
        ),
        migrations.AlterField(
            model_name='gparequirement',
            name='minimum_overall',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='gparequirement',
            name='minimum_prereq',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='gparequirement',
            name='minimum_science',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='healthcareexperience',
            name='time_limit',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='pancepassrate',
            name='year',
            field=models.Char<PERSON>ield(max_length=10),
        ),
        migrations.Alter<PERSON>ield(
            model_name='program',
            name='description',
            field=models.TextField(blank=True, null=True),
        ),
    ]
