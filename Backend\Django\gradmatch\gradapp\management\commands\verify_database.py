"""
Django management command to verify and display database content for PA programs
"""

from django.core.management.base import BaseCommand
from django.db.models import Count, Q
from gradapp.models import (
    School, Program, PrerequisiteCourse, HealthcareExperience,
    PatientCareExperience, ShadowingRequirement, GRERequirement,
    GPARequirement, InterviewRequirement, ApplicationRequirement,
    OtherRequirement, RecommendationRequirement, ClassProfile,
    PANCEPassRate, AttritionData, EnhancedClassProfile,
    MatriculantDemographics, ProgramCurriculum, CASPARequirement,
    TuitionInformation
)
import json


class Command(BaseCommand):
    help = 'Verify and display database content for PA programs'

    def add_arguments(self, parser):
        parser.add_argument(
            '--program-name',
            type=str,
            help='Show detailed information for a specific program'
        )
        
        parser.add_argument(
            '--summary',
            action='store_true',
            help='Show summary statistics only'
        )
        
        parser.add_argument(
            '--enhanced-data',
            action='store_true',
            help='Check for enhanced data fields specifically'
        )
        
        parser.add_argument(
            '--export-json',
            type=str,
            help='Export program data to JSON file'
        )

    def handle(self, *args, **options):
        """Main command handler"""
        
        if options['summary']:
            self._show_summary()
        elif options['program_name']:
            self._show_program_details(options['program_name'])
        elif options['enhanced_data']:
            self._check_enhanced_data()
        elif options['export_json']:
            self._export_to_json(options['export_json'])
        else:
            self._show_all_programs()

    def _show_summary(self):
        """Display summary statistics"""
        self.stdout.write(self.style.SUCCESS("=== DATABASE SUMMARY ==="))
        
        # Basic counts
        school_count = School.objects.count()
        program_count = Program.objects.count()
        
        self.stdout.write(f"Schools: {school_count}")
        self.stdout.write(f"Programs: {program_count}")
        
        # Enhanced data counts
        pance_count = PANCEPassRate.objects.count()
        attrition_count = AttritionData.objects.count()
        demographics_count = MatriculantDemographics.objects.count()
        curriculum_count = ProgramCurriculum.objects.count()
        enhanced_profile_count = EnhancedClassProfile.objects.count()
        
        self.stdout.write(f"\n=== ENHANCED DATA ===")
        self.stdout.write(f"PANCE Pass Rates: {pance_count}")
        self.stdout.write(f"Attrition Data: {attrition_count}")
        self.stdout.write(f"Demographics: {demographics_count}")
        self.stdout.write(f"Curriculum Data: {curriculum_count}")
        self.stdout.write(f"Enhanced Class Profiles: {enhanced_profile_count}")
        
        # Requirements counts
        prereq_count = PrerequisiteCourse.objects.count()
        gpa_req_count = GPARequirement.objects.count()
        healthcare_exp_count = HealthcareExperience.objects.count()
        
        self.stdout.write(f"\n=== REQUIREMENTS ===")
        self.stdout.write(f"Prerequisite Courses: {prereq_count}")
        self.stdout.write(f"GPA Requirements: {gpa_req_count}")
        self.stdout.write(f"Healthcare Experience: {healthcare_exp_count}")

    def _show_program_details(self, program_name):
        """Show detailed information for a specific program"""
        try:
            program = Program.objects.get(name__icontains=program_name)
        except Program.DoesNotExist:
            self.stdout.write(self.style.ERROR(f"Program not found: {program_name}"))
            # Show available programs
            programs = Program.objects.all()[:10]
            self.stdout.write("Available programs (first 10):")
            for p in programs:
                self.stdout.write(f"  - {p.name}")
            return
        except Program.MultipleObjectsReturned:
            programs = Program.objects.filter(name__icontains=program_name)
            self.stdout.write(self.style.WARNING(f"Multiple programs found matching '{program_name}':"))
            for p in programs:
                self.stdout.write(f"  - {p.name}")
            return

        self._display_program_info(program)

    def _display_program_info(self, program):
        """Display comprehensive program information"""
        self.stdout.write(self.style.SUCCESS(f"\n=== {program.name} ==="))
        self.stdout.write(f"School: {program.school.name}")
        self.stdout.write(f"Description: {program.description[:200]}..." if program.description and len(program.description) > 200 else f"Description: {program.description}")
        self.stdout.write(f"URL: {program.url}")
        self.stdout.write(f"Application Deadline: {program.application_deadline}")
        self.stdout.write(f"Program Start Date: {program.program_start_date}")
        self.stdout.write(f"Average GPA: {program.average_gpa}")
        self.stdout.write(f"Class Size: {program.class_size}")
        
        # Enhanced data
        self.stdout.write(f"\n--- ENHANCED DATA ---")
        
        # PANCE Pass Rates
        pance_rates = program.pance_pass_rates.all()
        if pance_rates:
            self.stdout.write(f"PANCE Pass Rates ({pance_rates.count()} records):")
            for rate in pance_rates:
                self.stdout.write(f"  {rate.year} ({rate.group}): {rate.program_pass_rate}")
        
        # Attrition Data
        attrition_data = program.attrition_data.all()
        if attrition_data:
            self.stdout.write(f"Attrition Data ({attrition_data.count()} records):")
            for data in attrition_data:
                self.stdout.write(f"  {data.class_year}: {data.graduation_rate} graduation rate")
        
        # Demographics
        demographics = program.matriculant_demographics.all()
        if demographics:
            self.stdout.write(f"Demographics ({demographics.count()} records):")
            for demo in demographics:
                self.stdout.write(f"  {demo.year or 'N/A'}: Female: {demo.female_count}, Male: {demo.male_count}")
        
        # Curriculum
        curriculum = program.program_curriculum.all()
        if curriculum:
            self.stdout.write(f"Curriculum Data ({curriculum.count()} records):")
            for curr in curriculum:
                if curr.didactic_courses:
                    self.stdout.write(f"  Didactic courses: {len(curr.didactic_courses) if isinstance(curr.didactic_courses, list) else 'Available'}")
                if curr.clinical_rotations:
                    self.stdout.write(f"  Clinical rotations: {len(curr.clinical_rotations) if isinstance(curr.clinical_rotations, list) else 'Available'}")

    def _check_enhanced_data(self):
        """Check specifically for enhanced data fields"""
        self.stdout.write(self.style.SUCCESS("=== ENHANCED DATA CHECK ==="))
        
        # Check for programs with enhanced data
        programs_with_pance = Program.objects.filter(pance_pass_rates__isnull=False).distinct().count()
        programs_with_attrition = Program.objects.filter(attrition_data__isnull=False).distinct().count()
        programs_with_demographics = Program.objects.filter(matriculant_demographics__isnull=False).distinct().count()
        programs_with_curriculum = Program.objects.filter(program_curriculum__isnull=False).distinct().count()
        
        total_programs = Program.objects.count()
        
        self.stdout.write(f"Total Programs: {total_programs}")
        self.stdout.write(f"Programs with PANCE data: {programs_with_pance}")
        self.stdout.write(f"Programs with Attrition data: {programs_with_attrition}")
        self.stdout.write(f"Programs with Demographics: {programs_with_demographics}")
        self.stdout.write(f"Programs with Curriculum: {programs_with_curriculum}")
        
        # Show sample programs with enhanced data
        sample_programs = Program.objects.filter(
            Q(pance_pass_rates__isnull=False) |
            Q(attrition_data__isnull=False) |
            Q(matriculant_demographics__isnull=False) |
            Q(program_curriculum__isnull=False)
        ).distinct()[:5]
        
        if sample_programs:
            self.stdout.write(f"\nSample programs with enhanced data:")
            for program in sample_programs:
                self.stdout.write(f"  - {program.name}")

    def _show_all_programs(self):
        """Show all programs with basic info"""
        programs = Program.objects.select_related('school').all()
        
        self.stdout.write(self.style.SUCCESS(f"=== ALL PROGRAMS ({programs.count()}) ==="))
        
        for program in programs:
            self.stdout.write(f"\n{program.name}")
            self.stdout.write(f"  School: {program.school.name}")
            self.stdout.write(f"  Class Size: {program.class_size}")
            self.stdout.write(f"  Average GPA: {program.average_gpa}")
            
            # Quick enhanced data check
            has_pance = program.pance_pass_rates.exists()
            has_attrition = program.attrition_data.exists()
            has_demographics = program.matriculant_demographics.exists()
            has_curriculum = program.program_curriculum.exists()
            
            enhanced_features = []
            if has_pance: enhanced_features.append("PANCE")
            if has_attrition: enhanced_features.append("Attrition")
            if has_demographics: enhanced_features.append("Demographics")
            if has_curriculum: enhanced_features.append("Curriculum")
            
            if enhanced_features:
                self.stdout.write(f"  Enhanced Data: {', '.join(enhanced_features)}")
            else:
                self.stdout.write(f"  Enhanced Data: None")

    def _export_to_json(self, filename):
        """Export program data to JSON file"""
        programs = Program.objects.select_related('school').prefetch_related(
            'pance_pass_rates',
            'attrition_data',
            'matriculant_demographics',
            'program_curriculum',
            'enhanced_class_profiles'
        ).all()

        export_data = []

        for program in programs:
            program_data = {
                'program_name': program.name,
                'school': program.school.name,
                'description': program.description,
                'url': program.url,
                'application_deadline': program.application_deadline.isoformat() if program.application_deadline else None,
                'program_start_date': program.program_start_date.isoformat() if program.program_start_date else None,
                'average_gpa': str(program.average_gpa) if program.average_gpa else None,
                'class_size': program.class_size,
                'pance_pass_rates': [
                    {
                        'year': rate.year,
                        'group': rate.group,
                        'candidates_took_pance': rate.candidates_took_pance,
                        'program_pass_rate': rate.program_pass_rate,
                        'national_pass_rate': rate.national_pass_rate,
                        'ultimate_pass_rate': rate.ultimate_pass_rate
                    }
                    for rate in program.pance_pass_rates.all()
                ],
                'attrition_data': [
                    {
                        'class_year': data.class_year,
                        'entering_class_size': data.entering_class_size,
                        'graduates': data.graduates,
                        'graduation_rate': data.graduation_rate,
                        'attrition_rate': data.attrition_rate
                    }
                    for data in program.attrition_data.all()
                ]
            }
            export_data.append(program_data)

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)

        self.stdout.write(self.style.SUCCESS(f"Exported {len(export_data)} programs to {filename}"))
