import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import styled from '@emotion/styled';

const Container = styled.div`
  min-height: 100vh;
  background-image: url('/Parkwithcityinback.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: 2rem;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    z-index: 1;
  }
`;

const Header = styled.header`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3rem;
  position: relative;
  z-index: 2;

  h1 {
    color: white;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
    font-size: 2.5rem;
  }
`;

const NavGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
`;

const NavCard = styled.div`
  background: white;
  border-radius: 10px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s ease;

  &:hover {
    transform: translateY(-5px);
  }

  h2 {
    color: #2c3e50;
    margin-bottom: 1rem;
  }

  p {
    color: #7f8c8d;
    line-height: 1.6;
  }
`;

const LogoutButton = styled.button`
  background: #e74c3c;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  cursor: pointer;
  transition: background 0.2s ease;

  &:hover {
    background: #c0392b;
  }
`;

const HomeHub = () => {
  const navigate = useNavigate();
  const { signOut } = useAuth();

  const handleLogout = () => {
    signOut();
    navigate('/');
  };

  const navigationCards = [
    {
      title: 'My Profile',
      description: 'View and manage your profile information, academic history, and transcript data.',
      path: '/dashboard'
    },

    {
      title: 'PA Program Explorer',
      description: 'Explore and compare different PA programs, ask questions, and get detailed insights.',
      path: '/pamatch'
    }
  ];

  return (
    <Container>
      <Header>
        <h1>Welcome to GradMatch</h1>
        <LogoutButton onClick={handleLogout}>Logout</LogoutButton>
      </Header>

      <NavGrid>
        {navigationCards.map((card, index) => (
          <NavCard key={index} onClick={() => navigate(card.path)}>
            <h2>{card.title}</h2>
            <p>{card.description}</p>
          </NavCard>
        ))}
      </NavGrid>
    </Container>
  );
};

export default HomeHub; 