from django.core.management.base import BaseCommand
from users.models import UserProfile
from django.contrib.auth import get_user_model

User = get_user_model()

class Command(BaseCommand):
    help = 'Display all user profiles'

    def handle(self, *args, **kwargs):
        profiles = UserProfile.objects.all().select_related('user')
        
        for profile in profiles:
            self.stdout.write(self.style.SUCCESS(f"\nProfile for user: {profile.user.email}"))
            self.stdout.write("-" * 50)
            self.stdout.write(f"First Name: {profile.first_name}")
            self.stdout.write(f"Last Name: {profile.last_name}")
            self.stdout.write(f"Date of Birth: {profile.date_of_birth}")
            self.stdout.write(f"Gender: {profile.gender}")
            self.stdout.write(f"Ethnicity: {profile.ethnicity}")
            self.stdout.write(f"Direct Patient Care Hours: {profile.direct_patient_care_hours}")
            self.stdout.write(f"Shadowing Hours: {profile.shadowing_hours}")
            self.stdout.write("-" * 50) 